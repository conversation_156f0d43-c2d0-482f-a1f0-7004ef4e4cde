USE rms2019;
GO

IF OBJECT_ID('dbo.usp_Sync_RMS_DailyBookData', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_Sync_RMS_DailyBookData;
GO

CREATE PROCEDURE dbo.usp_Sync_RMS_DailyBookData
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @TargetNaturalDate DATE = GETDATE() - 1;

    BEGIN TRY
        -- Merge bookcacheinfo
        MERGE INTO dbo.bookcacheinfo AS T
        USING (
            SELECT Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, 
                   Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, 
                   PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, 
                   BookUserName, BookDateTime, OrderUserID, OrderUserName, RmNo, Val1
            FROM cloudRms2019.rms2019.dbo.bookcacheinfo 
            WHERE CAST(BookDateTime AS DATE) = @TargetNaturalDate
        ) AS S
        ON T.Ikey = S.Ikey
        WHEN MATCHED THEN 
            UPDATE SET T.BookStatus = S.BookStatus,
                       T.CheckinStatus = S.CheckinStatus,
                       T.RmNo = S.RmNo
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, 
                    Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, 
                    PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, 
                    BookUserName, BookDateTime, OrderUserID, OrderUserName, RmNo, Val1)
            VALUES (S.Ikey, S.BookNo, S.ShopId, S.CustKey, S.CustName, S.CustTel, S.ComeDate, S.ComeTime, 
                    S.Beg_Key, S.Beg_Name, S.End_Key, S.End_Name, S.Numbers, S.RtNo, S.RtName, S.CtNo, S.CtName, 
                    S.PtNo, S.PtName, S.BookMemory, S.BookStatus, S.CheckinStatus, S.BookShopId, S.BookUserId, 
                    S.BookUserName, S.BookDateTime, S.OrderUserID, S.OrderUserName, S.RmNo, S.Val1);

        -- Merge bookhistory
        MERGE INTO dbo.bookhistory AS T
        USING (
            SELECT Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, 
                   Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, 
                   PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, 
                   BookUserName, BookDateTime, OrderUserID, OrderUserName, RmNo, Val1, CallTel, 
                   DelUserName, DemandNumber, DepositTot, EditDateTime, EditShopId, EditUserId, 
                   EditUserName, IsDelete, OpenId
            FROM cloudRms2019.rms2019.dbo.bookhistory 
            WHERE CAST(BookDateTime AS DATE) = @TargetNaturalDate
        ) AS S
        ON T.Ikey = S.Ikey
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, 
                    Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, 
                    PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, 
                    BookUserName, BookDateTime, OrderUserID, OrderUserName, RmNo, Val1, CallTel, 
                    DelUserName, DemandNumber, DepositTot, EditDateTime, EditShopId, EditUserId, 
                    EditUserName, IsDelete, OpenId)
            VALUES (S.Ikey, S.BookNo, S.ShopId, S.CustKey, S.CustName, S.CustTel, S.ComeDate, S.ComeTime, 
                    S.Beg_Key, S.Beg_Name, S.End_Key, S.End_Name, S.Numbers, S.RtNo, S.RtName, S.CtNo, S.CtName, 
                    S.PtNo, S.PtName, S.BookMemory, S.BookStatus, S.CheckinStatus, S.BookShopId, S.BookUserId, 
                    S.BookUserName, S.BookDateTime, S.OrderUserID, S.OrderUserName, S.RmNo, S.Val1, S.CallTel, 
                    S.DelUserName, S.DemandNumber, S.DepositTot, S.EditDateTime, S.EditShopId, S.EditUserId, 
                    S.EditUserName, S.IsDelete, S.OpenId);

    END TRY
    BEGIN CATCH
        PRINT 'Error in usp_Sync_RMS_DailyBookData: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END
GO

PRINT 'Stored procedure dbo.usp_Sync_RMS_DailyBookData has been created successfully.';
GO
