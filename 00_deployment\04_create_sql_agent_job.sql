USE msdb;
GO

DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'RMS_Daily_Data_Sync_to_HQ';

-- 1. 如果作业已存在，则先删除，确保脚本可重复执行
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    PRINT N'Job "' + @jobName + N'" already exists. Deleting it now.';
    EXEC dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
END

-- 2. 创建作业
EXEC dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @description = N'每日从门店服务器(193)同步开台和预订数据到总部服务器(2.5)的rms2019数据库。';

-- 3. 创建作业步骤
-- 步骤 1: 同步开台数据 (opencacheinfo, openhistory)
EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'Sync Open Data (opencacheinfo & openhistory)',
    @subsystem = N'TSQL',
    @command = N'EXEC rms2019.dbo.usp_Sync_RMS_DailyOpenData;',
    @database_name = N'rms2019',
    @on_success_action = 3; -- 成功后转到下一步

-- 步骤 2: 同步预订数据 (bookcacheinfo, bookhistory)
EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'Sync Book Data (bookcacheinfo & bookhistory)',
    @subsystem = N'TSQL',
    @command = N'EXEC rms2019.dbo.usp_Sync_RMS_DailyBookData;',
    @database_name = N'rms2019',
    @on_success_action = 1; -- 成功后完成作业

-- 4. 创建调度
EXEC dbo.sp_add_schedule
    @schedule_name = N'Daily_0600_For_RMS_Sync',
    @freq_type = 4, -- 每日
    @freq_interval = 1, -- 每 1 天
    @active_start_time = 60000; -- 早上 6:00:00

-- 5. 将调度附加到作业
EXEC dbo.sp_attach_schedule
    @job_name = @jobName,
    @schedule_name = N'Daily_0600_For_RMS_Sync';

-- 6. 指定作业的目标服务器 (local 表示在本服务器上运行)
EXEC dbo.sp_add_jobserver
    @job_name = @jobName,
    @server_name = N'(local)';

PRINT N'Job "' + @jobName + N'" and its schedule have been created successfully.';
GO
