-- ===================================================================
-- 统一分析数据仓 - 数据表创建脚本 (V2 - 已根据源表优化)
-- ===================================================================
-- 本脚本用于创建数据分析平台所需的核心表结构，包括3个维度表和2个事实表。
-- 执行前请确保当前数据库中不存在同名表，或根据需要修改脚本。
-- ===================================================================

--
-- 维度表 (Dimension Tables)
--

-- 1. Dim_Date (日期维度表)
-- 用途: 提供统一的、丰富的日期维度信息，是所有时间相关分析的基础。
PRINT 'Creating table: Dim_Date';
CREATE TABLE Dim_Date (
    DateSK INT PRIMARY KEY,              -- 代理键
    FullDate DATE NOT NULL,              -- 完整日期
    Year INT NOT NULL,                   -- 年份
    Month INT NOT NULL,                  -- 月份
    Day INT NOT NULL,                    -- 日
    DayOfWeek INT NOT NULL,              -- 一周中的第几天 (1=周一, 7=周日)
    WeekdayName_ZH NVARCHAR(10) NOT NULL, -- 中文星期名
    IsWeekend BIT NOT NULL,              -- 是否为周末
    IsHoliday BIT NOT NULL,              -- 是否为法定节假日
    HolidayName NVARCHAR(50) NULL        -- 节假日名称
);
GO

-- 2. Dim_Shop (门店维度表)
-- 用途: 提供所有门店的静态信息，确保门店名称、区域等信息的统一性。
PRINT 'Creating table: Dim_Shop';
CREATE TABLE Dim_Shop (
    ShopSK INT PRIMARY KEY IDENTITY(1,1), -- 代理键
    ShopID INT NOT NULL,                 -- 业务主键，源系统ShopInfo表的ShopID
    ShopName NVARCHAR(100) NOT NULL,     -- 门店官方名称
    Address NVARCHAR(100) NULL,          -- 门店完整地址
    City NVARCHAR(50) NULL,              -- 所在城市
    Region NVARCHAR(50) NULL,            -- 所在区域
    OpenDate DATE NULL,                  -- 开业日期 (源表为nvarchar，ETL时需转换)
    IsActive BIT NOT NULL                -- 门店是否在用 (源自IsUse字段)
);
GO

-- 3. Dim_TimeSlot (时段维度表)
-- 用途: 统一定义业务上使用的所有特殊分析时段。
PRINT 'Creating table: Dim_TimeSlot';
CREATE TABLE Dim_TimeSlot (
    TimeSlotSK INT PRIMARY KEY IDENTITY(1,1), -- 代理键
    TimeSlotBusinessKey NVARCHAR(10) NOT NULL, -- 业务主键, 源系统timeinfo表的TimeNo
    TimeSlotName NVARCHAR(50) NOT NULL,  -- 时段名称
    StartTime TIME NOT NULL,             -- 开始时间 (源表为int，ETL时需转换)
    EndTime TIME NOT NULL,               -- 结束时间 (源表为int，ETL时需转换)
    TimeSlotGroup NVARCHAR(50) NULL,     -- 时段分组, 如 '白天档'
    IsSpecial BIT NOT NULL               -- 是否为特殊时段 (源自IsSpecial字段)
);
GO

--
-- 事实表 (Fact Tables)
--

-- 4. Fact_Daily_TimeSlot_Summary (时段级事实表)
-- 用途: 存放最精细的、与每个具体“时段”相关的运营指标。
PRINT 'Creating table: Fact_Daily_TimeSlot_Summary';
CREATE TABLE Fact_Daily_TimeSlot_Summary (
    TimeSlotSummarySK BIGINT PRIMARY KEY IDENTITY(1,1), -- 代理键
    DateSK INT NOT NULL,                         -- 外键，关联 Dim_Date
    ShopSK INT NOT NULL,                         -- 外键，关联 Dim_Shop
    TimeSlotSK INT NOT NULL,                     -- 外键，关联 Dim_TimeSlot
    BookedRooms INT NULL,                        -- 预订房间数
    BookedGuests INT NULL,                       -- 预订人数
    OccupiedRooms INT NULL,                      -- 待客房间数
    OccupiedGuests INT NULL,                     -- 待客人数
    OccupancyRate DECIMAL(5, 4) NULL,            -- 开房率
    Revenue DECIMAL(18, 2) NULL,                 -- 该时段总营业额
    Revenue_By_Channel_KPlus DECIMAL(18, 2) NULL,    -- K+渠道收入
    Revenue_By_Channel_Special DECIMAL(18, 2) NULL,  -- 特权预约渠道收入
    Revenue_By_Channel_Meituan DECIMAL(18, 2) NULL,  -- 美团渠道收入
    Revenue_By_Channel_Douyin DECIMAL(18, 2) NULL,   -- 抖音渠道收入
    Revenue_By_Channel_RoomFee DECIMAL(18, 2) NULL,  -- 纯房费收入
    DirectFall_Batches INT NULL                  -- 直落批次数
);
GO

-- 5. Fact_Daily_Shop_Summary (门店级事实表)
-- 用途: 存放按“天”汇总的全局性、总结性指标。
PRINT 'Creating table: Fact_Daily_Shop_Summary';
CREATE TABLE Fact_Daily_Shop_Summary (
    ShopSummarySK BIGINT PRIMARY KEY IDENTITY(1,1), -- 代理键
    DateSK INT NOT NULL,                         -- 外键，关联 Dim_Date
    ShopSK INT NOT NULL,                         -- 外键，关联 Dim_Shop
    TotalRevenue DECIMAL(18, 2) NULL,            -- 全天总营业额
    DayTimeRevenue DECIMAL(18, 2) NULL,          -- 白天档总营业额
    NightTimeRevenue DECIMAL(18, 2) NULL,        -- 晚上档总营业额
    TotalBatches INT NULL,                       -- 全天总批次数
    BuffetGuestCount INT NULL,                   -- 全天自助餐总人数
    TotalDirectFallGuests INT NULL,              -- 全天直落总人数
    ComplimentaryBatches INT NULL,               -- 全天招待总批次数
    ComplimentaryRevenue DECIMAL(18, 2) NULL,    -- 全天招待总金额
    PrivilegeBooking_Count_0Yuan INT NULL,       -- 0元特权预约执行次数
    PrivilegeBooking_Count_5Yuan INT NULL,       -- 5元特权预约执行次数
    PrivilegeBooking_Count_10Yuan INT NULL,      -- 10元特权预约执行次数
    PrivilegeBooking_Count_15Yuan INT NULL,      -- 15元特权预约执行次数
    Fee_Meituan_Booking DECIMAL(18, 2) NULL,     -- 美团预约类总手续费
    Fee_Meituan_GroupBuy DECIMAL(18, 2) NULL,    -- 美团团购类总手续费
    Fee_Douyin_Booking DECIMAL(18, 2) NULL,      -- 抖音预约类总手续费
    Fee_Douyin_GroupBuy DECIMAL(18, 2) NULL,     -- 抖音团购类总手续费
    Fee_Bank_GF DECIMAL(18, 2) NULL,             -- 广发银行总手续费
    Fee_Bank_CITIC DECIMAL(18, 2) NULL,          -- 中信银行总手续费
    Fee_Bank_UnionPay DECIMAL(18, 2) NULL        -- 银联总手续费
);
GO

PRINT 'All tables created successfully.';