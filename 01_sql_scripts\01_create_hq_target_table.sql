
-- ===================================================================
-- 脚本1: 在总部服务器 (192.168.2.5) 的 operatedata 数据库执行
-- ===================================================================
-- 用途: 创建一个中央表，用于接收所有门店上传的小时房态统计数据。
-- ===================================================================

IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RoomStatisticsHourly]') AND type IN ('U'))
	DROP TABLE [dbo].[RoomStatisticsHourly]
GO

PRINT 'Creating table: RoomStatisticsHourly on HQ Server';
CREATE TABLE [dbo].[RoomStatisticsHourly] (
    [ReportID] BIGINT PRIMARY KEY IDENTITY(1,1), -- 主键
    [ShopID] INT NOT NULL,                      -- 新增字段，用于区分数据来自哪个门店
    [LogTime] DATETIME NOT NULL,                 -- 统计时间
    [ValidRoomsCount] INT NULL,                  -- 可供出租房间数
    [BadRoomsCount] INT NULL,                    -- 坏房房间数
    [Status_A_Count] INT NULL,                   -- 结账房间数
    [Status_B_Count] INT NULL,                   -- 坏房房间数 (冗余，但保留以匹配源)
    [Status_E_Count] INT NULL,                   -- 空闲房间数
    [Status_U_Count] INT NULL                    -- 占用房间数
);
GO

PRINT 'Table RoomStatisticsHourly created successfully on HQ server.';
GO
