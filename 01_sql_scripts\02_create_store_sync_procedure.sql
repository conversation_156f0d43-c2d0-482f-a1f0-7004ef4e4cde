
-- ===================================================================
-- 脚本2: 在每个门店服务器 (如 193.112.2.229) 的 dbfood 数据库执行
-- ===================================================================
-- 用途: 创建一个存储过程，用于将本店当日的小时房态统计数据，回传到总部服务器。
-- 调用时机: 在门店“收市”流程的最后一步调用此存储过程。
-- ===================================================================

IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_SyncHourlyStatsToHQ]') AND type IN ('P', 'PC'))
	DROP PROCEDURE [dbo].[usp_SyncHourlyStatsToHQ]
GO

PRINT 'Creating procedure: usp_SyncHourlyStatsToHQ on Store Server';
GO

CREATE PROCEDURE [dbo].[usp_SyncHourlyStatsToHQ]
    @WorkDate DATE, -- 需要同步的营业日期
    @ShopID INT      -- 当前门店的ID
AS
BEGIN
    SET NOCOUNT ON;

    -- 使用 TRY...CATCH 块来捕获潜在的错误，确保同步失败时不影响门店正常的收市流程。
    BEGIN TRY
        PRINT 'Starting data synchronization to HQ server...';

        -- 重要: [HQ_SERVER] 是一个占位符，它代表您配置的、从门店指向总部的链接服务器的名称。
        -- 请确保该链接服务器存在且配置正确。
        INSERT INTO [HQ_SERVER].[operatedata].[dbo].[RoomStatisticsHourly] (
            ShopID,
            LogTime,
            ValidRoomsCount,
            BadRoomsCount,
            Status_A_Count,
            Status_B_Count,
            Status_E_Count,
            Status_U_Count
        )
        SELECT
            @ShopID, -- 直接使用传入的门店ID
            LogTime,
            ValidRoomsCount,
            BadRoomsCount,
            Status_A_Count,
            Status_B_Count,
            Status_E_Count,
            Status_U_Count
        FROM
            dbo.RoomStatisticsHourly -- 从本店的源表中读取
        WHERE
            CONVERT(DATE, LogTime) = @WorkDate; -- 只同步指定营业日的数据

        PRINT 'Data synchronization completed successfully.';

    END TRY
    BEGIN CATCH
        -- 如果发生错误，只打印错误信息，不中断主流程。
        PRINT 'ERROR: Data synchronization to HQ failed.';
        PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR);
        PRINT 'Error Message: ' + ERROR_MESSAGE();
    END CATCH

    SET NOCOUNT OFF;
END
GO

PRINT 'Procedure usp_SyncHourlyStatsToHQ created successfully on Store server.';
GO
