
USE operatedata;
GO

-- First, check if a primary key already exists and drop it if it's incorrect (unlikely since our check showed it was missing)
IF EXISTS (SELECT * FROM sys.key_constraints WHERE type = 'PK' AND parent_object_id = OBJECT_ID('dbo.RoomStatisticsHourly'))
BEGIN
    DECLARE @pk_name NVARCHAR(200)
    SELECT @pk_name = name FROM sys.key_constraints WHERE type = 'PK' AND parent_object_id = OBJECT_ID('dbo.RoomStatisticsHourly')
    -- This check is important to prevent SQL injection if @pk_name was from user input
    IF @pk_name IS NOT NULL
    BEGIN
        EXEC('ALTER TABLE dbo.RoomStatisticsHourly DROP CONSTRAINT ' + @pk_name)
        PRINT 'Dropped existing primary key.'
    END
END
GO

-- Add the new composite primary key
ALTER TABLE dbo.RoomStatisticsHourly
ADD CONSTRAINT PK_RoomStatisticsHourly PRIMARY KEY CLUSTERED (ShopID, LogTime);
GO

PRINT 'Successfully added composite primary key (ShopID, LogTime) to RoomStatisticsHourly.';
GO
