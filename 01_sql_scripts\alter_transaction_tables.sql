
-- Use the target database
USE operatedata;
GO

-- Alter msginfo table: Add WorkDate if it does not exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = N'WorkDate' AND Object_ID = Object_ID(N'dbo.msginfo'))
BEGIN
    ALTER TABLE dbo.msginfo ADD WorkDate DATE NULL;
    PRINT 'Column WorkDate added to dbo.msginfo.';
END
ELSE
BEGIN
    PRINT 'Column WorkDate already exists in dbo.msginfo.';
END
GO

-- Alter drinksinfo table: Add ShopId if it does not exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = N'ShopId' AND Object_ID = Object_ID(N'dbo.drinksinfo'))
BEGIN
    ALTER TABLE dbo.drinksinfo ADD ShopId INT NULL;
    PRINT 'Column ShopId added to dbo.drinksinfo.';
END
ELSE
BEGIN
    PRINT 'Column ShopId already exists in dbo.drinksinfo.';
END
GO

-- Alter drinksinfo table: Add WorkDate if it does not exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = N'WorkDate' AND Object_ID = Object_ID(N'dbo.drinksinfo'))
BEGIN
    ALTER TABLE dbo.drinksinfo ADD WorkDate DATE NULL;
    PRINT 'Column WorkDate added to dbo.drinksinfo.';
END
ELSE
BEGIN
    PRINT 'Column WorkDate already exists in dbo.drinksinfo.';
END
GO

PRINT 'Table alteration script finished.';
GO
