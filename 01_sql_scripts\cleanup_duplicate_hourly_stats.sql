
USE operatedata;
GO

-- Use a Common Table Expression (CTE) with ROW_NUMBER to find and delete duplicates.
-- This method identifies all rows that have the same ShopID and LogTime, 
-- and deletes all but one instance of each group, making the remaining rows unique.
WITH Duplicates AS (
    SELECT
        ROW_NUMBER() OVER(PARTITION BY ShopID, LogTime ORDER BY (SELECT NULL)) AS rn
    FROM
        dbo.RoomStatisticsHourly
)
DELETE FROM Duplicates WHERE rn > 1;
GO

PRINT 'Duplicate row cleanup finished.';
GO
