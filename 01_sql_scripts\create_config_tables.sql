
-- Use the target database
USE operatedata;
GO

-- Drop tables if they exist to ensure a clean slate
IF OBJECT_ID('dbo.barfdtype', 'U') IS NOT NULL DROP TABLE dbo.barfdtype;
IF OBJECT_ID('dbo.barfood', 'U') IS NOT NULL DROP TABLE dbo.barfood;
GO

-- Create table: barfdtype
CREATE TABLE dbo.barfdtype (
    FtNo NVARCHAR(2) NOT NULL,
    FtCName NVARCHAR(50) NOT NULL DEFAULT '',
    ShopId INT NOT NULL DEFAULT 1
);
GO

-- Create table: barfood
CREATE TABLE dbo.barfood (
    FtNo NVARCHAR(2) NOT NULL,
    FdNo NVARCHAR(5) NOT NULL,
    FdCName NVARCHAR(50) NOT NULL DEFAULT '',
    SumTotal INT NOT NULL DEFAULT 0,
    KindeeId NVARCHAR(50) NOT NULL DEFAULT '',
    ShopId INT NOT NULL DEFAULT 1,
    Number NVARCHAR(2) NOT NULL
);
GO

PRINT 'Config tables (barfdtype, barfood) created successfully.';
GO
