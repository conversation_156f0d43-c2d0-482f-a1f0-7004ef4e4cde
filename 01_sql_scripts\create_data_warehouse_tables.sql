
-- ==================================================================================================
-- 文件名: create_data_warehouse_tables.sql
-- 描述: 用于创建“统一分析数据仓”的所有核心表，包括维度表和事实表。
-- 设计版本: V2
-- ==================================================================================================

-- ----------------------------
-- 1. 创建 Dim_Date (日期维度表)
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Dim_Date]') AND type IN ('U'))
	DROP TABLE [dbo].[Dim_Date]
GO

CREATE TABLE [dbo].[Dim_Date] (
  [DateSK] INT NOT NULL PRIMARY KEY,
  [FullDate] DATE NOT NULL,
  [Year] INT NOT NULL,
  [Month] INT NOT NULL,
  [Day] INT NOT NULL,
  [DayOfWeek] INT NOT NULL,
  [WeekdayName_ZH] NVARCHAR(10) NOT NULL,
  [IsWeekend] BIT NOT NULL,
  [IsHoliday] BIT NOT NULL,
  [HolidayName] NVARCHAR(50) NULL
);
GO

-- ----------------------------
-- 2. 创建 Dim_Shop (门店维度表)
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Dim_Shop]') AND type IN ('U'))
	DROP TABLE [dbo].[Dim_Shop]
GO

CREATE TABLE [dbo].[Dim_Shop] (
  [ShopSK] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
  [ShopID] INT NOT NULL, -- 源系统业务主键
  [ShopName] NVARCHAR(100) NOT NULL,
  [Address] NVARCHAR(100) NULL,
  [City] NVARCHAR(50) NULL,
  [Region] NVARCHAR(50) NULL,
  [OpenDate] DATE NULL,
  [IsActive] BIT NOT NULL DEFAULT 1
);
GO

-- ----------------------------
-- 3. 创建 Dim_TimeSlot (时段维度表)
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Dim_TimeSlot]') AND type IN ('U'))
	DROP TABLE [dbo].[Dim_TimeSlot]
GO

CREATE TABLE [dbo].[Dim_TimeSlot] (
  [TimeSlotSK] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
  [TimeSlotBusinessKey] NVARCHAR(10) NOT NULL, -- 源系统业务主键
  [TimeSlotName] NVARCHAR(50) NOT NULL,
  [StartTime] TIME NULL,
  [EndTime] TIME NULL,
  [TimeSlotGroup] NVARCHAR(50) NULL,
  [IsSpecial] BIT NOT NULL DEFAULT 0
);
GO

-- ----------------------------
-- 4. 创建 Fact_Daily_TimeSlot_Summary (时段级事实表)
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Fact_Daily_TimeSlot_Summary]') AND type IN ('U'))
	DROP TABLE [dbo].[Fact_Daily_TimeSlot_Summary]
GO

CREATE TABLE [dbo].[Fact_Daily_TimeSlot_Summary] (
  [TimeSlotSummarySK] BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
  [DateSK] INT NOT NULL, -- 外键 -> Dim_Date
  [ShopSK] INT NOT NULL, -- 外键 -> Dim_Shop
  [TimeSlotSK] INT NOT NULL, -- 外键 -> Dim_TimeSlot
  [BookedRooms] INT NULL,
  [BookedGuests] INT NULL,
  [OccupiedRooms] INT NULL,
  [OccupiedGuests] INT NULL,
  [OccupancyRate] DECIMAL(5, 4) NULL,
  [Revenue] DECIMAL(18, 2) NULL,
  [Revenue_By_Channel_KPlus] DECIMAL(18, 2) NULL,
  [Revenue_By_Channel_Special] DECIMAL(18, 2) NULL,
  [Revenue_By_Channel_Meituan] DECIMAL(18, 2) NULL,
  [Revenue_By_Channel_Douyin] DECIMAL(18, 2) NULL,
  [Revenue_By_Channel_RoomFee] DECIMAL(18, 2) NULL,
  [DirectFall_Batches] INT NULL
);
GO

-- ----------------------------
-- 5. 创建 Fact_Daily_Shop_Summary (门店级事实表)
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Fact_Daily_Shop_Summary]') AND type IN ('U'))
	DROP TABLE [dbo].[Fact_Daily_Shop_Summary]
GO

CREATE TABLE [dbo].[Fact_Daily_Shop_Summary] (
  [ShopSummarySK] BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
  [DateSK] INT NOT NULL, -- 外键 -> Dim_Date
  [ShopSK] INT NOT NULL, -- 外键 -> Dim_Shop
  [TotalRevenue] DECIMAL(18, 2) NULL,
  [DayTimeRevenue] DECIMAL(18, 2) NULL,
  [NightTimeRevenue] DECIMAL(18, 2) NULL,
  [TotalBatches] INT NULL,
  [BuffetGuestCount] INT NULL,
  [TotalDirectFallGuests] INT NULL,
  [ComplimentaryBatches] INT NULL,
  [ComplimentaryRevenue] DECIMAL(18, 2) NULL,
  [PrivilegeBooking_Count_0Yuan] INT NULL,
  [PrivilegeBooking_Count_5Yuan] INT NULL,
  [PrivilegeBooking_Count_10Yuan] INT NULL,
  [PrivilegeBooking_Count_15Yuan] INT NULL,
  [Fee_Meituan_Booking] DECIMAL(18, 2) NULL,
  [Fee_Meituan_GroupBuy] DECIMAL(18, 2) NULL,
  [Fee_Douyin_Booking] DECIMAL(18, 2) NULL,
  [Fee_Douyin_GroupBuy] DECIMAL(18, 2) NULL,
  [Fee_Bank_GF] DECIMAL(18, 2) NULL,
  [Fee_Bank_CITIC] DECIMAL(18, 2) NULL,
  [Fee_Bank_UnionPay] DECIMAL(18, 2) NULL
);
GO

PRINT 'Data warehouse tables created successfully.';

