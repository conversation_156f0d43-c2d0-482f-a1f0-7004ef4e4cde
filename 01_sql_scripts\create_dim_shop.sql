
USE operatedata;
GO

IF OBJECT_ID('dbo.Dim_Shop', 'U') IS NOT NULL
    DROP TABLE dbo.Dim_Shop;
GO

CREATE TABLE dbo.Dim_Shop (
    ShopSK INT IDENTITY(1,1) PRIMARY KEY,
    ShopID INT NOT NULL,
    ShopName NVARCHAR(100) NOT NULL,
    Address NVARCHAR(100) NULL,
    City NVARCHAR(50) NULL,
    Region NVARCHAR(50) NULL,
    OpenDate DATE NULL,
    IsActive BIT NOT NULL
);
GO

-- Add a unique constraint on the business key to prevent duplicate shop entries
CREATE UNIQUE NONCLUSTERED INDEX UIX_Dim_Shop_ShopID ON dbo.Dim_Shop(ShopID);
GO

PRINT 'Table Dim_Shop created successfully.';
GO
