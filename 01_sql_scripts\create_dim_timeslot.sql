
USE operatedata;
GO

IF OBJECT_ID('dbo.Dim_TimeSlot', 'U') IS NOT NULL
    DROP TABLE dbo.Dim_TimeSlot;
GO

CREATE TABLE dbo.Dim_TimeSlot (
    TimeSlotSK INT IDENTITY(1,1) PRIMARY KEY,
    TimeSlotBusinessKey NVARCHAR(10) NOT NULL,
    TimeSlotName NVARCHAR(50) NULL,
    StartTime TIME NULL,
    EndTime TIME NULL,
    TimeSlotGroup NVARCHAR(50) NULL,
    IsSpecial BIT NOT NULL
);
GO

CREATE UNIQUE NONCLUSTERED INDEX UIX_Dim_TimeSlot_BusinessKey ON dbo.Dim_TimeSlot(TimeSlotBusinessKey);
GO

PRINT 'Table Dim_TimeSlot created successfully.';
GO
