
USE operatedata;
GO

IF OBJECT_ID('dbo.Fact_Deal_Redemption', 'U') IS NOT NULL
    DROP TABLE dbo.Fact_Deal_Redemption;
GO

CREATE TABLE dbo.Fact_Deal_Redemption (
    RedemptionSK BIGINT IDENTITY(1,1) PRIMARY KEY,
    DateSK INT NOT NULL,
    ShopSK INT NOT NULL,
    DealSK INT NOT NULL,
    RedemptionCount INT NOT NULL,
    RedemptionAmount DECIMAL(18, 2) NOT NULL,
    SubsidyAmount DECIMAL(18, 2) NOT NULL,
    PlatformFee DECIMAL(18, 2) NOT NULL,
    NetAmount DECIMAL(18, 2) NOT NULL,
    CONSTRAINT FK_Fact_Deal_Redemption_Date FOREIGN KEY (DateSK) REFERENCES dbo.Dim_Date(DateSK),
    CONSTRAINT FK_Fact_Deal_Redemption_Shop FOREIGN KEY (ShopSK) REFERENCES dbo.Dim_Shop(ShopSK),
    CONSTRAINT FK_Fact_Deal_Redemption_Deal FOREIGN KEY (DealSK) REFERENCES dbo.Dim_Bank_Deal(DealSK)
);
GO

-- Add a unique constraint to prevent duplicate ETL runs for the same day/shop/deal combination
CREATE UNIQUE NONCLUSTERED INDEX UIX_Fact_Deal_Redemption_NaturalKey ON dbo.Fact_Deal_Redemption(DateSK, ShopSK, DealSK);
GO

PRINT 'Fact table Fact_Deal_Redemption created successfully.';
GO
