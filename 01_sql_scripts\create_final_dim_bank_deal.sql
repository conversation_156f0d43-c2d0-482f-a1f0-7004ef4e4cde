
USE operatedata;
GO

IF OBJECT_ID('dbo.Dim_Bank_Deal', 'U') IS NOT NULL
    DROP TABLE dbo.Dim_Bank_Deal;
GO

CREATE TABLE dbo.Dim_Bank_Deal (
    DealSK INT IDENTITY(1,1) PRIMARY KEY,
    BankSK INT NULL, -- Foreign key to Dim_Bank
    FdNo VARCHAR(5) NULL, -- From the food table
    DealName NVARCHAR(100) NOT NULL,
    DealAmount DECIMAL(18, 2) NULL,
    SubsidyAmount DECIMAL(18, 2) NULL,
    TotalAmount DECIMAL(18, 2) NULL,
    ServiceFee DECIMAL(18, 2) NULL,
    NetAmount DECIMAL(18, 2) NULL,
    CONSTRAINT FK_Dim_Bank_Deal_BankSK FOREIGN KEY (BankSK) REFERENCES dbo.Dim_Bank(BankSK)
);
GO

PRINT 'Table Dim_Bank_Deal recreated successfully with the final schema.';
GO
