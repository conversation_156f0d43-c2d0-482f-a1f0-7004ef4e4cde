
-- Use the target database
USE operatedata;
GO

-- Drop tables if they exist to ensure a clean slate
IF OBJECT_ID('dbo.msginfo', 'U') IS NOT NULL DROP TABLE dbo.msginfo;
IF OBJECT_ID('dbo.drinksinfo', 'U') IS NOT NULL DROP TABLE dbo.drinksinfo;
IF OBJECT_ID('dbo.barfdtype', 'U') IS NOT NULL DROP TABLE dbo.barfdtype;
IF OBJECT_ID('dbo.barfood', 'U') IS NOT NULL DROP TABLE dbo.barfood;
GO

-- Create table: msginfo
CREATE TABLE dbo.msginfo (
    iKeyMsg NVARCHAR(50) NOT NULL,
    CustName NVARCHAR(50) NOT NULL DEFAULT '',
    CustTel NVARCHAR(11) NOT NULL DEFAULT '',
    MsgStatus INT NOT NULL DEFAULT 1,
    MsgPassword NVARCHAR(50) NOT NULL DEFAULT '',
    DeShopId INT NOT NULL DEFAULT 1,
    DeRmNo NVARCHAR(10) NOT NULL DEFAULT '',
    DeDatetime DATETIME2 NOT NULL DEFAULT '1900-01-01 00:00:00',
    DeBarName NVARCHAR(50) NOT NULL DEFAULT '',
    DeServiceName NVARCHAR(50) NOT NULL DEFAULT '',
    DeCheckName NVARCHAR(50) NOT NULL DEFAULT '',
    DeMemory NVARCHAR(500) NOT NULL DEFAULT '',
    DrShopId INT NOT NULL DEFAULT 1,
    DrRmNo NVARCHAR(10) NOT NULL DEFAULT '',
    DrDatetime DATETIME2 NULL,
    DrBarName NVARCHAR(50) NOT NULL DEFAULT '',
    DrServiceName NVARCHAR(50) NOT NULL DEFAULT '',
    DrCheckName NVARCHAR(50) NOT NULL DEFAULT '',
    DrMemory NVARCHAR(500) NOT NULL DEFAULT '',
    IsDelete BIT NOT NULL DEFAULT 0,
    DeleteUserName NVARCHAR(50) NOT NULL DEFAULT '',
    Val1 INT NOT NULL DEFAULT 0,
    Val2 INT NOT NULL DEFAULT 0,
    Val3 NVARCHAR(100) NOT NULL DEFAULT '',
    Val4 NVARCHAR(30) NOT NULL DEFAULT '',
    Val5 NVARCHAR(100) NOT NULL DEFAULT '',
    BrandId INT NOT NULL DEFAULT 0,
    ReNew INT NOT NULL DEFAULT 0,
    ICode NVARCHAR(15) NOT NULL DEFAULT '',
    DrCheckId INT NOT NULL,
    ExDatetime DATETIME2 NOT NULL,
    PRIMARY KEY (iKeyMsg)
);
GO

-- Create table: drinksinfo
CREATE TABLE dbo.drinksinfo (
    iKeyDrinks NVARCHAR(50) NOT NULL,
    iKeyMsg NVARCHAR(50) NOT NULL,
    DrinksName NVARCHAR(100) NOT NULL DEFAULT '',
    Unit NVARCHAR(10) NOT NULL DEFAULT '',
    DrinksQty INT NOT NULL DEFAULT 0,
    IsDelete BIT NOT NULL DEFAULT 0,
    DeleteUserName NVARCHAR(50) NOT NULL DEFAULT '',
    Val1 INT NOT NULL DEFAULT 0,
    Val2 INT NOT NULL DEFAULT 0,
    Val3 NVARCHAR(100) NOT NULL DEFAULT '',
    Val4 NVARCHAR(100) NOT NULL DEFAULT '',
    Val5 NVARCHAR(100) NOT NULL DEFAULT '',
    BrandId NVARCHAR(255) NOT NULL DEFAULT '1',
    GiveNum NVARCHAR(20) NOT NULL DEFAULT ' '
);
GO

-- Create table: barfdtype
CREATE TABLE dbo.barfdtype (
    FtNo NVARCHAR(2) NOT NULL,
    FtCName NVARCHAR(50) NOT NULL DEFAULT '',
    ShopId INT NOT NULL DEFAULT 1
);
GO

-- Create table: barfood
CREATE TABLE dbo.barfood (
    FtNo NVARCHAR(2) NOT NULL,
    FdNo NVARCHAR(5) NOT NULL,
    FdCName NVARCHAR(50) NOT NULL DEFAULT '',
    SumTotal INT NOT NULL DEFAULT 0,
    KindeeId NVARCHAR(50) NOT NULL DEFAULT '',
    ShopId INT NOT NULL DEFAULT 1,
    Number NVARCHAR(2) NOT NULL
);
GO

-- Create indexes after tables are created
PRINT 'Creating indexes...';
CREATE INDEX index_CustTel ON dbo.msginfo (CustTel);
CREATE INDEX index_MsgPassword ON dbo.msginfo (MsgPassword);
CREATE INDEX index_ICode ON dbo.msginfo (ICode);
CREATE INDEX index_MsgStatus ON dbo.msginfo (MsgStatus);
CREATE INDEX index_DeDatetime ON dbo.msginfo (DeDatetime);
CREATE INDEX index_Val4 ON dbo.msginfo (Val4);
CREATE INDEX index_IsDelete ON dbo.msginfo (IsDelete);
GO

CREATE INDEX index_key ON dbo.drinksinfo (iKeyMsg);
CREATE INDEX index_DrinksName ON dbo.drinksinfo (DrinksName);
GO

CREATE INDEX index_FdCName ON dbo.barfood (FdCName);
GO

PRINT 'Tables and indexes created successfully.';
GO
