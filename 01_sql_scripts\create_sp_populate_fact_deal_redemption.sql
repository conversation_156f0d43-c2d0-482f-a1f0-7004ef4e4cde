
USE operatedata;
GO

IF OBJECT_ID('dbo.usp_Populate_Fact_Deal_Redemption_Daily', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_Populate_Fact_Deal_Redemption_Daily;
GO

CREATE PROCEDURE dbo.usp_Populate_Fact_Deal_Redemption_Daily
    @TargetDate DATE
AS
BEGIN
    -- SET NOCOUNT ON prevents the sending of DONEINPROC messages for each statement
    -- in a stored procedure. For procedures that contain several statements that do not
    -- return much actual data, this can provide a significant performance boost.
    SET NOCOUNT ON;

    PRINT 'Starting ETL for Fact_Deal_Redemption for date: ' + CONVERT(VARCHAR, @TargetDate);

    -- Use a Common Table Expression (CTE) to hold the aggregated data for the target date.
    -- This improves readability and modularity.
    WITH AggregatedData AS (
        SELECT
            dt.DateSK,
            s.ShopSK,
            d.DealSK,
            SUM(f.FdQty) AS RedemptionCount,
            SUM(d.DealAmount * f.FdQty) AS RedemptionAmount,
            SUM(d.SubsidyAmount * f.FdQty) AS SubsidyAmount,
            SUM(d.ServiceFee * f.FdQty) AS PlatformFee,
            SUM(d.NetAmount * f.FdQty) AS NetAmount
        FROM
            dbo.fdcashbak AS f
        JOIN
            dbo.rmcloseinfo AS r ON f.InvNo = r.InvNo
        JOIN
            dbo.Dim_Bank_Deal AS d ON f.FdNo = d.FdNo COLLATE Chinese_PRC_CI_AS
        JOIN
            dbo.Dim_Shop AS s ON f.ShopId = s.ShopID
        JOIN
            dbo.Dim_Date AS dt ON CONVERT(date, r.workdate) = dt.FullDate
        WHERE
            d.FdNo IS NOT NULL
            AND CONVERT(date, r.workdate) = @TargetDate
        GROUP BY
            dt.DateSK, s.ShopSK, d.DealSK
    )
    -- MERGE the aggregated data into the fact table.
    -- This statement handles both INSERTs for new data and UPDATEs for existing data,
    -- making the stored procedure idempotent (safe to run multiple times for the same date).
    MERGE dbo.Fact_Deal_Redemption AS T
    USING AggregatedData AS S
    ON T.DateSK = S.DateSK AND T.ShopSK = S.ShopSK AND T.DealSK = S.DealSK
    WHEN MATCHED THEN
        UPDATE SET
            T.RedemptionCount = S.RedemptionCount,
            T.RedemptionAmount = S.RedemptionAmount,
            T.SubsidyAmount = S.SubsidyAmount,
            T.PlatformFee = S.PlatformFee,
            T.NetAmount = S.NetAmount
    WHEN NOT MATCHED BY TARGET THEN
        INSERT (DateSK, ShopSK, DealSK, RedemptionCount, RedemptionAmount, SubsidyAmount, PlatformFee, NetAmount)
        VALUES (S.DateSK, S.ShopSK, S.DealSK, S.RedemptionCount, S.RedemptionAmount, S.SubsidyAmount, S.PlatformFee, S.NetAmount);

    PRINT 'ETL process for Fact_Deal_Redemption completed for date: ' + CONVERT(VARCHAR, @TargetDate) + '. Rows affected: ' + CAST(@@ROWCOUNT AS VARCHAR);

END;
GO

PRINT 'Stored procedure usp_Populate_Fact_Deal_Redemption_Daily created successfully.';
GO
