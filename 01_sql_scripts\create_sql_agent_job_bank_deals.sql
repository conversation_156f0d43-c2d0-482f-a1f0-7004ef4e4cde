
USE msdb;
GO

-- ***************************************************************************
-- ** <PERSON><PERSON><PERSON> to create the SQL Server Agent Job for daily deal redemption ETL **
-- ***************************************************************************

DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'每日银行优惠券数据汇总';

-- -----------------------------------------------------
-- ** 1. Add the Job **
-- First, delete the job if it already exists to ensure a clean creation.
-- -----------------------------------------------------
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    EXEC dbo.sp_delete_job @jobId = @jobId, @delete_unused_schedule = 1;
    PRINT 'Existing job "' + @jobName + '" deleted.';
END

PRINT 'Creating job "' + @jobName + '"...';
EXEC dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @description = N'每日执行，汇总银行优惠券的核销数据到Fact_Deal_Redemption事实表。';

-- -----------------------------------------------------
-- ** 2. Add the Job Step (The actual work) **
-- -----------------------------------------------------
PRINT 'Adding job step to execute the stored procedure...';
EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'执行ETL存储过程',
    @subsystem = N'TSQL',
    @command = N'-- Execute the SP for the previous day
EXEC dbo.usp_Populate_Fact_Deal_Redemption_Daily @TargetDate = CAST(GETDATE() - 1 AS DATE);',
    @database_name = N'operatedata'; -- IMPORTANT: Run the command in the correct database context

-- -----------------------------------------------------
-- ** 3. Create the Schedule **
-- -----------------------------------------------------
PRINT 'Creating the daily schedule...';
EXEC dbo.sp_add_schedule
    @schedule_name = N'每日凌晨3点执行',
    @freq_type = 4, -- Daily
    @freq_interval = 1, -- Every 1 day
    @active_start_time = 30000; -- 03:00:00

-- -----------------------------------------------------
-- ** 4. Attach the Schedule to the Job **
-- -----------------------------------------------------
PRINT 'Attaching schedule to the job...';
EXEC dbo.sp_attach_schedule
    @job_name = @jobName,
    @schedule_name = N'每日凌晨3点执行';

-- -----------------------------------------------------
-- ** 5. Add the Job to a Server (the current server) **
-- -----------------------------------------------------
PRINT 'Adding job to the server...';
EXEC dbo.sp_add_jobserver
    @job_name = @jobName,
    @server_name = N'(local)';

PRINT ''
PRINT 'SQL Server Agent Job "' + @jobName + '" created successfully.';
GO
