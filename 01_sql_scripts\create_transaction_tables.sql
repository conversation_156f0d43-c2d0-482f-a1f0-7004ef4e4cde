
-- Use the target database
USE operatedata;
GO

-- Drop tables if they exist to ensure a clean slate
IF OBJECT_ID('dbo.synced_msginfo', 'U') IS NOT NULL DROP TABLE dbo.synced_msginfo;
IF OBJECT_ID('dbo.synced_drinksinfo', 'U') IS NOT NULL DROP TABLE dbo.synced_drinksinfo;
GO

-- Create table: synced_msginfo
-- This table is a replica of MySQL's msginfo but with an added WorkDate column.
CREATE TABLE dbo.synced_msginfo (
    -- New business logic columns
    WorkDate DATE NULL,

    -- Original columns from MySQL
    iKeyMsg NVARCHAR(50) NOT NULL,
    CustName NVARCHAR(50) NOT NULL DEFAULT '',
    CustTel NVARCHAR(11) NOT NULL DEFAULT '',
    MsgStatus INT NOT NULL DEFAULT 1,
    MsgPassword NVARCHAR(50) NOT NULL DEFAULT '',
    DeShopId INT NOT NULL DEFAULT 1,
    DeRmNo NVARCHAR(10) NOT NULL DEFAULT '',
    DeDatetime DATETIME2 NOT NULL DEFAULT '1900-01-01 00:00:00',
    DeBarName NVARCHAR(50) NOT NULL DEFAULT '',
    DeServiceName NVARCHAR(50) NOT NULL DEFAULT '',
    DeCheckName NVARCHAR(50) NOT NULL DEFAULT '',
    DeMemory NVARCHAR(500) NOT NULL DEFAULT '',
    DrShopId INT NOT NULL DEFAULT 1,
    DrRmNo NVARCHAR(10) NOT NULL DEFAULT '',
    DrDatetime DATETIME2 NULL,
    DrBarName NVARCHAR(50) NOT NULL DEFAULT '',
    DrServiceName NVARCHAR(50) NOT NULL DEFAULT '',
    DrCheckName NVARCHAR(50) NOT NULL DEFAULT '',
    DrMemory NVARCHAR(500) NOT NULL DEFAULT '',
    IsDelete BIT NOT NULL DEFAULT 0,
    DeleteUserName NVARCHAR(50) NOT NULL DEFAULT '',
    Val1 INT NOT NULL DEFAULT 0,
    Val2 INT NOT NULL DEFAULT 0,
    Val3 NVARCHAR(100) NOT NULL DEFAULT '',
    Val4 NVARCHAR(30) NOT NULL DEFAULT '',
    Val5 NVARCHAR(100) NOT NULL DEFAULT '',
    BrandId INT NOT NULL DEFAULT 0,
    ReNew INT NOT NULL DEFAULT 0,
    ICode NVARCHAR(15) NOT NULL DEFAULT '',
    DrCheckId INT NOT NULL,
    ExDatetime DATETIME2 NOT NULL,
    PRIMARY KEY (iKeyMsg)
);
GO

-- Create table: synced_drinksinfo
-- This table is a replica of MySQL's drinksinfo but with added ShopId and WorkDate columns.
CREATE TABLE dbo.synced_drinksinfo (
    -- New business logic columns
    ShopId INT NULL,
    WorkDate DATE NULL,

    -- Original columns from MySQL
    iKeyDrinks NVARCHAR(50) NOT NULL,
    iKeyMsg NVARCHAR(50) NOT NULL,
    DrinksName NVARCHAR(100) NOT NULL DEFAULT '',
    Unit NVARCHAR(10) NOT NULL DEFAULT '',
    DrinksQty INT NOT NULL DEFAULT 0,
    IsDelete BIT NOT NULL DEFAULT 0,
    DeleteUserName NVARCHAR(50) NOT NULL DEFAULT '',
    Val1 INT NOT NULL DEFAULT 0,
    Val2 INT NOT NULL DEFAULT 0,
    Val3 NVARCHAR(100) NOT NULL DEFAULT '',
    Val4 NVARCHAR(100) NOT NULL DEFAULT '',
    Val5 NVARCHAR(100) NOT NULL DEFAULT '',
    BrandId NVARCHAR(255) NOT NULL DEFAULT '1',
    GiveNum NVARCHAR(20) NOT NULL DEFAULT ' ',
    PRIMARY KEY (iKeyDrinks)
);
GO

PRINT 'Transaction tables (synced_msginfo, synced_drinksinfo) created successfully.';
GO
