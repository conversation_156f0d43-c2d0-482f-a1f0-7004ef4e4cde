
USE operatedata;
GO

PRINT 'Showing rows where LogID is the same (e.g., 17) but ShopID is different.';
PRINT 'This demonstrates that LogID repetition is normal and expected as long as the composite key (ShopID, LogTime) is unique.';
GO

SELECT TOP 10
    ShopID,
    LogID,
    LogTime,
    WorkDate
FROM
    dbo.RoomStatisticsHourly
WHERE
    LogID = 17 -- An example LogID we saw was repeated in the previous sample
ORDER BY
    ShopID, LogTime;
GO
