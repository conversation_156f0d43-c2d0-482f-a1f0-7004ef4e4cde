USE operatedata
GO

-- 查找所有包含job相关词汇的存储过程
SELECT 
    name AS procedure_name,
    type AS type_code,
    type_desc AS type_description,
    create_date,
    modify_date
FROM sys.procedures 
WHERE name LIKE '%job%' OR name LIKE '%Job%' OR name LIKE '%JOB%' 
ORDER BY name

GO

-- 也查找包含job的用户定义函数
SELECT 
    name AS function_name,
    type AS type_code,
    type_desc AS type_description,
    create_date,
    modify_date
FROM sys.objects 
WHERE type IN ('FN', 'IF', 'TF', 'FS', 'FT') -- 函数类型
AND (name LIKE '%job%' OR name LIKE '%Job%' OR name LIKE '%JOB%')
ORDER BY name

GO

-- 查找所有与SQL Agent作业相关的存储过程
SELECT 
    name AS procedure_name,
    type AS type_code,
    type_desc AS type_description,
    create_date,
    modify_date
FROM sys.procedures 
WHERE name LIKE '%agent%' OR name LIKE '%Agent%' OR name LIKE '%AGENT%'
    OR name LIKE '%job%' OR name LIKE '%Job%' OR name LIKE '%JOB%'
    OR name LIKE '%schedule%' OR name LIKE '%Schedule%' OR name LIKE '%SCHEDULE%'
ORDER BY name

GO