-- Step 1: Drop obsolete columns from the header table
PRINT 'Dropping obsolete columns from FullDailyReport_Header...';
IF EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'MealBatchCount' AND Object_ID = Object_ID(N'dbo.FullDailyReport_Header'))
BEGIN
    ALTER TABLE dbo.FullDailyReport_Header DROP COLUMN MealBatchCount;
END

IF EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'MealDirectFallBatchCount' AND Object_ID = Object_ID(N'dbo.FullDailyReport_Header'))
BEGIN
    ALTER TABLE dbo.FullDailyReport_Header DROP COLUMN MealDirectFallBatchCount;
END

IF EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'MealDirectFallRevenue' AND Object_ID = Object_ID(N'dbo.FullDailyReport_Header'))
BEGIN
    ALTER TABLE dbo.FullDailyReport_Header DROP COLUMN MealDirectFallRevenue;
END

IF EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'TotalDropInGuests' AND Object_ID = Object_ID(N'dbo.FullDailyReport_Header'))
BEGIN
    ALTER TABLE dbo.FullDailyReport_Header DROP COLUMN TotalDropInGuests;
END

IF EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'Night_After20_Revenue' AND Object_ID = Object_ID(N'dbo.FullDailyReport_Header'))
BEGIN
    ALTER TABLE dbo.FullDailyReport_Header DROP COLUMN Night_After20_Revenue;
END

-- Step 2: Rename existing column for consistency
PRINT 'Renaming DayTimeDirectFall to DayTimeDropInBatch...';
IF EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'DayTimeDirectFall' AND Object_ID = Object_ID(N'dbo.FullDailyReport_Header'))
BEGIN
    EXEC sp_rename 'dbo.FullDailyReport_Header.DayTimeDirectFall', 'DayTimeDropInBatch', 'COLUMN';
END

-- Step 3: Add the new column for direct fall guests
PRINT 'Adding new TotalDirectFallGuests column...';
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'TotalDirectFallGuests' AND Object_ID = Object_ID(N'dbo.FullDailyReport_Header'))
BEGIN
    ALTER TABLE dbo.FullDailyReport_Header ADD TotalDirectFallGuests INT NULL;
END

PRINT 'Table FullDailyReport_Header has been successfully updated.';