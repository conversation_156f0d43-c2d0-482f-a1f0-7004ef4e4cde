-- Add columns for Discount-Free metrics
PRINT 'Adding DiscountFree_BatchCount to FullDailyReport_NightDetails...';
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'DiscountFree_BatchCount' AND Object_ID = Object_ID(N'dbo.FullDailyReport_NightDetails'))
BEGIN
    ALTER TABLE dbo.FullDailyReport_NightDetails ADD DiscountFree_BatchCount INT NULL;
END

PRINT 'Adding DiscountFree_Revenue to FullDailyReport_NightDetails...';
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'DiscountFree_Revenue' AND Object_ID = Object_ID(N'dbo.FullDailyReport_NightDetails'))
BEGIN
    ALTER TABLE dbo.FullDailyReport_NightDetails ADD DiscountFree_Revenue DECIMAL(18, 2) NULL;
END

PRINT 'Table FullDailyReport_NightDetails has been successfully updated.';