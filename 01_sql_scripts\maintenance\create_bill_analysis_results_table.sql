USE dbfood;
GO

-- 检查表是否已存在，只有在不存在时才创建
IF OBJECT_ID('BillAnalysisResults', 'U') IS NULL
BEGIN
    PRINT 'Table BillAnalysisResults does not exist. Creating it now...';
    CREATE TABLE BillAnalysisResults (
        ResultID INT IDENTITY(1,1) PRIMARY KEY,
        InvNo NVARCHAR(50) NOT NULL,
        RmNo NVARCHAR(50),
        RoomSystemAmount DECIMAL(18, 2),
        WxPayTotalAmount DECIMAL(18, 2),
        Difference DECIMAL(18, 2),
        TransactionIDs NVARCHAR(MAX),
        CheckoutTime DATETIME,
        CloseTime DATETIME,
        IsDifferenceNormal BIT,
        LogTime DATETIME DEFAULT GETDATE()
    );
    PRINT 'Table BillAnalysisResults created successfully.';
END
ELSE
BEGIN
    PRINT 'Table BillAnalysisResults already exists.';
END
GO
