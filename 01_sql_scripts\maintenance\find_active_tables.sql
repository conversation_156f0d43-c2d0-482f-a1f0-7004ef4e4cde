
SELECT
    OBJECT_NAME(s.object_id) AS TableName,
    SUM(s.user_updates) AS TotalUpdates -- (Inserts + Deletes + Updates)
FROM
    sys.dm_db_index_usage_stats AS s
INNER JOIN
    sys.objects AS o ON s.object_id = o.object_id
WHERE
    s.database_id = DB_ID('dbfood')
    AND o.type = 'U' -- User Tables
GROUP BY
    s.object_id, OBJECT_NAME(s.object_id)
HAVING
    SUM(s.user_updates) > 0
ORDER BY
    TotalUpdates DESC;
