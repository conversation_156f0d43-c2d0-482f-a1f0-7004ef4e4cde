SET NOCOUNT ON;

PRINT '# 数据库表清单';
PRINT '';
PRINT '*报告生成时间: ' + CONVERT(varchar, GETDATE(), 20) + '*';
PRINT '';

PRINT '## 本地服务器: 192.168.2.5';
PRINT '';

PRINT '### 数据库: `dbfood`';
SELECT CONCAT('- `', TABLE_SCHEMA, '.', TABLE_NAME, '`') FROM dbfood.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_SCHEMA, TABLE_NAME;

PRINT '';
PRINT '### 数据库: `mims`';
SELECT CONCAT('- `', TABLE_SCHEMA, '.', TABLE_NAME, '`') FROM mims.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_SCHEMA, TABLE_NAME;

PRINT '';
PRINT '### 数据库: `operatedata`';
SELECT CONCAT('- `', TABLE_SCHEMA, '.', TABLE_NAME, '`') FROM operatedata.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_SCHEMA, TABLE_NAME;

PRINT '';
PRINT '### 数据库: `rms2019`';
SELECT CONCAT('- `', TABLE_SCHEMA, '.', TABLE_NAME, '`') FROM rms2019.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_SCHEMA, TABLE_NAME;

PRINT '';
PRINT '---
PRINT '## 远程服务器: 193.112.2.229 (通过链接服务器: cloudRms2019)';
PRINT '';
PRINT '### 数据库: `rms2019`';
SELECT CONCAT('- `', TABLE_SCHEMA, '.', TABLE_NAME, '`') FROM OPENQUERY(cloudRms2019, 'SELECT TABLE_SCHEMA, TABLE_NAME FROM rms2019.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = ''BASE TABLE'' ORDER BY TABLE_SCHEMA, TABLE_NAME');

SET NOCOUNT OFF;
