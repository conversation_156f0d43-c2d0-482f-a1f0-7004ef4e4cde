
-- =============================================
-- 查询指定日期和店铺的详细预订信息
-- =============================================

-- 1. 定义参数
DECLARE @TargetDateStr VARCHAR(8) = '20250731';
DECLARE @ShopIDStr VARCHAR(10) = '11';

PRINT N'--- 正在查询 ShopID=' + @ShopIDStr + ' 在 ' + @TargetDateStr + ' 的所有预订详情 ---';

-- 2. 准备临时表
CREATE TABLE #BookingDetails (
    SourceTable VARCHAR(20),
    ComeTime VARCHAR(20),
    BookStatus INT,
    CheckinStatus INT,
    Numbers INT,
    RmNo VARCHAR(20)
);

DECLARE @Sql NVARCHAR(MAX);

-- 3. 从 bookcacheinfo 获取数据
SET @Sql = N'
SELECT 
    ''bookcacheinfo'' AS SourceTable, 
    ComeTime, BookStatus, CheckinStatus, Numbers, RmNo 
FROM OPENQUERY([cloudRms2019], ''SELECT ComeTime, BookStatus, CheckinStatus, Numbers, RmNo FROM rms2019.dbo.bookcacheinfo WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''')';
INSERT INTO #BookingDetails EXEC sp_executesql @Sql;

-- 4. 从 bookhistory 获取数据
SET @Sql = N'
SELECT 
    ''bookhistory'' AS SourceTable, 
    ComeTime, BookStatus, CheckinStatus, Numbers, RmNo 
FROM OPENQUERY([cloudRms2019], ''SELECT ComeTime, BookStatus, CheckinStatus, Numbers, RmNo FROM rms2019.dbo.bookhistory WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''')';
INSERT INTO #BookingDetails EXEC sp_executesql @Sql;

-- 5. 显示所有找到的预订详情
SELECT 
    SourceTable AS '来源表',
    ComeTime AS '预订时间',
    BookStatus AS '预订状态',
    CheckinStatus AS '入住状态',
    Numbers AS '预订人数',
    RmNo AS '房间号'
FROM 
    #BookingDetails
ORDER BY
    ComeTime;

-- 6. 清理
DROP TABLE #BookingDetails;
GO
