
-- =============================================
-- 查询待客详情脚本 (V7 - 最终修正版)
-- =============================================

-- 1. 定义参数
DECLARE @TargetDate DATE = '20250731';
DECLARE @ShopID INT = 11;

-- 2. 准备临时表
IF OBJECT_ID('tempdb..#AllOccupancies_Raw') IS NOT NULL DROP TABLE #AllOccupancies_Raw;
IF OBJECT_ID('tempdb..#AllFdCashBak_Raw') IS NOT NULL DROP TABLE #AllFdCashBak_Raw;

CREATE TABLE #AllOccupancies_Raw (Beg_Key VARCHAR(20), End_Key VARCHAR(20), RmNo VARCHAR(20), Invno VARCHAR(30));
CREATE TABLE #AllFdCashBak_Raw (Invno VARCHAR(30), FdCName NVARCHAR(100), FdQty INT, CashType VARCHAR(1));

-- 3. 拉取数据
-- a. 获取待客数据
INSERT INTO #AllOccupancies_Raw (Beg_Key, End_Key, RmNo, Invno)
SELECT Beg_Key, End_Key, RmNo, Invno FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, End_Key, RmNo, Invno FROM rms2019.dbo.opencacheinfo WHERE ComeDate = ''20250731'' AND ShopID = 11');
INSERT INTO #AllOccupancies_Raw (Beg_Key, End_Key, RmNo, Invno)
SELECT Beg_Key, End_Key, RmNo, Invno FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, End_Key, RmNo, Invno FROM rms2019.dbo.openhistory WHERE ComeDate = ''20250731'' AND ShopID = 11');

-- b. 获取所有相关的消费详情 (在正确的位置修正排序规则冲突)
INSERT INTO #AllFdCashBak_Raw (Invno, FdCName, FdQty, CashType)
SELECT f.Invno, f.FdCName, f.FdQty, f.CashType
FROM dbo.FdCashBak f
JOIN (SELECT DISTINCT Invno FROM #AllOccupancies_Raw WHERE Invno IS NOT NULL) o 
    ON f.Invno = o.Invno COLLATE Chinese_PRC_CI_AS;

-- 4. 最终查询与展示
PRINT N'--- ' + CONVERT(VARCHAR, @TargetDate, 23) + ' 店铺 ' + CAST(@ShopID AS VARCHAR) + ' 待客详情 ---';

;WITH TimeSlots AS (
    SELECT t.timeno, t.TimeName 
    FROM dbo.timeinfo t
    JOIN dbo.shoptimeinfo st ON t.timeno = st.timeno
    WHERE st.shopid = @ShopID
),
SingleSlotOccupancies AS (
    -- 使用单时段逻辑，将每个待客记录归属到其开台时段
    SELECT Beg_Key, RmNo, Invno 
    FROM #AllOccupancies_Raw 
    WHERE Beg_Key = End_Key AND Invno IS NOT NULL
)
SELECT 
    ts.TimeName AS '时间段',
    sso.RmNo AS '房间号',
    sso.Invno AS '账单号',
    fb.FdCName AS '下单项名称',
    fb.FdQty AS '数量',
    fb.CashType AS '支付类型'
FROM TimeSlots ts
JOIN SingleSlotOccupancies sso ON ts.timeno = sso.Beg_Key
LEFT JOIN #AllFdCashBak_Raw fb ON sso.Invno = fb.Invno COLLATE Chinese_PRC_CI_AS
ORDER BY 
    ts.timeno, sso.RmNo, sso.Invno;

-- 5. 清理
DROP TABLE #AllOccupancies_Raw;
DROP TABLE #AllFdCashBak_Raw;
GO
