SET NOCOUNT ON;

PRINT '## 远程服务器 (193.112.2.229) 存储过程动态';
PRINT '';

PRINT '### 1. 最近执行的存储过程 (TOP 10) - 修正版';
PRINT '';
PRINT '| 最后执行时间        | 执行次数 | 存储过程名                               |';
PRINT '|:--------------------|:---------|:-----------------------------------------|';
SELECT TOP 10
    CONCAT('| ', FORMAT(ps.last_execution_time, 'yyyy-MM-dd HH:mm:ss'), ' | ', ps.execution_count, ' | ', p.name, ' |')
FROM 
    OPENQUERY(cloudRms2019, '
        SELECT TOP 10 
            ps.object_id, 
            ps.database_id, 
            ps.execution_count, 
            ps.last_execution_time
        FROM sys.dm_exec_procedure_stats AS ps
        ORDER BY ps.last_execution_time DESC') AS ps
JOIN 
    OPENQUERY(cloudRms2019, 'SELECT name, object_id FROM rms2019.sys.procedures') AS p
ON ps.object_id = p.object_id;

PRINT '';
PRINT '### 2. 最近创建或修改的存储过程 (TOP 10)';
PRINT '';
PRINT '| 最后修改时间        | 创建时间            | 存储过程名                               |';
PRINT '|:--------------------|:--------------------|:-----------------------------------------|';
SELECT TOP 10
    CONCAT('| ', FORMAT(modify_date, 'yyyy-MM-dd HH:mm:ss'), ' | ', FORMAT(create_date, 'yyyy-MM-dd HH:mm:ss'), ' | ', name, ' |')
FROM 
    OPENQUERY(cloudRms2019, 'SELECT TOP 10 name, create_date, modify_date FROM rms2019.sys.procedures ORDER BY modify_date DESC');

SET NOCOUNT OFF;
