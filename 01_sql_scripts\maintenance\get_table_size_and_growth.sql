-- 关闭不必要的计数信息，使输出更整洁
SET NOCOUNT ON;

-- ===================================================================
-- 1. 数据库 dbfood 分析
-- ===================================================================
USE dbfood;
PRINT ''
PRINT '--- [dbfood] Top 15 Largest Tables (by Row Count) ---';

-- 查询dbfood中行数最多的表
SELECT TOP 15
    t.NAME AS TableName,
    p.rows AS RowCount
FROM 
    sys.tables t
INNER JOIN 
    sys.indexes i ON t.object_id = i.object_id
INNER JOIN 
    sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
WHERE 
    i.index_id <= 1
ORDER BY 
    p.rows DESC;

PRINT ''
PRINT '--- [dbfood] Daily Growth Check for Key Tables (Last 3 Days) ---';

-- 检查 FdCashBak (结账消费明细) 的日增长
SELECT 'FdCashBak' as TableName, CONVERT(date, CashTime) as Day, COUNT(*) as DailyCount FROM FdCashBak WHERE CashTime >= DATEADD(day, -3, GETDATE()) GROUP BY CONVERT(date, CashTime) ORDER BY Day DESC;

-- 检查 FdInv (总账单) 的日增长
SELECT 'FdInv' as TableName, CONVERT(date, AccDate) as Day, COUNT(*) as DailyCount FROM FdInv WHERE AccDate >= CONVERT(char(8), DATEADD(day, -3, GETDATE()), 112) GROUP BY CONVERT(date, AccDate) ORDER BY Day DESC;

-- 检查 wxPayInfo (微信支付记录) 的日增长
SELECT 'wxPayInfo' as TableName, CONVERT(date, InputTime) as Day, COUNT(*) as DailyCount FROM wxPayInfo WHERE InputTime >= DATEADD(day, -3, GETDATE()) GROUP BY CONVERT(date, InputTime) ORDER BY Day DESC;


-- ===================================================================
-- 2. 数据库 rms2019 分析
-- ===================================================================
USE rms2019;
PRINT ''
PRINT '--- [rms2019] Top 15 Largest Tables (by Row Count) ---';

-- 查询rms2019中行数最多的表
SELECT TOP 15
    t.NAME AS TableName,
    p.rows AS RowCount
FROM 
    sys.tables t
INNER JOIN 
    sys.indexes i ON t.object_id = i.object_id
INNER JOIN 
    sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
WHERE 
    i.index_id <= 1
ORDER BY 
    p.rows DESC;

PRINT ''
PRINT '--- [rms2019] Daily Growth Check for Key Tables (Last 3 Days) ---';

-- 检查 openhistory (历史开台记录) 的日增长
SELECT 'openhistory' as TableName, CONVERT(date, ComeDate) as Day, COUNT(*) as DailyCount FROM openhistory WHERE ComeDate >= DATEADD(day, -3, GETDATE()) GROUP BY CONVERT(date, ComeDate) ORDER BY Day DESC;

-- 检查 rmcloseinfo (日结房间信息) 的日增长
SELECT 'rmcloseinfo' as TableName, CONVERT(date, WorkDate) as Day, COUNT(*) as DailyCount FROM rmcloseinfo WHERE WorkDate >= DATEADD(day, -3, GETDATE()) GROUP BY CONVERT(date, WorkDate) ORDER BY Day DESC;

-- 检查 bookhistory (历史预订记录) 的日增长
SELECT 'bookhistory' as TableName, CONVERT(date, ComeDate) as Day, COUNT(*) as DailyCount FROM bookhistory WHERE ComeDate >= DATEADD(day, -3, GETDATE()) GROUP BY CONVERT(date, ComeDate) ORDER BY Day DESC;