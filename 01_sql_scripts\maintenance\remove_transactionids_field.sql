
-- 使用 dbfood 数据库
USE dbfood;
GO

-- 1. 从 BillAnalysisResults 表中删除 TransactionIDs 字段（如果存在）
IF COL_LENGTH('BillAnalysisResults', 'TransactionIDs') IS NOT NULL
BEGIN
    ALTER TABLE BillAnalysisResults DROP COLUMN TransactionIDs;
    PRINT 'Column TransactionIDs dropped from BillAnalysisResults table.';
END
ELSE
BEGIN
    PRINT 'Column TransactionIDs does not exist in BillAnalysisResults table.';
END
GO

-- 2. 重新创建不含 TransactionIDs 的存储过程
IF OBJECT_ID('usp_CalculateAndStoreBillDifferences', 'P') IS NOT NULL
    DROP PROCEDURE usp_CalculateAndStoreBillDifferences;
GO

CREATE PROCEDURE usp_CalculateAndStoreBillDifferences
AS
BEGIN
    SET NOCOUNT ON;

    -- WxPayAggregated 不再需要聚合 TransactionIDs
    WITH WxPayAggregated AS (
        SELECT
            p.InvNo,
            SUM(p.Tot) AS WxPayTotalForInv
        FROM
            wxpayinfo p
        GROUP BY
            p.InvNo
    ),
    AllBillDetails AS (
        SELECT
            r.InvNo,
            r.RmNo,
            r.Tot AS RoomSystemAmount,
            w.WxPayTotalForInv AS WxPayTotalAmount,
            (w.WxPayTotalForInv - r.Tot) AS Difference,
            CONVERT(datetime, r.AccDate + ' ' + r.AccTime) AS CheckoutTime,
            r.CloseTime
        FROM
            ROOM r
        INNER JOIN
            WxPayAggregated w ON r.InvNo = w.InvNo
        WHERE
            r.RmStatus = 'A'
            AND DATEDIFF(minute, CONVERT(datetime, r.AccDate + ' ' + r.AccTime), GETDATE()) BETWEEN 0 AND 10
    )
    INSERT INTO BillAnalysisResults (
        InvNo,
        RmNo,
        RoomSystemAmount,
        WxPayTotalAmount,
        Difference,
        CheckoutTime,
        CloseTime,
        IsDifferenceNormal
    )
    SELECT
        InvNo,
        RmNo,
        RoomSystemAmount,
        WxPayTotalAmount,
        Difference,
        CheckoutTime,
        CloseTime,
        CASE
            WHEN Difference <= 0 THEN 1
            ELSE 0
        END AS IsDifferenceNormal
    FROM
        AllBillDetails;

    SELECT @@ROWCOUNT AS InsertedRows;

END;
GO

PRINT 'Stored procedure usp_CalculateAndStoreBillDifferences has been updated to remove TransactionIDs.';
GO
