
-- 使用 dbfood 数据库
USE dbfood;
GO

-- 1. 为 ROOM 表添加 CloseTime 字段（如果不存在）
IF COL_LENGTH('ROOM', 'CloseTime') IS NULL
BEGIN
    ALTER TABLE ROOM ADD CloseTime DATETIME NULL;
    PRINT 'Column CloseTime added to ROOM table.';
END
ELSE
BEGIN
    PRINT 'Column CloseTime already exists in ROOM table.';
END
GO

-- 2. 为 BillAnalysisResults 表添加 CloseTime 字段（如果不存在）
IF COL_LENGTH('BillAnalysisResults', 'CloseTime') IS NULL
BEGIN
    ALTER TABLE BillAnalysisResults ADD CloseTime DATETIME NULL;
    PRINT 'Column CloseTime added to BillAnalysisResults table.';
END
ELSE
BEGIN
    PRINT 'Column CloseTime already exists in BillAnalysisResults table.';
END
GO

-- 3. 重新创建存储过程以包含新字段
IF OBJECT_ID('usp_CalculateAndStoreBillDifferences', 'P') IS NOT NULL
    DROP PROCEDURE usp_CalculateAndStoreBillDifferences;
GO

CREATE PROCEDURE usp_CalculateAndStoreBillDifferences
AS
BEGIN
    SET NOCOUNT ON;

    WITH WxPayAggregated AS (
        SELECT
            p.InvNo,
            SUM(p.Tot) AS WxPayTotalForInv,
            STUFF(
                (
                    SELECT ', ' + w.transaction_id
                    FROM wxpayinfo w
                    WHERE w.InvNo = p.InvNo
                    FOR XML PATH('')
                ), 1, 2, ''
            ) AS TransactionIDs
        FROM
            wxpayinfo p
        GROUP BY
            p.InvNo
    ),
    AllBillDetails AS (
        SELECT
            r.InvNo,
            r.RmNo,
            r.Tot AS RoomSystemAmount,
            w.WxPayTotalForInv AS WxPayTotalAmount,
            (w.WxPayTotalForInv - r.Tot) AS Difference,
            w.TransactionIDs,
            CONVERT(datetime, r.AccDate + ' ' + r.AccTime) AS CheckoutTime,
            r.CloseTime -- 新增字段
        FROM
            ROOM r
        INNER JOIN
            WxPayAggregated w ON r.InvNo = w.InvNo
        WHERE
            r.RmStatus = 'A'
            AND DATEDIFF(minute, CONVERT(datetime, r.AccDate + ' ' + r.AccTime), GETDATE()) BETWEEN 0 AND 10
    )
    INSERT INTO BillAnalysisResults (
        InvNo,
        RmNo,
        RoomSystemAmount,
        WxPayTotalAmount,
        Difference,
        TransactionIDs,
        CheckoutTime,
        CloseTime, -- 新增字段
        IsDifferenceNormal
    )
    SELECT
        InvNo,
        RmNo,
        RoomSystemAmount,
        WxPayTotalAmount,
        Difference,
        TransactionIDs,
        CheckoutTime,
        CloseTime, -- 新增字段
        CASE
            WHEN Difference <= 0 THEN 1
            ELSE 0
        END AS IsDifferenceNormal
    FROM
        AllBillDetails;

    SELECT @@ROWCOUNT AS InsertedRows;

END;
GO

PRINT 'Stored procedure usp_CalculateAndStoreBillDifferences has been updated successfully.';
GO
