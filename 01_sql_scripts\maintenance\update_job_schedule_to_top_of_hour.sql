
-- 确保在 msdb 数据库上下文中执行，因为作业和计划都存储在这里
USE msdb;
GO

DECLARE @jobName NVARCHAR(128) = N'LogRoomStats_EveryHour';
DECLARE @oldScheduleName NVARCHAR(128) = N'Run Every Hour';
DECLARE @newScheduleName NVARCHAR(128) = N'Run at the Top of Every Hour';
DECLARE @jobId BINARY(16);

-- 1. 查找作业ID
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;

IF (@jobId IS NULL)
BEGIN
    PRINT 'Job ' + @jobName + ' not found. Aborting.';
    RETURN;
END

-- 2. 分离并删除旧的计划（如果存在）
IF EXISTS (SELECT 1 FROM dbo.sysschedules WHERE name = @oldScheduleName)
BEGIN
    PRINT 'Detaching and deleting old schedule: ' + @oldScheduleName;
    EXEC dbo.sp_detach_schedule @job_name = @jobName, @schedule_name = @oldScheduleName;
    EXEC dbo.sp_delete_schedule @schedule_name = @oldScheduleName;
    PRINT 'Old schedule deleted.';
END

-- 3. 如果新的精确计划也存在，同样先删除，确保脚本可重复执行
IF EXISTS (SELECT 1 FROM dbo.sysschedules WHERE name = @newScheduleName)
BEGIN
    PRINT 'A schedule with the new name already exists. Deleting it first: ' + @newScheduleName;
    -- 检查是否已附加，如果是，则先分离
    IF EXISTS (SELECT 1 FROM dbo.sysjobschedules js JOIN dbo.sysschedules s ON js.schedule_id = s.schedule_id WHERE js.job_id = @jobId AND s.name = @newScheduleName)
    BEGIN
        EXEC dbo.sp_detach_schedule @job_name = @jobName, @schedule_name = @newScheduleName;
    END
    EXEC dbo.sp_delete_schedule @schedule_name = @newScheduleName;
    PRINT 'Existing new-named schedule deleted.';
END

-- 4. 创建新的、精确到整点执行的计划
PRINT 'Creating new schedule: ' + @newScheduleName;
EXEC dbo.sp_add_schedule
    @schedule_name = @newScheduleName,
    @freq_type = 4, -- 每天
    @freq_interval = 1, -- 每1天
    @freq_subday_type = 8, -- 小时
    @freq_subday_interval = 1, -- 每1小时
    @active_start_time = 0; -- 从 00:00:00 开始

-- 5. 将新计划附加到作业
PRINT 'Attaching new schedule to the job.';
EXEC dbo.sp_attach_schedule
    @job_name = @jobName,
    @schedule_name = @newScheduleName;

PRINT 'Job schedule for ' + @jobName + ' has been successfully updated to run at the top of every hour.';
GO
