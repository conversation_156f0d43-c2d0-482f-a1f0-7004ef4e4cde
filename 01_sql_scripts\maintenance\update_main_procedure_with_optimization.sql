-- =================================================================================
-- 更新主存储过程以使用优化的插入逻辑
-- 修改 usp_RunUnifiedDailyReportJob 以支持DiscountFree字段并优化性能
-- =================================================================================

-- 方案1: 修改现有存储过程，使用优化的插入语句
CREATE OR ALTER PROCEDURE dbo.usp_RunUnifiedDailyReportJob_Optimized
    @TargetDate DATE = NULL,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report_Optimized';
    DECLARE @ReportID INT;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- === Step 0: Clean up existing data ===
        PRINT N'Step 0: Deleting existing data for ' + CONVERT(NVARCHAR, @TargetDate) + N'...';
        SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
        IF @ReportID IS NOT NULL
        BEGIN
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;
        END

        -- === Step 1: Generate and Insert Header Data ===
        PRINT N'Step 1: Generating Header data...';
        CREATE TABLE #TempHeader (
            WorkDate DATE, ShopName NVARCHAR(100), WeekdayName NVARCHAR(20),
            DayTimeRevenue DECIMAL(18,2), NightTimeRevenue DECIMAL(18,2), TotalRevenue DECIMAL(18,2),
            DayTimeBatchCount INT, NightTimeBatchCount INT, TotalBatchCount INT,
            TotalGuestCount INT, BuffetGuestCount INT,
            DayTimeDropInBatch INT, NightTimeDropInBatch INT, TotalDirectFallGuests INT
        );
        INSERT INTO #TempHeader EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        ) SELECT @TargetDate, @ShopId, ShopName, WeekdayName, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests FROM #TempHeader;
        SET @ReportID = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- === Step 2: Generate and Insert OPTIMIZED Night Details Data ===
        PRINT N'Step 2: Generating OPTIMIZED Night Details data...';
        -- 使用优化版本的存储过程（包含DiscountFree字段）
        CREATE TABLE #TempNightDetails (
            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
            FreeConsumption_BatchCount int,
            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
            DiscountFree_BatchCount INT, DiscountFree_Revenue DECIMAL(18, 2)  -- 新增字段
        );
        
        -- 调用优化版本的存储过程
        INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V8_Optimized @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;
        
        -- 使用优化的插入语句（只插入有意义的数据）
        INSERT INTO dbo.FullDailyReport_NightDetails (
            ReportID, 
            Buyout_BatchCount, Buyout_Revenue,
            Changyin_BatchCount, Changyin_Revenue,
            FreeConsumption_BatchCount,
            NonPackage_Others,
            DiscountFree_BatchCount, DiscountFree_Revenue
        ) SELECT 
            @ReportID,
            Buyout_BatchCount, Buyout_Revenue,
            Changyin_BatchCount, Changyin_Revenue,
            FreeConsumption_BatchCount,
            NonPackage_Others,
            DiscountFree_BatchCount, DiscountFree_Revenue
        FROM #TempNightDetails;
        
        PRINT N'Optimized Night Details data inserted (only meaningful fields).';

        -- === Step 3: Generate and Insert Time Slot Details Data ===
        PRINT N'Step 3: Generating Time Slot Details data...';
        CREATE TABLE #TempTimeSlotDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        INSERT INTO #TempTimeSlotDetails EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        ) SELECT @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall FROM #TempTimeSlotDetails;
        PRINT N'Time Slot Details data inserted.';

        -- Cleanup
        DROP TABLE #TempHeader;
        DROP TABLE #TempNightDetails;
        DROP TABLE #TempTimeSlotDetails;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Optimized unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempNightDetails') IS NOT NULL DROP TABLE #TempNightDetails;
        IF OBJECT_ID('tempdb..#TempTimeSlotDetails') IS NOT NULL DROP TABLE #TempTimeSlotDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing optimized unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =================================================================================
-- 方案2: 修改现有存储过程的临时表结构，添加DiscountFree字段
-- =================================================================================

-- 如果你想保持使用原有的存储过程，只需要修改临时表结构和插入语句
-- 这是对原有 usp_RunUnifiedDailyReportJob 的最小化修改

/*
-- 在原存储过程的Step 2中，修改临时表结构：
CREATE TABLE #TempNightDetails (
    ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
    TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
    TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
    FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
    FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
    Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
    Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
    FreeConsumption_BatchCount int,
    NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
    Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
    DiscountFree_BatchCount INT, DiscountFree_Revenue DECIMAL(18, 2)  -- 新增这两个字段
);

-- 然后修改插入语句为优化版本：
INSERT INTO dbo.FullDailyReport_NightDetails (
    ReportID, 
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NonPackage_Others,
    DiscountFree_BatchCount, DiscountFree_Revenue
) SELECT 
    @ReportID,
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NonPackage_Others,
    DiscountFree_BatchCount, DiscountFree_Revenue
FROM #TempNightDetails;
*/

-- =================================================================================
-- 使用说明和建议
-- =================================================================================

/*
🎯 推荐实施步骤：

1. 首先部署优化的存储过程：
   - 部署 usp_GenerateSimplifiedDailyReport_V8_Optimized
   - 测试确保它能正确输出DiscountFree字段

2. 然后选择以下方案之一：
   方案A: 使用新的优化主存储过程 usp_RunUnifiedDailyReportJob_Optimized
   方案B: 修改现有的 usp_RunUnifiedDailyReportJob

3. 验证数据完整性：
   - 检查DiscountFree字段的业务逻辑是否正确
   - 验证只插入有意义的数据是否满足业务需求
   - 对比优化前后的性能差异

4. 监控和调优：
   - 监控存储过程执行时间
   - 检查数据质量和完整性
   - 根据实际使用情况进一步优化

⚠️ 注意事项：
- DiscountFree的业务逻辑需要根据实际业务需求调整
- 建议在测试环境先验证所有功能
- 考虑数据迁移和兼容性问题
*/
