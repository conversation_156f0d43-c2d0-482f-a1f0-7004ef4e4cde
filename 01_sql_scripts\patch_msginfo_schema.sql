
-- Use the target database
USE operatedata;
GO

-- Add ICode column if it does not exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = N'ICode' AND Object_ID = Object_ID(N'dbo.msginfo'))
BEGIN
    ALTER TABLE dbo.msginfo ADD ICode NVARCHAR(15) NOT NULL DEFAULT '';
    PRINT 'Column ICode added to dbo.msginfo.';
END
ELSE
BEGIN
    PRINT 'Column ICode already exists in dbo.msginfo.';
END
GO

-- Add DrCheckId column if it does not exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = N'DrCheckId' AND Object_ID = Object_ID(N'dbo.msginfo'))
BEGIN
    ALTER TABLE dbo.msginfo ADD DrCheckId INT NOT NULL DEFAULT 0;
    PRINT 'Column DrCheckId added to dbo.msginfo.';
END
ELSE
BEGIN
    PRINT 'Column DrCheckId already exists in dbo.msginfo.';
END
GO

-- Add ExDatetime column if it does not exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = N'ExDatetime' AND Object_ID = Object_ID(N'dbo.msginfo'))
BEGIN
    ALTER TABLE dbo.msginfo ADD ExDatetime DATETIME2 NOT NULL DEFAULT '1900-01-01';
    PRINT 'Column ExDatetime added to dbo.msginfo.';
END
ELSE
BEGIN
    PRINT 'Column ExDatetime already exists in dbo.msginfo.';
END
GO

PRINT 'Schema patch script finished.';
GO
