
USE operatedata;
GO

-- Use MERGE to insert bank names if they don't already exist.
-- This makes the script safe to run multiple times.
MERGE dbo.Dim_Bank AS target
USING (VALUES
    (N'广发银行'),
    (N'中信银行'),
    (N'广日银联')
) AS source (BankName)
ON target.BankName = source.BankName
WHEN NOT MATCHED BY TARGET THEN
    INSERT (BankName) VALUES (source.BankName);
GO

PRINT 'Dim_Bank table populated/updated successfully.';
GO
