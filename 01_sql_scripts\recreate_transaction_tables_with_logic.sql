
-- Use the target database
USE operatedata;
GO

-- Drop the temporary synced tables if they exist
IF OBJECT_ID('dbo.synced_msginfo', 'U') IS NOT NULL DROP TABLE dbo.synced_msginfo;
IF OBJECT_ID('dbo.synced_drinksinfo', 'U') IS NOT NULL DROP TABLE dbo.synced_drinksinfo;
GO

-- Drop the final target tables if they exist to ensure a clean slate for recreation
IF OBJECT_ID('dbo.msginfo', 'U') IS NOT NULL DROP TABLE dbo.msginfo;
IF OBJECT_ID('dbo.drinksinfo', 'U') IS NOT NULL DROP TABLE dbo.drinksinfo;
GO

-- Create final table: msginfo (with WorkDate)
CREATE TABLE dbo.msginfo (
    WorkDate DATE NULL,
    iKeyMsg NVARCHAR(50) NOT NULL,
    CustName NVARCHAR(50) NOT NULL DEFAULT '',
    CustTel NVARCHAR(11) NOT NULL DEFAULT '',
    MsgStatus INT NOT NULL DEFAULT 1,
    MsgPassword NVARCHAR(50) NOT NULL DEFAULT '',
    DeShopId INT NOT NULL DEFAULT 1,
    DeRmNo NVARCHAR(10) NOT NULL DEFAULT '',
    DeDatetime DATETIME2 NOT NULL DEFAULT '1900-01-01 00:00:00',
    DeBarName NVARCHAR(50) NOT NULL DEFAULT '',
    DeServiceName NVARCHAR(50) NOT NULL DEFAULT '',
    DeCheckName NVARCHAR(50) NOT NULL DEFAULT '',
    DeMemory NVARCHAR(500) NOT NULL DEFAULT '',
    DrShopId INT NOT NULL DEFAULT 1,
    DrRmNo NVARCHAR(10) NOT NULL DEFAULT '',
    DrDatetime DATETIME2 NULL,
    DrBarName NVARCHAR(50) NOT NULL DEFAULT '',
    DrServiceName NVARCHAR(50) NOT NULL DEFAULT '',
    DrCheckName NVARCHAR(50) NOT NULL DEFAULT '',
    DrMemory NVARCHAR(500) NOT NULL DEFAULT '',
    IsDelete BIT NOT NULL DEFAULT 0,
    DeleteUserName NVARCHAR(50) NOT NULL DEFAULT '',
    Val1 INT NOT NULL DEFAULT 0,
    Val2 INT NOT NULL DEFAULT 0,
    Val3 NVARCHAR(100) NOT NULL DEFAULT '',
    Val4 NVARCHAR(30) NOT NULL DEFAULT '',
    Val5 NVARCHAR(100) NOT NULL DEFAULT '',
    BrandId INT NOT NULL DEFAULT 0,
    ReNew INT NOT NULL DEFAULT 0,
    ICode NVARCHAR(15) NOT NULL DEFAULT '',
    DrCheckId INT NOT NULL,
    ExDatetime DATETIME2 NOT NULL,
    PRIMARY KEY (iKeyMsg)
);
GO

-- Create final table: drinksinfo (with ShopId and WorkDate)
CREATE TABLE dbo.drinksinfo (
    ShopId INT NULL,
    WorkDate DATE NULL,
    iKeyDrinks NVARCHAR(50) NOT NULL,
    iKeyMsg NVARCHAR(50) NOT NULL,
    DrinksName NVARCHAR(100) NOT NULL DEFAULT '',
    Unit NVARCHAR(10) NOT NULL DEFAULT '',
    DrinksQty INT NOT NULL DEFAULT 0,
    IsDelete BIT NOT NULL DEFAULT 0,
    DeleteUserName NVARCHAR(50) NOT NULL DEFAULT '',
    Val1 INT NOT NULL DEFAULT 0,
    Val2 INT NOT NULL DEFAULT 0,
    Val3 NVARCHAR(100) NOT NULL DEFAULT '',
    Val4 NVARCHAR(100) NOT NULL DEFAULT '',
    Val5 NVARCHAR(100) NOT NULL DEFAULT '',
    BrandId NVARCHAR(255) NOT NULL DEFAULT '1',
    GiveNum NVARCHAR(20) NOT NULL DEFAULT ' ',
    PRIMARY KEY (iKeyDrinks)
);
GO

PRINT 'Tables (msginfo, drinksinfo) recreated successfully with added business logic columns.';
GO
