-- ====================================================================
-- KTV每日报告综合数据表设计
-- 包含总览数据、时段数据和上一档直落数据的统一表结构
-- 创建时间: 2025-01-23
-- ====================================================================

USE OperateData;
GO

-- 如果表已存在，则先删除
IF OBJECT_ID('dbo.KTV_DailyReport_Comprehensive', 'U') IS NOT NULL
    DROP TABLE dbo.KTV_DailyReport_Comprehensive;
GO

-- 创建综合数据表
CREATE TABLE dbo.KTV_DailyReport_Comprehensive (
    -- 主键和基础信息
    ID int IDENTITY(1,1) PRIMARY KEY,
    日期 varchar(8) NOT NULL,
    门店 nvarchar(100) NOT NULL,
    星期 nvarchar(20) NOT NULL,
    
    -- 总览数据 - 营收指标 (3个字段)
    营收_总收入 decimal(18,2) DEFAULT 0,
    营收_白天档 decimal(18,2) DEFAULT 0,
    营收_晚上档 decimal(18,2) DEFAULT 0,
    
    -- 总览数据 - 客户指标 (6个字段)
    带客_全天总批数 int DEFAULT 0,
    带客_白天档_总批次 int DEFAULT 0,
    带客_晚上档_总批次 int DEFAULT 0,
    带客_白天档_直落 int DEFAULT 0,
    带客_晚上档_直落 int DEFAULT 0,
    
    -- 总览数据 - 餐饮指标 (3个字段)
    用餐_总人数 int DEFAULT 0,
    用餐_自助餐人数 int DEFAULT 0,
    用餐_直落人数 int DEFAULT 0,
    
    -- 时段数据 - 10:50-13:50 (7个字段)
    [10:50-13:50_K+] int DEFAULT 0,
    [10:50-13:50_特权预约] int DEFAULT 0,
    [10:50-13:50_美团] int DEFAULT 0,
    [10:50-13:50_抖音] int DEFAULT 0,
    [10:50-13:50_房费] int DEFAULT 0,
    [10:50-13:50_小计] int DEFAULT 0,
    [10:50-13:50_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 11:50-14:50 (7个字段)
    [11:50-14:50_K+] int DEFAULT 0,
    [11:50-14:50_特权预约] int DEFAULT 0,
    [11:50-14:50_美团] int DEFAULT 0,
    [11:50-14:50_抖音] int DEFAULT 0,
    [11:50-14:50_房费] int DEFAULT 0,
    [11:50-14:50_小计] int DEFAULT 0,
    [11:50-14:50_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 13:30-16:30 (7个字段)
    [13:30-16:30_K+] int DEFAULT 0,
    [13:30-16:30_特权预约] int DEFAULT 0,
    [13:30-16:30_美团] int DEFAULT 0,
    [13:30-16:30_抖音] int DEFAULT 0,
    [13:30-16:30_房费] int DEFAULT 0,
    [13:30-16:30_小计] int DEFAULT 0,
    [13:30-16:30_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 14:00-17:00 (7个字段)
    [14:00-17:00_K+] int DEFAULT 0,
    [14:00-17:00_特权预约] int DEFAULT 0,
    [14:00-17:00_美团] int DEFAULT 0,
    [14:00-17:00_抖音] int DEFAULT 0,
    [14:00-17:00_房费] int DEFAULT 0,
    [14:00-17:00_小计] int DEFAULT 0,
    [14:00-17:00_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 15:00-18:00 (7个字段)
    [15:00-18:00_K+] int DEFAULT 0,
    [15:00-18:00_特权预约] int DEFAULT 0,
    [15:00-18:00_美团] int DEFAULT 0,
    [15:00-18:00_抖音] int DEFAULT 0,
    [15:00-18:00_房费] int DEFAULT 0,
    [15:00-18:00_小计] int DEFAULT 0,
    [15:00-18:00_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 16:50-19:50 (7个字段)
    [16:50-19:50_K+] int DEFAULT 0,
    [16:50-19:50_特权预约] int DEFAULT 0,
    [16:50-19:50_美团] int DEFAULT 0,
    [16:50-19:50_抖音] int DEFAULT 0,
    [16:50-19:50_房费] int DEFAULT 0,
    [16:50-19:50_小计] int DEFAULT 0,
    [16:50-19:50_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 17:10-20:10 (7个字段)
    [17:10-20:10_K+] int DEFAULT 0,
    [17:10-20:10_特权预约] int DEFAULT 0,
    [17:10-20:10_美团] int DEFAULT 0,
    [17:10-20:10_抖音] int DEFAULT 0,
    [17:10-20:10_房费] int DEFAULT 0,
    [17:10-20:10_小计] int DEFAULT 0,
    [17:10-20:10_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 18:00-21:00 (7个字段)
    [18:00-21:00_K+] int DEFAULT 0,
    [18:00-21:00_特权预约] int DEFAULT 0,
    [18:00-21:00_美团] int DEFAULT 0,
    [18:00-21:00_抖音] int DEFAULT 0,
    [18:00-21:00_房费] int DEFAULT 0,
    [18:00-21:00_小计] int DEFAULT 0,
    [18:00-21:00_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 18:10-21:10 (7个字段)
    [18:10-21:10_K+] int DEFAULT 0,
    [18:10-21:10_特权预约] int DEFAULT 0,
    [18:10-21:10_美团] int DEFAULT 0,
    [18:10-21:10_抖音] int DEFAULT 0,
    [18:10-21:10_房费] int DEFAULT 0,
    [18:10-21:10_小计] int DEFAULT 0,
    [18:10-21:10_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 19:00-20:30 (7个字段)
    [19:00-20:30_K+] int DEFAULT 0,
    [19:00-20:30_特权预约] int DEFAULT 0,
    [19:00-20:30_美团] int DEFAULT 0,
    [19:00-20:30_抖音] int DEFAULT 0,
    [19:00-20:30_房费] int DEFAULT 0,
    [19:00-20:30_小计] int DEFAULT 0,
    [19:00-20:30_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 19:00-22:00 (7个字段)
    [19:00-22:00_K+] int DEFAULT 0,
    [19:00-22:00_特权预约] int DEFAULT 0,
    [19:00-22:00_美团] int DEFAULT 0,
    [19:00-22:00_抖音] int DEFAULT 0,
    [19:00-22:00_房费] int DEFAULT 0,
    [19:00-22:00_小计] int DEFAULT 0,
    [19:00-22:00_上一档直落] int DEFAULT 0,
    
    -- 时段数据 - 19:30-22:30 (7个字段)
    [19:30-22:30_K+] int DEFAULT 0,
    [19:30-22:30_特权预约] int DEFAULT 0,
    [19:30-22:30_美团] int DEFAULT 0,
    [19:30-22:30_抖音] int DEFAULT 0,
    [19:30-22:30_房费] int DEFAULT 0,
    [19:30-22:30_小计] int DEFAULT 0,
    [19:30-22:30_上一档直落] int DEFAULT 0,
    
    -- 晚间数据 - 自由餐 (6个字段)
    晚间_自由餐_K+ int DEFAULT 0,
    晚间_自由餐_特权预约 int DEFAULT 0,
    晚间_自由餐_美团 int DEFAULT 0,
    晚间_自由餐_抖音 int DEFAULT 0,
    晚间_自由餐_小计 int DEFAULT 0,
    晚间_自由餐_消费金额 decimal(18,2) DEFAULT 0,
    
    -- 晚间数据 - 20点后 (8个字段)
    晚间_20点后_K+ int DEFAULT 0,
    晚间_20点后_特权预约 int DEFAULT 0,
    晚间_20点后_美团 int DEFAULT 0,
    晚间_20点后_抖音 int DEFAULT 0,
    晚间_20点后_房费 int DEFAULT 0,
    晚间_20点后_其他 int DEFAULT 0,
    晚间_20点后_小计 int DEFAULT 0,
    晚间_20点后_营业额 decimal(18,2) DEFAULT 0,
    
    -- 审计字段
    创建时间 datetime2 DEFAULT GETDATE(),
    更新时间 datetime2 DEFAULT GETDATE()
);
GO

-- 创建索引以提高查询性能
CREATE UNIQUE INDEX IX_KTV_DailyReport_Date_Store ON dbo.KTV_DailyReport_Comprehensive (日期, 门店);
CREATE INDEX IX_KTV_DailyReport_Date ON dbo.KTV_DailyReport_Comprehensive (日期);
CREATE INDEX IX_KTV_DailyReport_Store ON dbo.KTV_DailyReport_Comprehensive (门店);
GO

-- 添加约束
ALTER TABLE dbo.KTV_DailyReport_Comprehensive
ADD CONSTRAINT CK_KTV_DailyReport_Date CHECK (LEN(日期) = 8 AND ISNUMERIC(日期) = 1);
GO

PRINT N'KTV每日报告综合数据表创建完成';
PRINT N'表名: KTV_DailyReport_Comprehensive';
PRINT N'总字段数: 约120个字段';
PRINT N'包含: 14个总览字段 + 84个时段字段(12个时段×7个字段) + 14个晚间字段 + 审计字段';
GO
