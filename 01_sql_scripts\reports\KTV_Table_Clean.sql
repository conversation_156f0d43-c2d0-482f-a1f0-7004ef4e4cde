USE OperateData;
GO

IF OBJECT_ID('dbo.KTV_DailyReport_Comprehensive', 'U') IS NOT NULL
    DROP TABLE dbo.KTV_DailyReport_Comprehensive;
GO

CREATE TABLE dbo.KTV_DailyReport_Comprehensive (
    ID int IDENTITY(1,1) PRIMARY KEY,
    日期 varchar(8) NOT NULL,
    门店 nvarchar(100) NOT NULL,
    星期 nvarchar(20) NOT NULL,

    营收_总收入 decimal(18,2) DEFAULT 0,
    营收_白天档 decimal(18,2) DEFAULT 0,
    营收_晚上档 decimal(18,2) DEFAULT 0,

    带客_全天总批数 int DEFAULT 0,
    带客_白天档_总批次 int DEFAULT 0,
    带客_晚上档_总批次 int DEFAULT 0,
    带客_白天档_直落 int DEFAULT 0,
    带客_晚上档_直落 int DEFAULT 0,

    用餐_总人数 int DEFAULT 0,
    用餐_自助餐人数 int DEFAULT 0,
    用餐_直落人数 int DEFAULT 0
    
    [13:30-16:30_K+] int DEFAULT 0,
    [13:30-16:30_特权预约] int DEFAULT 0,
    [13:30-16:30_美团] int DEFAULT 0,
    [13:30-16:30_抖音] int DEFAULT 0,
    [13:30-16:30_房费] int DEFAULT 0,
    [13:30-16:30_小计] int DEFAULT 0,
    [13:30-16:30_上一档直落] int DEFAULT 0,
    
    [14:00-17:00_K+] int DEFAULT 0,
    [14:00-17:00_特权预约] int DEFAULT 0,
    [14:00-17:00_美团] int DEFAULT 0,
    [14:00-17:00_抖音] int DEFAULT 0,
    [14:00-17:00_房费] int DEFAULT 0,
    [14:00-17:00_小计] int DEFAULT 0,
    [14:00-17:00_上一档直落] int DEFAULT 0,
    
    [15:00-18:00_K+] int DEFAULT 0,
    [15:00-18:00_特权预约] int DEFAULT 0,
    [15:00-18:00_美团] int DEFAULT 0,
    [15:00-18:00_抖音] int DEFAULT 0,
    [15:00-18:00_房费] int DEFAULT 0,
    [15:00-18:00_小计] int DEFAULT 0,
    [15:00-18:00_上一档直落] int DEFAULT 0,
    
    [16:50-19:50_K+] int DEFAULT 0,
    [16:50-19:50_特权预约] int DEFAULT 0,
    [16:50-19:50_美团] int DEFAULT 0,
    [16:50-19:50_抖音] int DEFAULT 0,
    [16:50-19:50_房费] int DEFAULT 0,
    [16:50-19:50_小计] int DEFAULT 0,
    [16:50-19:50_上一档直落] int DEFAULT 0,
    
    [17:10-20:10_K+] int DEFAULT 0,
    [17:10-20:10_特权预约] int DEFAULT 0,
    [17:10-20:10_美团] int DEFAULT 0,
    [17:10-20:10_抖音] int DEFAULT 0,
    [17:10-20:10_房费] int DEFAULT 0,
    [17:10-20:10_小计] int DEFAULT 0,
    [17:10-20:10_上一档直落] int DEFAULT 0,
    
    [18:00-21:00_K+] int DEFAULT 0,
    [18:00-21:00_特权预约] int DEFAULT 0,
    [18:00-21:00_美团] int DEFAULT 0,
    [18:00-21:00_抖音] int DEFAULT 0,
    [18:00-21:00_房费] int DEFAULT 0,
    [18:00-21:00_小计] int DEFAULT 0,
    [18:00-21:00_上一档直落] int DEFAULT 0,
    
    [18:10-21:10_K+] int DEFAULT 0,
    [18:10-21:10_特权预约] int DEFAULT 0,
    [18:10-21:10_美团] int DEFAULT 0,
    [18:10-21:10_抖音] int DEFAULT 0,
    [18:10-21:10_房费] int DEFAULT 0,
    [18:10-21:10_小计] int DEFAULT 0,
    [18:10-21:10_上一档直落] int DEFAULT 0,
    
    [19:00-20:30_K+] int DEFAULT 0,
    [19:00-20:30_特权预约] int DEFAULT 0,
    [19:00-20:30_美团] int DEFAULT 0,
    [19:00-20:30_抖音] int DEFAULT 0,
    [19:00-20:30_房费] int DEFAULT 0,
    [19:00-20:30_小计] int DEFAULT 0,
    [19:00-20:30_上一档直落] int DEFAULT 0,
    
    [19:00-22:00_K+] int DEFAULT 0,
    [19:00-22:00_特权预约] int DEFAULT 0,
    [19:00-22:00_美团] int DEFAULT 0,
    [19:00-22:00_抖音] int DEFAULT 0,
    [19:00-22:00_房费] int DEFAULT 0,
    [19:00-22:00_小计] int DEFAULT 0,
    [19:00-22:00_上一档直落] int DEFAULT 0,
    
    [19:30-22:30_K+] int DEFAULT 0,
    [19:30-22:30_特权预约] int DEFAULT 0,
    [19:30-22:30_美团] int DEFAULT 0,
    [19:30-22:30_抖音] int DEFAULT 0,
    [19:30-22:30_房费] int DEFAULT 0,
    [19:30-22:30_小计] int DEFAULT 0,
    [19:30-22:30_上一档直落] int DEFAULT 0,
    
    晚间_自由餐_K+ int DEFAULT 0,
    晚间_自由餐_特权预约 int DEFAULT 0,
    晚间_自由餐_美团 int DEFAULT 0,
    晚间_自由餐_抖音 int DEFAULT 0,
    晚间_自由餐_小计 int DEFAULT 0,
    晚间_自由餐_消费金额 decimal(18,2) DEFAULT 0,
    
    晚间_20点后_K+ int DEFAULT 0,
    晚间_20点后_特权预约 int DEFAULT 0,
    晚间_20点后_美团 int DEFAULT 0,
    晚间_20点后_抖音 int DEFAULT 0,
    晚间_20点后_房费 int DEFAULT 0,
    晚间_20点后_其他 int DEFAULT 0,
    晚间_20点后_小计 int DEFAULT 0,
    晚间_20点后_营业额 decimal(18,2) DEFAULT 0,
    
    创建时间 datetime2 DEFAULT GETDATE(),
    更新时间 datetime2 DEFAULT GETDATE()
);
GO

CREATE UNIQUE INDEX IX_KTV_DailyReport_Date_Store ON dbo.KTV_DailyReport_Comprehensive (日期, 门店);
CREATE INDEX IX_KTV_DailyReport_Date ON dbo.KTV_DailyReport_Comprehensive (日期);
CREATE INDEX IX_KTV_DailyReport_Store ON dbo.KTV_DailyReport_Comprehensive (门店);
GO

ALTER TABLE dbo.KTV_DailyReport_Comprehensive
ADD CONSTRAINT CK_KTV_DailyReport_Date CHECK (LEN(日期) = 8 AND ISNUMERIC(日期) = 1);
GO

PRINT N'KTV综合数据表创建完成';
GO
