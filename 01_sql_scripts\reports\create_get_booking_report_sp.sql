
-- =============================================
-- Author:      Gemini AI
-- Create date: 2025-08-01
-- Description: 从已计算好的报表中，查询并展示预订相关的数据
-- =============================================

IF OBJECT_ID('dbo.usp_GetBookingReport', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GetBookingReport;
GO

CREATE PROCEDURE usp_GetBookingReport
    @TargetDate DATE,
    @ShopID INT
AS
BEGIN
    SET NOCOUNT ON;

    -- 1. 查询总览
    PRINT N'--- 预订报表总览 ---';
    SELECT 
        h.ReportDate AS '报表日期',
        h.ShopID AS '店铺ID',
        h.Weekday AS '星期'
    FROM 
        dbo.DynamicReport_Header h
    WHERE 
        h.ReportDate = @TargetDate AND h.ShopID = @ShopID;

    -- 2. 查询时段详情
    PRINT N'
--- 预订报表时段详情 ---';
    SELECT 
        d.TimeSlotName AS '时间段',
        d.BookedRooms AS '预订房间数',
        d.BookedGuests AS '预订人数'
    FROM 
        dbo.DynamicReport_TimeSlotDetails d
    JOIN 
        dbo.DynamicReport_Header h ON d.HeaderReportID = h.ReportID
    WHERE 
        h.ReportDate = @TargetDate AND h.ShopID = @ShopID
    ORDER BY
        -- 确保时间段正确排序
        CASE 
            WHEN ISNUMERIC(LEFT(d.TimeSlotName, 2)) = 1 THEN CAST(LEFT(d.TimeSlotName, 2) AS INT)
            ELSE 99
        END;

END
GO
