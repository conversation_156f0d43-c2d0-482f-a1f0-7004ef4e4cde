USE operatedata;
GO

-- ====================================================================
-- 脚本: 创建一个存储过程，用于生成透视后的白天时段报表 (V2)
-- 版本: V2 - 添加了开始日期、结束日期和门店ID作为查询参数
-- ====================================================================

IF OBJECT_ID('dbo.usp_GenerateDaytimePivotedReport', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateDaytimePivotedReport;
GO

CREATE PROCEDURE dbo.usp_GenerateDaytimePivotedReport
    @BeginDate DATE,         -- 强制参数：查询开始日期
    @EndDate DATE,           -- 强制参数：查询结束日期
    @ShopId INT              -- 强制参数：指定门店ID
AS
BEGIN
    SET NOCOUNT ON;

    -- 1. 主查询，使用 LEFT JOIN 和 GROUP BY 实现静态透视
    SELECT
        -- 主表 (Header) 的字段，并重命名为中文
        h.ReportDate AS '日期',
        h.ShopName AS '门店',
        h.Weekday AS '星期',
        ISNULL(h.TotalRevenue, 0) AS '营收_总收入',
        ISNULL(h.DayTimeRevenue, 0) AS '营收_白天档',
        ISNULL(h.NightTimeRevenue, 0) AS '营收_晚上档',
        ISNULL(h.TotalBatchCount, 0) AS '带客_全天总批数',
        ISNULL(h.DayTimeBatchCount, 0) AS '带客_白天档_总批次',
        ISNULL(h.NightTimeBatchCount, 0) AS '带客_晚上档_总批次',
        ISNULL(h.DayTimeDirectFall, 0) AS '带客_白天档_直落',
        ISNULL(h.NightTimeDropInBatch, 0) AS '带客_晚上档_直落',
        ISNULL(h.TotalGuestCount, 0) AS '用餐_总人数',
        ISNULL(h.BuffetGuestCount, 0) AS '用餐_自助餐人数',
        ISNULL(h.TotalDropInGuests, 0) AS '用餐_直落人数',

        -- 动态生成的时段详情列 (Pivoted Details)
        -- 使用条件聚合，为每个时段的每个指标生成一列
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '10:50-13:50' THEN d.KPlus_Count END), 0) AS '10:50-13:50_K+',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '10:50-13:50' THEN d.Special_Count END), 0) AS '10:50-13:50_特权预约',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '10:50-13:50' THEN d.Meituan_Count END), 0) AS '10:50-13:50_美团',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '10:50-13:50' THEN d.Douyin_Count END), 0) AS '10:50-13:50_抖音',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '10:50-13:50' THEN d.RoomFee_Count END), 0) AS '10:50-13:50_房费',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '10:50-13:50' THEN d.Subtotal_Count END), 0) AS '10:50-13:50_小计',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '10:50-13:50' THEN d.PreviousSlot_DirectFall END), 0) AS '10:50-13:50_上档直落',

        ISNULL(MAX(CASE WHEN d.TimeSlotName = '11:50-14:50' THEN d.KPlus_Count END), 0) AS '11:50-14:50_K+',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '11:50-14:50' THEN d.Special_Count END), 0) AS '11:50-14:50_特权预约',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '11:50-14:50' THEN d.Meituan_Count END), 0) AS '11:50-14:50_美团',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '11:50-14:50' THEN d.Douyin_Count END), 0) AS '11:50-14:50_抖音',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '11:50-14:50' THEN d.RoomFee_Count END), 0) AS '11:50-14:50_房费',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '11:50-14:50' THEN d.Subtotal_Count END), 0) AS '11:50-14:50_小计',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '11:50-14:50' THEN d.PreviousSlot_DirectFall END), 0) AS '11:50-14:50_上档直落',

        ISNULL(MAX(CASE WHEN d.TimeSlotName = '13:30-16:30' THEN d.KPlus_Count END), 0) AS '13:30-16:30_K+',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '13:30-16:30' THEN d.Special_Count END), 0) AS '13:30-16:30_特权预约',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '13:30-16:30' THEN d.Meituan_Count END), 0) AS '13:30-16:30_美团',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '13:30-16:30' THEN d.Douyin_Count END), 0) AS '13:30-16:30_抖音',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '13:30-16:30' THEN d.RoomFee_Count END), 0) AS '13:30-16:30_房费',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '13:30-16:30' THEN d.Subtotal_Count END), 0) AS '13:30-16:30_小计',
        ISNULL(MAX(CASE WHEN d.TimeSlotName = '13:30-16:30' THEN d.PreviousSlot_DirectFall END), 0) AS '13:30-16:30_上档直落'

    FROM 
        dbo.FullDailyReport_Header AS h
    LEFT JOIN 
        dbo.FullDailyReport_TimeSlotDetails AS d ON h.ReportID = d.ReportID
    WHERE 
        h.ReportDate BETWEEN @BeginDate AND @EndDate -- 使用日期范围
        AND h.ShopID = @ShopId                      -- 使用指定门店ID
        AND (d.TimeSlotName IS NULL OR LEFT(d.TimeSlotName, 2) < '20') -- 过滤夜间时段
    GROUP BY
        h.ReportID, h.ReportDate, h.ShopName, h.Weekday, h.TotalRevenue, h.DayTimeRevenue, 
        h.NightTimeRevenue, h.TotalBatchCount, h.DayTimeBatchCount, h.NightTimeBatchCount, 
        h.DayTimeDirectFall, h.NightTimeDropInBatch, h.TotalGuestCount, h.BuffetGuestCount, h.TotalDropInGuests
    ORDER BY
        h.ReportDate DESC, h.ShopName;

END
GO

PRINT 'Stored procedure [usp_GenerateDaytimePivotedReport] (V2) created successfully.';
GO