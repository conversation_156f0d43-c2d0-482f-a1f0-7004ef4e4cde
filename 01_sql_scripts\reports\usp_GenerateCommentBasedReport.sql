-- =============================================
-- Author:      Gemini AI
-- Create date: 2025-07-25
-- Description: Dynamically generates a report from KTV_Simplified_Daily_Report,
--              selecting only columns that have comments (MS_Description)
--              and using the comment text as the column alias.
-- Version:     2.0 - Removed brackets from aliases and added ReportDate.
-- =============================================
CREATE OR ALTER PROCEDURE usp_GenerateCommentBasedReport
    @ShopName NVARCHAR(50),
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    -- 1. Dynamically construct the SELECT clause from extended properties (comments)
    DECLARE @SelectClauses NVARCHAR(MAX);

    -- Manually add ReportDate as the very first column
    SET @SelectClauses = N'[ReportDate] AS [报表日期]';

    SELECT @SelectClauses = @SelectClauses + N', ' + STUFF(
        (
            SELECT ', [' + c.name + '] AS [' + CAST(p.value AS NVARCHAR(100)) + ']'
            FROM 
                sys.extended_properties p
            INNER JOIN 
                sys.tables t ON p.major_id = t.object_id
            INNER JOIN 
                sys.columns c ON p.major_id = c.object_id AND p.minor_id = c.column_id
            WHERE 
                t.name = 'KTV_Simplified_Daily_Report' 
                AND p.name = 'MS_Description'
                -- Exclude ReportDate if it has a comment, since we added it manually
                AND c.name <> 'ReportDate' 
            ORDER BY 
                c.column_id -- Maintain the original column order
            FOR XML PATH(''), TYPE
        ).value('.', 'NVARCHAR(MAX)')
    , 1, 2, ''); -- Remove the leading ', '

    -- 2. Construct the full dynamic SQL query
    DECLARE @FullQuery NVARCHAR(MAX);
    
    SET @FullQuery = N'
        SELECT ' + @SelectClauses + N'
        FROM dbo.KTV_Simplified_Daily_Report
        WHERE
            ShopName = @ShopName_Param
            AND ReportDate >= @BeginDate_Param
            AND ReportDate <= @EndDate_Param
        ORDER BY
            ReportDate;';

    -- 3. Execute the dynamic SQL with parameters
    PRINT 'Executing Query:';
    PRINT @FullQuery;

    EXEC sp_executesql 
        @FullQuery,
        N'@ShopName_Param NVARCHAR(50), @BeginDate_Param DATE, @EndDate_Param DATE',
        @ShopName_Param = @ShopName,
        @BeginDate_Param = @BeginDate,
        @EndDate_Param = @EndDate;

END
GO