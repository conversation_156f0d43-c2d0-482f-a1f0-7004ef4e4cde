-- ====================================================================
-- KTV白天档报告存储过程
-- 只包含总览和白天档数据，移除夜间档，添加上一档直落字段
-- 创建时间: 2025-01-23
-- ====================================================================

USE OperateData;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateDayTimeReport', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateDayTimeReport;
GO

CREATE PROCEDURE dbo.usp_GenerateDayTimeReport
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- ====================================================================
    -- 步骤 1: 参数处理
    -- ====================================================================
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- 参数验证
    IF @ShopId <= 0
    BEGIN
        RAISERROR(N'门店ID必须大于0', 16, 1);
        RETURN;
    END

    BEGIN TRY
        -- ====================================================================
        -- 步骤 2: 动态构建白天分时段统计的列（包含上一档直落）
        -- ====================================================================
        DECLARE @PivotColumns nvarchar(MAX);
        DECLARE @PivotSelectColumns nvarchar(MAX);

        SELECT @PivotColumns = STUFF((
            SELECT
                ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.CtNo = 2 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_K+') +
                ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.AliPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_特权预约') +
                ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.MTPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_美团') +
                ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.DZPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_抖音') +
                ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.CtNo = 1 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_房费') +
                ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_小计') +
                ', 0 AS ' + QUOTENAME(t.TimeName + '_上一档直落')
            FROM (
                SELECT DISTINCT ti.TimeName, ti.BegTime
                FROM dbo.shoptimeinfo sti
                JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
                WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000
            ) AS t
            ORDER BY BegTime
            FOR XML PATH(''), TYPE
        ).value('.', 'nvarchar(MAX)'), 1, 1, '');

        SELECT @PivotSelectColumns = STUFF((
            SELECT ',' + QUOTENAME(t.TimeName + '_K+') + ',' + QUOTENAME(t.TimeName + '_特权预约') + ',' + QUOTENAME(t.TimeName + '_美团') + ',' + QUOTENAME(t.TimeName + '_抖音') + ',' + QUOTENAME(t.TimeName + '_房费') + ',' + QUOTENAME(t.TimeName + '_小计') + ',' + QUOTENAME(t.TimeName + '_上一档直落')
            FROM (SELECT DISTINCT ti.TimeName, ti.BegTime FROM dbo.shoptimeinfo sti JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000) AS t
            ORDER BY BegTime FOR XML PATH('')
        ), 1, 1, '');

        -- ====================================================================
        -- 步骤 3: 构建并执行完整的动态 SQL 查询
        -- ====================================================================
        DECLARE @DynamicSQL nvarchar(MAX);

        SET @DynamicSQL = N'
        -- CTE 1: 预处理总览和直落指标
        WITH OverviewAndDropInData AS (
            SELECT
                rt.WorkDate, b.ShopName, DATENAME(weekday, CAST(rt.WorkDate AS date)) AS WeekdayName,
                SUM(rt.TotalAmount) AS TotalRevenue, 
                SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END) AS DayTimeRevenue, 
                SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END) AS NightTimeRevenue,
                COUNT(rt.InvNo) AS TotalBatchCount, 
                COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount, 
                COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
                SUM(rt.Numbers) AS TotalGuestCount, 
                SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,
                SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END) AS DayTimeDropInBatch,
                SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END) AS NightTimeDropInBatch,
                SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END) AS TotalDropInGuests
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
            LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
            LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
            LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
            LEFT JOIN dbo.timeinfo AS ti_end ON sti_end.TimeNo = ti_end.TimeNo
            WHERE rt.ShopId = @ShopId_Param AND CAST(rt.WorkDate AS date) >= @BeginDate_Param AND CAST(rt.WorkDate AS date) <= @EndDate_Param AND rt.OpenDateTime IS NOT NULL
            GROUP BY rt.WorkDate, b.ShopName
        ),
        -- CTE 2: 预处理白天分时段指标
        DayTimePivotedData AS (
            SELECT rt.WorkDate';

        IF ISNULL(@PivotColumns, '') <> ''
        BEGIN
            SET @DynamicSQL = @DynamicSQL + ', ' + @PivotColumns;
        END

        SET @DynamicSQL = @DynamicSQL + N'
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN dbo.timeinfo AS ti ON rt.Beg_Key = ti.TimeNo
            WHERE rt.ShopId = @ShopId_Param
              AND CAST(rt.WorkDate AS date) >= @BeginDate_Param
              AND CAST(rt.WorkDate AS date) <= @EndDate_Param
              AND rt.OpenDateTime IS NOT NULL
              AND ti.BegTime < 2000
            GROUP BY rt.WorkDate
        )
        -- 最终联接所有 CTE 的结果
        SELECT
            ovd.WorkDate AS N''日期'', 
            ovd.ShopName AS N''门店'', 
            ovd.WeekdayName AS N''星期'',
            -- 总览指标
            ISNULL(ovd.TotalRevenue, 0) AS N''营收_总收入'', 
            ISNULL(ovd.DayTimeRevenue, 0) AS N''营收_白天档'', 
            ISNULL(ovd.NightTimeRevenue, 0) AS N''营收_晚上档'',
            ISNULL(ovd.TotalBatchCount, 0) AS N''带客_全天总批数'', 
            ISNULL(ovd.DayTimeBatchCount, 0) AS N''带客_白天档_总批次'', 
            ISNULL(ovd.NightTimeBatchCount, 0) AS N''带客_晚上档_总批次'',
            ISNULL(ovd.DayTimeDropInBatch, 0) AS N''带客_白天档_直落'', 
            ISNULL(ovd.NightTimeDropInBatch, 0) AS N''带客_晚上档_直落'',
            ISNULL(ovd.TotalGuestCount, 0) AS N''用餐_总人数'', 
            ISNULL(ovd.BuffetGuestCount, 0) AS N''用餐_自助餐人数'', 
            ISNULL(ovd.TotalDropInGuests, 0) AS N''用餐_直落人数''';

        IF ISNULL(@PivotSelectColumns, '') <> ''
        BEGIN
            SET @DynamicSQL = @DynamicSQL + ', ' + @PivotSelectColumns;
        END

        SET @DynamicSQL = @DynamicSQL + N'
        FROM
            OverviewAndDropInData AS ovd
        LEFT JOIN
            DayTimePivotedData AS dtp ON ovd.WorkDate = dtp.WorkDate
        ORDER BY
            ovd.WorkDate;
        ';

        -- 调试信息输出
        IF @Debug = 1
        BEGIN
            PRINT N'动态SQL构建完成 - 白天档版本';
            PRINT N'查询日期范围: ' + CONVERT(nvarchar(10), @BeginDate, 120) + N' 到 ' + CONVERT(nvarchar(10), @EndDate, 120);
            PRINT N'门店ID: ' + CAST(@ShopId AS nvarchar(10));
            PRINT N'动态SQL: ' + LEFT(@DynamicSQL, 4000);
        END

        -- 使用 sp_executesql 执行动态 SQL，并传入参数
        EXEC sp_executesql @DynamicSQL,
            N'@BeginDate_Param date, @EndDate_Param date, @ShopId_Param int',
            @BeginDate_Param = @BeginDate,
            @EndDate_Param = @EndDate,
            @ShopId_Param = @ShopId;

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
