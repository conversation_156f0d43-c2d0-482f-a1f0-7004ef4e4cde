USE OperateData;
GO

IF OBJECT_ID('dbo.usp_GenerateDayTimeReport_Clean', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateDayTimeReport_Clean;
GO

CREATE PROCEDURE dbo.usp_GenerateDayTimeReport_Clean
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    IF @ShopId <= 0
    BEGIN
        RAISERROR(N'门店ID必须大于0', 16, 1);
        RETURN;
    END

    BEGIN TRY
        DECLARE @CurrentDate date = @BeginDate;
        
        WHILE @CurrentDate <= @EndDate
        BEGIN
            DECLARE @CurrentWorkDate varchar(8) = CONVERT(varchar(8), @CurrentDate, 112);
            
            SELECT 
                @CurrentWorkDate AS 日期,
                MAX(b.ShopName) AS 门店,
                DATENAME(weekday, @CurrentDate) AS 星期,
                SUM(rt.TotalAmount) AS 营收_总收入,
                SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END) AS 营收_白天档,
                SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END) AS 营收_晚上档,
                COUNT(rt.InvNo) AS 带客_全天总批数,
                COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS 带客_白天档_总批次,
                COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS 带客_晚上档_总批次,
                SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END) AS 带客_白天档_直落,
                SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END) AS 带客_晚上档_直落,
                SUM(rt.Numbers) AS 用餐_总人数,
                SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS 用餐_自助餐人数,
                SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END) AS 用餐_直落人数
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
            LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
            LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
            LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
            WHERE rt.ShopId = @ShopId 
              AND rt.WorkDate = @CurrentWorkDate 
              AND rt.OpenDateTime IS NOT NULL;

            WITH TimeSlots AS (
                SELECT
                    ti.TimeNo, ti.TimeName, ti.BegTime,
                    DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@CurrentDate AS datetime))) AS SlotStartDateTime,
                    LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@CurrentDate AS datetime))), 1, '2999-12-31') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
                FROM dbo.shoptimeinfo AS sti
                JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
                WHERE sti.ShopId = @ShopId
            ),
            TrueDropInData AS (
                SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
                FROM dbo.RmCloseInfo_Test AS rt
                JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
                JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
                WHERE rt.ShopId = @ShopId 
                  AND rt.WorkDate = @CurrentWorkDate 
                  AND rt.OpenDateTime IS NOT NULL
                  AND rt.Beg_Key <> rt.End_Key
                  AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
                  AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
            )
            SELECT 
                ti_main.TimeName AS 时间段,
                COUNT(CASE WHEN rt.CtNo = 2 THEN 1 ELSE NULL END) AS 'K+',
                COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS 特权预约,
                COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS 美团,
                COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS 抖音,
                COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS 房费,
                COUNT(rt.InvNo) AS 小计,
                ISNULL((
                    SELECT COUNT(tdi.InvNo)
                    FROM TrueDropInData AS tdi
                    JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo
                    WHERE ts_beg.BegTime < ti_main.BegTime
                      AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime
                ), 0) AS 上一档直落
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
            JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
            WHERE rt.ShopId = @ShopId
              AND rt.WorkDate = @CurrentWorkDate
              AND rt.OpenDateTime IS NOT NULL
              AND ti_main.BegTime < 2000
            GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
            ORDER BY ti_main.BegTime;

            SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
        END

        IF @Debug = 1
        BEGIN
            PRINT N'存储过程执行完成';
            PRINT N'查询日期范围: ' + CONVERT(nvarchar(10), @BeginDate, 120) + N' 到 ' + CONVERT(nvarchar(10), @EndDate, 120);
            PRINT N'门店ID: ' + CAST(@ShopId AS nvarchar(10));
        END

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
