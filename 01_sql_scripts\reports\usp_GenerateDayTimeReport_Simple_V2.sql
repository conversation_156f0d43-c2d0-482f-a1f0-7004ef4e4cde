USE OperateData;
GO

-- ====================================================================
-- 脚本: 优化版的总览数据存储过程 (V2)
-- 整合了 youhua.sql 中的所有总览和夜晚详细指标
-- ====================================================================

IF OBJECT_ID('dbo.usp_GenerateDayTimeReport_Simple_V2', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V2;
GO

CREATE PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V2
    @ShopId int,
    @TargetDate date
AS
BEGIN
    SET NOCOUNT ON;

    -- CTE 1: 预处理总览和直落指标 (来自 youhua.sql)
    WITH OverviewAndDropInData AS (
        SELECT
            rt.WorkDate, b.ShopName, DATENAME(weekday, @TargetDate) AS WeekdayName,
            SUM(rt.TotalAmount) AS TotalRevenue,
            SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END) AS DayTimeRevenue,
            SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END) AS NightTimeRevenue,
            COUNT(rt.InvNo) AS TotalBatchCount,
            COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
            SUM(rt.Numbers) AS TotalGuestCount,
            SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END) AS DayTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END) AS NightTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END) AS TotalDropInGuests
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
        LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
        WHERE rt.ShopId = @ShopId AND rt.WorkDate = @TargetDate AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.WorkDate, b.ShopName
    ),
    -- CTE 2: 预处理夜晚详细指标 (来自 youhua.sql)
    NightTimeDetailData AS (
        SELECT
            WorkDate,
            ISNULL(SUM(CASE WHEN ConsumptionType = '自由餐' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Subtotal,
            ISNULL(SUM(CASE WHEN ConsumptionType = '自由餐' THEN TotalAmount ELSE 0 END), 0) AS Night_FreeMeal_Amount,
            ISNULL(SUM(CASE WHEN ConsumptionType = '非自由餐' THEN TotalAmount ELSE 0 END), 0) AS Night_After20_Revenue
        FROM (
            SELECT rt.WorkDate, rt.TotalAmount,
                   CASE WHEN rt.CtNo = 19 THEN '自由餐' ELSE '非自由餐' END AS ConsumptionType
            FROM dbo.RmCloseInfo_Test rt
            WHERE rt.OpenDateTime >= DATEADD(hour, 20, CAST(CAST(rt.WorkDate AS date) AS datetime))
              AND rt.ShopId = @ShopId AND rt.WorkDate = @TargetDate
        ) AS NightShiftData
        GROUP BY WorkDate
    )
    -- 最终联接所有 CTE 的结果并输出
    SELECT
        ovd.WorkDate,
        ovd.ShopName,
        ovd.WeekdayName,
        ISNULL(ovd.TotalRevenue, 0) AS TotalRevenue,
        ISNULL(ovd.DayTimeRevenue, 0) AS DayTimeRevenue,
        ISNULL(ovd.NightTimeRevenue, 0) AS NightTimeRevenue,
        ISNULL(ovd.TotalBatchCount, 0) AS TotalBatchCount,
        ISNULL(ovd.DayTimeBatchCount, 0) AS DayTimeBatchCount,
        ISNULL(ovd.NightTimeBatchCount, 0) AS NightTimeBatchCount,
        ISNULL(ovd.DayTimeDropInBatch, 0) AS DayTimeDropInBatch,
        ISNULL(ovd.NightTimeDropInBatch, 0) AS NightTimeDropInBatch,
        ISNULL(ovd.TotalGuestCount, 0) AS TotalGuestCount,
        ISNULL(ovd.BuffetGuestCount, 0) AS BuffetGuestCount,
        ISNULL(ovd.TotalDropInGuests, 0) AS TotalDropInGuests,
        ISNULL(ntd.Night_FreeMeal_Subtotal, 0) AS Night_FreeMeal_Subtotal,
        ISNULL(ntd.Night_FreeMeal_Amount, 0) AS Night_FreeMeal_Amount,
        ISNULL(ntd.Night_After20_Revenue, 0) AS Night_After20_Revenue
    FROM
        OverviewAndDropInData AS ovd
    LEFT JOIN
        NightTimeDetailData AS ntd ON ovd.WorkDate = ntd.WorkDate;

END
GO

PRINT 'Stored procedure [usp_GenerateDayTimeReport_Simple_V2] created successfully.';
GO
