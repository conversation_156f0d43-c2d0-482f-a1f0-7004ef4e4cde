-- Final Version with Item-Name-Based Direct Fall Logic, retaining original field names
CREATE PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V3_final_fix
    @ShopId int,
    @TargetDate date
AS
BEGIN
    SET NOCOUNT ON;

    -- Define the time range based on the logic from Staff<PERSON><PERSON>ort SP
    DECLARE @BeginDate datetime = DATEADD(hour, 8, CAST(@TargetDate AS datetime));
    DECLARE @EndDate datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @TargetDate) AS datetime));

    -- CTE 1: Calculate the new direct fall counts based on item name
    WITH DirectFallCounts AS (
        SELECT
            COUNT(DISTINCT CASE WHEN ti_beg.BegTime < 1700 THEN rci.InvNo END) AS DayTimeDropInBatch_New,
            COUNT(DISTINCT CASE WHEN ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN rci.InvNo END) AS NightTimeDropInBatch_New
        FROM dbo.RmCloseInfo AS rci
        -- This join is crucial to find the items
        JOIN operatedata.dbo.FdCashBak fdc ON rci.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS AND rci.Shopid = fdc.ShopId
        -- These joins are for getting the BegTime
        LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rci.Shopid = sti_beg.Shopid AND rci.Beg_Key = sti_beg.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        WHERE rci.ShopId = @ShopId
          AND rci.CloseDatetime BETWEEN @BeginDate AND @EndDate -- Consistent filtering with the main query
          AND fdc.FdCName LIKE N'%直落%'
    ),
    -- CTE 2: Aggregate all other metrics
    AllOtherMetrics AS (
        SELECT
            -- Revenue metrics
            SUM((rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount)) AS TotalRevenue,
            SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS DayTimeRevenue,
            SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS NightTimeRevenue,
            -- Batch metrics
            COUNT(rt.InvNo) AS TotalBatchCount,
            COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
            -- Guest metrics
            SUM(rt.Numbers) AS TotalGuestCount,
            SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount
        FROM dbo.RmCloseInfo AS rt
        WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @BeginDate AND @EndDate
    )
    -- Final SELECT statement combining the two CTEs
    SELECT
        @TargetDate AS WorkDate,
        (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
        DATENAME(weekday, @TargetDate) AS WeekdayName,
        ISNULL(aom.TotalRevenue, 0) AS TotalRevenue,
        ISNULL(aom.DayTimeRevenue, 0) AS DayTimeRevenue,
        ISNULL(aom.NightTimeRevenue, 0) AS NightTimeRevenue,
        ISNULL(aom.TotalBatchCount, 0) AS TotalBatchCount,
        ISNULL(aom.DayTimeBatchCount, 0) AS DayTimeBatchCount,
        ISNULL(aom.NightTimeBatchCount, 0) AS NightTimeBatchCount,
        ISNULL(aom.TotalGuestCount, 0) AS TotalGuestCount,
        ISNULL(aom.BuffetGuestCount, 0) AS BuffetGuestCount,
        -- Get the new direct fall counts from the first CTE, using original field names
        ISNULL(dfc.DayTimeDropInBatch_New, 0) AS DayTimeDropInBatch,
        ISNULL(dfc.NightTimeDropInBatch_New, 0) AS NightTimeDropInBatch
    FROM AllOtherMetrics aom
    CROSS JOIN DirectFallCounts dfc;

END