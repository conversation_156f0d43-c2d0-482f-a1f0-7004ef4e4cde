CREATE OR ALTER PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V3_fixed
    @ShopId int,
    @TargetDate date
AS
BEGIN
    SET NOCOUNT ON;

    -- CTE 1: 在一个步骤内聚合计算所有总览指标
    WITH AllMetrics AS (
        SELECT
            rt.WorkDate,
            b.ShopName,
            DATENAME(weekday, @TargetDate) AS WeekdayName,

            -- 营收指标 (已修正)
            SUM((rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount)) AS TotalRevenue,
            SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS DayTimeRevenue,
            SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS NightTimeRevenue,

            -- 批次指标
            COUNT(rt.InvNo) AS TotalBatchCount,
            COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,

            -- 人数指标
            SUM(rt.Numbers) AS TotalGuestCount,
            SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,

            -- 直落指标
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END) AS DayTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END) AS NightTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END) AS TotalDropInGuests,

            -- 餐别指标 (基于 CtNo <> 1 的逻辑)
            COUNT(CASE WHEN rt.CtNo <> 1 THEN rt.InvNo ELSE NULL END) AS MealBatchCount,
            SUM(CASE WHEN rt.CtNo <> 1 AND (rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180) THEN 1 ELSE 0 END) AS MealDirectFallBatchCount,
            SUM(CASE WHEN rt.CtNo <> 1 AND (rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180) THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS MealDirectFallRevenue

        FROM dbo.RmCloseInfo AS rt
        JOIN MIMS.dbo.ShopInfo AS b ON rt.Shopid = b.Shopid
        LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.Shopid = sti_beg.Shopid AND rt.Beg_Key = sti_beg.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.Shopid = sti_end.Shopid AND rt.End_Key = sti_end.TimeNo
        WHERE rt.Shopid = @ShopId AND rt.WorkDate = @TargetDate AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.WorkDate, b.ShopName
    ),

    -- CTE 2: 聚合夜晚自由餐指标 (营收已修正)
    NightTimeDetailData AS (
        SELECT
            WorkDate,
            ISNULL(SUM(CASE WHEN CtNo = 19 THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Subtotal,
            ISNULL(SUM(CASE WHEN CtNo = 19 THEN (Cash+Cash_Targ*0.8+Vesa+GZ+AccOkZD+RechargeAccount+NoPayed+WXPay+AliPay+MTPay+DZPay+NMPay+[Check]+WechatDeposit+WechatShopping+ReturnAccount) ELSE 0 END), 0) AS Night_FreeMeal_Amount,
            ISNULL(SUM(CASE WHEN CtNo <> 19 THEN (Cash+Cash_Targ*0.8+Vesa+GZ+AccOkZD+RechargeAccount+NoPayed+WXPay+AliPay+MTPay+DZPay+NMPay+[Check]+WechatDeposit+WechatShopping+ReturnAccount) ELSE 0 END), 0) AS Night_After20_Revenue
        FROM dbo.RmCloseInfo
        WHERE OpenDateTime >= DATEADD(hour, 20, CAST(CAST(WorkDate AS date) AS datetime))
          AND ShopId = @ShopId AND WorkDate = @TargetDate
        GROUP BY WorkDate
    )

    -- 最终输出
    SELECT
        am.WorkDate,
        am.ShopName,
        am.WeekdayName,
        am.TotalRevenue, am.DayTimeRevenue, am.NightTimeRevenue,
        am.TotalBatchCount, am.DayTimeBatchCount, am.NightTimeBatchCount,
        am.DayTimeDropInBatch, am.NightTimeDropInBatch,
        am.TotalGuestCount, am.BuffetGuestCount, am.TotalDropInGuests,
        am.MealBatchCount, am.MealDirectFallBatchCount, am.MealDirectFallRevenue,
        ISNULL(ntd.Night_FreeMeal_Subtotal, 0) AS Night_FreeMeal_Subtotal,
        ISNULL(ntd.Night_FreeMeal_Amount, 0) AS Night_FreeMeal_Amount,
        ISNULL(ntd.Night_After20_Revenue, 0) AS Night_After20_Revenue
    FROM AllMetrics am
    LEFT JOIN NightTimeDetailData ntd ON am.WorkDate = ntd.WorkDate;

END
