
-- Final Merged Version with TotalDirectFallGuests (Corrected Parameters)
CREATE PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V3
    @ShopId int,
    @TargetDate date
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @BeginDate datetime = DATEADD(hour, 8, CAST(@TargetDate AS datetime));
    DECLARE @EndDate datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @TargetDate) AS datetime));

    -- CTE 1: The correct, TimeMode-based logic for identifying revenue and batches (from V7)
    WITH RecordsWithTimeMode AS (
        SELECT
            rt.*,
            sti.TimeMode,
            (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
            CASE
                WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                ELSE 2
            END AS RevenueClassificationMode
        FROM dbo.RmCloseInfo AS rt
        LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
        WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @BeginDate AND @EndDate
    ),
    -- CTE 2: The new, item-name-based logic for direct fall counts and guests
    DirectFallMetrics AS (
        SELECT
            COUNT(DISTINCT CASE WHEN ti_beg.BegTime < 1700 THEN rci.InvNo END) AS DayTimeDropInBatch_New,
            COUNT(DISTINCT CASE WHEN ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN rci.InvNo END) AS NightTimeDropInBatch_New,
            SUM(rci.Numbers) AS TotalDirectFallGuests_New
        FROM dbo.RmCloseInfo AS rci
        LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rci.Shopid = sti_beg.Shopid AND rci.Beg_Key = sti_beg.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        WHERE rci.InvNo IN (
            SELECT DISTINCT InvNo 
            FROM operatedata.dbo.FdCashBak 
            WHERE ShopId = @ShopId AND FdCName LIKE N'%直落%'
        )
        AND rci.ShopId = @ShopId
        AND rci.CloseDatetime BETWEEN @BeginDate AND @EndDate
    )
    -- Final Aggregation
    SELECT
        @TargetDate AS WorkDate,
        (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
        DATENAME(weekday, @TargetDate) AS WeekdayName,
        
        -- Correct Revenue and Batch counts from the V7 logic
        ISNULL(SUM(CASE WHEN rt.RevenueClassificationMode = 1 THEN rt.Revenue ELSE 0 END), 0) AS DayTimeRevenue,
        ISNULL(SUM(CASE WHEN rt.RevenueClassificationMode = 2 THEN rt.Revenue ELSE 0 END), 0) AS NightTimeRevenue,
        ISNULL(SUM(rt.Revenue), 0) AS TotalRevenue,
        COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
        COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
        (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,

        -- Guest metrics
        SUM(rt.Numbers) AS TotalGuestCount,
        SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,

        -- New Direct Fall Metrics from CTE
        ISNULL((SELECT DayTimeDropInBatch_New FROM DirectFallMetrics), 0) AS DayTimeDropInBatch,
        ISNULL((SELECT NightTimeDropInBatch_New FROM DirectFallMetrics), 0) AS NightTimeDropInBatch,
        ISNULL((SELECT TotalDirectFallGuests_New FROM DirectFallMetrics), 0) AS TotalDirectFallGuests

    FROM RecordsWithTimeMode rt
    GROUP BY rt.ShopId;

END
