CREATE PROCEDURE dbo.usp_GenerateDaytimePivotedReport

    @BeginDate DATE,

    @EndDate DATE,

    @ShopId INT

AS

BEGIN

    SET NOCOUNT ON;



    DECLARE @sql NVARCHAR(MAX) = N'';

    DECLARE @pivot_columns NVARCHAR(MAX) = N'';



    -- 1. Get the distinct, ordered list of daytime slots for the given shop

    SELECT @pivot_columns = @pivot_columns + 

        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.KPlus_Count END), 0) AS ''' + ti.TimeName + '_K+''' + 

        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Special_Count END), 0) AS ''' + ti.TimeName + '_特权预约''' + 

        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.<PERSON>_Count END), 0) AS ''' + ti.TimeName + '_美团''' + 

        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Douyin_Count END), 0) AS ''' + ti.TimeName + '_抖音''' + 

        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.RoomFee_Count END), 0) AS ''' + ti.TimeName + '_房费''' + 

        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Subtotal_Count END), 0) AS ''' + ti.TimeName + '_小计''' + 

        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.PreviousSlot_DirectFall END), 0) AS ''' + ti.TimeName + '_上档直落'''

    FROM dbo.shoptimeinfo sti

    JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo

    WHERE sti.Shopid = @ShopId AND sti.TimeMode = 1 -- *** DYNAMICALLY GETTING DAYTIME SLOTS ***

    ORDER BY ti.BegTime;



    -- 2. Build the full dynamic SQL query

    SET @sql = N'

    SELECT

        h.ReportDate AS [日期],

        h.ShopName AS [门店],

        h.Weekday AS [星期],

        ISNULL(h.TotalRevenue, 0) AS [营收_总收入],

        ISNULL(h.DayTimeRevenue, 0) AS [营收_白天档],

        ISNULL(h.NightTimeRevenue, 0) AS [营收_晚上档],

        ISNULL(h.TotalBatchCount, 0) AS [全天总批数],

        ISNULL(h.DayTimeBatchCount, 0) AS [白天档_总批次],

        ISNULL(h.NightTimeBatchCount, 0) AS [晚上档_总批次],

        ISNULL(h.DayTimeDropInBatch, 0) AS [白天档_直落],

        ISNULL(h.NightTimeDropInBatch, 0) AS [晚上档_直落],

        ISNULL(h.BuffetGuestCount, 0) AS [自助餐人数],

        ISNULL(h.TotalDirectFallGuests, 0) AS [直落人数]'

        + @pivot_columns + ',

        -- Newly added fields

        ISNULL(h.DayTimeBatchCount, 0) AS [k+餐批次],

        ISNULL(h.DayTimeDropInBatch, 0) AS [k+餐直落批数],

        ISNULL(h.NightTimeDropInBatch, 0) AS [17点 18点 19点档直落]

    FROM

        dbo.FullDailyReport_Header AS h

    LEFT JOIN

        dbo.FullDailyReport_TimeSlotDetails AS d ON h.ReportID = d.ReportID

    WHERE

        h.ReportDate BETWEEN @BeginDate AND @EndDate

        AND h.ShopID = @ShopId

    GROUP BY

        h.ReportID, h.ReportDate, h.ShopName, h.Weekday, h.TotalRevenue, h.DayTimeRevenue,

        h.NightTimeRevenue, h.TotalBatchCount, h.DayTimeBatchCount, h.NightTimeBatchCount,

        h.DayTimeDropInBatch, h.NightTimeDropInBatch, h.TotalGuestCount, h.BuffetGuestCount, h.TotalDirectFallGuests

    ORDER BY

        h.ReportDate DESC, h.ShopName;'



    -- 3. Execute the dynamic SQL

    EXEC sp_executesql @sql, N'@BeginDate DATE, @EndDate DATE, @ShopId INT', 

        @BeginDate = @BeginDate, 

        @EndDate = @EndDate, 

        @ShopId = @ShopId;



END