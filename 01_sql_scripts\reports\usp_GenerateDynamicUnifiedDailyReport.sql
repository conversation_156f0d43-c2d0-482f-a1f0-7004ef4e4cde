
CREATE PROCEDURE dbo.usp_GenerateDynamicUnifiedDailyReport
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX) = N'';
    DECLARE @pivot_columns NVARCHAR(MAX) = N'';

    -- 1. 动态获取该店铺的白天档时段（与usp_GenerateDaytimePivotedReport相同逻辑）
    SELECT @pivot_columns = @pivot_columns + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.KPlus_Count END), 0) AS [' + ti.TimeName + '_K+]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Special_Count END), 0) AS [' + ti.TimeName + '_特权预约]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.<PERSON><PERSON><PERSON>_Count END), 0) AS [' + ti.TimeName + '_美团]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Douyin_Count END), 0) AS [' + ti.TimeName + '_抖音]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.RoomFee_Count END), 0) AS [' + ti.TimeName + '_房费]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Subtotal_Count END), 0) AS [' + ti.TimeName + '_小计]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.PreviousSlot_DirectFall END), 0) AS [' + ti.TimeName + '_上档直落]'
    FROM dbo.shoptimeinfo sti
    JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
    WHERE sti.Shopid = @ShopId AND sti.TimeMode = 1 -- 白天档时段
    ORDER BY ti.BegTime;

    -- 2. 构建动态SQL查询
    SET @sql = N'
    SELECT
        -- 基础信息
        h.ReportDate AS [日期],
        h.ShopName AS [门店],
        h.Weekday AS [星期],
        ISNULL(h.TotalRevenue, 0) AS [营收_总收入],
        ISNULL(h.DayTimeRevenue, 0) AS [营收_白天档],
        ISNULL(h.NightTimeRevenue, 0) AS [营收_晚上档],
        ISNULL(h.TotalBatchCount, 0) AS [全天总批数],
        ISNULL(h.DayTimeBatchCount, 0) AS [白天档_总批次],
        ISNULL(h.NightTimeBatchCount, 0) AS [晚上档_总批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [白天档_直落],
        ISNULL(h.NightTimeDropInBatch, 0) AS [晚上档_直落],
        ISNULL(h.BuffetGuestCount, 0) AS [自助餐人数],
        ISNULL(h.TotalDirectFallGuests, 0) AS [直落人数]'
        + @pivot_columns + ',
        -- 白天档汇总字段
        ISNULL(h.DayTimeBatchCount, 0) AS [k+餐批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [k+餐直落批数],
        ISNULL(h.NightTimeDropInBatch, 0) AS [17点 18点 19点档直落],
        
        -- 晚上档数据（使用注释中的正确名称）
        ISNULL(nd.FreeMeal_KPlus, 0) AS [k+自由餐_k+],
        ISNULL(nd.FreeMeal_Special, 0) AS [k+自由餐_特权预约],
        ISNULL(nd.FreeMeal_Meituan, 0) AS [k+自由餐_美团],
        ISNULL(nd.FreeMeal_Douyin, 0) AS [k+自由餐_抖音],
        ISNULL(nd.FreeMeal_BatchCount, 0) AS [k+自由餐_小计],
        ISNULL(nd.FreeMeal_Revenue, 0) AS [k+自由餐_营业额],
        
        ISNULL(nd.Buyout_BatchCount, 0) AS [20点后_买断],
        ISNULL(nd.Buyout_Revenue, 0) AS [20点后_买断_营业额],
        
        ISNULL(nd.Changyin_BatchCount, 0) AS [20点后_畅饮],
        ISNULL(nd.Changyin_Revenue, 0) AS [20点后_畅饮_营业额],
        
        ISNULL(nd.FreeConsumption_BatchCount, 0) AS [20点后_自由消套餐],
        
        ISNULL(nd.NonPackage_Special, 0) AS [20点后_促销套餐_特权预约],
        ISNULL(nd.NonPackage_Meituan, 0) AS [20点后_促销套餐_美团],
        ISNULL(nd.NonPackage_Douyin, 0) AS [20点后_促销套餐_抖音],
        ISNULL(nd.NonPackage_Others, 0) AS [20点后其他批次],
        
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [招待批次],
        ISNULL(nd.DiscountFree_Revenue, 0) AS [招待金额],
        
        ISNULL(nd.Night_Verify_BatchCount, 0) AS [20点后_批次小计],
        ISNULL(nd.Night_Verify_Revenue, 0) AS [20点后_营收金额]
        
    FROM
        dbo.FullDailyReport_Header AS h
    LEFT JOIN
        dbo.FullDailyReport_TimeSlotDetails AS d ON h.ReportID = d.ReportID
    LEFT JOIN
        dbo.FullDailyReport_NightDetails AS nd ON h.ReportID = nd.ReportID
    WHERE 
        h.ReportDate BETWEEN ''' + CONVERT(NVARCHAR, @BeginDate, 23) + ''' AND ''' + CONVERT(NVARCHAR, @EndDate, 23) + '''
        AND h.ShopID = ' + CAST(@ShopId AS NVARCHAR) + '
    GROUP BY
        h.ReportDate, h.ShopName, h.Weekday, h.TotalRevenue, h.DayTimeRevenue, h.NightTimeRevenue,
        h.TotalBatchCount, h.DayTimeBatchCount, h.NightTimeBatchCount, h.DayTimeDropInBatch, 
        h.NightTimeDropInBatch, h.BuffetGuestCount, h.TotalDirectFallGuests,
        nd.FreeMeal_KPlus, nd.FreeMeal_Special, nd.FreeMeal_Meituan, nd.FreeMeal_Douyin,
        nd.FreeMeal_BatchCount, nd.FreeMeal_Revenue, nd.Buyout_BatchCount, nd.Buyout_Revenue,
        nd.Changyin_BatchCount, nd.Changyin_Revenue, nd.FreeConsumption_BatchCount,
        nd.NonPackage_Special, nd.NonPackage_Meituan, nd.NonPackage_Douyin, nd.NonPackage_Others,
        nd.DiscountFree_BatchCount, nd.DiscountFree_Revenue, nd.Night_Verify_BatchCount, nd.Night_Verify_Revenue
    ORDER BY h.ReportDate';

    -- 3. 执行动态SQL
    EXEC sp_executesql @sql;
END
