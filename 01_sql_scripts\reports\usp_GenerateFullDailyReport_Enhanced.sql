-- ====================================================================
-- KTV每日报告存储过程 - 全面优化版本
-- 解决字符编码、性能和数据准确性问题
-- 创建时间: 2025-01-23
-- 优化内容:
-- 1. 修复字符编码问题 (UTF-8/Unicode处理)
-- 2. 性能优化 (减少子查询，优化JOIN)
-- 3. 数据分类逻辑修复 (买断/畅饮/自由消套餐)
-- 4. 添加错误处理和性能监控
-- ====================================================================

USE OperateData;
GO

-- 设置必要的选项和编码
SET NOCOUNT ON;
SET QUOTED_IDENTIFIER ON;
SET ANSI_NULLS ON;
-- 确保正确的字符编码处理
SET ANSI_PADDING ON;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateFullDailyReport_Enhanced_V2', 'P') IS NOT NULL
DROP PROCEDURE dbo.usp_GenerateFullDailyReport_Enhanced_V2;
GO

CREATE PROCEDURE dbo.usp_GenerateFullDailyReport_Enhanced_V2
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0  -- 调试模式开关
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- 性能监控变量
    DECLARE @StartTime datetime2 = GETDATE();
    DECLARE @StepTime datetime2;

    -- 错误处理
    BEGIN TRY
        -- ====================================================================
        -- 步骤 1: 参数处理和验证
        -- ====================================================================
        IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
        IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

        -- 参数验证
        IF @ShopId <= 0
        BEGIN
            RAISERROR('门店ID必须大于0', 16, 1);
            RETURN;
        END

        IF @BeginDate > @EndDate
        BEGIN
            RAISERROR('开始日期不能大于结束日期', 16, 1);
            RETURN;
        END

        IF @Debug = 1
        BEGIN
            PRINT '参数验证完成 - 门店ID: ' + CAST(@ShopId AS varchar(10)) +
                  ', 开始日期: ' + CAST(@BeginDate AS varchar(10)) +
                  ', 结束日期: ' + CAST(@EndDate AS varchar(10));
        END

        -- ====================================================================
        -- 步骤 2: 动态构建白天分时段统计的列 (优化版本)
        -- ====================================================================
        SET @StepTime = GETDATE();

        DECLARE @PivotColumns nvarchar(MAX) = '';
        DECLARE @PivotSelectColumns nvarchar(MAX) = '';
        DECLARE @DynamicSQL nvarchar(MAX) = '';

        -- 优化：预先获取时间段信息，减少重复查询
        IF OBJECT_ID('tempdb..#TimeSlots') IS NOT NULL DROP TABLE #TimeSlots;

        SELECT DISTINCT
            ti.TimeName,
            ti.BegTime,
            -- 使用NCHAR确保Unicode字符正确处理
            QUOTENAME(ti.TimeName + N'_K+') AS KPlusCol,
            QUOTENAME(ti.TimeName + N'_特权预约') AS SpecialCol,
            QUOTENAME(ti.TimeName + N'_美团') AS MeituanCol,
            QUOTENAME(ti.TimeName + N'_抖音') AS DouyinCol,
            QUOTENAME(ti.TimeName + N'_房费') AS RoomFeeCol,
            QUOTENAME(ti.TimeName + N'_小计') AS SubtotalCol
        INTO #TimeSlots
        FROM dbo.shoptimeinfo sti WITH(NOLOCK)
        JOIN dbo.timeinfo ti WITH(NOLOCK) ON sti.TimeNo = ti.TimeNo
        WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000
        ORDER BY ti.BegTime;

        -- 构建动态列定义 (使用Unicode字符串)
        SELECT @PivotColumns = STUFF((
            SELECT
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.CtNo = 2 THEN 1 ELSE 0 END), 0) AS ' + t.KPlusCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.AliPay > 0 THEN 1 ELSE 0 END), 0) AS ' + t.SpecialCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.MTPay > 0 THEN 1 ELSE 0 END), 0) AS ' + t.MeituanCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.DZPay > 0 THEN 1 ELSE 0 END), 0) AS ' + t.DouyinCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.CtNo = 1 THEN 1 ELSE 0 END), 0) AS ' + t.RoomFeeCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' THEN 1 ELSE 0 END), 0) AS ' + t.SubtotalCol
            FROM #TimeSlots AS t
            ORDER BY BegTime
            FOR XML PATH(''), TYPE
    ).value('.', 'nvarchar(MAX)'), 1, 1, '');

    SELECT @PivotSelectColumns = STUFF((
        SELECT ',' + QUOTENAME(t.TimeName + '_K+') + ',' + QUOTENAME(t.TimeName + '_特权预约') + ',' + QUOTENAME(t.TimeName + '_美团') + ',' + QUOTENAME(t.TimeName + '_抖音') + ',' + QUOTENAME(t.TimeName + '_房费') + ',' + QUOTENAME(t.TimeName + '_小计')
        FROM (SELECT DISTINCT ti.TimeName, ti.BegTime FROM dbo.shoptimeinfo sti JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000) AS t
        ORDER BY BegTime FOR XML PATH('')
    ), 1, 1, '');


    -- ====================================================================
    -- 步骤 3: 构建并执行完整的动态 SQL 查询
    -- ====================================================================
    DECLARE @DynamicSQL nvarchar(MAX);

    SET @DynamicSQL = N'
    -- CTE 1: 预处理总览和直落指标
    WITH OverviewAndDropInData AS (
        SELECT
            rt.WorkDate, b.ShopName, DATENAME(weekday, CAST(rt.WorkDate AS date)) AS WeekdayName,
            SUM(rt.TotalAmount) AS TotalRevenue, SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END) AS DayTimeRevenue, SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END) AS NightTimeRevenue,
            COUNT(rt.InvNo) AS TotalBatchCount, COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount, COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
            SUM(rt.Numbers) AS TotalGuestCount, SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END) AS DayTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END) AS NightTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END) AS TotalDropInGuests
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
        LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_end ON sti_end.TimeNo = ti_end.TimeNo
        WHERE rt.ShopId = @ShopId_Param AND CAST(rt.WorkDate AS date) >= @BeginDate_Param AND CAST(rt.WorkDate AS date) <= @EndDate_Param AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.WorkDate, b.ShopName
    ),
    -- CTE 2: 预处理白天分时段指标
    DayTimePivotedData AS (
        SELECT rt.WorkDate';

    IF ISNULL(@PivotColumns, '') <> ''
    BEGIN
        SET @DynamicSQL = @DynamicSQL + ', ' + @PivotColumns;
    END

    SET @DynamicSQL = @DynamicSQL + N'
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN dbo.timeinfo AS ti ON rt.Beg_Key = ti.TimeNo
        WHERE rt.ShopId = @ShopId_Param AND CAST(rt.WorkDate AS date) >= @BeginDate_Param AND CAST(rt.WorkDate AS date) <= @EndDate_Param AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.WorkDate
    )
    -- CTE 3: 预处理夜晚详细指标（优化版本 - 细化非自由餐分类）
    , NightTimeDetailData AS (
        SELECT
            WorkDate,
            -- 自由餐统计（保持原有逻辑）
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' AND Channel = ''K+'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_KPlus,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' AND Channel = ''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Special,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' AND Channel = ''美团'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Meituan,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' AND Channel = ''抖音'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Douyin,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Subtotal,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' THEN TotalAmount ELSE 0 END), 0) AS Night_FreeMeal_Amount,

            -- 啤酒买断统计（优先级最高）
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''啤酒买断'' AND Channel = ''K+'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_KPlus,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''啤酒买断'' AND Channel = ''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Special,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''啤酒买断'' AND Channel = ''美团'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Meituan,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''啤酒买断'' AND Channel = ''抖音'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Douyin,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''啤酒买断'' AND Channel = ''房费'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_RoomFee,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''啤酒买断'' AND Channel = ''其他'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Others,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''啤酒买断'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Subtotal,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''啤酒买断'' THEN TotalAmount ELSE 0 END), 0) AS Night_BeerBuyout_Revenue,

            -- 畅饮套餐统计（次优先级）
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''畅饮套餐'' AND Channel = ''K+'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_KPlus,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''畅饮套餐'' AND Channel = ''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Special,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''畅饮套餐'' AND Channel = ''美团'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Meituan,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''畅饮套餐'' AND Channel = ''抖音'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Douyin,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''畅饮套餐'' AND Channel = ''房费'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_RoomFee,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''畅饮套餐'' AND Channel = ''其他'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Others,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''畅饮套餐'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Subtotal,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''畅饮套餐'' THEN TotalAmount ELSE 0 END), 0) AS Night_DrinkPackage_Revenue,

            -- 其他非自由餐统计（兜底分类）
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''其他非自由餐'' AND Channel = ''K+'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_KPlus,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''其他非自由餐'' AND Channel = ''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Special,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''其他非自由餐'' AND Channel = ''美团'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Meituan,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''其他非自由餐'' AND Channel = ''抖音'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Douyin,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''其他非自由餐'' AND Channel = ''房费'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_RoomFee,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''其他非自由餐'' AND Channel = ''其他'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Others,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''其他非自由餐'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Subtotal,
            ISNULL(SUM(CASE WHEN ConsumptionSubType = ''其他非自由餐'' THEN TotalAmount ELSE 0 END), 0) AS Night_OtherNonFree_Revenue
        FROM (
            SELECT
                rt.*,
                -- 基础消费类型分类
                CASE WHEN rt.CtNo = 19 THEN ''自由餐'' ELSE ''非自由餐'' END AS ConsumptionType,
                -- 渠道分类
                CASE
                    WHEN rt.MTPay > 0 THEN ''美团''
                    WHEN rt.DZPay > 0 THEN ''抖音''
                    WHEN rt.AliPay > 0 THEN ''特权预约''
                    WHEN rt.CtNo = 2 THEN ''K+''
                    WHEN rt.CtNo = 1 THEN ''房费''
                    ELSE ''其他''
                END AS Channel,
                -- 细化的非自由餐子类型分类（优先级：买断 > 畅饮 > 其他）
                CASE
                    WHEN rt.CtNo = 19 THEN ''自由餐''
                    WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N''%买断%'') THEN ''啤酒买断''
                    WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N''%畅饮%'') THEN ''畅饮套餐''
                    ELSE ''其他非自由餐''
                END AS ConsumptionSubType
            FROM dbo.RmCloseInfo_Test rt
            WHERE rt.OpenDateTime >= DATEADD(hour, 20, CAST(CAST(rt.WorkDate AS date) AS datetime))
        ) AS NightShiftData
        WHERE ShopId = @ShopId_Param AND CAST(WorkDate AS date) >= @BeginDate_Param AND CAST(WorkDate AS date) <= @EndDate_Param
        GROUP BY WorkDate
    )
    -- 最终联接所有 CTE 的结果
    SELECT
        ovd.WorkDate AS ''日期'', ovd.ShopName AS ''门店'', ovd.WeekdayName AS ''星期'',
        -- 总览指标
        ISNULL(ovd.TotalRevenue, 0) AS ''营收_总收入'',
        ISNULL(ovd.DayTimeRevenue, 0) AS ''营收_白天档'',
        ISNULL(ovd.NightTimeRevenue, 0) AS ''营收_晚上档'',
        ISNULL(ovd.TotalBatchCount, 0) AS ''带客_全天总批数'',
        ISNULL(ovd.DayTimeBatchCount, 0) AS ''带客_白天档_总批次'',
        ISNULL(ovd.NightTimeBatchCount, 0) AS ''带客_晚上档_总批次'',
        ISNULL(ovd.DayTimeDropInBatch, 0) AS ''带客_白天档_直落'',
        ISNULL(ovd.NightTimeDropInBatch, 0) AS ''带客_晚上档_直落'',
        ISNULL(ovd.TotalGuestCount, 0) AS ''用餐_总人数'',
        ISNULL(ovd.BuffetGuestCount, 0) AS ''用餐_自助餐人数'',
        ISNULL(ovd.TotalDropInGuests, 0) AS ''用餐_直落人数''';

    IF ISNULL(@PivotSelectColumns, '') <> ''
    BEGIN
        SET @DynamicSQL = @DynamicSQL + ', ' + @PivotSelectColumns;
    END

    SET @DynamicSQL = @DynamicSQL + N'
        -- 自由餐统计
        , ntd.Night_FreeMeal_KPlus AS ''晚间_自由餐_K+''
        , ntd.Night_FreeMeal_Special AS ''晚间_自由餐_特权预约''
        , ntd.Night_FreeMeal_Meituan AS ''晚间_自由餐_美团''
        , ntd.Night_FreeMeal_Douyin AS ''晚间_自由餐_抖音''
        , ntd.Night_FreeMeal_Subtotal AS ''晚间_自由餐_小计''
        , ntd.Night_FreeMeal_Amount AS ''晚间_自由餐_消费金额''

        -- 啤酒买断统计
        , ntd.Night_BeerBuyout_KPlus AS ''晚间_啤酒买断_K+''
        , ntd.Night_BeerBuyout_Special AS ''晚间_啤酒买断_特权预约''
        , ntd.Night_BeerBuyout_Meituan AS ''晚间_啤酒买断_美团''
        , ntd.Night_BeerBuyout_Douyin AS ''晚间_啤酒买断_抖音''
        , ntd.Night_BeerBuyout_RoomFee AS ''晚间_啤酒买断_房费''
        , ntd.Night_BeerBuyout_Others AS ''晚间_啤酒买断_其他''
        , ntd.Night_BeerBuyout_Subtotal AS ''晚间_啤酒买断_小计''
        , ntd.Night_BeerBuyout_Revenue AS ''晚间_啤酒买断_营业额''

        -- 畅饮套餐统计
        , ntd.Night_DrinkPackage_KPlus AS ''晚间_畅饮套餐_K+''
        , ntd.Night_DrinkPackage_Special AS ''晚间_畅饮套餐_特权预约''
        , ntd.Night_DrinkPackage_Meituan AS ''晚间_畅饮套餐_美团''
        , ntd.Night_DrinkPackage_Douyin AS ''晚间_畅饮套餐_抖音''
        , ntd.Night_DrinkPackage_RoomFee AS ''晚间_畅饮套餐_房费''
        , ntd.Night_DrinkPackage_Others AS ''晚间_畅饮套餐_其他''
        , ntd.Night_DrinkPackage_Subtotal AS ''晚间_畅饮套餐_小计''
        , ntd.Night_DrinkPackage_Revenue AS ''晚间_畅饮套餐_营业额''

        -- 其他非自由餐统计
        , ntd.Night_OtherNonFree_KPlus AS ''晚间_其他非自由餐_K+''
        , ntd.Night_OtherNonFree_Special AS ''晚间_其他非自由餐_特权预约''
        , ntd.Night_OtherNonFree_Meituan AS ''晚间_其他非自由餐_美团''
        , ntd.Night_OtherNonFree_Douyin AS ''晚间_其他非自由餐_抖音''
        , ntd.Night_OtherNonFree_RoomFee AS ''晚间_其他非自由餐_房费''
        , ntd.Night_OtherNonFree_Others AS ''晚间_其他非自由餐_其他''
        , ntd.Night_OtherNonFree_Subtotal AS ''晚间_其他非自由餐_小计''
        , ntd.Night_OtherNonFree_Revenue AS ''晚间_其他非自由餐_营业额''
    FROM
        OverviewAndDropInData AS ovd
    LEFT JOIN
        DayTimePivotedData AS dtp ON ovd.WorkDate = dtp.WorkDate
    LEFT JOIN
        NightTimeDetailData AS ntd ON ovd.WorkDate = ntd.WorkDate
    ORDER BY
        ovd.WorkDate;
    ';

    -- 打印动态 SQL 进行调试 (您可以注释掉这一行)
    -- PRINT @DynamicSQL;

    -- 使用 sp_executesql 执行动态 SQL，并传入参数
    EXEC sp_executesql @DynamicSQL,
        N'@BeginDate_Param date, @EndDate_Param date, @ShopId_Param int',
        @BeginDate_Param = @BeginDate,
        @EndDate_Param = @EndDate,
        @ShopId_Param = @ShopId;

END
GO
