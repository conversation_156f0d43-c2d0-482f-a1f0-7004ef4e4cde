-- ====================================================================
-- KTV每日报告存储过程 - 最终工作版本
-- 修复数据准确性问题，确保买断和畅饮统计正确
-- 创建时间: 2025-01-23
-- ====================================================================

USE OperateData;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateFullDailyReport_Final_Working', 'P') IS NOT NULL
DROP PROCEDURE dbo.usp_GenerateFullDailyReport_Final_Working;
GO

CREATE PROCEDURE dbo.usp_GenerateFullDailyReport_Final_Working
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- 参数默认值处理
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- 参数验证
    IF @ShopId <= 0
    BEGIN
        RAISERROR(N'门店ID必须大于0', 16, 1);
        RETURN;
    END

    BEGIN TRY
        -- 主查询：使用简化但准确的逻辑
        WITH 
        -- CTE 1: 基础数据预处理
        BaseData AS (
            SELECT
                rt.WorkDate, rt.InvNo, rt.TotalAmount, rt.Numbers, rt.CtNo,
                rt.MTPay, rt.DZPay, rt.AliPay, rt.OpenDateTime, rt.CloseDatetime,
                rt.Beg_Key, rt.End_Key,
                b.ShopName,
                DATENAME(weekday, CAST(rt.WorkDate AS date)) AS WeekdayName
            FROM dbo.RmCloseInfo_Test AS rt WITH(NOLOCK)
            JOIN MIMS.dbo.ShopInfo AS b WITH(NOLOCK) ON rt.ShopId = b.Shopid
            WHERE rt.ShopId = @ShopId
                AND CAST(rt.WorkDate AS date) >= @BeginDate
                AND CAST(rt.WorkDate AS date) <= @EndDate
                AND rt.OpenDateTime IS NOT NULL
        ),
        -- CTE 2: 总览统计
        OverviewData AS (
            SELECT
                bd.WorkDate,
                bd.ShopName,
                bd.WeekdayName,
                SUM(bd.TotalAmount) AS TotalRevenue,
                SUM(CASE WHEN DATEPART(hour, bd.OpenDateTime) < 20 THEN bd.TotalAmount ELSE 0 END) AS DayTimeRevenue,
                SUM(CASE WHEN DATEPART(hour, bd.OpenDateTime) >= 20 THEN bd.TotalAmount ELSE 0 END) AS NightTimeRevenue,
                COUNT(bd.InvNo) AS TotalBatchCount,
                COUNT(CASE WHEN DATEPART(hour, bd.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
                COUNT(CASE WHEN DATEPART(hour, bd.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
                SUM(bd.Numbers) AS TotalGuestCount,
                SUM(bd.Numbers) - SUM(CASE WHEN bd.CtNo = 1 THEN bd.Numbers ELSE 0 END) AS BuffetGuestCount
            FROM BaseData AS bd
            GROUP BY bd.WorkDate, bd.ShopName, bd.WeekdayName
        ),
        -- CTE 3: 夜间档详细统计（使用简化的方法）
        NightDetailData AS (
            SELECT
                rt.WorkDate,
                -- 自由餐统计（CtNo=19表示自由餐）
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.CtNo = 2 THEN rt.InvNo ELSE NULL END) AS Night_FreeMeal_KPlus,
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_FreeMeal_Special,
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_FreeMeal_Meituan,
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_FreeMeal_Douyin,
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 THEN rt.InvNo ELSE NULL END) AS Night_FreeMeal_Subtotal,
                SUM(CASE WHEN rt.CtNo = 19 THEN rt.TotalAmount ELSE 0 END) AS Night_FreeMeal_Revenue,

                -- 啤酒买断统计（使用COUNT DISTINCT避免重复）
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.CtNo = 2 THEN rt.InvNo ELSE NULL END) AS Night_Buyout_KPlus,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_Buyout_Special,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_Buyout_Meituan,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_Buyout_Douyin,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.CtNo = 1 THEN rt.InvNo ELSE NULL END) AS Night_Buyout_RoomFee,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.CtNo NOT IN (1,2,19) AND rt.AliPay = 0 AND rt.MTPay = 0 AND rt.DZPay = 0 THEN rt.InvNo ELSE NULL END) AS Night_Buyout_Others,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' THEN rt.InvNo ELSE NULL END) AS Night_Buyout_Subtotal,
                SUM(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN rt.TotalAmount ELSE 0 END) AS Night_Buyout_Revenue,

                -- 畅饮套餐统计（简化版本：先统计所有畅饮，后续可以优化优先级）
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.CtNo = 2 THEN rt.InvNo ELSE NULL END) AS Night_Changyin_KPlus,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_Changyin_Special,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_Changyin_Meituan,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_Changyin_Douyin,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.CtNo = 1 THEN rt.InvNo ELSE NULL END) AS Night_Changyin_RoomFee,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.CtNo NOT IN (1,2,19) AND rt.AliPay = 0 AND rt.MTPay = 0 AND rt.DZPay = 0 THEN rt.InvNo ELSE NULL END) AS Night_Changyin_Others,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN rt.InvNo ELSE NULL END) AS Night_Changyin_Subtotal,
                SUM(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN rt.TotalAmount ELSE 0 END) AS Night_Changyin_Revenue,

                -- 自由消套餐统计
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.CtNo = 2 THEN rt.InvNo ELSE NULL END) AS Night_FreeConsumption_KPlus,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_FreeConsumption_Special,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_FreeConsumption_Meituan,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS Night_FreeConsumption_Douyin,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.CtNo = 1 THEN rt.InvNo ELSE NULL END) AS Night_FreeConsumption_RoomFee,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.CtNo NOT IN (1,2,19) AND rt.AliPay = 0 AND rt.MTPay = 0 AND rt.DZPay = 0 THEN rt.InvNo ELSE NULL END) AS Night_FreeConsumption_Others,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN rt.InvNo ELSE NULL END) AS Night_FreeConsumption_Subtotal,
                SUM(CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN rt.TotalAmount ELSE 0 END) AS Night_FreeConsumption_Revenue
            FROM dbo.RmCloseInfo_Test AS rt WITH(NOLOCK)
            LEFT JOIN dbo.FdCashBak AS fcb WITH(NOLOCK) ON rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
            WHERE rt.ShopId = @ShopId
                AND CAST(rt.WorkDate AS date) >= @BeginDate
                AND CAST(rt.WorkDate AS date) <= @EndDate
                AND rt.OpenDateTime IS NOT NULL
                AND DATEPART(hour, rt.OpenDateTime) >= 20  -- 夜间档筛选
            GROUP BY rt.WorkDate
        )
        -- 最终结果（确保所有列名都是中文）
        SELECT
            od.WorkDate AS N'日期',
            od.ShopName AS N'门店',
            od.WeekdayName AS N'星期',
            ISNULL(od.TotalRevenue, 0) AS N'营收_总收入',
            ISNULL(od.DayTimeRevenue, 0) AS N'营收_白天档',
            ISNULL(od.NightTimeRevenue, 0) AS N'营收_晚上档',
            ISNULL(od.TotalBatchCount, 0) AS N'带客_全天总批数',
            ISNULL(od.DayTimeBatchCount, 0) AS N'带客_白天档_总批次',
            ISNULL(od.NightTimeBatchCount, 0) AS N'带客_晚上档_总批次',
            ISNULL(od.TotalGuestCount, 0) AS N'用餐_总人数',
            ISNULL(od.BuffetGuestCount, 0) AS N'用餐_自助餐人数',
            
            -- 夜间档自由餐统计
            ISNULL(ntd.Night_FreeMeal_KPlus, 0) AS N'晚间_自由餐_K+',
            ISNULL(ntd.Night_FreeMeal_Special, 0) AS N'晚间_自由餐_特权预约',
            ISNULL(ntd.Night_FreeMeal_Meituan, 0) AS N'晚间_自由餐_美团',
            ISNULL(ntd.Night_FreeMeal_Douyin, 0) AS N'晚间_自由餐_抖音',
            ISNULL(ntd.Night_FreeMeal_Subtotal, 0) AS N'晚间_自由餐_小计',
            ISNULL(ntd.Night_FreeMeal_Revenue, 0) AS N'晚间_自由餐_消费金额',

            -- 啤酒买断统计
            ISNULL(ntd.Night_Buyout_KPlus, 0) AS N'晚间_啤酒买断_K+',
            ISNULL(ntd.Night_Buyout_Special, 0) AS N'晚间_啤酒买断_特权预约',
            ISNULL(ntd.Night_Buyout_Meituan, 0) AS N'晚间_啤酒买断_美团',
            ISNULL(ntd.Night_Buyout_Douyin, 0) AS N'晚间_啤酒买断_抖音',
            ISNULL(ntd.Night_Buyout_RoomFee, 0) AS N'晚间_啤酒买断_房费',
            ISNULL(ntd.Night_Buyout_Others, 0) AS N'晚间_啤酒买断_其他',
            ISNULL(ntd.Night_Buyout_Subtotal, 0) AS N'晚间_啤酒买断_小计',
            ISNULL(ntd.Night_Buyout_Revenue, 0) AS N'晚间_啤酒买断_营业额',

            -- 畅饮套餐统计
            ISNULL(ntd.Night_Changyin_KPlus, 0) AS N'晚间_畅饮套餐_K+',
            ISNULL(ntd.Night_Changyin_Special, 0) AS N'晚间_畅饮套餐_特权预约',
            ISNULL(ntd.Night_Changyin_Meituan, 0) AS N'晚间_畅饮套餐_美团',
            ISNULL(ntd.Night_Changyin_Douyin, 0) AS N'晚间_畅饮套餐_抖音',
            ISNULL(ntd.Night_Changyin_RoomFee, 0) AS N'晚间_畅饮套餐_房费',
            ISNULL(ntd.Night_Changyin_Others, 0) AS N'晚间_畅饮套餐_其他',
            ISNULL(ntd.Night_Changyin_Subtotal, 0) AS N'晚间_畅饮套餐_小计',
            ISNULL(ntd.Night_Changyin_Revenue, 0) AS N'晚间_畅饮套餐_营业额',

            -- 自由消套餐统计
            ISNULL(ntd.Night_FreeConsumption_KPlus, 0) AS N'晚间_自由消套餐_K+',
            ISNULL(ntd.Night_FreeConsumption_Special, 0) AS N'晚间_自由消套餐_特权预约',
            ISNULL(ntd.Night_FreeConsumption_Meituan, 0) AS N'晚间_自由消套餐_美团',
            ISNULL(ntd.Night_FreeConsumption_Douyin, 0) AS N'晚间_自由消套餐_抖音',
            ISNULL(ntd.Night_FreeConsumption_RoomFee, 0) AS N'晚间_自由消套餐_房费',
            ISNULL(ntd.Night_FreeConsumption_Others, 0) AS N'晚间_自由消套餐_其他',
            ISNULL(ntd.Night_FreeConsumption_Subtotal, 0) AS N'晚间_自由消套餐_小计',
            ISNULL(ntd.Night_FreeConsumption_Revenue, 0) AS N'晚间_自由消套餐_营业额'
        FROM OverviewData AS od
        LEFT JOIN NightDetailData AS ntd ON od.WorkDate = ntd.WorkDate
        ORDER BY od.WorkDate;

        -- 调试信息输出
        IF @Debug = 1
        BEGIN
            PRINT N'存储过程执行完成';
            PRINT N'查询日期范围: ' + CONVERT(nvarchar(10), @BeginDate, 120) + N' 到 ' + CONVERT(nvarchar(10), @EndDate, 120);
            PRINT N'门店ID: ' + CAST(@ShopId AS nvarchar(10));
        END

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
