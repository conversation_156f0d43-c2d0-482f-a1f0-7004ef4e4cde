-- 连接到 OperateData 数据库
USE OperateData;
GO

-- 设置必要的选项
SET QUOTED_IDENTIFIER ON;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateOptimizedDailyReport', 'P') IS NOT NULL
DROP PROCEDURE dbo.usp_GenerateOptimizedDailyReport;
GO

CREATE PROCEDURE dbo.usp_GenerateOptimizedDailyReport
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- ====================================================================
    -- 步骤 1: 参数处理
    -- ====================================================================
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- ====================================================================
    -- 步骤 2: 创建临时表存储多日期结果
    -- ====================================================================
    CREATE TABLE #DailyResults (
        WorkDate varchar(8),
        ShopName nvarchar(50),
        WeekdayName nvarchar(10),
        -- 总览指标
        TotalRevenue int,
        DayTimeRevenue int,
        NightTimeRevenue int,
        TotalBatchCount int,
        DayTimeBatchCount int,
        NightTimeBatchCount int,
        DayTimeDropInBatch int,
        NightTimeDropInBatch int,
        TotalGuestCount int,
        BuffetGuestCount int,
        TotalDropInGuests int,
        -- 动态时段字段将通过动态SQL添加
        -- 夜间档细化统计
        Night_FreeMeal_KPlus int,
        Night_FreeMeal_Special int,
        Night_FreeMeal_Meituan int,
        Night_FreeMeal_Douyin int,
        Night_FreeMeal_Subtotal int,
        Night_FreeMeal_Amount int,
        Night_BeerBuyout_KPlus int,
        Night_BeerBuyout_Special int,
        Night_BeerBuyout_Meituan int,
        Night_BeerBuyout_Douyin int,
        Night_BeerBuyout_RoomFee int,
        Night_BeerBuyout_Others int,
        Night_BeerBuyout_Subtotal int,
        Night_BeerBuyout_Revenue int,
        Night_DrinkPackage_KPlus int,
        Night_DrinkPackage_Special int,
        Night_DrinkPackage_Meituan int,
        Night_DrinkPackage_Douyin int,
        Night_DrinkPackage_RoomFee int,
        Night_DrinkPackage_Others int,
        Night_DrinkPackage_Subtotal int,
        Night_DrinkPackage_Revenue int,
        Night_FreeConsumption_KPlus int,
        Night_FreeConsumption_Special int,
        Night_FreeConsumption_Meituan int,
        Night_FreeConsumption_Douyin int,
        Night_FreeConsumption_RoomFee int,
        Night_FreeConsumption_Others int,
        Night_FreeConsumption_Subtotal int,
        Night_FreeConsumption_Revenue int,
        Night_OtherNonFree_KPlus int,
        Night_OtherNonFree_Special int,
        Night_OtherNonFree_Meituan int,
        Night_OtherNonFree_Douyin int,
        Night_OtherNonFree_RoomFee int,
        Night_OtherNonFree_Others int,
        Night_OtherNonFree_Subtotal int,
        Night_OtherNonFree_Revenue int
    );

    -- ====================================================================
    -- 步骤 3: 循环处理每个日期
    -- ====================================================================
    DECLARE @CurrentDate date = @BeginDate;
    DECLARE @CurrentWorkDate varchar(8);

    WHILE @CurrentDate <= @EndDate
    BEGIN
        SET @CurrentWorkDate = FORMAT(@CurrentDate, 'yyyyMMdd');
        
        -- 使用优化的CTE方式处理单日数据
        WITH 
        -- CTE 1: 基础订单数据预筛选
        BaseOrders AS (
            SELECT 
                rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Numbers, rt.TotalAmount, 
                rt.CtNo, rt.Beg_Key, rt.End_Key, rt.MTPay, rt.DZPay, rt.AliPay,
                CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 'Day' ELSE 'Night' END AS TimeCategory
            FROM dbo.RmCloseInfo_Test AS rt
            WHERE rt.ShopId = @ShopId 
                AND rt.WorkDate = @CurrentWorkDate 
                AND rt.OpenDateTime IS NOT NULL
        ),
        -- CTE 2: 夜间档订单分类（优化版本 - 新增自由消套餐）
        NightOrderClassifications AS (
            SELECT
                bo.InvNo,
                bo.TotalAmount,
                bo.CtNo,
                bo.MTPay,
                bo.DZPay,
                bo.AliPay,
                MAX(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN 1 ELSE 0 END) AS HasBuyout,
                MAX(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN 1 ELSE 0 END) AS HasChangyin,
                MAX(CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN 1 ELSE 0 END) AS HasFreeConsumption
            FROM BaseOrders AS bo
            LEFT JOIN dbo.FdCashBak AS fcb ON bo.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
            WHERE bo.TimeCategory = 'Night'
            GROUP BY bo.InvNo, bo.TotalAmount, bo.CtNo, bo.MTPay, bo.DZPay, bo.AliPay
        ),
        -- CTE 3: 直落数据预处理
        DropInData AS (
            SELECT 
                bo.InvNo, bo.Numbers, bo.Beg_Key, bo.OpenDateTime, bo.CloseDatetime,
                ti_beg.BegTime,
                CASE WHEN ti_beg.BegTime < 1700 THEN 'Day' ELSE 'Night' END AS DropInCategory
            FROM BaseOrders AS bo
            JOIN dbo.shoptimeinfo AS sti_beg ON bo.Beg_Key = sti_beg.TimeNo AND sti_beg.ShopId = @ShopId
            JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
            JOIN dbo.shoptimeinfo AS sti_end ON bo.End_Key = sti_end.TimeNo AND sti_end.ShopId = @ShopId
            WHERE bo.Beg_Key <> bo.End_Key
                AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
                AND DATEDIFF(minute, bo.OpenDateTime, bo.CloseDatetime) >= 180
        )
        -- 插入当日统计结果
        INSERT INTO #DailyResults (
            WorkDate, ShopName, WeekdayName,
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDropInBatch, NightTimeDropInBatch,
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            -- 夜间档统计
            Night_FreeMeal_KPlus, Night_FreeMeal_Special, Night_FreeMeal_Meituan, Night_FreeMeal_Douyin,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount,
            Night_BeerBuyout_KPlus, Night_BeerBuyout_Special, Night_BeerBuyout_Meituan, Night_BeerBuyout_Douyin,
            Night_BeerBuyout_RoomFee, Night_BeerBuyout_Others, Night_BeerBuyout_Subtotal, Night_BeerBuyout_Revenue,
            Night_DrinkPackage_KPlus, Night_DrinkPackage_Special, Night_DrinkPackage_Meituan, Night_DrinkPackage_Douyin,
            Night_DrinkPackage_RoomFee, Night_DrinkPackage_Others, Night_DrinkPackage_Subtotal, Night_DrinkPackage_Revenue,
            Night_FreeConsumption_KPlus, Night_FreeConsumption_Special, Night_FreeConsumption_Meituan, Night_FreeConsumption_Douyin,
            Night_FreeConsumption_RoomFee, Night_FreeConsumption_Others, Night_FreeConsumption_Subtotal, Night_FreeConsumption_Revenue,
            Night_OtherNonFree_KPlus, Night_OtherNonFree_Special, Night_OtherNonFree_Meituan, Night_OtherNonFree_Douyin,
            Night_OtherNonFree_RoomFee, Night_OtherNonFree_Others, Night_OtherNonFree_Subtotal, Night_OtherNonFree_Revenue
        )
        SELECT
            @CurrentWorkDate,
            (SELECT ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId),
            DATENAME(weekday, @CurrentDate),
            -- 总览统计
            ISNULL(SUM(bo.TotalAmount), 0),
            ISNULL(SUM(CASE WHEN bo.TimeCategory = 'Day' THEN bo.TotalAmount ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN bo.TimeCategory = 'Night' THEN bo.TotalAmount ELSE 0 END), 0),
            COUNT(bo.InvNo),
            COUNT(CASE WHEN bo.TimeCategory = 'Day' THEN 1 ELSE NULL END),
            COUNT(CASE WHEN bo.TimeCategory = 'Night' THEN 1 ELSE NULL END),
            ISNULL((SELECT COUNT(*) FROM DropInData WHERE DropInCategory = 'Day'), 0),
            ISNULL((SELECT COUNT(*) FROM DropInData WHERE DropInCategory = 'Night'), 0),
            ISNULL(SUM(bo.Numbers), 0),
            ISNULL(SUM(bo.Numbers) - SUM(CASE WHEN bo.CtNo = 1 THEN bo.Numbers ELSE 0 END), 0),
            ISNULL((SELECT SUM(Numbers) FROM DropInData), 0),
            -- 夜间自由餐统计
            ISNULL(SUM(CASE WHEN noc.CtNo = 19 AND noc.CtNo = 2 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo = 19 AND noc.AliPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo = 19 AND noc.MTPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo = 19 AND noc.DZPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo = 19 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo = 19 THEN noc.TotalAmount ELSE 0 END), 0),
            -- 夜间啤酒买断统计
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 1 AND noc.CtNo = 2 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 1 AND noc.AliPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 1 AND noc.MTPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 1 AND noc.DZPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 1 AND noc.CtNo = 1 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 1 AND noc.CtNo NOT IN (1,2) AND noc.MTPay = 0 AND noc.DZPay = 0 AND noc.AliPay = 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 1 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 1 THEN noc.TotalAmount ELSE 0 END), 0),
            -- 夜间畅饮套餐统计（优先级2：排除买断）
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 1 AND noc.CtNo = 2 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 1 AND noc.AliPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 1 AND noc.MTPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 1 AND noc.DZPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 1 AND noc.CtNo = 1 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 1 AND noc.CtNo NOT IN (1,2) AND noc.MTPay = 0 AND noc.DZPay = 0 AND noc.AliPay = 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 1 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 1 THEN noc.TotalAmount ELSE 0 END), 0),
            -- 夜间自由消套餐统计（优先级3：排除买断和畅饮）
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 1 AND noc.CtNo = 2 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 1 AND noc.AliPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 1 AND noc.MTPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 1 AND noc.DZPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 1 AND noc.CtNo = 1 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 1 AND noc.CtNo NOT IN (1,2) AND noc.MTPay = 0 AND noc.DZPay = 0 AND noc.AliPay = 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 1 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 1 THEN noc.TotalAmount ELSE 0 END), 0),
            -- 夜间其他非自由餐统计（优先级4：排除买断、畅饮、自由消套餐）
            ISNULL(SUM(CASE WHEN noc.CtNo <> 19 AND noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 0 AND noc.CtNo = 2 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo <> 19 AND noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 0 AND noc.AliPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo <> 19 AND noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 0 AND noc.MTPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo <> 19 AND noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 0 AND noc.DZPay > 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo <> 19 AND noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 0 AND noc.CtNo = 1 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo <> 19 AND noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 0 AND noc.CtNo NOT IN (1,2) AND noc.MTPay = 0 AND noc.DZPay = 0 AND noc.AliPay = 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo <> 19 AND noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 0 THEN 1 ELSE 0 END), 0),
            ISNULL(SUM(CASE WHEN noc.CtNo <> 19 AND noc.HasBuyout = 0 AND noc.HasChangyin = 0 AND noc.HasFreeConsumption = 0 THEN noc.TotalAmount ELSE 0 END), 0)
        FROM BaseOrders bo
        LEFT JOIN NightOrderClassifications noc ON bo.InvNo = noc.InvNo;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    -- ====================================================================
    -- 步骤 4: 输出最终结果（暂时不包含动态时段字段）
    -- ====================================================================
    SELECT 
        WorkDate AS '日期',
        ShopName AS '门店',
        WeekdayName AS '星期',
        TotalRevenue AS '营收_总收入',
        DayTimeRevenue AS '营收_白天档',
        NightTimeRevenue AS '营收_晚上档',
        TotalBatchCount AS '带客_全天总批数',
        DayTimeBatchCount AS '带客_白天档_总批次',
        NightTimeBatchCount AS '带客_晚上档_总批次',
        DayTimeDropInBatch AS '带客_白天档_直落',
        NightTimeDropInBatch AS '带客_晚上档_直落',
        TotalGuestCount AS '用餐_总人数',
        BuffetGuestCount AS '用餐_自助餐人数',
        TotalDropInGuests AS '用餐_直落人数',
        -- 夜间档细化统计
        Night_FreeMeal_KPlus AS '晚间_自由餐_K+',
        Night_FreeMeal_Special AS '晚间_自由餐_特权预约',
        Night_FreeMeal_Meituan AS '晚间_自由餐_美团',
        Night_FreeMeal_Douyin AS '晚间_自由餐_抖音',
        Night_FreeMeal_Subtotal AS '晚间_自由餐_小计',
        Night_FreeMeal_Amount AS '晚间_自由餐_消费金额',
        Night_BeerBuyout_KPlus AS '晚间_啤酒买断_K+',
        Night_BeerBuyout_Special AS '晚间_啤酒买断_特权预约',
        Night_BeerBuyout_Meituan AS '晚间_啤酒买断_美团',
        Night_BeerBuyout_Douyin AS '晚间_啤酒买断_抖音',
        Night_BeerBuyout_RoomFee AS '晚间_啤酒买断_房费',
        Night_BeerBuyout_Others AS '晚间_啤酒买断_其他',
        Night_BeerBuyout_Subtotal AS '晚间_啤酒买断_小计',
        Night_BeerBuyout_Revenue AS '晚间_啤酒买断_营业额',
        Night_DrinkPackage_KPlus AS '晚间_畅饮套餐_K+',
        Night_DrinkPackage_Special AS '晚间_畅饮套餐_特权预约',
        Night_DrinkPackage_Meituan AS '晚间_畅饮套餐_美团',
        Night_DrinkPackage_Douyin AS '晚间_畅饮套餐_抖音',
        Night_DrinkPackage_RoomFee AS '晚间_畅饮套餐_房费',
        Night_DrinkPackage_Others AS '晚间_畅饮套餐_其他',
        Night_DrinkPackage_Subtotal AS '晚间_畅饮套餐_小计',
        Night_DrinkPackage_Revenue AS '晚间_畅饮套餐_营业额',
        Night_FreeConsumption_KPlus AS '晚间_自由消套餐_K+',
        Night_FreeConsumption_Special AS '晚间_自由消套餐_特权预约',
        Night_FreeConsumption_Meituan AS '晚间_自由消套餐_美团',
        Night_FreeConsumption_Douyin AS '晚间_自由消套餐_抖音',
        Night_FreeConsumption_RoomFee AS '晚间_自由消套餐_房费',
        Night_FreeConsumption_Others AS '晚间_自由消套餐_其他',
        Night_FreeConsumption_Subtotal AS '晚间_自由消套餐_小计',
        Night_FreeConsumption_Revenue AS '晚间_自由消套餐_营业额',
        Night_OtherNonFree_KPlus AS '晚间_其他非自由餐_K+',
        Night_OtherNonFree_Special AS '晚间_其他非自由餐_特权预约',
        Night_OtherNonFree_Meituan AS '晚间_其他非自由餐_美团',
        Night_OtherNonFree_Douyin AS '晚间_其他非自由餐_抖音',
        Night_OtherNonFree_RoomFee AS '晚间_其他非自由餐_房费',
        Night_OtherNonFree_Others AS '晚间_其他非自由餐_其他',
        Night_OtherNonFree_Subtotal AS '晚间_其他非自由餐_小计',
        Night_OtherNonFree_Revenue AS '晚间_其他非自由餐_营业额'
    FROM #DailyResults
    ORDER BY WorkDate;

    -- 清理临时表
    DROP TABLE #DailyResults;

END
GO
