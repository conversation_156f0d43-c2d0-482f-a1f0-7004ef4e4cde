-- Final Corrected Version using FdCashBak.FdCName and ensuring all calculations are for Night Shift (TimeMode=2)
CREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V5_Final
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    CREATE TABLE #DailyReports (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        <PERSON>yi<PERSON>_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2)
    );

    DECLARE @CurrentDate DATE = @BeginDate;

    WHILE @CurrentDate <= @EndDate
    BEGIN
        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));
        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));

        -- CTE to get all relevant records for the day
        WITH DailyRecords AS (
            SELECT rt.*
            FROM dbo.RmCloseInfo rt
            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
        ),
        -- CTE with robust, unified logic for time classification
        RecordsWithTimeMode AS (
            SELECT
                dr.*,
                (dr.Cash + dr.Cash_Targ * 0.8 + dr.Vesa + dr.GZ + dr.AccOkZD + dr.RechargeAccount + dr.NoPayed + dr.WXPay + dr.AliPay + dr.MTPay + dr.DZPay + dr.NMPay + dr.[Check] + dr.WechatDeposit + dr.WechatShopping + dr.ReturnAccount) AS Revenue,
                CASE
                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                    WHEN dr.OpenDateTime IS NOT NULL AND DATEPART(hour, dr.OpenDateTime) < 20 THEN 1
                    WHEN dr.OpenDateTime IS NOT NULL AND DATEPART(hour, dr.OpenDateTime) >= 20 THEN 2
                    WHEN DATEPART(hour, dr.CloseDatetime) < 20 THEN 1
                    ELSE 2
                END AS FinalTimeMode
            FROM DailyRecords dr
            LEFT JOIN dbo.shoptimeinfo AS sti ON dr.Shopid = sti.Shopid AND dr.Beg_Key = sti.TimeNo
        )
        INSERT INTO #DailyReports
        SELECT
            @CurrentDate AS ReportDate,
            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
            DATENAME(weekday, @CurrentDate) AS Weekday,
            
            -- Core Metrics using unified FinalTimeMode
            ISNULL(SUM(Revenue), 0) AS TotalRevenue,
            ISNULL(SUM(CASE WHEN FinalTimeMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,
            ISNULL(SUM(CASE WHEN FinalTimeMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,
            COUNT(InvNo) AS TotalBatchCount,
            COUNT(CASE WHEN FinalTimeMode = 1 THEN 1 END) AS DayTimeBatchCount,
            COUNT(CASE WHEN FinalTimeMode = 2 THEN 1 END) AS NightTimeBatchCount,

            -- All detailed calculations below are strictly for Night Shift (FinalTimeMode = 2)
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo = 19 AND rt.CtNo = 2 THEN rt.InvNo END) AS FreeMeal_KPlus,
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo = 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS FreeMeal_Special,
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo = 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS FreeMeal_Meituan,
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo = 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS FreeMeal_Douyin,
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo = 19 THEN rt.InvNo END) AS FreeMeal_BatchCount,
            ISNULL(SUM(CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo = 19 THEN rt.Revenue ELSE 0 END), 0) AS FreeMeal_Revenue,

            -- Converted subqueries to conditional aggregations, filtered for Night Shift
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = rt.InvNo AND d.FdCName LIKE N'%买断%') THEN rt.InvNo END) AS Buyout_BatchCount,
            ISNULL(SUM(CASE WHEN rt.FinalTimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = rt.InvNo AND d.FdCName LIKE N'%买断%') THEN rt.Tot ELSE 0 END), 0) AS Buyout_Revenue,

            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = rt.InvNo AND (d.FdCName LIKE N'%畅饮%')) THEN rt.InvNo END) AS Changyin_BatchCount,
            ISNULL(SUM(CASE WHEN rt.FinalTimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = rt.InvNo AND (d.FdCName LIKE N'%畅饮%' OR d.FdCName LIKE N'%欢唱%')) THEN rt.Tot ELSE 0 END), 0) AS Changyin_Revenue,

            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = rt.InvNo AND d.FdCName LIKE N'%自由消%') THEN rt.InvNo END) AS FreeConsumption_BatchCount,

            -- Non-Package calculations filtered for Night Shift
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo <> 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS NonPackage_Special,
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo <> 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS NonPackage_Meituan,
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo <> 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS NonPackage_Douyin,
            COUNT(DISTINCT CASE WHEN rt.FinalTimeMode = 2 AND rt.CtNo <> 19 AND rt.AliPay <= 0 AND rt.MTPay <= 0 AND rt.DZPay <= 0 THEN rt.InvNo END) AS NonPackage_Others,

            -- Night Verify Metrics (Control Totals)
            COUNT(CASE WHEN FinalTimeMode = 2 THEN 1 END) AS Night_Verify_BatchCount,
            ISNULL(SUM(CASE WHEN FinalTimeMode = 2 THEN Revenue ELSE 0 END), 0) AS Night_Verify_Revenue

        FROM RecordsWithTimeMode rt;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    SELECT * FROM #DailyReports ORDER BY ReportDate;

    DROP TABLE #DailyReports;

END