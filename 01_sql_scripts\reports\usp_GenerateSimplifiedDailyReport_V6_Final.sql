
-- Final, Corrected Version (V6) - Special packages are now strictly night-time only.
CREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V6_Final
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    CREATE TABLE #DailyReports (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        <PERSON>yin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2)
    );

    DECLARE @CurrentDate DATE = @BeginDate;

    WHILE @CurrentDate <= @EndDate
    BEGIN
        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));
        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));

        -- CTE with the robust, verified logic for classification
        WITH RecordsWithTimeMode AS (
            SELECT
                rt.*,
                sti.TimeMode,
                (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
                CASE
                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                    WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                    ELSE 2
                END AS RevenueClassificationMode
            FROM dbo.RmCloseInfo AS rt
            LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
        )
        INSERT INTO #DailyReports
        SELECT
            @CurrentDate AS ReportDate,
            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
            DATENAME(weekday, @CurrentDate) AS Weekday,
            
            -- Correct Core Metrics
            ISNULL(SUM(Revenue), 0) AS TotalRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,
            COUNT(InvNo) AS TotalBatchCount,
            COUNT(CASE WHEN TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,

            -- Restored Original Detailed Logic for FreeMeal & NonPackage
            COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.CtNo = 2 THEN rt.InvNo END) AS FreeMeal_KPlus,
            COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS FreeMeal_Special,
            COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS FreeMeal_Meituan,
            COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS FreeMeal_Douyin,
            COUNT(DISTINCT CASE WHEN rt.CtNo = 19 THEN rt.InvNo END) AS FreeMeal_BatchCount,
            ISNULL(SUM(CASE WHEN rt.CtNo = 19 THEN rt.Revenue ELSE 0 END), 0) AS FreeMeal_Revenue,

            -- *** ULTIMATE FINAL LOGIC: Subqueries now filter for TimeMode = 2 ***
            (SELECT COUNT(DISTINCT dr.InvNo) FROM RecordsWithTimeMode dr WHERE dr.TimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = dr.InvNo AND d.FdCName LIKE N'%买断%')) AS Buyout_BatchCount,
            ISNULL((SELECT SUM(dr.Tot) FROM RecordsWithTimeMode dr WHERE dr.TimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = dr.InvNo AND d.FdCName LIKE N'%买断%')), 0) AS Buyout_Revenue,

            (SELECT COUNT(DISTINCT dr.InvNo) FROM RecordsWithTimeMode dr WHERE dr.TimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = dr.InvNo AND (d.FdCName LIKE N'%畅饮%' OR d.FdCName LIKE N'%欢唱%'))) AS Changyin_BatchCount,
            ISNULL((SELECT SUM(dr.Tot) FROM RecordsWithTimeMode dr WHERE dr.TimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = dr.InvNo AND (d.FdCName LIKE N'%畅饮%' OR d.FdCName LIKE N'%欢唱%'))), 0) AS Changyin_Revenue,

            (SELECT COUNT(DISTINCT dr.InvNo) FROM RecordsWithTimeMode dr WHERE dr.TimeMode = 2 AND EXISTS (SELECT 1 FROM operatedata.dbo.FdCashBak d WHERE d.InvNo = dr.InvNo AND d.FdCName LIKE N'%自由消%')) AS FreeConsumption_BatchCount,

            -- Restored Original Detailed Logic
            COUNT(DISTINCT CASE WHEN rt.CtNo <> 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS NonPackage_Special,
            COUNT(DISTINCT CASE WHEN rt.CtNo <> 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS NonPackage_Meituan,
            COUNT(DISTINCT CASE WHEN rt.CtNo <> 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS NonPackage_Douyin,
            COUNT(DISTINCT CASE WHEN rt.CtNo <> 19 AND rt.AliPay <= 0 AND rt.MTPay <= 0 AND rt.DZPay <= 0 THEN rt.InvNo END) AS NonPackage_Others,

            -- Correct Night Verify Metrics
            COUNT(CASE WHEN TimeMode = 2 THEN 1 ELSE NULL END) AS Night_Verify_BatchCount,
            ISNULL(SUM(CASE WHEN TimeMode = 2 THEN Revenue ELSE 0 END), 0) AS Night_Verify_Revenue

        FROM RecordsWithTimeMode rt;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    SELECT * FROM #DailyReports ORDER BY ReportDate;

    DROP TABLE #DailyReports;

END
