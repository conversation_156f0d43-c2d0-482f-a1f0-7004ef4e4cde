
-- 优化版本: usp_GenerateSimplifiedDailyReport_V8_Optimized
-- 添加DiscountFree字段并优化输出结构
CREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V8_Optimized
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    CREATE TABLE #DailyReports (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        
        -- FreeMeal相关字段（当前未使用，但保留结构）
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        
        -- 活跃使用的字段
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        
        -- NonPackage相关字段（部分未使用）
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
        
        -- 验证字段
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
        
        -- 新增：DiscountFree字段
        DiscountFree_BatchCount int, DiscountFree_Revenue decimal(18, 2)
    );

    DECLARE @CurrentDate DATE = @BeginDate;

    WHILE @CurrentDate <= @EndDate
    BEGIN
        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));
        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));

        -- CTE for base records
        WITH RecordsWithTimeMode AS (
            SELECT
                rt.*,
                sti.TimeMode,
                (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
                CASE
                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                    WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                    ELSE 2
                END AS RevenueClassificationMode
            FROM dbo.RmCloseInfo AS rt
            LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
        ),
        -- CTE for package data (night-time only)
        PackageData AS (
            SELECT
                r.InvNo,
                fdc.FdCName,
                (fdc.FdPrice * fdc.FdQty) as ItemRevenue
            FROM RecordsWithTimeMode r
            JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE r.TimeMode = 2
            AND fdc.ShopId = @ShopId
            AND (fdc.FdCName LIKE N'%买断%' OR fdc.FdCName LIKE N'%畅饮%' OR fdc.FdCName LIKE N'%折扣免费%')
        )
        -- Final aggregation and insertion
        INSERT INTO #DailyReports
        SELECT
            @CurrentDate AS ReportDate,
            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
            DATENAME(weekday, @CurrentDate) AS Weekday,
            
            -- Core Metrics
            ISNULL(SUM(Revenue), 0) AS TotalRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,
            (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,

            -- Free Meal Metrics (当前逻辑可能有问题，需要根据实际业务调整)
            0 AS FreeMeal_KPlus,      -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_Special,    -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_Meituan,    -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_Douyin,     -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_BatchCount, -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_Revenue,    -- 暂时设为0，需要实现具体逻辑

            -- Package Metrics (活跃字段)
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') AS Buyout_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%买断%'), 0) AS Buyout_Revenue,
            ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) AS Changyin_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%畅饮%' AND FdCName NOT LIKE N'%自由畅饮%'), 0) AS Changyin_Revenue,
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') AS FreeConsumption_BatchCount,

            -- Non-Package Metrics (大部分未使用)
            0 AS NonPackage_Special,  -- 暂时设为0
            0 AS NonPackage_Meituan,  -- 暂时设为0
            0 AS NonPackage_Douyin,   -- 暂时设为0
            
            -- NonPackage_Others (活跃字段，保持原逻辑)
            (COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) - 
            (
                0 + -- FreeMeal相关暂时为0
                (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') + 
                ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) + 
                (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') + 
                0 + 0 + 0  -- NonPackage_Special, Meituan, Douyin暂时为0
            ) AS NonPackage_Others,

            -- Verification Metrics
            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS Night_Verify_BatchCount,
            ISNULL(SUM(CASE WHEN rt.TimeMode = 2 THEN rt.Revenue ELSE 0 END), 0) AS Night_Verify_Revenue,
            
            -- 新增：DiscountFree Metrics
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%折扣免费%') AS DiscountFree_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%折扣免费%'), 0) AS DiscountFree_Revenue

        FROM RecordsWithTimeMode rt;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    SELECT * FROM #DailyReports ORDER BY ReportDate;

    DROP TABLE #DailyReports;

END
