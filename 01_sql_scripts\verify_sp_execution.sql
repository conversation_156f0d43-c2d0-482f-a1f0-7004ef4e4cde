
USE operatedata;
GO

DECLARE @TargetDateSK INT = ********; -- The SK for '2025-08-12'

PRINT '--- Final Verification: Displaying data processed by the stored procedure for 2025-08-12 ---';

SELECT
    f.DateSK,
    s.ShopName,
    d.DealName,
    b.Bank<PERSON>ame,
    f.RedemptionCount,
    f.Redemption<PERSON>mount,
    f.SubsidyAmount,
    f.PlatformFee
FROM
    dbo.Fact_Deal_Redemption f
JOIN
    dbo.Dim_Shop s ON f.ShopSK = s.ShopSK
JOIN
    dbo.Dim_Bank_Deal d ON f.DealSK = d.DealSK
JOIN
    dbo.Dim_Bank b ON d.BankSK = b.BankSK
WHERE
    f.DateSK = @TargetDateSK
ORDER BY
    s.ShopName, d.DealName;
GO
