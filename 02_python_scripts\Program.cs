using MySql.Data.MySqlClient;
using System.Data;
using Microsoft.Data.SqlClient;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

public class DailySync
{
    // --- 配置信息 ---
    private const string MySqlConnectionString = "server=yy.tang-hui.com.cn;port=3306;database=nwechat_bar;user=user_bar;password=***************;";
    private const string SqlServerConnectionString = "server=192.168.2.5;database=operatedata;user id=sa;password=***********;TrustServerCertificate=true;";
    private const int DaysToSync = 1; // 只同步昨天的数据

    public static async Task Main(string[] args)
    {
        Console.WriteLine("Starting daily transaction sync process (C# version)...");
        var mysqlConnection = new MySqlConnection(MySqlConnectionString);
        var sqlConnection = new SqlConnection(SqlServerConnectionString);

        try
        {
            await mysqlConnection.OpenAsync();
            Console.WriteLine("MySQL connection successful.");
            await sqlConnection.OpenAsync();
            Console.WriteLine("SQL Server connection successful.");

            var syncDate = DateTime.Today.AddDays(-DaysToSync);
            Console.WriteLine($"Fetching records for date: {syncDate:yyyy-MM-dd}");

            // 1. 获取 msginfo 数据
            var msginfoDt = await GetMySqlDataAsync(mysqlConnection, syncDate, "msginfo");
            if (msginfoDt.Rows.Count == 0)
            {
                Console.WriteLine("No new msginfo records found. Process finished.");
                return;
            }
            Console.WriteLine($"Found {msginfoDt.Rows.Count} msginfo records to process.");

            // 2. 处理 msginfo 数据，并获取 iKeyMsg 列表
            var msginfoMap = new Dictionary<string, (DateTime WorkDate, int ShopId)>();
            var msginfoKeys = new List<string>();
            foreach (DataRow row in msginfoDt.Rows)
            {
                var deDatetime = (DateTime)row["DeDatetime"];
                var workDate = GetWorkDate(deDatetime);
                var shopId = (int)row["DeShopId"];
                var iKeyMsg = row["iKeyMsg"].ToString();

                row["WorkDate"] = workDate; // 在DataTable中直接添加/修改列
                if (!msginfoMap.ContainsKey(iKeyMsg))
                {
                    msginfoMap.Add(iKeyMsg, (workDate, shopId));
                    msginfoKeys.Add(iKeyMsg);
                }
            }

            // 3. 获取关联的 drinksinfo 数据
            var drinksinfoDt = await GetMySqlDataAsync(mysqlConnection, msginfoKeys, "drinksinfo");
            Console.WriteLine($"Found {drinksinfoDt.Rows.Count} related drinksinfo records.");

            // 4. 处理 drinksinfo 数据
            foreach (DataRow row in drinksinfoDt.Rows)
            {
                var parentInfo = msginfoMap[row["iKeyMsg"].ToString()];
                row["WorkDate"] = parentInfo.WorkDate;
                row["ShopId"] = parentInfo.ShopId;
            }

            // 5. 使用 MERGE 更新到 SQL Server
            await UpsertDataAsync(sqlConnection, msginfoDt, "msginfo");
            await UpsertDataAsync(sqlConnection, drinksinfoDt, "drinksinfo");

            Console.WriteLine("Daily transaction sync process finished successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred: {ex.Message}");
        }
        finally
        {
            await mysqlConnection.CloseAsync();
            await sqlConnection.CloseAsync();
            Console.WriteLine("Connections closed.");
        }
    }

    private static DateTime GetWorkDate(DateTime dt) => dt.Hour < 9 ? dt.Date.AddDays(-1) : dt.Date;

    private static async Task<DataTable> GetMySqlDataAsync(MySqlConnection connection, DateTime syncDate, string tableName)
    {
        var dt = new DataTable();
        string query = $"SELECT * FROM {tableName} WHERE DeDatetime >= @syncDate AND DeDatetime < @endDate";
        var command = new MySqlCommand(query, connection);
        command.Parameters.AddWithValue("@syncDate", syncDate);
        command.Parameters.AddWithValue("@endDate", syncDate.AddDays(1));
        using (var adapter = new MySqlDataAdapter(command))
        {
            await adapter.FillAsync(dt);
        }
        // 为后续处理添加额外的列
        if (!dt.Columns.Contains("WorkDate")) dt.Columns.Add("WorkDate", typeof(DateTime));
        if (tableName == "drinksinfo" && !dt.Columns.Contains("ShopId")) dt.Columns.Add("ShopId", typeof(int));
        return dt;
    }
    
    private static async Task<DataTable> GetMySqlDataAsync(MySqlConnection connection, List<string> keys, string tableName)
    {
        var dt = new DataTable();
        string query = $"SELECT * FROM {tableName} WHERE iKeyMsg IN ({string.Join(",", keys.Select(k => $"'{k}'"))})";
        var command = new MySqlCommand(query, connection);
        using (var adapter = new MySqlDataAdapter(command))
        {
            await adapter.FillAsync(dt);
        }
        if (!dt.Columns.Contains("WorkDate")) dt.Columns.Add("WorkDate", typeof(DateTime));
        if (!dt.Columns.Contains("ShopId")) dt.Columns.Add("ShopId", typeof(int));
        return dt;
    }


    private static async Task UpsertDataAsync(SqlConnection connection, DataTable dt, string tableName)
    {
        string tempTableName = $"#temp_{tableName}";
        
        // 1. 创建临时表
        var command = connection.CreateCommand();
        command.CommandText = $"SELECT TOP 0 * INTO {tempTableName} FROM {tableName}";
        await command.ExecuteNonQueryAsync();

        // 2. Bulk Copy 到临时表
        using (var bulkCopy = new SqlBulkCopy(connection))
        {
            bulkCopy.DestinationTableName = tempTableName;
            foreach (DataColumn col in dt.Columns)
            {
                bulkCopy.ColumnMappings.Add(col.ColumnName, col.ColumnName);
            }
            await bulkCopy.WriteToServerAsync(dt);
        }
        
        // 3. 执行 MERGE
        string primaryKey = (tableName == "msginfo") ? "iKeyMsg" : "iKeyDrinks";
        var columns = new List<string>();
        foreach (DataColumn col in dt.Columns) columns.Add(col.ColumnName);
        
        string updateSet = string.Join(", ", columns.Where(c => c != primaryKey).Select(c => $"T.{c} = S.{c}"));
        string insertCols = string.Join(", ", columns);
        string sourceCols = string.Join(", ", columns.Select(c => $"S.{c}"));

        command.CommandText = $@"
            MERGE {tableName} AS T
            USING {tempTableName} AS S ON T.{primaryKey} = S.{primaryKey}
            WHEN MATCHED THEN UPDATE SET {updateSet}
            WHEN NOT MATCHED BY TARGET THEN INSERT ({insertCols}) VALUES ({sourceCols});";
        
        int affectedRows = await command.ExecuteNonQueryAsync();
        Console.WriteLine($"{affectedRows} rows merged into {tableName}.");

        // 4. 删除临时表
        command.CommandText = $"DROP TABLE {tempTableName}";
        await command.ExecuteNonQueryAsync();
    }
}
