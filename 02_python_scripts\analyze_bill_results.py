
import pyodbc
from collections import defaultdict

# Connection details
server = '193.112.2.229'
database = 'dbfood'
username = 'sa'
password = 'Musicbox@123'
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Connection Timeout=10;'

try:
    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    
    # Query to get the distinct rooms per day for the most recent days
    # Using a CTE to first get the raw data, then grouping for clarity
    query = """
    WITH DailyData AS (
        SELECT 
            CONVERT(DATE, CheckoutTime) AS CheckoutDate,
            RmNo
        FROM 
            BillAnalysisResults
        WHERE 
            RmNo IS NOT NULL AND CheckoutTime IS NOT NULL
    )
    SELECT 
        d.CheckoutDate,
        COUNT(DISTINCT d.RmNo) as DistinctRoomCount,
        STUFF((
            SELECT DISTINCT ', ' + CAST(d2.RmNo AS VARCHAR(MAX))
            FROM DailyData d2
            WHERE d2.CheckoutDate = d.CheckoutDate
            FOR XML PATH('')
        ), 1, 2, '') AS RoomList
    FROM 
        DailyData d
    GROUP BY
        d.CheckoutDate
    ORDER BY
        d.CheckoutDate DESC;
    """
    
    print("Executing query to get daily room analysis...")
    cursor.execute(query)
    
    rows = cursor.fetchall()
    
    if rows:
        print("\n--- Analysis Results (Most Recent Days) ---")
        # Limiting output to the top 5 recent days as requested
        for i, row in enumerate(rows[:5]):
            print(f"\nDate: {row.CheckoutDate}")
            print(f"  - Distinct Room Count: {row.DistinctRoomCount}")
            # Formatting the room list for better readability
            room_list = ", ".join(sorted(list(set(row.RoomList.split(', ')))))
            print(f"  - Rooms: {room_list}")
    else:
        print("No data found in BillAnalysisResults.")

    cnxn.close()

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database Error Occurred: SQLSTATE {sqlstate}")
    print(ex)
except Exception as e:
    print(f"An error occurred: {e}")
