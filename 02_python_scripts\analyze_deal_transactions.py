
import pyodbc
import sys

def main():
    # Connection details
    CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
    
    print("--- Analyzing join between fdcashbak and Dim_Bank_Deal ---")
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        query = """
        SELECT TOP 100
            d.DealName,
            b.BankName,
            f.ShopId,
            f.FdQty,
            f.FdPrice,
            f.FdPriceBeforeDisc,
            f.CashTime
        FROM
            dbo.fdcashbak AS f
        JOIN
            dbo.Dim_Bank_Deal AS d ON f.FdNo = d.FdNo COLLATE Chinese_PRC_CI_AS
        JOIN
            dbo.Dim_Bank AS b ON d.BankSK = b.BankSK
        WHERE
            d.FdNo IS NOT NULL;
        """
        
        print("Executing join query...")
        cursor.execute(query)
        rows = cursor.fetchall()

        if rows:
            print(f"[SUCCESS] Found {len(rows)} transaction(s) for our bank deals.")
            print("\n--- Sample Matched Transactions ---")
            print(f"{ 'Bank':<10} {'Deal Name':<40} {'Shop':<5} {'Qty':<5} {'Price':<10}")
            print(f"{'-'*10:<10} {'-'*40:<40} {'-'*5:<5} {'-'*5:<5} {'-'*10:<10}")
            for row in rows:
                print(f"{row.BankName:<10} {row.DealName:<40} {row.ShopId:<5} {row.FdQty:<5} {row.FdPrice:<10}")
        else:
            print("[-] No matching transactions found between fdcashbak and Dim_Bank_Deal.")
            print("This might be because there are no recent transactions for the mapped FdNos.")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
