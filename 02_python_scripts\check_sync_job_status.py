import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
JOB_NAME = 'RMS_Daily_Data_Sync_to_HQ'

def check_agent_and_job_status():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    print(f"--- 正在连接到服务器 {SERVER} ---")
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print("--- 连接成功！---")

            # 1. 检查 SQL Server Agent 服务状态
            print("\n--- 1. 检查 SQL Server 代理服务状态 ---")
            # 这个查询会返回代理服务的状态描述
            agent_status_sql = "SELECT dss.status_desc FROM sys.dm_server_services dss WHERE dss.servicename LIKE 'SQL Server Agent%';"
            cursor.execute(agent_status_sql)
            result = cursor.fetchone()
            if result:
                print(f"代理服务状态: {result[0]}")
                if "Running" not in result[0]:
                    print("警告: SQL Server 代理服务当前未运行，这是作业不执行的最可能原因！")
            else:
                print("无法获取代理服务状态。")

            # 2. 检查作业的启用状态和调度
            print(f"\n--- 2. 检查作业 [{JOB_NAME}] 的状态和调度 ---")
            job_status_sql = """
            SELECT 
                j.name AS JobName,
                j.enabled AS IsJobEnabled,
                s.name AS ScheduleName,
                s.enabled AS IsScheduleEnabled,
                CASE s.freq_type
                    WHEN 4 THEN '每日'
                    ELSE '其他频率'
                END AS Frequency,
                s.freq_interval AS Interval,
                STUFF(STUFF(RIGHT('000000' + CAST(s.active_start_time AS VARCHAR(6)), 6), 3, 0, ':'), 6, 0, ':') AS StartTime
            FROM msdb.dbo.sysjobs j
            JOIN msdb.dbo.sysjobschedules js ON j.job_id = js.job_id
            JOIN msdb.dbo.sysschedules s ON js.schedule_id = s.schedule_id
            WHERE j.name = ?
            """
            cursor.execute(job_status_sql, JOB_NAME)
            job_info = cursor.fetchone()
            if job_info:
                print(f"  - 作业名称: {job_info.JobName}")
                print(f"  - 作业是否启用: {'是' if job_info.IsJobEnabled == 1 else '否'}")
                print(f"  - 调度名称: {job_info.ScheduleName}")
                print(f"  - 调度是否启用: {'是' if job_info.IsScheduleEnabled == 1 else '否'}")
                print(f"  - 频率: {job_info.Frequency}")
                print(f"  - 间隔: 每 {job_info.Interval} 天")
                print(f"  - 开始时间: {job_info.StartTime}")
                if job_info.IsJobEnabled == 0 or job_info.IsScheduleEnabled == 0:
                    print("警告: 作业或其调度已被禁用！")
            else:
                print(f"未找到名为 [{JOB_NAME}] 的作业或其调度信息。")

    except Exception as e:
        print(f"\n--- 执行过程中发生错误 ---: {e}")

if __name__ == '__main__':
    check_agent_and_job_status()
