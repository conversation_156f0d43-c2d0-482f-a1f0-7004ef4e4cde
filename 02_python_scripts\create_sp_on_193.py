import pyodbc

# --- 远程服务器配置 ---
SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

# --- SQL 定义 ---

# 1. 如果存储过程存在，则先删除
DROP_PROC_SQL = """
IF OBJECT_ID('usp_CalculateAndStoreBillDifferences', 'P') IS NOT NULL
    DROP PROCEDURE usp_CalculateAndStoreBillDifferences;
"""

# 2. 存储过程的主体
CREATE_PROC_SQL = """
CREATE PROCEDURE usp_CalculateAndStoreBillDifferences
AS
BEGIN
    SET NOCOUNT ON;

    WITH WxPayAggregated AS (
        SELECT
            p.InvNo,
            SUM(p.Tot) AS WxPayTotalForInv,
            STUFF((
                SELECT ', ' + w.transaction_id
                FROM wxpayinfo w
                WHERE w.InvNo = p.InvNo FOR XML PATH('')
            ), 1, 2, '') AS TransactionIDs
        FROM wxpayinfo p
        GROUP BY p.InvNo
    ),
    AllBillDetails AS (
        SELECT
            r.InvNo, r.RmNo, r.Tot AS RoomSystemAmount,
            w.WxPayTotalForInv AS WxPayTotalAmount,
            (w.WxPayTotalForInv - r.Tot) AS Difference,
            w.TransactionIDs,
            CONVERT(datetime, r.AccDate + ' ' + r.AccTime) AS CheckoutTime,
            r.CloseTime
        FROM ROOM r
        INNER JOIN WxPayAggregated w ON r.InvNo = w.InvNo
        WHERE r.RmStatus = 'A'
          AND DATEDIFF(minute, CONVERT(datetime, r.AccDate + ' ' + r.AccTime), GETDATE()) BETWEEN 0 AND 10
    )
    INSERT INTO BillAnalysisResults (
        InvNo, RmNo, RoomSystemAmount, WxPayTotalAmount, Difference,
        TransactionIDs, CheckoutTime, CloseTime, IsDifferenceNormal
    )
    SELECT
        InvNo, RmNo, RoomSystemAmount, WxPayTotalAmount, Difference,
        TransactionIDs, CheckoutTime, CloseTime,
        CASE WHEN Difference <= 0 THEN 1 ELSE 0 END AS IsDifferenceNormal
    FROM AllBillDetails;

    SELECT @@ROWCOUNT AS InsertedRows;
END;
"""

def create_sp_on_193():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    print(f"--- 正在尝试直接连接到 {SERVER}/{DATABASE} ---")
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=20) as conn:
            cursor = conn.cursor()
            print("--- 连接成功！---")

            print("\n--- 正在创建 usp_CalculateAndStoreBillDifferences 存储过程... ---")
            cursor.execute(DROP_PROC_SQL)   # 先执行DROP
            cursor.execute(CREATE_PROC_SQL) # 再执行CREATE
            print("--- 存储过程创建成功。 ---")

            print("\n部署完成！账单差异分析存储过程已成功部署到 193 服务器。 সন")

    except Exception as e:
        print(f"\n--- 执行过程中发生错误 ---: {e}")

if __name__ == '__main__':
    create_sp_on_193()
