# -*- coding: utf-8 -*-
import mysql.connector
import pyodbc
import sys
import datetime

# --- Configuration ---
MYSQL_CONFIG = {
    'user': 'user_bar',
    'password': 'TJwe9JZB8YokL0O',
    'host': 'yy.tang-hui.com.cn',
    'port': 3306,
    'database': 'nwechat_bar',
}

SQL_SERVER_CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
DAYS_TO_SYNC = 3

# --- Column Mappings ---
MSGINFO_COLS = ['WorkDate', 'iKeyMsg', 'CustName', 'CustTel', 'MsgStatus', 'MsgPassword', 'DeShopId', 'DeRmNo', 'DeDatetime', 'DeBarName', 'DeServiceName', 'DeCheckName', 'DeMemory', 'DrShopId', 'DrRmNo', 'DrDatetime', 'DrBarName', 'DrServiceName', 'DrCheckName', 'DrMemory', 'IsDelete', 'DeleteUserName', 'Val1', 'Val2', 'Val3', 'Val4', 'Val5', 'BrandId', 'ReNew', 'ICode', 'DrCheckId', 'ExDatetime']
DRINKSINFO_COLS = ['ShopId', 'WorkDate', 'iKeyDrinks', 'iKeyMsg', 'DrinksName', 'Unit', 'DrinksQty', 'IsDelete', 'DeleteUserName', 'Val1', 'Val2', 'Val3', 'Val4', 'Val5', 'BrandId', 'GiveNum']

def get_workdate(dt):
    if dt is None: return None
    # A day is from 9 AM to 8:59 AM the next day
    if dt.hour < 9:
        return (dt - datetime.timedelta(days=1)).date()
    return dt.date()

def main():
    print("Starting daily transaction sync process...")
    sql_conn = None
    mysql_conn = None
    try:
        print("Connecting to MySQL...")
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        print("Connecting to SQL Server...")
        sql_conn = pyodbc.connect(SQL_SERVER_CONN_STR)
        sql_cursor = sql_conn.cursor()

        date_limit = datetime.date.today() - datetime.timedelta(days=DAYS_TO_SYNC)
        print(f"Fetching records since {date_limit}...")

        # 1. Process msginfo
        mysql_cursor.execute("SELECT * FROM msginfo WHERE DeDatetime >= %s", (date_limit,))
        msginfo_rows = mysql_cursor.fetchall()
        print(f"Found {len(msginfo_rows)} msginfo records to process.")
        
        msginfo_map = {}
        msginfo_to_upsert = []
        if msginfo_rows:
            for row in msginfo_rows:
                work_date = get_workdate(row['DeDatetime'])
                msginfo_map[row['iKeyMsg']] = {'WorkDate': work_date, 'ShopId': row['DeShopId']}
                msginfo_to_upsert.append([work_date] + [row.get(col) for col in MSGINFO_COLS[1:]])

            print("Merging data into dbo.msginfo...")
            sql_cursor.execute("SELECT TOP 0 * INTO #temp_msginfo FROM dbo.msginfo")
            sql_cursor.fast_executemany = True
            sql_cursor.executemany(f"INSERT INTO #temp_msginfo({', '.join(MSGINFO_COLS)}) VALUES ({', '.join(['?'] * len(MSGINFO_COLS))})", msginfo_to_upsert)
            
            merge_sql = f"""
            MERGE dbo.msginfo AS T
            USING #temp_msginfo AS S ON T.iKeyMsg = S.iKeyMsg
            WHEN MATCHED THEN UPDATE SET { ' , '.join([f'T.{col}=S.{col}' for col in MSGINFO_COLS if col != 'iKeyMsg']) }
            WHEN NOT MATCHED BY TARGET THEN INSERT ({ ', '.join(MSGINFO_COLS) }) VALUES ({ ', '.join([f'S.{col}' for col in MSGINFO_COLS]) });
            """
            sql_cursor.execute(merge_sql)
            print(f"{sql_cursor.rowcount} rows merged into msginfo.")
            sql_cursor.execute("DROP TABLE #temp_msginfo")

        # 2. Process drinksinfo
        if msginfo_map:
            keys = list(msginfo_map.keys())
            query_placeholders = ', '.join(['%s'] * len(keys))
            mysql_cursor.execute(f"SELECT * FROM drinksinfo WHERE iKeyMsg IN ({query_placeholders})", keys)
            drinksinfo_rows = mysql_cursor.fetchall()
            print(f"Found {len(drinksinfo_rows)} related drinksinfo records.")

            if drinksinfo_rows:
                drinksinfo_to_upsert = []
                for row in drinksinfo_rows:
                    parent_info = msginfo_map.get(row['iKeyMsg'])
                    if parent_info:
                        drinksinfo_to_upsert.append([parent_info['ShopId'], parent_info['WorkDate']] + [row.get(col) for col in DRINKSINFO_COLS[2:]])
                
                print("Merging data into dbo.drinksinfo...")
                sql_cursor.execute("SELECT TOP 0 * INTO #temp_drinksinfo FROM dbo.drinksinfo")
                sql_cursor.fast_executemany = True
                sql_cursor.executemany(f"INSERT INTO #temp_drinksinfo({ ', '.join(DRINKSINFO_COLS) }) VALUES ({', '.join(['?'] * len(DRINKSINFO_COLS))})", drinksinfo_to_upsert)

                merge_sql = f"""
                MERGE dbo.drinksinfo AS T
                USING #temp_drinksinfo AS S ON T.iKeyDrinks = S.iKeyDrinks
                WHEN MATCHED THEN UPDATE SET { ' , '.join([f'T.{col}=S.{col}' for col in DRINKSINFO_COLS if col != 'iKeyDrinks']) }
                WHEN NOT MATCHED BY TARGET THEN INSERT ({ ', '.join(DRINKSINFO_COLS) }) VALUES ({ ', '.join([f'S.{col}' for col in DRINKSINFO_COLS]) });
                """
                sql_cursor.execute(merge_sql)
                print(f"{sql_cursor.rowcount} rows merged into drinksinfo.")
                sql_cursor.execute("DROP TABLE #temp_drinksinfo")

        sql_conn.commit()
        print("\nDaily transaction sync process finished successfully.")

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        if sql_conn:
            sql_conn.rollback()
            print("Transaction rolled back.")
    finally:
        if mysql_conn and mysql_conn.is_connected():
            mysql_cursor.close()
            mysql_conn.close()
            print("MySQL connection closed.")
        if sql_conn:
            sql_cursor.close()
            sql_conn.close()
            print("SQL Server connection closed.")

if __name__ == "__main__":
    main()