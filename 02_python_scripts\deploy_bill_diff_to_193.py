import pyodbc

# --- 远程服务器配置 ---
SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

# --- SQL 定义 ---

# 1. 创建结果表的SQL
CREATE_TABLE_SQL = """
IF OBJECT_ID('BillAnalysisResults', 'U') IS NULL
BEGIN
    PRINT 'Table BillAnalysisResults does not exist. Creating it now...';
    CREATE TABLE BillAnalysisResults (
        ResultID INT IDENTITY(1,1) PRIMARY KEY,
        InvNo NVARCHAR(50) NOT NULL,
        RmNo NVARCHAR(50),
        RoomSystemAmount DECIMAL(18, 2),
        WxPayTotalAmount DECIMAL(18, 2),
        Difference DECIMAL(18, 2),
        TransactionIDs NVARCHAR(MAX),
        CheckoutTime DATETIME,
        CloseTime DATETIME,
        IsDifferenceNormal BIT,
        LogTime DATETIME DEFAULT GETDATE()
    );
    PRINT 'Table BillAnalysisResults created successfully.';
END
ELSE
BEGIN
    PRINT 'Table BillAnalysisResults already exists.';
END
"

# 2. 创建存储过程的SQL
CREATE_PROC_SQL = """
IF OBJECT_ID('usp_CalculateAndStoreBillDifferences', 'P') IS NOT NULL
    DROP PROCEDURE usp_CalculateAndStoreBillDifferences;
"

# 存储过程主体需要分开，因为它不能和DROP在同一个批处理中
PROC_BODY_SQL = """
CREATE PROCEDURE usp_CalculateAndStoreBillDifferences
AS
BEGIN
    SET NOCOUNT ON;

    WITH WxPayAggregated AS (
        SELECT
            p.InvNo,
            SUM(p.Tot) AS WxPayTotalForInv,
            STUFF((
                SELECT ', ' + w.transaction_id
                FROM wxpayinfo w
                WHERE w.InvNo = p.InvNo FOR XML PATH('')
            ), 1, 2, '') AS TransactionIDs
        FROM wxpayinfo p
        GROUP BY p.InvNo
    ),
    AllBillDetails AS (
        SELECT
            r.InvNo, r.RmNo, r.Tot AS RoomSystemAmount,
            w.WxPayTotalForInv AS WxPayTotalAmount,
            (w.WxPayTotalForInv - r.Tot) AS Difference,
            w.TransactionIDs,
            CONVERT(datetime, r.AccDate + ' ' + r.AccTime) AS CheckoutTime,
            r.CloseTime
        FROM ROOM r
        INNER JOIN WxPayAggregated w ON r.InvNo = w.InvNo
        WHERE r.RmStatus = 'A'
          AND DATEDIFF(minute, CONVERT(datetime, r.AccDate + ' ' + r.AccTime), GETDATE()) BETWEEN 0 AND 10
    )
    INSERT INTO BillAnalysisResults (
        InvNo, RmNo, RoomSystemAmount, WxPayTotalAmount, Difference,
        TransactionIDs, CheckoutTime, CloseTime, IsDifferenceNormal
    )
    SELECT
        InvNo, RmNo, RoomSystemAmount, WxPayTotalAmount, Difference,
        TransactionIDs, CheckoutTime, CloseTime,
        CASE WHEN Difference <= 0 THEN 1 ELSE 0 END AS IsDifferenceNormal
    FROM AllBillDetails;

    SELECT @@ROWCOUNT AS InsertedRows;
END;
"""

def deploy_to_193():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    print(f"--- 正在尝试直接连接到 {SERVER}/{DATABASE} ---")
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=20) as conn:
            cursor = conn.cursor()
            print("--- 连接成功！---")

            # 步骤 1: 创建结果表
            print("\n--- 步骤 1: 正在创建 BillAnalysisResults 表 (如果不存在)... ---")
            cursor.execute(CREATE_TABLE_SQL)

            # 步骤 2: 创建存储过程
            print("\n--- 步骤 2: 正在创建 usp_CalculateAndStoreBillDifferences 存储过程... ---")
            cursor.execute(CREATE_PROC_SQL) # 先执行DROP
            cursor.execute(PROC_BODY_SQL)   # 再执行CREATE
            print("--- 存储过程创建成功。 ---")

            print("\n部署完成！账单差异分析功能已成功部署到 193 服务器。")

    except Exception as e:
        print(f"\n--- 执行过程中发生错误 ---: {e}")

if __name__ == '__main__':
    deploy_to_193()
