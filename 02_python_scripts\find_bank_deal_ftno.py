
import pyodbc
import sys

# Connection details
CONN_193 = {
    'server': '193.112.2.229',
    'db': 'dbfood',
    'uid': 'sa',
    'pwd': 'Musicbox@123'
}

# Bank keywords to search for
BANK_KEYWORDS = ['广发', '中信', '银联']

def main():
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={CONN_193['server']};DATABASE={CONN_193['db']};UID={CONN_193['uid']};PWD={CONN_193['pwd']};TrustServerCertificate=yes;Connection Timeout=10;"
    
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("--- Finding Bank Deals in 'food' table by keywords ---")

        # Build the dynamic WHERE clause with placeholders
        where_clauses = ["FdCName LIKE ?" for _ in BANK_KEYWORDS]
        where_clause_str = ' OR '.join(where_clauses)
        query = f"SELECT DISTINCT FtNo, FdCName FROM food WHERE {where_clause_str} ORDER BY FtNo, FdCName;"
        
        # Build the parameters list
        params = [f'%{keyword}%' for keyword in BANK_KEYWORDS]

        cursor.execute(query, params)
        rows = cursor.fetchall()

        if rows:
            print(f"[+] Found {len(rows)} item(s) related to bank deals:")
            print(f"{'FtNo':<10} {'FdCName'}")
            print(f"{'-'*10:<10} {'-'*30}")
            for row in rows:
                print(f"{row.FtNo:<10} {row.FdCName}")
            
            # Check if FtNo is fixed
            unique_ftnos = {row.FtNo for row in rows}
            if len(unique_ftnos) == 1:
                print(f"\n[SUCCESS] As expected, all bank deals map to a single, fixed FtNo: {unique_ftnos.pop()}")
            else:
                print(f"\n[INFO] Bank deals map to multiple FtNos: {list(unique_ftnos)}")

        else:
            print("[-] No items found matching the bank keywords.")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
