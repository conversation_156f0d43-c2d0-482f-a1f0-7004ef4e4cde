import pyodbc
import datetime
import sys

# 设置标准输出编码为UTF-8
sys.stdout.reconfigure(encoding='utf-8')

# Database connection details for headquarters server
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=msdb;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# SQL query to find all job steps that reference rms2019 or data sync related terms
SQL_QUERY = """
WITH LastJobRun AS (
    SELECT
        job_id,
        run_status,
        run_date,
        run_time,
        ROW_NUMBER() OVER(PARTITION BY job_id ORDER BY run_date DESC, run_time DESC) as rn
    FROM
        dbo.sysjobhistory
    WHERE step_id = 0 -- Job outcome status
)
SELECT DISTINCT
    j.name AS job_name,
    s.step_id,
    s.step_name,
    s.command,
    CASE ljr.run_status
        WHEN 0 THEN 'Failed'
        WHEN 1 THEN 'Succeeded'
        WHEN 2 THEN 'Retry'
        WHEN 3 THEN 'Canceled'
        WHEN 4 THEN 'In progress'
        ELSE 'Unknown / Not run'
    END AS last_run_status,
    ljr.run_date,
    ljr.run_time
FROM
    dbo.sysjobs j
INNER JOIN
    dbo.sysjobsteps s ON j.job_id = s.job_id
LEFT JOIN
    LastJobRun ljr ON j.job_id = ljr.job_id AND ljr.rn = 1
WHERE
    (s.command LIKE '%rms2019%' 
     OR s.command LIKE '%RMS2019%'
     OR s.command LIKE '%Sync%'
     OR s.command LIKE '%sync%'
     OR s.command LIKE '%同步%'
     OR j.name LIKE '%rms2019%'
     OR j.name LIKE '%RMS2019%'
     OR j.name LIKE '%Sync%'
     OR j.name LIKE '%sync%'
     OR j.name LIKE '%同步%')
    AND j.enabled = 1
ORDER BY
    j.name, s.step_id;
"""

def format_datetime(date_int, time_int):
    """Converts SQL Server's date and time integers to a datetime object."""
    if date_int is None or time_int is None or date_int == 0:
        return None
    s_date = str(date_int)
    s_time = str(time_int).zfill(6)
    try:
        dt_str = f"{s_date[:4]}-{s_date[4:6]}-{s_date[6:]} {s_time[:2]}:{s_time[2:4]}:{s_time[4:]}"
        return datetime.datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
    except (ValueError, IndexError):
        return None

def find_data_sync_jobs():
    """Connects to msdb and finds jobs that are related to data synchronization."""
    conn = None
    try:
        print(f"正在连接到总部服务器的 'msdb' 数据库...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print(f"正在查找与数据同步相关的定时任务...")
        cursor.execute(SQL_QUERY)
        rows = cursor.fetchall()

        if not rows:
            print(f"\n查询完成。没有找到任何与数据同步相关的定时任务。\n")
            return

        print(f"\n查询到 {len(rows)} 个与数据同步相关的任务步骤：")
        print("-" * 100)

        for row in rows:
            last_run_dt = format_datetime(row.run_date, row.run_time)
            print(f"任务名称: {row.job_name}")
            print(f"步骤名称: {row.step_name}")
            print(f"最后运行状态: {row.last_run_status}")
            if last_run_dt:
                print(f"最后运行时间: {last_run_dt.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                print("最后运行时间: 从未运行")
            print(f"执行的命令: {row.command.strip()}")
            print("-" * 100)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。\n")

if __name__ == "__main__":
    find_data_sync_jobs()