
import pyodbc
import sys

# Set encoding for stdout
sys.stdout.reconfigure(encoding='utf-8')

# Connection details
server = '193.112.2.229'
database = 'rms2019'
username = 'sa'
password = 'Musicbox@123'

# SQL Query to find duplicate BillNo
# Using correct column names: Invno and ComeDate
sql_query = """
SELECT 
    Invno, 
    COUNT(*) AS DuplicateCount
FROM 
    openhistory
WHERE 
    ShopId = 9 
    AND ComeDate >= '2025-08-01' AND ComeDate <= '2025-08-31'
GROUP BY 
    Invno
HAVING 
    COUNT(*) > 1
ORDER BY
    DuplicateCount DESC, Invno;
"""

print(f"Connecting to {server}/{database}...")

try:
    # Establish connection
    cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'
    cnxn = pyodbc.connect(cnxn_str)
    cursor = cnxn.cursor()
    
    print("Executing query to find duplicate bill numbers...")
    cursor.execute(sql_query)
    
    rows = cursor.fetchall()
    
    if not rows:
        print("\n[Analysis Result]: No duplicate bill numbers found for ShopID=9 in the current month (August 2025).")
    else:
        print("\n[Analysis Result]: Found the following duplicate bill numbers:")
        print("----------------------------------------")
        print(f"{'Invno':<20} | {'DuplicateCount':<15}")
        print("----------------------------------------")
        for row in rows:
            print(f"{row.Invno:<20} | {row.DuplicateCount:<15}")
        print("----------------------------------------")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database Error! SQLSTATE: {sqlstate}")
    print(ex)
except Exception as e:
    print(f"An unexpected error occurred: {e}")

finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
        print("\nConnection closed.")
