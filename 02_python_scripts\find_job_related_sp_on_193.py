import pyodbc
import traceback

# Connection details for the remote server (193.112.2.229)
server = '193.112.2.229'
database = 'rms2019'  # Using rms2019 as this is likely the database we want to connect to
username = 'sa'
password = 'Musicbox@123'

# Connection string
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Connection Timeout=10;'

print(f"Attempting to connect to {server}...")
print(f"Connection String: {conn_str}")

try:
    # Establish the connection
    cnxn = pyodbc.connect(conn_str)
    print("Connection successful!")
    
    # Create a cursor
    cursor = cnxn.cursor()
    
    # Query to find stored procedures that contain 'job' in their name
    query = """
    SELECT 
        name, 
        create_date, 
        modify_date
    FROM 
        sys.procedures 
    WHERE 
        name LIKE '%job%' 
        OR name LIKE '%Job%'
        OR name LIKE '%JOB%'
    ORDER BY 
        name
    """
    
    print("\nSearching for stored procedures containing 'job' in their name...")
    cursor.execute(query)
    rows = cursor.fetchall()
    
    if rows:
        print(f"\nFound {len(rows)} stored procedures containing 'job' in their name:")
        print("-" * 80)
        for row in rows:
            print(f"Procedure Name: {row.name}")
            print(f"Created: {row.create_date}")
            print(f"Modified: {row.modify_date}")
            print("-" * 80)
    else:
        print("\nNo stored procedures containing 'job' found.")
        
    # Query to find stored procedures that might be related to synchronization
    sync_query = """
    SELECT 
        name, 
        create_date, 
        modify_date
    FROM 
        sys.procedures 
    WHERE 
        name LIKE '%sync%' 
        OR name LIKE '%Sync%'
        OR name LIKE '%SYNC%'
    ORDER BY 
        name
    """
    
    print("\nSearching for stored procedures containing 'sync' in their name...")
    cursor.execute(sync_query)
    sync_rows = cursor.fetchall()
    
    if sync_rows:
        print(f"\nFound {len(sync_rows)} stored procedures containing 'sync' in their name:")
        print("-" * 80)
        for row in sync_rows:
            print(f"Procedure Name: {row.name}")
            print(f"Created: {row.create_date}")
            print(f"Modified: {row.modify_date}")
            print("-" * 80)
    else:
        print("\nNo stored procedures containing 'sync' found.")
    
    # Close the connection
    cnxn.close()
    print("Connection closed.")
    
except pyodbc.Error as ex:
    print("Connection failed!")
    # Get the SQLSTATE and error message
    sqlstate = ex.args[0]
    print(f"SQLSTATE: {sqlstate}")
    print("Error details:")
    # Print the full traceback for detailed diagnostics
    traceback.print_exc()
    
except Exception as e:
    print("An unexpected error occurred.")
    traceback.print_exc()