
import pyodbc
import sys

# --- Config ---
SERVER = '192.168.2.5'
USER = 'sa'
PASS = 'Musicbox123'
DATABASES_TO_CHECK = ['operatedata', 'rms2019', 'mims', 'dbfood']

def find_table(table_name):
    print(f"--- Searching for table '{table_name}' on server {SERVER} ---")
    found_location = None
    for db in DATABASES_TO_CHECK:
        conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={db};UID={USER};PWD={PASS};TrustServerCertificate=yes;Connection Timeout=5;"
        try:
            # print(f"Checking database '{db}'...")
            conn = pyodbc.connect(conn_str, timeout=5)
            cursor = conn.cursor()
            if cursor.tables(table=table_name, tableType='TABLE').fetchone():
                print(f"[SUCCESS] Found '{table_name}' in database: '{db}'")
                found_location = db
                break # Stop after first match
            cursor.close()
            conn.close()
        except Exception:
            # print(f"Could not connect to db '{db}' or table not found.", file=sys.stderr)
            pass # Ignore databases we can't connect to or other errors
    
    if not found_location:
        print(f"[-] Table '{table_name}' not found in any of the specified databases.")

if __name__ == "__main__":
    # For this execution, we are looking for ShopInfo
    find_table('timeinfo')
