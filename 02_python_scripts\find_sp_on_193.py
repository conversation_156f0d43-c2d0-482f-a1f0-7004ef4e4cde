import pyodbc
import sys

# Connection details for the 193 server, dbfood database
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=193.112.2.229;DATABASE=dbfood;UID=sa;PWD=Musicbox@123;TrustServerCertificate=yes;Connection Timeout=10;'
SP_NAME = 'date_roomstatisticshourly'

try:
    print(f"Connecting to 193.112.2.229, database 'dbfood'...")
    conn = pyodbc.connect(CONN_STR)
    cursor = conn.cursor()
    print("Connection successful.")

    print(f"Searching for stored procedure: {SP_NAME}...")
    # Use sp_helptext for better formatting and to ensure we get the full definition
    query = "sp_helptext ?"
    cursor.execute(query, SP_NAME)
    rows = cursor.fetchall()

    if rows:
        print(f"--- Found stored procedure: {SP_NAME} ---")
        print("\n--- Definition ---")
        for row in rows:
            print(row[0], end='')
    else:
        # sp_helptext doesn't return empty rows for non-existent objects, it throws an error.
        # We can check the INFORMATION_SCHEMA as a fallback.
        cursor.execute("SELECT 1 FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_TYPE = 'PROCEDURE' AND ROUTINE_NAME = ?", SP_NAME)
        if not cursor.fetchone():
             print(f"Stored procedure '{SP_NAME}' not found in database 'dbfood'.")

    cursor.close()
    conn.close()

except pyodbc.ProgrammingError as e:
    # A common error if the object doesn't exist
    if 'The object' in str(e) and 'does not exist' in str(e):
        print(f"Stored procedure '{SP_NAME}' not found in database 'dbfood'.")
    else:
        print(f"A database error occurred: {e}", file=sys.stderr)
        sys.exit(1)
except Exception as e:
    print(f"An error occurred: {e}", file=sys.stderr)
    sys.exit(1)
