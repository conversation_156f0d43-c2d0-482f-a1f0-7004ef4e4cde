
import sys

file_path = '月度总表.csv'

try:
    # Open the file in binary mode to read the raw bytes
    with open(file_path, 'rb') as f:
        raw_bytes = f.read()
    
    # Decode the bytes using the suspected original encoding (GBK is a common one)
    # If this doesn't work, we could try 'gb2312'
    decoded_content = raw_bytes.decode('gb2312')
    
    # Print the correctly decoded content, ensuring stdout is configured for UTF-8
    sys.stdout.reconfigure(encoding='utf-8')
    print(decoded_content)

except FileNotFoundError:
    print(f"Error: The file '{file_path}' was not found.")
except UnicodeDecodeError as e:
    print(f"Failed to decode with GBK: {e}")
    print("The file may not be GBK encoded or is corrupted.")
except Exception as e:
    print(f"An error occurred: {e}")
