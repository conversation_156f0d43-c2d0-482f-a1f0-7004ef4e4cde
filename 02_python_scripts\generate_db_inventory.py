import pyodbc
import datetime

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
LOCAL_DATABASES = ['dbfood', 'mims', 'operatedata', 'rms2019']
REMOTE_SERVER = 'cloudRms2019'
REMOTE_DATABASE = 'rms2019'
OUTPUT_FILE = "C:/Users/<USER>/CascadeProjects/KTV_Data_Analysis/05_documentation/database_table_inventory.md"

def get_tables_from_db(cursor, db_name):
    """获取指定数据库中的所有用户表"""
    query = f"SELECT TABLE_SCHEMA, TABLE_NAME FROM {db_name}.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_SCHEMA, TABLE_NAME;"
    cursor.execute(query)
    return cursor.fetchall()

def get_tables_from_linked_server(cursor, linked_server, remote_db_name):
    """通过OPENQUERY获取链接服务器上的所有用户表"""
    query = f"""SELECT * FROM OPENQUERY({linked_server}, 
               'SELECT TABLE_SCHEMA, TABLE_NAME FROM {remote_db_name}.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = \'BASE TABLE\' ORDER BY TABLE_SCHEMA, TABLE_NAME;')"""
    cursor.execute(query)
    return cursor.fetchall()

def generate_inventory_report():
    report_content = []
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=30) as conn:
            cursor = conn.cursor()
            print(f'--- 成功连接到 {SERVER} ---')

            report_content.append(f"# 数据库表清单")
            report_content.append(f"*报告生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
")

            # 1. 获取本地数据库的表
            report_content.append(f"## 本地服务器: {SERVER}")
            for db in LOCAL_DATABASES:
                try:
                    print(f"正在查询本地数据库: {db}...")
                    report_content.append(f"\n### 数据库: `{db}`")
                    tables = get_tables_from_db(cursor, db)
                    if tables:
                        for row in tables:
                            report_content.append(f"- `{row.TABLE_SCHEMA}.{row.TABLE_NAME}`")
                    else:
                        report_content.append("- *未找到任何表。*")
                except Exception as e:
                    report_content.append(f"- *查询失败: {e}*")
            
            # 2. 获取远程数据库的表
            report_content.append(f"\n---\n## 远程服务器: 193.112.2.229 (通过链接服务器: {REMOTE_SERVER})")
            try:
                print(f"正在查询远程数据库: {REMOTE_DATABASE}...")
                report_content.append(f"\n### 数据库: `{REMOTE_DATABASE}`")
                remote_tables = get_tables_from_linked_server(cursor, REMOTE_SERVER, REMOTE_DATABASE)
                if remote_tables:
                    for row in remote_tables:
                        report_content.append(f"- `{row.TABLE_SCHEMA}.{row.TABLE_NAME}`")
                else:
                    report_content.append("- *未找到任何表。*")
            except Exception as e:
                report_content.append(f"- *查询失败: {e}*")

        # 3. 写入文件
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            f.write("\n".join(report_content))
        print(f"\n报告已成功生成: {OUTPUT_FILE}")

    except Exception as e:
        print(f"执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    generate_inventory_report()