
import pyodbc
import sys

# Connection details
CONN_193 = {
    'server': '193.112.2.229',
    'db': 'dbfood',
    'uid': 'sa',
    'pwd': 'Musicbox@123'
}
TABLE_NAME = 'food'

def main():
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={CONN_193['server']};DATABASE={CONN_193['db']};UID={CONN_193['uid']};PWD={CONN_193['pwd']};TrustServerCertificate=yes;Connection Timeout=10;"
    
    print(f"--- Getting schema for table '{TABLE_NAME}' from 193.dbfood ---")
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        query = """
        SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION;
        """
        cursor.execute(query, TABLE_NAME)
        rows = cursor.fetchall()

        if rows:
            print(f"Schema for '{TABLE_NAME}':")
            print(f"{'Column Name':<30} {'Data Type':<20} {'Max Length'}")
            print(f"{'-'*30:<30} {'-'*20:<20} {'-'*10}")
            for row in rows:
                length = str(row.CHARACTER_MAXIMUM_LENGTH) if row.CHARACTER_MAXIMUM_LENGTH is not None else 'N/A'
                print(f"{row.COLUMN_NAME:<30} {row.DATA_TYPE:<20} {length}")
        else:
            print(f"Table '{TABLE_NAME}' not found.")
            
        cursor.close()
        conn.close()
    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
