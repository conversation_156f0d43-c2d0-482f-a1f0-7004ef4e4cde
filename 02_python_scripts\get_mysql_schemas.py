
import mysql.connector
import sys

config = {
    'user': 'user_bar',
    'password': 'TJwe9JZB8YokL0O',
    'host': 'yy.tang-hui.com.cn',
    'port': 3306,
    'database': 'nwechat_bar',
    'raise_on_warnings': True,
    'connection_timeout': 10
}

tables_to_query = ['msginfo', 'drinksinfo', 'barfdtype', 'barfood']

try:
    print("Connecting to MySQL to retrieve table schemas...")
    cnx = mysql.connector.connect(**config)
    cursor = cnx.cursor()
    
    print("--- MySQL Table Schemas ---")
    for table_name in tables_to_query:
        print(f"\n-- Schema for table: {table_name} --")
        cursor.execute(f"SHOW CREATE TABLE {table_name}")
        result = cursor.fetchone()
        print(result[1]) # The CREATE TABLE statement is in the second column

    cursor.close()
    cnx.close()
    print("\n--- Successfully retrieved all schemas. ---")

except mysql.connector.Error as err:
    print(f"Error: {err}", file=sys.stderr)
    sys.exit(1)
