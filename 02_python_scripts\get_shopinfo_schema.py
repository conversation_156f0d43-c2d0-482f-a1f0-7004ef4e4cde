
import pyodbc
import sys

# Connection details
CONN_2_5 = {
    'server': '192.168.2.5',
    'db': 'mims', # Source database
    'uid': 'sa',
    'pwd': 'Musicbox123'
}
TABLE_NAME = 'ShopInfo'

def main():
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={CONN_2_5['server']};DATABASE={CONN_2_5['db']};UID={CONN_2_5['uid']};PWD={CONN_2_5['pwd']};TrustServerCertificate=yes;"
    
    print(f"--- Getting schema for table '{TABLE_NAME}' from 192.168.2.5:{CONN_2_5['db']} ---")
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        query = """
        SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION;
        """
        cursor.execute(query, TABLE_NAME)
        rows = cursor.fetchall()

        if rows:
            print(f"Schema for '{TABLE_NAME}':")
            print(f"{'Column Name':<30} {'Data Type':<20} {'Max Length'}")
            print(f"{'-'*30:<30} {'-'*20:<20} {'-'*10}")
            for row in rows:
                length = str(row.CHARACTER_MAXIMUM_LENGTH) if row.CHARACTER_MAXIMUM_LENGTH is not None else 'N/A'
                print(f"{row.COLUMN_NAME:<30} {row.DATA_TYPE:<20} {length}")
        else:
            print(f"Table '{TABLE_NAME}' not found in database '{CONN_2_5['db']}'.")
            
        cursor.close()
        conn.close()
    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
