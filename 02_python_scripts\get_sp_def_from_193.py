import pyodbc

# --- 远程服务器配置 ---
SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'
PROC_NAME = 'th_rms2019'

def get_remote_sp_definition():
    # 注意：直连远程服务器需要防火墙和网络配置允许
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    print(f"--- 正在尝试直接连接到 {SERVER}/{DATABASE} ---")
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=20) as conn:
            cursor = conn.cursor()
            print(f"--- 连接成功！正在获取存储过程 [{PROC_NAME}] 的定义 ---")
            
            # 使用 sp_helptext 获取存储过程的文本
            sql_query = f"EXEC sp_helptext \'{PROC_NAME}\'"
            cursor.execute(sql_query)
            
            print("\n--- 存储过程定义如下: ---")
            definition = []
            for row in cursor.fetchall():
                definition.append(row.Text)
            
            print("".join(definition))

    except Exception as e:
        print(f"\n--- 执行过程中发生错误 ---: {e}")

if __name__ == '__main__':
    get_remote_sp_definition()
