import pyodbc
import traceback

# Connection details for the remote server (193.112.2.229)
server = '193.112.2.229'
database = 'rms2019'
username = 'sa'
password = 'Musicbox@123'

# Connection string
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Connection Timeout=10;'

print(f"Attempting to connect to {server}...")
print(f"Connection String: {conn_str}")

try:
    # Establish the connection
    cnxn = pyodbc.connect(conn_str)
    print("Connection successful!")
    
    # Create a cursor
    cursor = cnxn.cursor()
    
    # Get the definition of the O_Synchro stored procedure
    print("\nGetting definition of O_Synchro stored procedure...")
    cursor.execute("EXEC sp_helptext 'O_Synchro'")
    rows = cursor.fetchall()
    
    if rows:
        print("\nDefinition of O_Synchro:")
        print("=" * 80)
        for row in rows:
            print(row[0], end='')
        print("\n" + "=" * 80)
    else:
        print("No definition found for O_Synchro.")
        
    # Get the definition of the trigger_synchro_rms2019_room stored procedure
    print("\nGetting definition of trigger_synchro_rms2019_room stored procedure...")
    cursor.execute("EXEC sp_helptext 'trigger_synchro_rms2019_room'")
    rows = cursor.fetchall()
    
    if rows:
        print("\nDefinition of trigger_synchro_rms2019_room:")
        print("=" * 80)
        for row in rows:
            print(row[0], end='')
        print("\n" + "=" * 80)
    else:
        print("No definition found for trigger_synchro_rms2019_room.")
    
    # Close the connection
    cnxn.close()
    print("Connection closed.")
    
except pyodbc.Error as ex:
    print("Connection failed!")
    # Get the SQLSTATE and error message
    sqlstate = ex.args[0]
    print(f"SQLSTATE: {sqlstate}")
    print("Error details:")
    # Print the full traceback for detailed diagnostics
    traceback.print_exc()
    
except Exception as e:
    print("An unexpected error occurred.")
    traceback.print_exc()