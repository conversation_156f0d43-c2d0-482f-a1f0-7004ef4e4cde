
import pyodbc

# Connection details
server = '193.112.2.229'
database = 'dbfood'
username = 'sa'
password = 'Musicbox@123'
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Connection Timeout=10;'

sp_name = 'date_rmcloseinfo'

try:
    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    
    print(f"Fetching source code for stored procedure: {sp_name}\n")
    
    # Using sp_helptext to get the definition
    query = f"EXEC sp_helptext '{sp_name}'"
    
    cursor.execute(query)
    
    # Fetch and print results
    rows = cursor.fetchall()
    if rows:
        print("-- Stored Procedure Definition --")
        for row in rows:
            print(row[0], end='')
        print("\n-- End of Definition --")
    else:
        print(f"Stored procedure '{sp_name}' not found or you don't have permission to view its definition.")

    cnxn.close()

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database Error Occurred: SQLSTATE {sqlstate}")
    print(ex)
except Exception as e:
    print(f"An error occurred: {e}")
