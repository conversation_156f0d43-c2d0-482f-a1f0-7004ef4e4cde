import pyodbc
import traceback
import sys

# 设置标准输出编码为UTF-8
sys.stdout.reconfigure(encoding='utf-8')

# Connection details for the headquarters server
server = '192.168.2.5'
database = 'rms2019'  # We need to connect to rms2019 database to get the SP definitions
username = 'sa'
password = 'Musicbox123'

# Connection string
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Connection Timeout=10;'

print(f"Attempting to connect to {server}...")
print(f"Connection String: {conn_str}")

try:
    # Establish the connection
    cnxn = pyodbc.connect(conn_str)
    print("Connection successful!")
    
    # Create a cursor
    cursor = cnxn.cursor()
    
    # Get the definition of the usp_Sync_RMS_DailyOpenData stored procedure
    print("\nGetting definition of usp_Sync_RMS_DailyOpenData stored procedure...")
    try:
        cursor.execute("EXEC sp_helptext 'usp_Sync_RMS_DailyOpenData'")
        rows = cursor.fetchall()
        
        if rows:
            print("\nDefinition of usp_Sync_RMS_DailyOpenData:")
            print("=" * 80)
            for row in rows:
                print(row[0], end='')
            print("\n" + "=" * 80)
        else:
            print("No definition found for usp_Sync_RMS_DailyOpenData.")
    except Exception as e:
        print(f"Error getting definition of usp_Sync_RMS_DailyOpenData: {e}")
        
    # Get the definition of the usp_Sync_RMS_DailyBookData stored procedure
    print("\nGetting definition of usp_Sync_RMS_DailyBookData stored procedure...")
    try:
        cursor.execute("EXEC sp_helptext 'usp_Sync_RMS_DailyBookData'")
        rows = cursor.fetchall()
        
        if rows:
            print("\nDefinition of usp_Sync_RMS_DailyBookData:")
            print("=" * 80)
            for row in rows:
                print(row[0], end='')
            print("\n" + "=" * 80)
        else:
            print("No definition found for usp_Sync_RMS_DailyBookData.")
    except Exception as e:
        print(f"Error getting definition of usp_Sync_RMS_DailyBookData: {e}")
    
    # Close the connection
    cnxn.close()
    print("Connection closed.")
    
except pyodbc.Error as ex:
    print("Connection failed!")
    # Get the SQLSTATE and error message
    sqlstate = ex.args[0]
    print(f"SQLSTATE: {sqlstate}")
    print("Error details:")
    # Print the full traceback for detailed diagnostics
    traceback.print_exc()
    
except Exception as e:
    print("An unexpected error occurred.")
    traceback.print_exc()