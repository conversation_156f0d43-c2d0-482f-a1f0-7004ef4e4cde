import pyodbc
import sys

sys.stdout.reconfigure(encoding='utf-8')

# Connection details
server = '193.112.2.229'
database = 'rms2019'
username = 'sa'
password = 'Musicbox@123'
table_name = 'openhistory'

# SQL Query to get table schema
sql_query = f"""
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    CHARACTER_MAXIMUM_LENGTH
FROM 
    INFORMATION_SCHEMA.COLUMNS
WHERE 
    TABLE_NAME = '{table_name}'
ORDER BY
    ORDINAL_POSITION;
"""

print(f"Connecting to {server}/{database} to get schema for table '{table_name}'...")

try:
    cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'
    cnxn = pyodbc.connect(cnxn_str)
    cursor = cnxn.cursor()
    
    cursor.execute(sql_query)
    rows = cursor.fetchall()
    
    if not rows:
        print(f"Could not find schema for table '{table_name}'. Please check if the table exists.")
    else:
        print(f"\n--- Schema for table: {table_name} ---")
        print("----------------------------------------------------------")
        print(f"{ 'Column Name':<30} | { 'Data Type':<15} | { 'Max Length':<10}")
        print("----------------------------------------------------------")
        for row in rows:
            max_len = str(row.CHARACTER_MAXIMUM_LENGTH) if row.CHARACTER_MAXIMUM_LENGTH is not None else 'N/A'
            print(f"{row.COLUMN_NAME:<30} | {row.DATA_TYPE:<15} | {max_len:<10}")
        print("----------------------------------------------------------")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database Error! SQLSTATE: {sqlstate}")
    print(ex)
except Exception as e:
    print(f"An unexpected error occurred: {e}")

finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
        print("\nConnection closed.")