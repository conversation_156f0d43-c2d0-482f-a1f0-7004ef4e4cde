
import pyodbc

# --- Configuration ---
DB_SERVER = '192.168.2.5'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'
TABLE_NAME = 'Dim_Bank_Deal'

def get_table_schema():
    """Connects to the database and retrieves the schema for a specific table."""
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'
    
    cnxn = None
    try:
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print(f"数据库连接成功，正在获取 '{TABLE_NAME}' 的表结构...\n")
        
        columns = cursor.columns(table=TABLE_NAME)
        
        print(f"{'COLUMN_NAME':<20} {'TYPE_NAME':<15} {'COLUMN_SIZE':<12} {'IS_NULLABLE':<12}")
        print("-" * 60)
        
        for column in columns:
            print(f"{column.column_name:<20} {column.type_name:<15} {column.column_size:<12} {column.is_nullable:<12}")
            
    except pyodbc.Error as ex:
        print(f"数据库操作出错: {ex}")
    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    get_table_schema()
