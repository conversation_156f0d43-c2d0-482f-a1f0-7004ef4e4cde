
import pyodbc
import sys

# Connection details
CONN_193 = {
    'name': '193.112.2.229',
    'server': '192.168.2.5',
    'db': 'dbfood',
    'uid': 'sa',
    'pwd': 'Musicbox@123'
}
CONN_2_5 = {
    'name': '192.168.2.5',
    'server': '192.168.2.5',
    'db': 'operatedata', # Starting guess
    'uid': 'sa',
    'pwd': 'Musicbox123'
}
TABLE_NAME = 'RoomStatisticsHourly'

def get_table_schema(conn_details, table_name):
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={conn_details['server']};DATABASE={conn_details['db']};UID={conn_details['uid']};PWD={conn_details['pwd']};TrustServerCertificate=yes;Connection Timeout=10;"
    
    print(f"\n--- Checking for table '{table_name}' on server {conn_details['name']} in database '{conn_details['db']}' ---")
    
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        query = """
        SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION;
        """
        cursor.execute(query, table_name)
        rows = cursor.fetchall()

        if rows:
            print(f"Schema for '{table_name}':")
            print(f"{ 'Column Name':<30} { 'Data Type':<20} {'Max Length'}")
            print(f"{'-'*30:<30} {'-'*20:<20} {'-'*10}")
            for row in rows:
                length = str(row.CHARACTER_MAXIMUM_LENGTH) if row.CHARACTER_MAXIMUM_LENGTH is not None else 'N/A'
                print(f"{row.COLUMN_NAME:<30} {row.DATA_TYPE:<20} {length}")
        else:
            print(f"Table '{table_name}' not found.")
            
        cursor.close()
        conn.close()
    except Exception as e:
        print(f"An error occurred while connecting to {conn_details['name']}: {e}", file=sys.stderr)

def main():
    get_table_schema(CONN_193, TABLE_NAME)
    get_table_schema(CONN_2_5, TABLE_NAME)

if __name__ == "__main__":
    main()
