
import pyodbc
import csv
import sys
import io

# Connection details
SQL_SERVER_CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
TARGET_TABLE = 'Dim_Bank_Deal'

# CSV data from context
CSV_CONTENT = """
银行名称,团购名称,团购金额,补贴金额,总金额,服务费,实收金额
广发银行,广发-堂会70元单人K+自助餐券,70,20,90,0,90
,广发-堂会90元单人K+自助餐券,90,0,90,0,90
,广发-名堂123元单人k+海鲜自助券,123,35,158,0,158
,广发-名堂158元单人K+海鲜自助餐券,158,0,158,0,158
中信银行,中信-堂会45元单人K+自助餐券,45,45,90,0,90
,中信-堂会68元单人K+自助餐券,68,22,90,0,90
,中信-堂会70元单人K+自助餐券,70,20,90,0,90
,中信-堂会81元单人K+自助餐券,81,9,90,0,90
,中信-堂会87元单人K+自助餐券,87,3,90,0,90
,中信-堂会90元单人K+自助餐券,90,0,90,0,90
,中信-堂会90元单人K+自助餐券（新用户专享）,0,90,90,0,90
广日银联,广日银联-堂会70元单人K+自助餐券,70,20,90,5,85
,广日银联-堂会150 元双人 K+自助餐券,150,30,180,5,175
,广日银联-堂会155 元双人 K+自助餐券,155,25,180,5,175
,广日银联-堂会160 元双人 K+自助餐券,160,20,180,5,175
,广日银联-堂会162 元双人 K+自助餐券,162,18,180,5,175
,广日银联-堂会180 元双人 K+自助餐券,180,0,180,5,175
,广日银联-名堂128 元单人 K+海鲜自助餐券,128,30,158,5,153
,广日银联-名堂138 元单人 K+海鲜自助餐券,138,20,158,5,153
,广日银联-名堂143 元单人 K+海鲜自助餐券,143,15,158,5,153
,广日银联-名堂158 元单人 K+海鲜自助餐券,158,0,158,5,153
"""

def main():
    print(f"Preparing to import data into {TARGET_TABLE}...")
    
    # Parse CSV data, handling the forward-filling of BankName
    csvfile = io.StringIO(CSV_CONTENT)
    reader = csv.reader(csvfile)
    header = next(reader)
    data_to_insert = []
    current_bank_name = ""
    for row in reader:
        try:
            if not row or not row[1]: continue # Skip empty rows
            
            bank_name = row[0] if row[0] else current_bank_name
            current_bank_name = bank_name
            
            # Append data, converting numeric fields to float
            data_to_insert.append([
                bank_name,
                row[1],
                float(row[2]),
                float(row[3]),
                float(row[4]),
                float(row[5]),
                float(row[6])
            ])
        except ValueError:
            print(f"Skipping malformed row that cannot be converted to numbers: {row}", file=sys.stderr)
            continue

    print(f"Parsed {len(data_to_insert)} rows from CSV data.")

    try:
        conn = pyodbc.connect(SQL_SERVER_CONN_STR)
        cursor = conn.cursor()

        print(f"Truncating table {TARGET_TABLE} before import...")
        cursor.execute(f"TRUNCATE TABLE {TARGET_TABLE}")

        print(f"Bulk inserting data...")
        sql_insert = f"""INSERT INTO {TARGET_TABLE} (BankName, DealName, DealAmount, SubsidyAmount, TotalAmount, ServiceFee, NetAmount)
                         VALUES (?, ?, ?, ?, ?, ?, ?)"""
        
        cursor.fast_executemany = True
        cursor.executemany(sql_insert, data_to_insert)
        conn.commit()

        print(f"Successfully imported {cursor.rowcount} rows.")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
