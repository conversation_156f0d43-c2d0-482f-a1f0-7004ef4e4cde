
import json
import pyodbc
import os

def import_deals_from_json_v2():
    """
    Reads deal data from a JSON file and inserts/updates it into the Dim_Bank_Deal table.
    This version correctly handles the non-nullable NetAmount column.
    """
    # Use an absolute path for the JSON file for robustness
    json_file_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\无标题.json'
    
    # --- Database Connection Details ---
    server = '192.168.2.5'
    database = 'operatedata'
    username = 'sa'
    password = 'Musicbox123'
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'

    try:
        # Specify utf-8-sig to handle potential BOM (Byte Order Mark) in the file
        with open(json_file_path, 'r', encoding='utf-8-sig') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误: JSO<PERSON>文件未找到 at '{json_file_path}'")
        return
    except json.JSONDecodeError as e:
        print(f"错误: JSON文件格式无效 at '{json_file_path}'. Details: {e}")
        return

    cnxn = None  # Initialize cnxn to None
    try:
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("数据库连接成功。")

        upserted_count = 0
        
        for item in data:
            fd_no = item.get('FdNo')
            deal_name = item.get('FdCName')
            deal_amount = item.get('FdPrice2')

            if not all([fd_no, deal_name, deal_amount is not None]):
                print(f"跳过不完整的记录: {item}")
                continue

            # Check if the record already exists
            cursor.execute("SELECT COUNT(1) FROM Dim_Bank_Deal WHERE FdNo = ?", fd_no)
            exists = cursor.fetchone()[0] > 0

            if exists:
                # Update existing record
                print(f"更新记录 FdNo: {fd_no}...")
                cursor.execute("""
                    UPDATE Dim_Bank_Deal
                    SET DealName = ?, DealAmount = ?, NetAmount = ?
                    WHERE FdNo = ?
                """, deal_name, deal_amount, deal_amount, fd_no)
            else:
                # Insert new record
                print(f"插入新记录 FdNo: {fd_no}...")
                cursor.execute("""
                    INSERT INTO Dim_Bank_Deal (FdNo, DealName, DealAmount, NetAmount)
                    VALUES (?, ?, ?, ?)
                """, fd_no, deal_name, deal_amount, deal_amount)
            
            upserted_count += 1

        cnxn.commit()
        print(f"\n处理完成。总共插入或更新了 {upserted_count} 条记录。")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库错误: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"发生了预料之外的错误: {e}")
    finally:
        if cnxn:
            cnxn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    import_deals_from_json_v2()
