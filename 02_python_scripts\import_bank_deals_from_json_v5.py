
import json
import pyodbc
import os
from datetime import datetime

# --- Configuration ---
JSON_FILE_PATH = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\无标题.json'
DB_SERVER = '192.168.2.5'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'

# Fetched from Dim_Bank table
BANK_MAPPING = {
    '广发银行': 1,
    '中信银行': 2,
    '广日银联': 3,
    '广发': 1, # Alias
    '中信': 2, # <PERSON><PERSON>
}

def get_bank_sk(deal_name):
    """Parses the deal name to find a matching bank and return its BankSK."""
    if not deal_name:
        return None
    for bank_name, bank_sk in sorted(BANK_MAPPING.items(), key=lambda item: len(item[0]), reverse=True):
        if bank_name in deal_name:
            return bank_sk
    return None

def import_deals_from_json_v5():
    """
    Reads deal data from a JSON file and inserts/updates it into the Dim_Bank_Deal table.
    This version populates the CreateTime and UpdateTime columns.
    """
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'

    try:
        with open(JSON_FILE_PATH, 'r', encoding='utf-8-sig') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误: JSON文件未找到 at '{JSON_FILE_PATH}'")
        return
    except json.JSONDecodeError as e:
        print(f"错误: JSON文件格式无效 at '{JSON_FILE_PATH}'. Details: {e}")
        return

    cnxn = None
    try:
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("数据库连接成功。")

        upserted_count = 0
        skipped_count = 0
        
        for item in data:
            fd_no = item.get('FdNo')
            deal_name = item.get('FdCName')
            deal_amount = item.get('FdPrice2')

            if not all([fd_no, deal_name, deal_amount is not None]):
                print(f"信息不全，跳过记录: {item}")
                skipped_count += 1
                continue

            bank_sk = get_bank_sk(deal_name)
            if bank_sk is None:
                print(f"无法识别银行，跳过记录: {deal_name}")
                skipped_count += 1
                continue
            
            current_time = datetime.now()
            subsidy_amount = 0
            total_amount = deal_amount
            service_fee = 0
            net_amount = deal_amount

            cursor.execute("SELECT COUNT(1) FROM Dim_Bank_Deal WHERE FdNo = ?", fd_no)
            exists = cursor.fetchone()[0] > 0

            if exists:
                print(f"更新记录 FdNo: {fd_no}...")
                cursor.execute("""
                    UPDATE Dim_Bank_Deal
                    SET DealName = ?, DealAmount = ?, SubsidyAmount = ?, TotalAmount = ?, 
                        ServiceFee = ?, NetAmount = ?, BankSK = ?, UpdateTime = ?
                    WHERE FdNo = ?
                """, deal_name, deal_amount, subsidy_amount, total_amount, 
                     service_fee, net_amount, bank_sk, current_time, fd_no)
            else:
                print(f"插入新记录 FdNo: {fd_no}...")
                cursor.execute("""
                    INSERT INTO Dim_Bank_Deal 
                        (FdNo, DealName, DealAmount, SubsidyAmount, TotalAmount, ServiceFee, NetAmount, BankSK, CreateTime, UpdateTime)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, fd_no, deal_name, deal_amount, subsidy_amount, total_amount, service_fee, net_amount, bank_sk, current_time, current_time)
            
            upserted_count += 1

        cnxn.commit()
        print(f"\n处理完成。\n")
        print(f"成功插入或更新了 {upserted_count} 条记录。\n")
        print(f"跳过了 {skipped_count} 条记录。\n")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库错误: {sqlstate}")
        print(ex)
        cnxn.rollback()
    except Exception as e:
        print(f"发生了预料之外的错误: {e}")
    finally:
        if cnxn:
            cnxn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    import_deals_from_json_v5()
