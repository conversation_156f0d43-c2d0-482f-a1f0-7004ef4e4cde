
import pyodbc
import sys

# Connection details
CONN_2_5 = {
    'server': '***********',
    'db': 'operatedata',
    'uid': 'sa',
    'pwd': 'Musicbox123'
}
TABLE_NAME = 'RoomStatisticsHourly'

def run_query(cursor, query, description):
    print(f"\n--- {description} ---")
    try:
        cursor.execute(query)
        # Using a list to hold rows to allow multiple iterations if needed
        rows = list(cursor.fetchall())
        if not rows:
            print("Query returned no results.")
            return

        # Print header
        columns = [column[0] for column in cursor.description]
        print(" | ".join(f'{col:<20}' for col in columns))
        print("-" * (sum(23 for _ in columns)))

        # Print rows
        for row in rows[:20]: # Limit output to 20 rows
            print(" | ".join(f'{str(item):<20}' for item in row))
        if len(rows) > 20:
            print(f"... and {len(rows) - 20} more rows.")

    except Exception as e:
        print(f"Query failed: {e}", file=sys.stderr)

def main():
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={CONN_2_5['server']};DATABASE={CONN_2_5['db']};UID={CONN_2_5['uid']};PWD={CONN_2_5['pwd']};TrustServerCertificate=yes;"
    
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print(f"--- Investigating table '{TABLE_NAME}' on {CONN_2_5['server']} ---")

        # 1. Get sample data
        run_query(cursor, f"SELECT TOP 20 * FROM {TABLE_NAME} ORDER BY LogTime DESC", "Sample Data (Top 20 most recent)")

        # 2. Get distinct ShopIDs
        run_query(cursor, f"SELECT DISTINCT ShopID FROM {TABLE_NAME} ORDER BY ShopID", "Distinct ShopIDs Present")

        # 3. Check for Primary Key
        pk_query = f"""
        SELECT C.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS T
        JOIN INFORMATION_SCHEMA.CONSTRAINT_COLUMN_USAGE C ON C.CONSTRAINT_NAME = T.CONSTRAINT_NAME
        WHERE C.TABLE_NAME = '{TABLE_NAME}' AND T.CONSTRAINT_TYPE = 'PRIMARY KEY';
        """
        run_query(cursor, pk_query, "Primary Key Columns")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
