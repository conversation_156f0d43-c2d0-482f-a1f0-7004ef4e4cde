# -*- coding: utf-8 -*-
import mysql.connector
import pyodbc
import sys

# --- Configuration ---
MYSQL_CONFIG = {
    'user': 'user_bar',
    'password': 'TJwe9JZB8YokL0O',
    'host': 'yy.tang-hui.com.cn',
    'port': 3306,
    'database': 'nwechat_bar',
}

SQL_SERVER_CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

TABLES_TO_SYNC = {
    # MySQL_table: SQL_Server_table
    'barfdtype': 'barfdtype',
    'barfood': 'barfood'
}

def sync_table(mysql_cursor, sql_server_conn, mysql_table, sql_server_table):
    """Fetches data from MySQL, truncates SQL Server table, and bulk inserts."""
    print(f"--- Starting sync for table: {mysql_table} -> {sql_server_table} ---")
    
    # 1. Fetch from MySQL
    print(f"Fetching data from MySQL table: {mysql_table}...")
    mysql_cursor.execute(f"SELECT * FROM {mysql_table}")
    rows = mysql_cursor.fetchall()
    if not rows:
        print(f"No data found in {mysql_table}. Skipping.")
        return
    print(f"Found {len(rows)} rows to sync.")

    # 2. Truncate SQL Server table
    sql_cursor = sql_server_conn.cursor()
    print(f"Truncating SQL Server table: {sql_server_table}...")
    sql_cursor.execute(f"TRUNCATE TABLE {sql_server_table}")
    
    # 3. Bulk insert into SQL Server
    print(f"Bulk inserting data into {sql_server_table}...")
    placeholders = ', '.join(['?'] * len(rows[0]))
    sql_insert_query = f"INSERT INTO {sql_server_table} VALUES ({placeholders})"

    sql_cursor.fast_executemany = True
    sql_cursor.executemany(sql_insert_query, rows)
    
    sql_server_conn.commit()
    sql_cursor.close()
    print(f"Sync for table {sql_server_table} completed successfully.")

def main():
    try:
        # Connect to databases
        print("Connecting to MySQL...")
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        mysql_cursor = mysql_conn.cursor()
        
        print("Connecting to SQL Server...")
        sql_server_conn = pyodbc.connect(SQL_SERVER_CONN_STR)

        # Sync each table
        for mysql_table, sql_server_table in TABLES_TO_SYNC.items():
            sync_table(mysql_cursor, sql_server_conn, mysql_table, sql_server_table)

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        # Close connections
        if 'mysql_conn' in locals() and mysql_conn.is_connected():
            mysql_cursor.close()
            mysql_conn.close()
            print("MySQL connection closed.")
        if 'sql_server_conn' in locals():
            sql_server_conn.close()
            print("SQL Server connection closed.")
    
    print("\nOne-time sync process finished.")

if __name__ == "__main__":
    main()
