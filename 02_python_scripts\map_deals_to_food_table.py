
import pyodbc
import csv
import sys
import io

# Connection details
CONN_193 = {
    'server': '193.112.2.229',
    'db': 'dbfood',
    'uid': 'sa',
    'pwd': 'Musicbox@123'
}

# CSV data from memory
CSV_CONTENT = """
银行名称,团购名称,团购金额,补贴金额,总金额,服务费,实收金额
广发银行,广发-堂会70元单人K+自助餐券,70,20,90,0,90
,广发-堂会90元单人K+自助餐券,90,0,90,0,90
,广发-名堂123元单人k+海鲜自助券,123,35,158,0,158
,广发-名堂158元单人K+海鲜自助餐券,158,0,158,0,158
中信银行,中信-堂会45元单人K+自助餐券,45,45,90,0,90
,中信-堂会68元单人K+自助餐券,68,22,90,0,90
,中信-堂会70元单人K+自助餐券,70,20,90,0,90
,中信-堂会81元单人K+自助餐券,81,9,90,0,90
,中信-堂会87元单人K+自助餐券,87,3,90,0,90
,中信-堂会90元单人K+自助餐券,90,0,90,0,90
,中信-堂会90元单人K+自助餐券（新用户专享）,0,90,90,0,90
广日银联,广日银联-堂会70元单人K+自助餐券,70,20,90,5,85
,广日银联-堂会150 元双人 K+自助餐券,150,30,180,5,175
,广日银联-堂会155 元双人 K+自助餐券,155,25,180,5,175
,广日银联-堂会160 元双人 K+自助餐券,160,20,180,5,175
,广日银联-堂会162 元双人 K+自助餐券,162,18,180,5,175
,广日银联-堂会180 元双人 K+自助餐券,180,0,180,5,175
,广日银联-名堂128 元单人 K+海鲜自助餐券,128,30,158,5,153
,广日银联-名堂138 元单人 K+海鲜自助餐券,138,20,158,5,153
,广日银联-名堂143 元单人 K+海鲜自助餐券,143,15,158,5,153
,广日银联-名堂158 元单人 K+海鲜自助餐券,158,0,158,5,153
"""

def extract_core_deal_name(full_name):
    """Extracts a more searchable part of the deal name."""
    if '-' in full_name:
        parts = full_name.split('-', 1)
        if len(parts) > 1:
            return parts[1]
    return full_name

def main():
    # Read deal names from CSV content
    csvfile = io.StringIO(CSV_CONTENT)
    reader = csv.reader(csvfile)
    next(reader) # Skip header
    deal_names = [row[1] for row in reader if row and row[1]]

    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={CONN_193['server']};DATABASE={CONN_193['db']};UID={CONN_193['uid']};PWD={CONN_193['pwd']};TrustServerCertificate=yes;Connection Timeout=10;"
    
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("--- Mapping Bank Deals to Food Table ---")

        for name in deal_names:
            core_name = extract_core_deal_name(name)
            print(f"\n[?] Searching for: '{name}' (using '{core_name}')")
            
            query = "SELECT FtNo, FdNo, FdCName, FdPrice1 FROM food WHERE FdCName LIKE ?"
            # Use a broader search term for better matching
            search_term = f'%{core_name}%'
            cursor.execute(query, search_term)
            rows = cursor.fetchall()

            if rows:
                print(f"[+] Found {len(rows)} match(es):")
                for row in rows:
                    print(f"  -> FtNo: {row.FtNo}, FdNo: {row.FdNo}, Name: {row.FdCName}, Price: {row.FdPrice1}")
            else:
                print("[-] No matches found.")

        cursor.close()
        conn.close()

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        if sqlstate == '28000':
            print(f"Authentication error for server {CONN_193['server']}. Please check credentials.", file=sys.stderr)
        else:
            print(f"Database Error: {ex}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
