
import pyodbc
import sys
import datetime

def main():
    # --- Configuration ---
    START_DATE = datetime.date(2020, 1, 1)
    END_DATE = datetime.date(2030, 12, 31)
    CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
    TARGET_TABLE = 'Dim_Date'
    WEEKDAY_MAP_ZH = {0: '星期一', 1: '星期二', 2: '星期三', 3: '星期四', 4: '星期五', 5: '星期六', 6: '星期日'}

    print(f"Generating date data from {START_DATE} to {END_DATE}...")
    
    date_data = []
    current_date = START_DATE
    while current_date <= END_DATE:
        date_sk = int(current_date.strftime('%Y%m%d'))
        is_weekend = 1 if current_date.weekday() >= 5 else 0
        
        # For simplicity, holiday data is not populated here.
        # This would typically require an external holiday calendar source.
        date_data.append([
            date_sk, 
            current_date, 
            current_date.year,
            current_date.month,
            current_date.day,
            current_date.weekday() + 1, # 1=Monday, 7=Sunday
            WEEKDAY_MAP_ZH[current_date.weekday()],
            is_weekend,
            0, # IsHoliday
            None # HolidayName
        ])
        current_date += datetime.timedelta(days=1)

    print(f"Generated {len(date_data)} rows of date dimension data.")

    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print(f"Truncating table {TARGET_TABLE}...")
        cursor.execute(f"TRUNCATE TABLE {TARGET_TABLE}")

        print(f"Bulk inserting data into {TARGET_TABLE}...")
        sql_insert = "INSERT INTO Dim_Date VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        
        cursor.fast_executemany = True
        cursor.executemany(sql_insert, date_data)
        conn.commit()

        print(f"Successfully populated {TARGET_TABLE} with {cursor.rowcount} rows.")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
