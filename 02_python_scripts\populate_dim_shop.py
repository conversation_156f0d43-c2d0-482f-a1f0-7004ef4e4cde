
import pyodbc
import sys
import datetime

# --- Config ---
SOURCE_CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=mims;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
TARGET_CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
SOURCE_TABLE = 'ShopInfo'
TARGET_TABLE = 'Dim_Shop'

def parse_date(date_str):
    """Safely parses date strings that might have time information or be in different formats."""
    if not date_str or not date_str.strip():
        return None
    try:
        # Take the first part of the string (in case it has time) and parse as YYYY-MM-DD
        return datetime.datetime.strptime(date_str.split()[0], '%Y-%m-%d').date()
    except (ValueError, TypeError):
        # Add other formats to try here if necessary
        return None

def main():
    print(f"Starting data sync from {SOURCE_TABLE} to {TARGET_TABLE}...")
    try:
        # 1. Read from source
        print(f"Reading data from {SOURCE_TABLE}...")
        source_conn = pyodbc.connect(SOURCE_CONN_STR)
        source_cursor = source_conn.cursor()
        source_cursor.execute(f"SELECT ShopID, ShopName, address, OpenDate, IsUse FROM {SOURCE_TABLE}")
        rows = source_cursor.fetchall()
        source_cursor.close()
        source_conn.close()
        print(f"Found {len(rows)} rows in source table.")

        # 2. Prepare data for upsert
        data_to_upsert = []
        for row in rows:
            open_date = parse_date(row.OpenDate)
            data_to_upsert.append([
                row.ShopID,
                row.ShopName,
                row.address,
                open_date,
                row.IsUse # IsActive
            ])

        # 3. Upsert into target using MERGE
        if not data_to_upsert:
            print("No data to sync.")
            return

        print(f"Connecting to target database to merge data into {TARGET_TABLE}...")
        target_conn = pyodbc.connect(TARGET_CONN_STR)
        target_cursor = target_conn.cursor()

        # For simplicity and to avoid MERGE complexity in pyodbc with many columns,
        # we will do a TRUNCATE and INSERT, which is acceptable for dimension tables.
        print("Truncating target table...")
        target_cursor.execute(f"TRUNCATE TABLE {TARGET_TABLE}")

        print("Inserting data...")
        insert_sql = f"INSERT INTO {TARGET_TABLE} (ShopID, ShopName, Address, OpenDate, IsActive) VALUES (?, ?, ?, ?, ?)"
        target_cursor.fast_executemany = True
        target_cursor.executemany(insert_sql, data_to_upsert)
        target_conn.commit()

        print(f"Successfully synced {target_cursor.rowcount} rows into {TARGET_TABLE}.")
        target_cursor.close()
        target_conn.close()

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
