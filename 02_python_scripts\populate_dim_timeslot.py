import pyodbc
import sys
import datetime

# --- Config ---
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
SOURCE_TABLE = 'timeinfo'
TARGET_TABLE = 'Dim_TimeSlot'

def parse_int_to_time(time_int):
    """Converts an integer like 1700 to a time object '17:00:00'."""
    if time_int is None:
        return None
    try:
        hour = time_int // 100
        minute = time_int % 100
        return datetime.time(hour, minute)
    except (ValueError, TypeError):
        return None

def get_time_mode(start_time):
    """Determines the time mode (1 for Day, 2 for Night) based on the start time."""
    if not isinstance(start_time, datetime.time):
        return 1 # Default to Day if time is invalid
    if start_time < datetime.time(18, 0):
        return 1 # Day
    else:
        return 2 # Night

def main():
    print(f"Starting data sync from {SOURCE_TABLE} to {TARGET_TABLE}...")
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        # 1. Read from source
        print(f"Reading data from {SOURCE_TABLE}...")
        cursor.execute(f"SELECT TimeNo, TimeName, BegTime, EndTime, IsSpecial FROM {SOURCE_TABLE}")
        rows = cursor.fetchall()
        print(f"Found {len(rows)} rows in source table.")

        # 2. Prepare data for insert
        data_to_insert = []
        for row in rows:
            start_time = parse_int_to_time(row.BegTime)
            end_time = parse_int_to_time(row.EndTime)
            time_mode = get_time_mode(start_time)
            
            data_to_insert.append([
                row.TimeNo,
                row.TimeName,
                start_time,
                end_time,
                row.IsSpecial,
                time_mode
            ])

        # 3. Truncate and Insert into target
        if not data_to_insert:
            print("No data to sync.")
            return

        print(f"Truncating target table {TARGET_TABLE}...")
        cursor.execute(f"TRUNCATE TABLE {TARGET_TABLE}")

        print("Inserting data...")
        insert_sql = f"INSERT INTO {TARGET_TABLE} (TimeSlotBusinessKey, TimeSlotName, StartTime, EndTime, IsSpecial, TimeMode) VALUES (?, ?, ?, ?, ?, ?)"
        cursor.fast_executemany = True
        cursor.executemany(insert_sql, data_to_insert)
        conn.commit()

        print(f"Successfully synced {cursor.rowcount} rows into {TARGET_TABLE}.")
        cursor.close()
        conn.close()

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()