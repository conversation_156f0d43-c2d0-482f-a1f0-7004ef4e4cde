import pyodbc
import sys
import datetime

# --- Config ---
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
TARGET_TABLE = 'Fact_Deal_Redemption'
DAYS_TO_PROCESS = 30 # Process data for the last 30 days for the initial run

def main():
    print(f"Starting ETL process for {TARGET_TABLE}...")
    
    # 1. Build the main aggregation query
    end_date = datetime.date.today()
    start_date = end_date - datetime.timedelta(days=DAYS_TO_PROCESS)
    
    # This query does all the hard work: joining sources, joining dimensions, grouping, and calculating measures.
    etl_query = """
    SELECT
        dt.DateSK,
        s.ShopSK,
        d.DealSK,
        SUM(f.FdQty) AS RedemptionCount,
        SUM(d.DealAmount * f.FdQty) AS RedemptionAmount,
        SUM(d.SubsidyAmount * f.FdQty) AS SubsidyAmount,
        SUM(d.ServiceFee * f.FdQty) AS PlatformFee, -- Assuming ServiceFee maps to PlatformFee
        SUM(d.NetAmount * f.FdQty) AS NetAmount -- Assuming NetAmount is per item
    FROM
        dbo.fdcashbak AS f
    JOIN
        dbo.rmcloseinfo AS r ON f.InvNo = r.InvNo
    JOIN
        dbo.Dim_Bank_Deal AS d ON f.FdNo = d.FdNo COLLATE Chinese_PRC_CI_AS
    JOIN
        dbo.Dim_Shop AS s ON f.ShopId = s.ShopID
    JOIN
        dbo.Dim_Date AS dt ON CONVERT(date, r.workdate) = dt.FullDate -- Assuming r.workdate is a datetime or date-like string
    WHERE
        d.FdNo IS NOT NULL
        AND CONVERT(date, r.workdate) BETWEEN ? AND ?
    GROUP BY
        dt.DateSK, s.ShopSK, d.DealSK
    """

    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        # 2. Extract aggregated data
        print(f"Extracting and transforming data from {start_date} to {end_date}...")
        cursor.execute(etl_query, start_date, end_date)
        data_to_load = cursor.fetchall()

        if not data_to_load:
            print("No source data found for the specified period.")
            return

        print(f"Extracted {len(data_to_load)} aggregated rows to load.")

        # 3. Load data into Fact table using MERGE
        print(f"Loading data into {TARGET_TABLE} using MERGE...")
        
        # For pyodbc, MERGE with a temp table is most robust
        # Create temp table
        cursor.execute(f"SELECT TOP 0 * INTO #temp_fact FROM {TARGET_TABLE}")

        # Bulk insert into temp table
        insert_temp_sql = "INSERT INTO #temp_fact (DateSK, ShopSK, DealSK, RedemptionCount, RedemptionAmount, SubsidyAmount, PlatformFee, NetAmount) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        
        cursor.executemany(insert_temp_sql, data_to_load)

        # Merge from temp table to actual table
        merge_sql = f"""
        MERGE {TARGET_TABLE} AS T
        USING #temp_fact AS S ON T.DateSK = S.DateSK AND T.ShopSK = S.ShopSK AND T.DealSK = S.DealSK
        WHEN MATCHED THEN
            UPDATE SET
                T.RedemptionCount = S.RedemptionCount,
                T.RedemptionAmount = S.RedemptionAmount,
                T.SubsidyAmount = S.SubsidyAmount,
                T.PlatformFee = S.PlatformFee,
                T.NetAmount = S.NetAmount
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (DateSK, ShopSK, DealSK, RedemptionCount, RedemptionAmount, SubsidyAmount, PlatformFee, NetAmount)
            VALUES (S.DateSK, S.ShopSK, S.DealSK, S.RedemptionCount, S.RedemptionAmount, S.SubsidyAmount, S.PlatformFee, S.NetAmount);
        """
        cursor.execute(merge_sql)
        conn.commit()

        print(f"Successfully merged {cursor.rowcount} rows into {TARGET_TABLE}.")

        # Clean up temp table
        cursor.execute("DROP TABLE #temp_fact")
        conn.commit()

        cursor.close()
        conn.close()
        print("\nETL process finished successfully!")

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
