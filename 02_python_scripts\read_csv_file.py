
import csv
import sys

# Set encoding to UTF-8 for stdout
sys.stdout.reconfigure(encoding='utf-8')

file_path = '月度总表.csv'

try:
    with open(file_path, mode='r', encoding='utf-8', errors='replace') as csvfile:
        # Read the content
        content = csvfile.read()
        # Print the content
        print(content)

except FileNotFoundError:
    print(f"Error: The file '{file_path}' was not found.")
except UnicodeDecodeError as e:
    print(f"UnicodeDecodeError: {e}")
    print("\nTrying with GBK encoding...")
    try:
        with open(file_path, mode='r', encoding='gbk', errors='replace') as csvfile:
            content = csvfile.read()
            print(content)
    except Exception as e_gbk:
        print(f"Failed to read with GBK as well: {e_gbk}")
except Exception as e:
    print(f"An error occurred: {e}")
