
import pandas as pd
import sys

# Set encoding to UTF-8
sys.stdout.reconfigure(encoding='utf-8')

file_path = '营业数据统计平台开发需求.xlsx'

try:
    # Load the Excel file
    xls = pd.ExcelFile(file_path)
    
    # Iterate through each sheet
    for sheet_name in xls.sheet_names:
        print(f"--- Sheet: {sheet_name} ---")
        df = pd.read_excel(xls, sheet_name=sheet_name)
        # Print dataframe as CSV to stdout, handling potential None values
        print(df.to_csv(index=False, na_rep='NULL'))
        print("\n")

except FileNotFoundError:
    print(f"Error: The file '{file_path}' was not found.")
except Exception as e:
    print(f"An error occurred: {e}")
