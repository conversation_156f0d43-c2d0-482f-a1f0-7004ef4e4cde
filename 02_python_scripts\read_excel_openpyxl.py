
import openpyxl
import sys

# Set encoding to UTF-8
sys.stdout.reconfigure(encoding='utf-8')

file_path = '营业数据统计平台开发需求.xlsx'

try:
    # Load the workbook
    workbook = openpyxl.load_workbook(file_path, data_only=True)
    
    # Iterate through each sheet
    for sheet_name in workbook.sheetnames:
        print(f"--- Sheet: {sheet_name} ---")
        sheet = workbook[sheet_name]
        # Iterate over all rows in the sheet
        for row in sheet.iter_rows():
            # Join cell values, converting None to an empty string
            print(",".join([str(cell.value) if cell.value is not None else '' for cell in row]))
        print("\n")

except FileNotFoundError:
    print(f"Error: The file '{file_path}' was not found.")
except Exception as e:
    print(f"An error occurred: {e}")
