import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- SQL 定义 ---
# 将整个SQL脚本作为一个字符串
CREATE_JOB_SQL = """
USE msdb;

DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'RMS_Daily_Data_Sync_to_HQ';

-- 1. 如果作业已存在，则先删除
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    EXEC dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
END

-- 2. 创建作业
EXEC dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @description = N'每日从门店服务器(193)同步开台和预订数据到总部服务器(2.5)的rms2019数据库。';

-- 3. 创建作业步骤
EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'Sync Open Data (opencacheinfo & openhistory)',
    @subsystem = N'TSQL',
    @command = N'EXEC rms2019.dbo.usp_Sync_RMS_DailyOpenData;',
    @database_name = N'rms2019',
    @on_success_action = 3;

EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'Sync Book Data (bookcacheinfo & bookhistory)',
    @subsystem = N'TSQL',
    @command = N'EXEC rms2019.dbo.usp_Sync_RMS_DailyBookData;',
    @database_name = N'rms2019',
    @on_success_action = 1;

-- 4. 创建调度
EXEC dbo.sp_add_schedule
    @schedule_name = N'Daily_0600_For_RMS_Sync',
    @freq_type = 4,
    @freq_interval = 1,
    @active_start_time = 60000;

-- 5. 将调度附加到作业
EXEC dbo.sp_attach_schedule
    @job_name = @jobName,
    @schedule_name = N'Daily_0600_For_RMS_Sync';

-- 6. 指定作业的目标服务器
EXEC dbo.sp_add_jobserver
    @job_name = @jobName,
    @server_name = N'(local)';
"""

def recreate_sql_agent_job():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    print(f"--- 正在连接到服务器 {SERVER} ---")
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print("--- 连接成功！---")

            print("\n--- 正在重新创建 SQL Server 代理作业... ---")
            # 因为pyodbc不支持GO，所以我们将SQL脚本作为一个整体执行
            cursor.execute(CREATE_JOB_SQL)
            print("--- 作业 'RMS_Daily_Data_Sync_to_HQ' 已成功创建。 ---")

    except Exception as e:
        print(f"\n--- 执行过程中发生错误 ---: {e}")

if __name__ == '__main__':
    recreate_sql_agent_job()
