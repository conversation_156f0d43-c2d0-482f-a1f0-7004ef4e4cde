
import pyodbc

def query_dim_bank():
    server = '192.168.2.5'
    database = 'operatedata'
    username = 'sa'
    password = 'Musicbox123'
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'
    
    cnxn = None
    try:
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("数据库连接成功。")
        
        cursor.execute("SELECT BankSK, BankName FROM Dim_Bank")
        rows = cursor.fetchall()
        
        if not rows:
            print("Dim_Bank 表中没有数据。")
        else:
            print("Dim_Bank 表内容:")
            for row in rows:
                print(f"BankSK: {row.BankSK}, BankName: {row.BankName}")
                
    except pyodbc.Error as ex:
        print(f"数据库查询出错: {ex}")
    finally:
        if cnxn:
            cnxn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    query_dim_bank()
