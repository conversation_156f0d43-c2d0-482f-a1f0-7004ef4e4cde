
import pyodbc
import traceback

# Connection details for the remote server
server = '193.112.2.229'
database = 'dbfood'  # Using dbfood as an example, can be changed
username = 'sa'
password = 'Musicbox@123'

# Connection string
# Using a common driver, adjust if a specific one is needed.
# Added TrustServerCertificate=yes to handle potential certificate issues.
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Connection Timeout=10;'

print(f"Attempting to connect to {server}...")
print(f"Connection String: {conn_str}")

try:
    # Establish the connection
    cnxn = pyodbc.connect(conn_str)
    print("Connection successful!")
    
    # You can perform a simple query here if needed
    # cursor = cnxn.cursor()
    # cursor.execute("SELECT @@VERSION;")
    # row = cursor.fetchone()
    # if row:
    #     print(f"SQL Server Version: {row[0]}")
    
    # Close the connection
    cnxn.close()
    print("Connection closed.")

except pyodbc.Error as ex:
    print("Connection failed!")
    # Get the SQLSTATE and error message
    sqlstate = ex.args[0]
    print(f"SQLSTATE: {sqlstate}")
    print("Error details:")
    # Print the full traceback for detailed diagnostics
    traceback.print_exc()

except Exception as e:
    print("An unexpected error occurred.")
    traceback.print_exc()
