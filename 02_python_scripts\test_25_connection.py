import pyodbc
import traceback

# Connection details for the HQ server (2.5)
server = '192.168.2.5'
database = 'msdb'  # 使用msdb数据库来查找job相关的存储过程
username = 'sa'
password = 'Musicbox123'

# Connection string
# 使用通用驱动程序，如需要可调整
# 添加TrustServerCertificate=yes以处理证书问题
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Connection Timeout=10;'

print(f"正在连接到 {server}...")
print(f"连接字符串: {conn_str}")

try:
    # 建立连接
    cnxn = pyodbc.connect(conn_str)
    print("连接成功!")
    
    # 执行查找job相关存储过程的查询
    cursor = cnxn.cursor()
    # 查找job相关存储过程
    cursor.execute("""
        SELECT 
            name AS procedure_name,
            type AS type_code,
            type_desc AS type_description,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%job%' OR name LIKE '%Job%' OR name LIKE '%JOB%'
        OR name LIKE '%agent%' OR name LIKE '%Agent%' OR name LIKE '%AGENT%'
        OR name LIKE '%schedule%' OR name LIKE '%Schedule%' OR name LIKE '%SCHEDULE%'
        ORDER BY name
    """)
    
    procedures = cursor.fetchall()
    print(f"总共查询到 {len(procedures)} 个符合条件的存储过程")
    
    # 检查作业表的行数
    cursor.execute("SELECT COUNT(*) FROM msdb.dbo.sysjobs")
    job_count = cursor.fetchone()[0]
    print(f"总共查询到 {job_count} 个SQL Agent作业")
    
    print("\n找到的job相关存储过程:")
    print("=" * 80)
    if procedures:
        for row in procedures:
            print(f"存储过程名称: {row.procedure_name}")
            print(f"类型代码: {row.type_code} ({row.type_description})")
            print(f"创建时间: {row.create_date}")
            print(f"修改时间: {row.modify_date}")
            print("-" * 80)
    else:
        print("没有找到包含job的存储过程")

    # 查找所有SQL Agent作业
    print("\n查找所有SQL Agent作业:")
    cursor.execute("""
        SELECT j.name AS job_name, 
               enabled, 
               date_modified,
               j.description
        FROM msdb.dbo.sysjobs j
        ORDER BY j.name
    """)
    
    jobs = cursor.fetchall()
    if jobs:
        for row in jobs:
            print(f"作业名称: {row.job_name}")
            print(f"状态: {'启用' if row.enabled == 1 else '禁用'}")
            print(f"修改时间: {row.date_modified}")
            print(f"描述: {row.description}")
            print("-" * 80)
    else:
        print("没有找到作业")
    
    # 关闭连接
    cnxn.close()
    print("连接已关闭。")

except pyodbc.Error as ex:
    print("连接失败!")
    # 获取SQLSTATE和错误消息
    sqlstate = ex.args[0]
    print(f"SQLSTATE: {sqlstate}")
    print("错误详情:")
    # 打印完整回溯进行详细诊断
    traceback.print_exc()

except Exception as e:
    print("发生意外错误。")
    traceback.print_exc()