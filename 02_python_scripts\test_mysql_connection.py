
import mysql.connector
import sys

config = {
    'user': 'user_bar',
    'password': 'TJwe9JZB8YokL0O',
    'host': 'yy.tang-hui.com.cn',
    'port': 3306,
    'database': 'nwechat_bar',
    'raise_on_warnings': True,
    'connection_timeout': 10
}

try:
    print("Attempting to connect to MySQL database at yy.tang-hui.com.cn...")
    cnx = mysql.connector.connect(**config)
    print("MySQL Connection successful!")
    cnx.close()
    print("Connection closed.")
except mysql.connector.Error as err:
    print(f"Error: {err}", file=sys.stderr)
    sys.exit(1)
