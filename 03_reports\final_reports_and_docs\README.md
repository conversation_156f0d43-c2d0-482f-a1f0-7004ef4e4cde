# KTV数据分析系统

## 项目概述

这是一个专为KTV门店设计的营业数据分析系统，能够分析开台、结账数据，实现时间段重叠检测和直落客户识别，解决KTV行业特有的业务分析需求。

## 核心功能

### 1. 时间段重叠检测
- **业务逻辑**: 检测客人的实际消费时段与业务时间档的重叠情况
- **重叠规则**: 只要客人的消费时段与某个业务时间档有任何时间交集，就计入该时间档
- **应用场景**: 一个消费时间较长的客人可能被计入多个时间档

### 2. 直落客户识别
- **真直落**: 客人从上一个时间档延续到当前时间档的正常消费
- **补时识别**: 区分真直落和补时消费（结账时间超档但实际是补时）
- **分析维度**: 统计每个时间档的直落客户数量和类型

### 3. 渠道分析
- **K+**: 默认渠道
- **美团**: 美团平台来源
- **抖音**: 抖音平台来源  
- **特权预约**: 特权预约渠道
- **自动识别**: 根据支付信息自动识别渠道

### 4. 营业报表生成
- 类似Excel的表格格式
- 按时间段统计订单数、营业额、直落数
- 按渠道分类统计
- 生成业务洞察和建议

## 数据库配置

### rms2019数据库 (开台数据)
- **服务器**: 193.112.2.229
- **用户名**: sa
- **密码**: Musicbox@123
- **数据库**: rms2019
- **主要表**: 
  - `opencacheinfo`: 开台数据
  - `timeinfo`: 时间段配置
  - `shoptimeinfo`: 门店时间段关联

### operatedata数据库 (结账数据)
- **服务器**: 192.168.2.5
- **用户名**: sa
- **密码**: Musicbox123
- **数据库**: operatedata
- **主要表**: 
  - `rmcloseinfo`: 结账数据

## 文件说明

### 核心分析脚本
1. **`final_analysis.py`** - 最终完整分析脚本
   - 实现完整的业务逻辑
   - 包含直落类型识别
   - 生成详细的业务报表

2. **`business_analysis.py`** - 业务分析脚本
   - 基础的时间段重叠分析
   - 渠道统计功能

3. **`simple_analysis.py`** - 简化分析脚本
   - 基本的数据统计
   - 适合快速查看数据概况

### 辅助脚本
4. **`debug_connection.py`** - 数据库连接调试
5. **`ktv_data_analysis.py`** - 原始分析脚本

### 配置文件
6. **`requirements.txt`** - Python依赖包列表

## 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行分析
```bash
# 运行完整分析
python final_analysis.py

# 运行简化分析
python simple_analysis.py
```

### 3. 查看结果
- 控制台输出: 实时分析结果
- JSON文件: 详细数据保存
- Excel文件: 报表格式（如果生成）

## 分析结果示例

```
============================================================
KTV业务数据分析 - 20250717
门店: 名堂店 (ID: 11)
============================================================

数据概览:
  结账记录: 113 条
  时间段配置: 10 个
  总营业额: ¥87,579

【11:50-14:50 (11:50-14:50)】
  总订单数: 21
  总营业额: ¥4,047
  直落分析:
    真直落: 7 单
    可能补时: 0 单
    正常消费: 14 单
  渠道分布:
    K+: 21单, ¥4,047
```

## 业务洞察

### 直落分析
- **真直落**: 客人从上一档延续过来的正常消费
- **可能补时**: 结账时间远超时间段结束的消费
- **正常消费**: 在时间段内开始和结束的消费

### 渠道优化建议
- 监控各渠道的转化率和客单价
- 针对性推广低占比但高价值的渠道
- 优化K+渠道的服务体验

## 技术特点

1. **智能时间匹配**: 自动处理跨天时间段
2. **灵活渠道识别**: 基于支付信息自动分类
3. **直落类型区分**: 区分真直落和补时消费
4. **多格式输出**: 支持控制台、JSON、Excel等格式

## 注意事项

1. **数据准确性**: 开台和结账数据的匹配基于时间估算，实际业务中建议通过房间号等精确匹配
2. **时间段配置**: 确保时间段配置正确，特别是跨天时间段
3. **补时识别**: 当前补时识别基于时间规则，可根据实际业务调整算法
4. **网络连接**: 确保能够访问数据库服务器

## 扩展功能

- [ ] 房间利用率分析
- [ ] 客户消费习惯分析
- [ ] 收入预测模型
- [ ] 实时数据监控
- [ ] 移动端报表查看

## 联系方式

如有问题或需要定制化功能，请联系开发团队。
