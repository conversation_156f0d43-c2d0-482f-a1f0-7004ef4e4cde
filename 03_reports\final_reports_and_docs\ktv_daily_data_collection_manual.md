# KTV每日数据收集系统使用说明

## 系统概述

KTV每日数据收集系统是一个完整的手动执行方案，允许您按需获取指定门店和日期的营业数据，并自动存储到KTV_DailyReport_Comprehensive综合数据表中。

## 核心功能

### ✅ 主要特性
- **门店名称识别**：支持通过门店名称（如"名堂店"）自动获取ShopId
- **日期格式验证**：严格的日期格式验证（YYYYMMDD）
- **数据完整性**：自动收集总览数据、时段数据和上一档直落数据
- **重复执行支持**：如果数据已存在则自动更新
- **错误处理**：完善的参数验证和错误反馈
- **执行状态反馈**：详细的执行过程和结果信息

### 📊 数据收集范围
- **营收指标**：总收入、白天档收入、晚上档收入
- **客户指标**：全天总批数、白天档批次、晚上档批次
- **餐饮指标**：总人数、自助餐人数
- **时段数据**：各时间段的详细统计（K+、特权预约、美团、抖音等）
- **直落数据**：上一档直落客户统计

## 数据库配置

### 连接信息
- **服务器**：192.168.2.5
- **数据库**：OperateData
- **用户名**：sa
- **密码**：Musicbox123

### 依赖表
- `MIMS.dbo.ShopInfo`：门店信息表
- `dbo.RmCloseInfo_Test`：结账数据表
- `dbo.KTV_DailyReport_Comprehensive`：综合数据表（目标表）

## 存储过程详情

### 存储过程名称
```sql
dbo.usp_CollectKTVDailyData
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| @ShopName | NVARCHAR(100) | 是 | 门店名称 | N'名堂店' |
| @TargetDate | VARCHAR(8) | 是 | 目标日期，格式YYYYMMDD | '20250720' |
| @Debug | BIT | 否 | 调试模式开关，默认0 | 1 |

### 门店名称映射

| 门店名称 | ShopId | 备注 |
|----------|--------|------|
| 名堂店 | 11 | 主要测试门店 |
| 天河店 | 3 | 备用测试门店 |

*注：更多门店信息请查询 MIMS.dbo.ShopInfo 表*

## 使用方法

### 1. 基本调用
```sql
-- 收集名堂店2025年7月20日的数据
EXEC dbo.usp_CollectKTVDailyData 
    @ShopName = N'名堂店', 
    @TargetDate = '20250720';
```

### 2. 开启调试模式
```sql
-- 开启调试模式，查看详细执行过程
EXEC dbo.usp_CollectKTVDailyData 
    @ShopName = N'名堂店', 
    @TargetDate = '20250720',
    @Debug = 1;
```

### 3. 收集其他门店数据
```sql
-- 收集天河店的数据
EXEC dbo.usp_CollectKTVDailyData 
    @ShopName = N'天河店', 
    @TargetDate = '20250720';
```

## 执行流程

### 第一步：参数验证
- 验证门店名称不为空
- 验证日期格式为8位数字
- 验证日期不能是未来日期
- 转换日期格式

### 第二步：门店ID获取
- 通过门店名称查询ShopId
- 验证门店是否存在

### 第三步：数据检查
- 检查目标日期的数据是否已存在
- 如存在则删除旧数据（支持重复执行）

### 第四步：数据收集
- 从RmCloseInfo_Test表收集基础数据
- 计算营收、客户、餐饮等各项指标
- 按时间段分类统计

### 第五步：数据存储
- 将收集的数据插入KTV_DailyReport_Comprehensive表
- 验证数据插入结果

### 第六步：结果反馈
- 输出执行状态和统计信息
- 显示生成的数据摘要

## 输出信息

### 成功执行示例
```
=== 数据收集完成 ===
门店: 名堂店 (ID: 11)
日期: 20250720
生成记录数: 1
执行时间: 1250 毫秒
完成时间: 2025-01-24 10:30:15.123

日期    门店    星期    营收_总收入    营收_白天档    营收_晚上档    带客_全天总批数    ...
20250720 名堂店  星期六   125000.00     95000.00      30000.00      485              ...
```

### 调试模式输出
```
=== 参数验证完成 ===
门店名称: 名堂店
门店ID: 11
目标日期: 20250720
开始时间: 2025-01-24 10:30:14.000

已删除现有数据，记录数: 1

=== 数据收集完成 ===
...
```

## 错误处理

### 常见错误及解决方案

#### 1. 门店名称错误
```
错误：未找到门店：名堂店1，请检查门店名称是否正确
解决：检查门店名称拼写，确保与ShopInfo表中的ShopName完全一致
```

#### 2. 日期格式错误
```
错误：日期格式错误，请使用YYYYMMDD格式，如：20250720
解决：使用8位数字格式，如20250720而不是2025-07-20
```

#### 3. 未来日期错误
```
错误：不能收集未来日期的数据
解决：只能收集当前日期及以前的数据
```

#### 4. 无业务数据
```
错误：数据生成失败，请检查指定日期是否有业务数据
解决：确认指定日期该门店是否有营业数据
```

## 数据验证

### 验证数据完整性
```sql
-- 查看收集的数据
SELECT 
    日期, 门店, 星期, 营收_总收入, 营收_白天档, 营收_晚上档,
    带客_全天总批数, 带客_白天档_总批次, 带客_晚上档_总批次,
    用餐_总人数, 用餐_自助餐人数
FROM dbo.KTV_DailyReport_Comprehensive
WHERE 日期 = '20250720' AND 门店 = N'名堂店';
```

### 验证数据准确性
```sql
-- 对比原始数据验证准确性
SELECT 
    COUNT(*) AS 总订单数,
    SUM(TotalAmount) AS 总营业额,
    SUM(Numbers) AS 总人数
FROM dbo.RmCloseInfo_Test
WHERE ShopId = 11 AND WorkDate = '20250720';
```

## 性能优化

### 建议索引
```sql
-- 为提高性能，建议创建以下索引
CREATE INDEX IX_RmCloseInfo_ShopId_WorkDate_OpenDateTime 
ON RmCloseInfo_Test (ShopId, WorkDate, OpenDateTime);

CREATE INDEX IX_ShopInfo_ShopName 
ON MIMS.dbo.ShopInfo (ShopName);
```

### 执行时间
- **单日数据收集**：通常1-3秒
- **包含调试信息**：2-5秒
- **大数据量日期**：可能需要5-10秒

## 注意事项

### ⚠️ 重要提醒
1. **数据覆盖**：重复执行会覆盖已有数据
2. **权限要求**：需要对相关表的读写权限
3. **时区问题**：确保服务器时区设置正确
4. **备份建议**：重要数据建议先备份

### 💡 最佳实践
1. **测试先行**：新门店数据收集前先用调试模式测试
2. **定期验证**：定期验证收集数据的准确性
3. **错误记录**：记录执行过程中的错误信息
4. **性能监控**：关注执行时间，必要时优化

## 技术支持

### 问题排查步骤
1. 检查参数格式是否正确
2. 验证数据库连接是否正常
3. 确认相关表是否存在数据
4. 检查是否有足够的数据库权限
5. 查看错误日志获取详细信息

### 联系方式
如遇到技术问题，请提供：
- 完整的错误信息
- 执行的SQL语句
- 门店名称和日期
- 调试模式的输出信息

---

**版本信息**：v1.0 (2025-01-24)  
**适用环境**：SQL Server 2016+  
**维护状态**：生产就绪
