
# KTV业务数据采集与关联策略

**文档创建时间:** 2025年8月7日

本文件旨在明确KTV业务中，为实现从客户预订到结账离店的全链路数据分析，所需采集的核心数据表、关键分析维度以及数据关联的核心逻辑。

---

## 一、 每日核心数据采集清单

要构建完整的客户消费故事，每日的数据抽取任务应至少包含以下四个核心表：

1.  **预订阶段: `rms2019.bookcacheinfo`**
    *   **业务含义:** 客户的预订意向，是客户旅程的起点。
    *   **关键字段:** `bookno` (预订号), `CustTel` (客户手机), `ComeDate` (预定到店日), `RtNo` (房型), `BookStatus` (预订状态)。

2.  **开台阶段: `rms2019.opencacheinfo`**
    *   **业务含义:** 客户实际到店，占用房间，标志消费正式开始。
    *   **关键字段:** `RmNo` (房间号), `CustTel` (客户手机), `OpenTime` (开房时间), `Numbers` (实际到店人数)。

3.  **消费阶段: `dbfood.FdCashBak`**
    *   **业务含义:** 实时记录房间内的每一笔商品消费明细。
    *   **关键字段:** `InvNo` (账单号), `RmNo` (房间号), `FdNo` (商品编号), `FdQty` (数量), `CashTime` (下单时间)。
    *   **注意:** 此表目前存在数据同步中断问题，需优先修复。

4.  **结账阶段: `dbfood.FdInv`**
    *   **业务含义:** 客户本次消费的最终汇总账单。
    *   **关键字段:** `InvNo` (最终账单号), `RmNo` (房间号), `AccDate` (结账日期), `Tot` (总金额), `PayType` (支付方式)。

---

## 二、 预订数据分析维度建议

对预订数据的分析有助于市场和运营决策，建议从以下维度展开：

*   **时间维度:**
    *   **预订提前期:** `预定到店日 - 预订创建日`，反映客户计划性。
    *   **预订高峰分析:** 找出每周、每日的预订高峰时段。

*   **客户维度:**
    *   **新老客分析:** 通过 `CustTel` 判断客户类型。
    *   **会员/非会员分析:** 对比两类客户的预订行为差异。

*   **渠道维度:**
    *   **预订来源分析:** (如系统支持) 分析电话、App、小程序等渠道的预订数量和转化率。

*   **结果维度:**
    *   **到店率 (Show Rate):** `(实际到店数 / 总预订数)`，衡量预订质量的核心指标。
    *   **取消率 (Cancel Rate) / 未到率 (No-Show Rate):** 分析预订流失情况，高“未到率”是运营的巨大损失，需重点关注。

---

## 三、 核心关联逻辑 (在无账单号前)

在最终的`InvNo` (账单号)生成之前，我们需要一个“临时粘合剂”来串联各个阶段的数据。

**最佳关联主键：客户手机号 (`CustTel`)**

`CustTel` 是在 `InvNo` 出现之前，识别同一个客户消费意图的**最可靠、最核心的关联键**。

#### 关联键的生命周期演变：

1.  **预订 -> 开台:**
    *   通过 **`CustTel` + `ComeDate`**，可以将 `bookcacheinfo` (预订信息) 和 `opencacheinfo` (开台信息) 关联起来。

2.  **开台 -> 消费:**
    *   开台后，通过 **`RmNo`** (房间号)，可以将 `opencacheinfo` (开台信息) 和 `FdCashBak` (消费明细) 关联起来。

3.  **消费 -> 结账:**
    *   结账时，系统生成唯一的 **`InvNo`** (账单号)。它将成为最终的、统一所有消费记录 (`FdCashBak` 和 `FdInv`) 的核心ID。

#### 关联路径总结：

**`CustTel`** (预订阶段) → **`RmNo`** (开台与消费中) → **`InvNo`** (结账后)
