
# KTV数据库关键表分析报告

**分析时间:** 2025年8月7日
**目标数据库:** `192.168.2.5` 上的 `operatedata` 数据库

---

## 一、 核心结论

本次分析旨在找出`operatedata`数据库中数据量最大、增长最活跃的核心业务表，为后续的数据仓库建设和ETL开发提供依据。我们成功描绘出了数据库的业务全景，并发现了一个严重的数据质量问题。

*   **首要任务:** **立即排查 `FdCashBak` (消费明细表) 数据同步中断的问题**。这是当前数据链路中最严重的故障，直接影响所有基于商品的精细化分析。
*   **核心数据源:** `FdInv` (总账单表) 和 `RmCloseInfo_Day` (日结总览表) 是当前最可靠、持续增长的业务核心表，可以立刻基于它们构建核心经营报表。
*   **数据质量问题:** `FdInv` 表中存在格式明显错误的日期（如`66660513`），在ETL时必须进行严格的清洗和校验。
*   **潜在优化点:** `bookcacheinfo` (预订缓存) 和 `opencacheinfo` (开台缓存) 两个表体量巨大但增长未知，可能存在大量冗余的已完成或已取消的记录，建议未来进行归档或清理优化。

---

## 二、 关键数据表深度分析

### (一) 核心业务数据表 (大规模 & 持续增长)

这些是系统的生命线，记录了最核心的业务活动。

#### 1. `FdInv` (总账单表)
*   **规模:** 3,232,007 行
*   **增长趋势:** **非常健康**。过去几天日均新增 **1000-1500** 条记录。
*   **业务含义:** 所有消费的头部信息，每一条记录代表一次完整的客户结账。它是所有经营分析的起点。
*   **风险:** **存在脏数据**。`AccDate`字段中包含无效日期，必须清洗。

#### 2. `RmCloseInfo_Day` (日结报表-按天)
*   **规模:** (其主表 `RmCloseInfo` 有 588万行)
*   **增长趋势:** **健康且关键**。每天稳定新增 8-9 条记录，此数字高度疑似集团下的分店数量。
*   **业务含义:** 每日营业的最终汇总数据，是生成集团层面经营报告的**黄金数据源**，数据质量相对较高。

### (二) 存在严重问题的核心表

#### 3. `FdCashBak` (消费明细表)
*   **规模:** **17,355,942 行 (系统第一大表)**
*   **增长趋势:** **严重异常！过去3天0增长。**
*   **业务含义:** 记录了顾客消费的每一项商品（酒水、小吃等），是进行商品分析、毛利分析、顾客消费行为分析的基础。
*   **问题分析:** 在`FdInv`主表持续增长的情况下，此明细表的0增长是反常的，强烈暗示**数据同步链路存在严重问题**。可能是ETL脚本失败，或者数据被错误地写入了其他归档表。**此问题修复的优先级最高。**

### (三) 历史与缓存数据表

这些表体量巨大，但目前增长缓慢或停滞，主要作为历史档案或系统缓存。

*   **`FdCashBak_2018/2019/2020` & `FdInv_2018/2019/2020`**: 明确的按年归档的历史数据。在进行跨年同比分析时需要用到。
*   **`bookcacheinfo` (预订缓存)** & **`opencacheinfo` (开台缓存)**: 规模巨大（分别为519万和423万行），可能存在大量未清理的冗余记录，是未来潜在的性能优化点。

### (四) 专项业务数据表

*   **`MemberCheckoutInfo` (会员结账信息)**: 规模115万行，是进行会员体系分析的核心数据。
*   **`wxPayInfo` (微信支付信息)**: 规模55万行，是财务对账和支付方式分析的重要依据。

---

## 三、 原始查询数据参考

### `operatedata` 数据库数据量排名前15的表

| 表名                   | 总行数     |
| ---------------------- | ---------- |
| FdCashBak              | 17,355,942 |
| fdcashbak_2018         | 5,977,277  |
| RmCloseInfo            | 5,888,575  |
| fdcashbak_2019         | 5,731,042  |
| bookcacheinfo          | 5,198,634  |
| opencacheinfo          | 4,232,212  |
| fdcashbak_2020         | 3,950,590  |
| FdInv                  | 3,232,007  |
| MemberCheckoutInfo     | 1,157,384  |
| FdInv_2018             | 1,116,588  |
| FdInv_2019             | 1,037,593  |
| reporting_basic        | 780,242    |
| reporting_to_examine   | 777,109    |
| FdInv_2020             | 652,315    |
| wxPayInfo              | 552,174    |

### 最近三日关键表增长情况

*   **FdCashBak:** 0条/天
*   **FdInv:** ~1200条/天 (存在脏数据)
*   **RmCloseInfo_Day:** 8-9条/天
