# KTV数据分析存储过程优化报告

## 项目概述
基于现有的KTV数据分析存储过程 `usp_GenerateFullDailyReport`，对夜间档的非自由餐统计进行了细化优化，实现了更精准的业务分类和统计。

## 优化目标
将原来粗糙的"非自由餐"单一分类细化为三个子类别：
1. **啤酒买断**（优先级最高）
2. **畅饮套餐**（次优先级）  
3. **其他非自由餐**（兜底分类）

## 技术实现

### 1. 核心分类逻辑
```sql
CASE 
    WHEN rt.CtNo = 19 THEN '自由餐'
    WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%买断%') THEN '啤酒买断'
    WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%畅饮%') THEN '畅饮套餐'
    ELSE '其他非自由餐'
END AS ConsumptionSubType
```

### 2. 优先级处理
- **买断优先**：如果订单同时包含买断和畅饮商品，按买断分类
- **畅饮次之**：仅包含畅饮商品的订单按畅饮分类
- **其他兜底**：既不包含买断也不包含畅饮的非自由餐订单

### 3. 渠道分类保持不变
- K+、特权预约、美团、抖音、房费、其他

## 新增统计字段

### 啤酒买断类别（8个字段）
- `晚间_啤酒买断_K+`
- `晚间_啤酒买断_特权预约`
- `晚间_啤酒买断_美团`
- `晚间_啤酒买断_抖音`
- `晚间_啤酒买断_房费`
- `晚间_啤酒买断_其他`
- `晚间_啤酒买断_小计`
- `晚间_啤酒买断_营业额`

### 畅饮套餐类别（8个字段）
- `晚间_畅饮套餐_K+`
- `晚间_畅饮套餐_特权预约`
- `晚间_畅饮套餐_美团`
- `晚间_畅饮套餐_抖音`
- `晚间_畅饮套餐_房费`
- `晚间_畅饮套餐_其他`
- `晚间_畅饮套餐_小计`
- `晚间_畅饮套餐_营业额`

### 其他非自由餐类别（8个字段）
- `晚间_其他非自由餐_K+`
- `晚间_其他非自由餐_特权预约`
- `晚间_其他非自由餐_美团`
- `晚间_其他非自由餐_抖音`
- `晚间_其他非自由餐_房费`
- `晚间_其他非自由餐_其他`
- `晚间_其他非自由餐_小计`
- `晚间_其他非自由餐_营业额`

## 测试验证

### 测试数据：天河店 2025-05-09
- **夜间档订单总数**：80个
- **买断订单**：18个（通过FdCashBak表关联识别）
- **畅饮订单**：14个（通过FdCashBak表关联识别）
- **其他非自由餐**：剩余订单

### 分类结果验证
通过测试存储过程 `usp_TestNightClassification` 验证：
- 啤酒买断：0个订单
- 畅饮套餐：10个订单（1个K+，9个其他渠道）
- 其他非自由餐：76个订单（1个K+，9个特权预约，18个房费，48个其他）

## 性能优化

### 1. 使用EXISTS而非JOIN
```sql
EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%买断%')
```
避免重复数据，提高查询效率。

### 2. 优先级逻辑
通过CASE WHEN的顺序实现优先级，避免复杂的嵌套查询。

### 3. 单次扫描
在一个CTE中完成所有分类逻辑，避免多次扫描同一数据集。

## 文件清单

1. **usp_GenerateFullDailyReport_Enhanced.sql** - 优化后的主存储过程
2. **test_night_classification.sql** - 测试存储过程
3. **basic_test.sql** - 基础验证查询
4. **simple_test.sql** - 简化测试查询

## 使用方法

```sql
-- 执行优化后的存储过程
EXEC dbo.usp_GenerateFullDailyReport_Enhanced 
    @ShopId = 3,           -- 门店ID（3=天河店）
    @BeginDate = '2025-05-09',  -- 开始日期
    @EndDate = '2025-05-09';    -- 结束日期
```

## 兼容性说明

- 保持与原存储过程的输出结构兼容
- 新增字段不影响现有报表逻辑
- 可以与原存储过程并行使用进行对比验证

## 后续扩展建议

1. **增加更多细分类别**：可以根据业务需要进一步细分商品类型
2. **时段细分**：可以将夜间档按小时进一步细分统计
3. **趋势分析**：增加同比、环比分析功能
4. **异常检测**：增加数据异常检测和预警功能
