<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>派房历史记录</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vant@2.12/lib/index.css"/>
    <script src="https://cdn.jsdelivr.net/npm/vant@2.12/lib/vant.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
            background-color: #f7f8fa;
            color: #323233;
            font-size: 14px;
        }
        .app-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ebedf0;
            margin-bottom: 10px;
        }
        .header-title {
            font-size: 16px;
            font-weight: bold;
        }
        .room-info {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .room-number {
            font-size: 20px;
            font-weight: bold;
            color: #1989fa;
            margin-bottom: 5px;
        }
        .room-type {
            font-size: 14px;
            color: #646566;
            margin-bottom: 10px;
        }
        .history-timeline {
            margin-bottom: 60px;
        }
        .history-item {
            position: relative;
            padding-left: 20px;
        }
        .history-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 24px;
            width: 1px;
            height: calc(100% - 24px);
            background-color: #ebedf0;
        }
        .history-item:last-child::before {
            display: none;
        }
        .history-dot {
            position: absolute;
            left: -4px;
            top: 24px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #1989fa;
            z-index: 1;
        }
        .history-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0 15px 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .history-time {
            font-size: 14px;
            color: #969799;
            margin-bottom: 10px;
        }
        .staff-tag {
            display: inline-block;
            background-color: #f2f3f5;
            color: #323233;
            padding: 2px 6px;
            border-radius: 4px;
            margin-right: 6px;
            margin-bottom: 6px;
            font-size: 12px;
        }
        .staff-tag.commission {
            background-color: #e8f3ff;
            color: #1989fa;
            border: 1px solid #d0e8ff;
        }
        .action-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            padding: 10px 15px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
        }
        .tab-bar {
            display: flex;
            justify-content: space-around;
            background-color: #fff;
            padding: 10px 0;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }
        .tab-item {
            text-align: center;
            font-size: 12px;
            color: #646566;
        }
        .tab-item.active {
            color: #1989fa;
        }
        .tab-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .stats-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-around;
        }
        .stats-item {
            text-align: center;
        }
        .stats-value {
            font-size: 18px;
            font-weight: bold;
            color: #1989fa;
        }
        .stats-label {
            font-size: 12px;
            color: #969799;
            margin-top: 5px;
        }
        .footer-space {
            height: 60px;
        }
        .scenario-tag {
            font-weight: bold;
            margin-right: 10px;
        }
        .amount-summary {
            font-size: 12px;
            color: #969799;
        }
        .staff-detail-list {
            margin-top: 10px;
            padding-left: 15px;
            border-left: 2px solid #ebedf0;
        }
        .staff-detail-item {
            margin-bottom: 8px;
        }
        .income-breakdown {
            font-size: 12px;
            color: #646566;
            margin-left: 8px;
        }
        .approval-status {
            text-align: right;
            font-size: 12px;
            font-style: italic;
            color: #1989fa;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <!-- 头部 -->
        <div class="header">
            <van-icon name="arrow-left" size="20" @click="goBack" />
            <div class="header-title">派房历史记录</div>
            <van-icon name="more-o" size="20" @click="showOptions" />
        </div>

        <!-- 房间信息 -->
        <div class="room-info">
            <div class="room-number">{{ roomInfo.number }}</div>
            <div class="room-type">{{ roomInfo.type }}</div>
            <van-tag type="primary" round>当前状态: {{ roomInfo.status }}</van-tag>
        </div>

        <!-- 统计信息 -->
        <div class="stats-card">
            <div class="stats-item">
                <div class="stats-value">{{ historyList.length }}</div>
                <div class="stats-label">总派房次数</div>
            </div>
        </div>

        <!-- 历史记录时间线 -->
        <van-tabs v-model="activeTab">
            <van-tab title="派房记录">
                <div class="history-timeline">
                    <div class="history-item" v-for="(item, index) in historyList" :key="index">
                        <div class="history-dot"></div>
                        <div class="history-card">
                            <div class="history-time">
                                <van-icon name="clock-o" /> {{ item.time }}
                            </div>

                            <div class="staff-detail-list">
                                <!-- 按角色分类显示员工 -->
                                <div class="staff-detail-item" v-if="getStaffByRole(item.staffList, '服务者').length > 0">
                                    <strong>服务:</strong> {{ getStaffByRole(item.staffList, '服务者').map(s => s.name).join(', ') }}
                                </div>
                                <div class="staff-detail-item" v-if="getStaffByRole(item.staffList, '预订者').length > 0">
                                    <strong>预订:</strong> {{ getStaffByRole(item.staffList, '预订者').map(s => s.name).join(', ') }}
                                </div>
                                <!-- 显示推荐关系 -->
                                <div class="staff-detail-item" v-if="item.referrals && item.referrals.length > 0">
                                    <strong>推荐人:</strong> {{ getRecommenders(item.referrals).join(', ') }}
                                </div>
                                <div class="staff-detail-item" v-if="item.referrals && item.referrals.length > 0">
                                    <strong>被推荐人:</strong> {{ getRecommendees(item.referrals).join(', ') }}
                                </div>
                            </div>

                            <div v-if="item.isApproved" class="approval-status">
                                <van-icon name="checked" /> 已由 {{ item.approverName }} 审批
                            </div>

                            <div style="display: flex; justify-content: space-between; font-size: 12px; color: #969799; margin-top: 10px;">
                                <div>操作人: {{ item.operatorName }}</div>
                                <div @click="showDetail(item)" style="color: #1989fa;">
                                    详情 <van-icon name="arrow" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </van-tab>
            <van-tab title="收入统计">
                <van-cell-group>
                    <van-cell 
                        v-for="(staff, index) in incomeStats" 
                        :key="index"
                        :title="staff.name" 
                        :label="getStaffRoles(staff.id)" />
                </van-cell-group>
            </van-tab>
        </van-tabs>

        <!-- 底部操作栏 -->
        <div class="action-bar">
            <van-button plain type="primary" icon="chat-o" size="small">消息通知</van-button>
            <van-button type="primary" icon="plus" size="small" @click="addNewAssignment">新增派房</van-button>
        </div>

        <!-- 底部空间占位 -->
        <div class="footer-space"></div>

        <!-- 弹窗组件 -->
        <van-dialog id="van-dialog" />
        <van-toast id="van-toast" />
        <van-action-sheet
            v-model="showActionSheet"
            :actions="actions"
            cancel-text="取消"
            @select="onSelect"
        />
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    activeTab: 0,
                    showActionSheet: false,
                    actions: [
                        { name: '导出记录', color: '#1989fa' },
                        { name: '打印记录' },
                        { name: '删除记录', color: '#ee0a24' }
                    ],
                    roomInfo: {
                        number: '858',
                        type: '豪华套房',
                        status: '已派房'
                    },
                    historyList: [
                        {
                            id: 1,
                            time: '2025-06-24 18:45',
                            operatorName: '李主任',
                            assignmentScenario: '公司派房',
                            totalAmount: 1588,
                            commissionableBase: 1288,
                            isApproved: true,
                            approverName: '李主任',
                            staffList: [
                                { id: 2, name: '李四', role: '服务者', commissionAmount: 77.28, fixedFee: 0, referralFee: 0 },
                                { id: 4, name: '赵六', role: '服务者', commissionAmount: 25.76, fixedFee: 0, referralFee: 0 },
                            ],
                        },
                        {
                            id: 2,
                            time: '2025-06-23 14:30',
                            operatorName: '张三',
                            assignmentScenario: '自订自看',
                            totalAmount: 988,
                            commissionableBase: 688,
                            isApproved: false,
                            approverName: null,
                            staffList: [
                                { id: 1, name: '张三', role: '预订者', commissionAmount: 41.28, fixedFee: 50, referralFee: 20 },
                                { id: 5, name: '钱七', role: '服务者', commissionAmount: 13.76, fixedFee: 0, referralFee: 0 },
                            ],
                            referrals: [{ recommender: '张三', recommended: '钱七', fee: 20 }]
                        },
                        {
                            id: 3,
                            time: '2025-06-22 20:00',
                            operatorName: '王五',
                            assignmentScenario: '自订非自看',
                            totalAmount: 300,
                            commissionableBase: 300,
                            isApproved: false,
                            approverName: null,
                            staffList: [
                                { id: 3, name: '王五', role: '预订者', commissionAmount: 18, fixedFee: 0, referralFee: 0 },
                                { id: 7, name: '周九', role: '服务者', commissionAmount: 0, fixedFee: 30, referralFee: 0 },
                            ],
                        }
                    ]
                }
            },
            computed: {

                incomeStats() {
                    const staffMap = {};
                    this.historyList.forEach(history => {
                        history.staffList.forEach(staff => {
                            if (!staffMap[staff.id]) {
                                staffMap[staff.id] = {
                                    id: staff.id,
                                    name: staff.name,
                                    roles: []
                                };
                            }
                            if (!staffMap[staff.id].roles.includes(staff.role)) {
                                staffMap[staff.id].roles.push(staff.role);
                            }
                        });
                    });
                    return Object.values(staffMap);
                }
            },
            methods: {
                goBack() {
                    // 返回上一页
                    vant.Toast('返回上一页');
                },
                showOptions() {
                    this.showActionSheet = true;
                },
                showDetail(item) {
                    // 按角色分类员工
                    const staffByRole = {
                        '服务': [],
                        '预订': []
                    };
                    
                    // 推荐人和被推荐人列表
                    const recommenders = [];
                    const recommendees = [];
                    
                    // 分类员工
                    item.staffList.forEach(staff => {
                        // 根据角色分类
                        if (staff.role.includes('服务')) {
                            staffByRole['服务'].push(staff.name);
                        } else if (staff.role.includes('预订')) {
                            staffByRole['预订'].push(staff.name);
                        }
                    });
                    
                    // 处理推荐关系
                    if (item.referrals) {
                        item.referrals.forEach(r => {
                            if (!recommenders.includes(r.recommender)) {
                                recommenders.push(r.recommender);
                            }
                            
                            r.recommended.forEach(rec => {
                                if (!recommendees.includes(rec)) {
                                    recommendees.push(rec);
                                }
                            });
                        });
                    }
                    
                    // 生成HTML
                    let rolesHtml = '';
                    
                    // 服务人员
                    if (staffByRole['服务'].length > 0) {
                        rolesHtml += `<p><strong>服务:</strong> ${staffByRole['服务'].join(', ')}</p>`;
                    }
                    
                    // 预订人员
                    if (staffByRole['预订'].length > 0) {
                        rolesHtml += `<p><strong>预订:</strong> ${staffByRole['预订'].join(', ')}</p>`;
                    }
                    
                    // 推荐关系
                    let referralHtml = '';
                    if (recommenders.length > 0) {
                        referralHtml += `<p><strong>推荐人:</strong> ${recommenders.join(', ')}</p>`;
                    }
                    if (recommendees.length > 0) {
                        referralHtml += `<p><strong>被推荐人:</strong> ${recommendees.join(', ')}</p>`;
                    }
                    
                    const messageHtml = `
                        <div style="text-align: left; padding: 10px;">
                            <p><strong>时间:</strong> ${item.time}</p>
                            <hr style="margin: 10px 0;" />
                            ${rolesHtml}
                            ${referralHtml}
                            <hr style="margin: 10px 0;" />
                            <p><strong>操作人:</strong> ${item.operatorName}</p>
                            ${item.isApproved ? `<p><strong>审批人:</strong> ${item.approverName}</p>` : ''}
                        </div>
                    `;

                    vant.Dialog.alert({
                        title: '派房详情',
                        message: messageHtml,
                        allowHtml: true
                    });
                },
                addNewAssignment() {
                    // 跳转到派房页面
                    vant.Toast('跳转到派房页面');
                    // 实际应用中可以使用小程序的导航API
                    // 例如: wx.navigateTo({ url: '/pages/room-assignment/index' })
                },
                onSelect(action) {
                    // 处理操作菜单选择
                    if (action.name === '导出记录') {
                        vant.Toast('导出记录功能待实现');
                    } else if (action.name === '打印记录') {
                        vant.Toast('打印记录功能待实现');
                    } else if (action.name === '删除记录') {
                        vant.Dialog.confirm({
                            title: '确认删除',
                            message: '确定要删除选中的记录吗？',
                        }).then(() => {
                            vant.Toast('删除功能待实现');
                        }).catch(() => {
                            // 取消删除
                        });
                    }
                },
                // 辅助方法：按角色获取员工
                getStaffByRole(staffList, role) {
                    return staffList.filter(staff => staff.role === role);
                },
                // 辅助方法：获取推荐人列表
                getRecommenders(referrals) {
                    return referrals.map(r => r.recommender).filter((v, i, a) => a.indexOf(v) === i);
                },
                // 辅助方法：获取被推荐人列表
                getRecommendees(referrals) {
                    const recommendees = [];
                    referrals.forEach(r => {
                        if (Array.isArray(r.recommended)) {
                            r.recommended.forEach(name => {
                                if (!recommendees.includes(name)) {
                                    recommendees.push(name);
                                }
                            });
                        } else if (!recommendees.includes(r.recommended)) {
                            recommendees.push(r.recommended);
                        }
                    });
                    return recommendees;
                },
                // 辅助方法：获取员工角色
                getStaffRoles(staffId) {
                    const roles = new Set();
                    this.historyList.forEach(history => {
                        history.staffList.forEach(staff => {
                            if (staff.id === staffId) {
                                roles.add(staff.role);
                            }
                        });
                    });
                    return Array.from(roles).join(', ');
                }
            }
        });
    </script>
</body>
</html>
