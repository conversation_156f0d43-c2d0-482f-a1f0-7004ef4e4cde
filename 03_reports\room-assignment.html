<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房间派发系统</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vant@2.12/lib/index.css"/>
    <script src="https://cdn.jsdelivr.net/npm/vant@2.12/lib/vant.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
            background-color: #f7f8fa;
            color: #323233;
            font-size: 14px;
        }
        .app-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ebedf0;
            margin-bottom: 10px;
        }
        .header-title {
            font-size: 16px;
            font-weight: bold;
        }
        .room-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        .room-item {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .room-item.selected {
            background-color: #1989fa;
            color: white;
        }
        .room-item.luxury {
            background-color: #8a2be2;
            color: white;
        }
        .action-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            padding: 10px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-around;
        }
        .tab-bar {
            display: flex;
            justify-content: space-around;
            background-color: #fff;
            padding: 10px 0;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }
        .tab-item {
            text-align: center;
            font-size: 12px;
            color: #646566;
        }
        .tab-item.active {
            color: #1989fa;
        }
        .tab-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .staff-list {
            margin-bottom: 60px;
        }
        .staff-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background-color: #fff;
            margin-bottom: 5px;
            border-radius: 4px;
        }
        .commission-toggle {
            margin-left: 10px;
        }
        .order-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
        }
        .order-item > div {
            margin-right: 10px;
        }
        .role-selector {
            margin-left: 10px;
            min-width: 120px;
        }
        .search-bar {
            margin-bottom: 10px;
        }
        .room-type-tabs {
            margin-bottom: 10px;
        }
        .footer-space {
            height: 50px;
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <!-- 头部 -->
        <div class="header">
            <div class="header-title">派房管理</div>
            <van-icon name="arrow-left" size="20" @click="goBack" />
        </div>

        <!-- 房间选择部分 -->
        <van-tabs v-model="activeTab" class="room-type-tabs">
            <van-tab title="已选房间">
                <div v-if="selectedRoom">
                    <van-cell-group>
                        <van-cell title="房间号" :value="selectedRoom" />
                    </van-cell-group>
                </div>
                <div v-else>
                    <van-empty description="请先选择房间" />
                </div>
            </van-tab>
        </van-tabs>

        <!-- 员工搜索 -->
        <div class="search-bar">
            <van-search
                v-model="searchValue"
                placeholder="搜索员工"
                @search="onSearch"
                shape="round"
                show-action
            />
        </div>

        <!-- 派房类型选择 -->
        <van-field name="radio" label="派房类型">
            <template #input>
                <van-radio-group v-model="assignmentType" direction="horizontal">
                    <van-radio name="公司派房">公司派房</van-radio>
                    <van-radio name="自订自看">自订自看</van-radio>
                    <van-radio name="自订非自看">自订非自看</van-radio>
                </van-radio-group>
            </template>
        </van-field>

        <!-- 员工列表 -->
        <div class="staff-list">
            <van-checkbox-group v-model="selectedStaff">
                <div class="staff-item" v-for="(staff, index) in filteredStaff" :key="staff.id">
                    <div style="display: flex; align-items: center; flex-grow: 1;">
                        <van-checkbox :name="staff.id" shape="square">
                            {{ staff.name }} ({{ staff.position }})
                        </van-checkbox>
                    </div>
                    <div v-if="selectedStaff.includes(staff.id)" style="display: flex; align-items: center;">
                        <!-- 角色分配 -->
                        <div class="role-selector">
                            <van-radio-group v-model="staff.role" direction="horizontal" @change="updateStaffRole(staff.id, staff.role)">
                                <van-radio name="主服务者">主服务</van-radio>
                                <van-radio name="辅助服务者">辅助</van-radio>
                                <van-radio name="预订者">预订</van-radio>
                            </van-radio-group>
                        </div>

                        <!-- 提成开关 (仅公司派房且操作员为主任时显示) -->
                        <div v-if="assignmentType === '公司派房' && currentUser.position === '主任'" style="display: flex; align-items: center; background-color: #f2f3f5; padding: 4px 8px; border-radius: 16px;">
                            <span style="font-weight: bold; margin-right: 6px; color: #323233;">提成:</span>
                            <van-switch v-model="staff.hasCommission" size="24px" />
                        </div>
                    </div>
                </div>
            </van-checkbox-group>
        </div>

        <!-- 消费项目录入 -->
        <div style="margin-bottom: 15px;">
            <van-divider>消费项目</van-divider>
            <div v-for="(item, index) in orderItems" :key="index" class="order-item">
                <div style="flex-grow: 1;">
                     <van-field
                        readonly
                        clickable
                        :value="item.productName"
                        placeholder="选择消费项目"
                        @click="showProductPicker = true; currentOrderItemIndex = index"
                    />
                </div>
                <div>
                    <van-stepper v-model="item.quantity" min="1" />
                </div>
                <div>
                    <span>小计: ¥{{ item.price * item.quantity }}</span>
                </div>
                <div>
                    <van-button icon="delete-o" type="danger" size="small" @click="removeOrderItem(index)" />
                </div>
            </div>
            <van-button icon="plus" type="primary" plain block @click="addOrderItem">添加消费项目</van-button>
            <div style="text-align: right; margin-top: 10px; font-weight: bold;">
                总计: ¥{{ totalAmount }}
            </div>
        </div>

        <!-- 推荐关系显示 -->
        <div v-if="referralLinks.length > 0" style="padding: 0 16px 10px;">
            <van-divider>已设置推荐关系</van-divider>
            <van-tag
                v-for="(link, index) in referralLinks"
                :key="index"
                closeable
                size="medium"
                type="primary"
                @close="removeReferralLink(index)"
                style="margin-right: 5px; margin-bottom: 5px;"
            >
                {{ link.recommender.name }} → {{ link.recommended.map(r => r.name).join(', ') }}
            </van-tag>
        </div>

        <!-- 操作按钮 -->
        <div style="padding: 16px; margin-bottom: 60px; display: flex; justify-content: space-between; gap: 10px;">
            <van-button plain type="default" @click="cancelAssignment" style="flex: 1;">清空选择</van-button>
            <van-button 
                type="info" 
                @click="openReferralDialog" 
                :disabled="!canSetReferral"
                style="flex: 1.2;"
            >
                设置推荐关系
            </van-button>
            <van-button type="primary" @click="onAssignClick" style="flex: 1;">确认派房</van-button>
        </div>

        <!-- 底部导航 -->
        <div class="tab-bar">
            <div class="tab-item">
                <van-icon name="wap-home-o" class="tab-icon" />
                <div>工作台</div>
            </div>
            <div class="tab-item active">
                <van-icon name="hotel-o" class="tab-icon" />
                <div>包房</div>
            </div>
            <div class="tab-item">
                <van-icon name="orders-o" class="tab-icon" />
                <div>订单</div>
            </div>
            <div class="tab-item">
                <van-icon name="contact" class="tab-icon" />
                <div>我的</div>
            </div>
        </div>

        <!-- 派房确认弹窗 -->
        <van-dialog
            v-model="showAssignDialog"
            title="确认派房信息"
            show-cancel-button
            :before-close="handleConfirmAssignment"
            :message="assignmentSummary"
        >
        </van-dialog>

        <van-toast id="van-toast" />

        <!-- 产品选择弹窗 -->
        <van-popup v-model="showProductPicker" round position="bottom">
            <van-picker
                show-toolbar
                :columns="productOptions"
                @cancel="showProductPicker = false"
                @confirm="onProductConfirm"
            />
        </van-popup>

        <!-- 推荐关系设置弹窗 (重构版) -->
        <van-dialog
            v-model="showReferralDialog"
            title="设置推荐关系"
            show-cancel-button
            @confirm="confirmReferral"
        >
            <div style="padding: 16px; max-height: 60vh; overflow-y: auto;">
                <van-field name="recommenderRadio" label="选择推荐人">
                    <template #input>
                        <van-radio-group v-model="tempRecommenderId" @change="onRecommenderChange">
                            <van-radio 
                                v-for="staff in selectedStaffDetails" 
                                :key="'radio_'+staff.id" 
                                :name="staff.id" 
                                style="margin-bottom: 8px;"
                            >
                                {{ staff.name }}
                            </van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                
                <van-divider />

                <van-field name="recommendedCheckbox" label="选择被推荐人">
                    <template #input>
                        <van-checkbox-group v-model="tempRecommendedIds">
                            <van-checkbox 
                                v-for="staff in selectedStaffDetails" 
                                :key="'checkbox_'+staff.id" 
                                :name="staff.id" 
                                :disabled="staff.id === tempRecommenderId"
                                shape="square"
                                style="margin-bottom: 8px;"
                            >
                                {{ staff.name }}
                            </van-checkbox>
                        </van-checkbox-group>
                    </template>
                </van-field>
            </div>
        </van-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    showTip: false,
                    activeTab: 0,
                    selectedRoom: '858', // 默认选中的房间
                    searchValue: '',
                    selectedStaff: [], // 选中的员工ID
                    assignmentType: '公司派房', // 派房类型
                    staffList: [],
                    showAssignDialog: false, // 控制派房确认弹窗

                    // --- 新增：订单化消费 --- 
                    orderItems: [],
                    products: [
                        { id: 1, name: '服务费', price: 300, is_commissionable: true },
                        { id: 2, name: '豪华酒水套餐', price: 1288, is_commissionable: true },
                        { id: 3, name: '场地清洁费', price: 100, is_commissionable: false },
                        { id: 4, name: '普通酒水', price: 688, is_commissionable: true },
                        { id: 5, name: '果盘', price: 188, is_commissionable: false },
                    ],
                    showProductPicker: false,
                    currentOrderItemIndex: -1,

                    // --- 新增：推荐关系管理 ---
                    referralLinks: [], // 存储最终的推荐关系 { recommender: {id, name}, recommended: [{id, name}] }
                    showReferralDialog: false,
                    tempRecommenderId: null, // 弹窗中临时的推荐人ID
                    tempRecommendedIds: [], // 弹窗中临时的被推荐人ID数组

                    // --- 当前用户信息 ---
                    currentUser: { id: 2, name: '李四', position: '主任' } // 模拟当前登录用户为主任
                }
            },
            created() {
                this.fetchStaffList();
            },
            computed: {
                filteredStaff() {
                    if (!this.searchValue) {
                        return this.staffList;
                    }
                    return this.staffList.filter(staff => 
                        staff.name.includes(this.searchValue) || 
                        staff.position.includes(this.searchValue)
                    );
                },
                selectedStaffDetails() {
                    return this.staffList.filter(s => this.selectedStaff.includes(s.id));
                },
                canSetReferral() {
                    return this.selectedStaff.length >= 2;
                },
                totalAmount() {
                    return this.orderItems.reduce((total, item) => total + item.price * item.quantity, 0);
                },
                commissionableBase() {
                    return this.orderItems
                        .filter(item => item.is_commissionable)
                        .reduce((total, item) => total + item.price * item.quantity, 0);
                },
                productOptions() {
                    return this.products.map(p => p.name);
                },
                assignmentSummary() {
                    if (!this.showAssignDialog) return '';
                    const staffSummary = this.selectedStaffDetails.map(s => `${s.name}(${s.role || '未分配'})`).join(', ');
                    return `派房场景: ${this.assignmentType}\n总消费: ¥${this.totalAmount}\n可提成金额: ¥${this.commissionableBase}\n参与人员: ${staffSummary}`;
                }
            },
            methods: {
                goBack() {
                    vant.Toast('返回上一页');
                },
                onSearch() {
                    vant.Toast('搜索: ' + this.searchValue);
                },
                cancelAssignment() {
                    this.selectedStaff = [];
                    this.referralLinks = []; // 同时清空推荐关系
                    vant.Toast('已清空选择');
                },
                fetchStaffList() {
                    // 增加role属性
                    const mockStaffList = [
                        { id: 1, name: '张三', position: '专员', hasCommission: true, role: null },
                        { id: 2, name: '李四', position: '主任', hasCommission: true, role: null },
                        { id: 3, name: '王五', position: '专员', hasCommission: false, role: null },
                        { id: 4, name: '赵六', position: '专员', hasCommission: false, role: null },
                        { id: 5, name: '钱七', position: '专员', hasCommission: true, role: null },
                        { id: 6, name: '孙八', position: '非专员', hasCommission: false, role: null },
                        { id: 7, name: '周九', position: '专员', hasCommission: false, role: null },
                        { id: 8, name: '吴十', position: '主任', hasCommission: true, role: null }
                    ];
                    this.staffList = mockStaffList;
                },
                onAssignClick() {
                    if (this.selectedStaff.length === 0) {
                        vant.Toast('请选择至少一名派房人员');
                        return;
                    }
                    if (this.orderItems.length === 0) {
                        vant.Toast('请至少添加一个消费项目');
                        return;
                    }
                    this.showAssignDialog = true;
                },
                handleConfirmAssignment(action, done) {
                    if (action === 'confirm') {
                        // 数据校验
                        const staffWithoutRole = this.selectedStaffDetails.find(s => !s.role);
                        if (staffWithoutRole) {
                            vant.Toast(`请为员工 ${staffWithoutRole.name} 分配角色`);
                            return done(false);
                        }

                        const assignmentData = {
                            roomId: this.selectedRoom,
                            assignmentScenario: this.assignmentType,
                            orderItems: this.orderItems.map(item => ({ 
                                productId: item.productId, 
                                quantity: item.quantity 
                            })),
                            staff: this.selectedStaffDetails.map(s => ({
                                employeeId: s.id,
                                role: s.role,
                                hasCommission: s.hasCommission // 主任审批的提成资格
                            })),
                            referrals: this.referralLinks.map(link => ({
                                recommenderId: link.recommender.id,
                                recommendedIds: link.recommended.map(r => r.id)
                            })),
                            operatorId: this.currentUser.id
                        };

                        console.log('派房数据:', JSON.stringify(assignmentData, null, 2));
                        vant.Toast.success('派房成功，请在控制台查看数据');
                        done();
                    } else {
                        done();
                    }
                },

                // --- 新增：推荐关系方法 ---
                openReferralDialog() {
                    this.tempRecommenderId = null;
                    this.tempRecommendedIds = [];
                    this.showReferralDialog = true;
                },
                onRecommenderChange(recommenderId) {
                    const index = this.tempRecommendedIds.indexOf(recommenderId);
                    if (index > -1) {
                        this.tempRecommendedIds.splice(index, 1);
                    }
                },
                confirmReferral() {
                    if (!this.tempRecommenderId) {
                        vant.Toast('请选择推荐人');
                        return;
                    }
                    if (this.tempRecommendedIds.length === 0) {
                        vant.Toast('请选择被推荐人');
                        return;
                    }
                    const recommender = this.selectedStaffDetails.find(s => s.id === this.tempRecommenderId);
                    const recommended = this.selectedStaffDetails.filter(s => this.tempRecommendedIds.includes(s.id));
                    
                    this.referralLinks.push({
                        recommender: { id: recommender.id, name: recommender.name },
                        recommended: recommended.map(r => ({ id: r.id, name: r.name }))
                    });
                },
                removeReferralLink(index) {
                    this.referralLinks.splice(index, 1);
                },
                updateStaffRole(staffId, role) {
                    const staff = this.staffList.find(s => s.id === staffId);
                    if (staff) {
                        staff.role = role;
                    }
                },
                addOrderItem() {
                    this.orderItems.push({
                        productId: null,
                        productName: '',
                        price: 0,
                        quantity: 1,
                        is_commissionable: false
                    });
                },
                removeOrderItem(index) {
                    this.orderItems.splice(index, 1);
                },
                onProductConfirm(value) {
                    const product = this.products.find(p => p.name === value);
                    if (product) {
                        const currentItem = this.orderItems[this.currentOrderItemIndex];
                        currentItem.productId = product.id;
                        currentItem.productName = product.name;
                        currentItem.price = product.price;
                        currentItem.is_commissionable = product.is_commissionable;
                    }
                    this.showProductPicker = false;
                }
            }
        });
    </script>
</body>
</html>
