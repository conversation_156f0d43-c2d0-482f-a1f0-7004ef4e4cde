<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工选择与推荐关系弹窗</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vant@2.12/lib/index.css"/>
    <script src="https://cdn.jsdelivr.net/npm/vant@2.12/lib/vant.min.js"></script>
    <style>
        body {
            font-family: sans-serif;
            background-color: #f7f8fa;
        }
        .app-container {
            max-width: 600px;
            margin: 20px auto;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
        }
        .staff-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            border-bottom: 1px solid #ebedf0;
        }
        .action-button {
            margin-top: 20px;
        }
        .dialog-field {
            padding: 16px;
            max-height: 60vh;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <!-- 员工列表 -->
        <h4>选择参与人员</h4>
        <van-checkbox-group v-model="selectedStaff">
            <div class="staff-item" v-for="staff in staffList" :key="staff.id">
                <van-checkbox :name="staff.id" shape="square">
                    {{ staff.name }} ({{ staff.position }})
                </van-checkbox>
            </div>
        </van-checkbox-group>

        <!-- 操作按钮 -->
        <van-button 
            class="action-button"
            type="info" 
            block
            @click="openReferralDialog" 
            :disabled="!canSetReferral"
        >
            设置推荐关系 (已选 {{ selectedStaff.length }} 人)
        </van-button>

        <!-- 推荐关系显示 -->
        <div v-if="referralLinks.length > 0" style="padding: 16px 0;">
            <van-divider>已设置推荐关系</van-divider>
            <van-tag
                v-for="(link, index) in referralLinks"
                :key="index"
                closeable
                size="medium"
                type="primary"
                @close="removeReferralLink(index)"
                style="margin-right: 5px; margin-bottom: 5px;"
            >
                {{ link.recommender.name }} → {{ link.recommended.map(r => r.name).join(', ') }}
            </van-tag>
        </div>

        <!-- 推荐关系设置弹窗 -->
        <van-dialog
            v-model="showReferralDialog"
            title="设置推荐关系"
            show-cancel-button
            @confirm="confirmReferral"
        >
            <div class="dialog-field">
                <van-field name="recommenderRadio" label="选择推荐人">
                    <template #input>
                        <van-radio-group v-model="tempRecommenderId" @change="onRecommenderChange">
                            <van-radio 
                                v-for="staff in selectedStaffDetails" 
                                :key="'radio_'+staff.id" 
                                :name="staff.id" 
                                style="margin-bottom: 8px;"
                            >
                                {{ staff.name }}
                            </van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                
                <van-divider />

                <van-field name="recommendedCheckbox" label="选择被推荐人">
                    <template #input>
                        <van-checkbox-group v-model="tempRecommendedIds">
                            <van-checkbox 
                                v-for="staff in selectedStaffDetails" 
                                :key="'checkbox_'+staff.id" 
                                :name="staff.id" 
                                :disabled="staff.id === tempRecommenderId"
                                shape="square"
                                style="margin-bottom: 8px;"
                            >
                                {{ staff.name }}
                            </van-checkbox>
                        </van-checkbox-group>
                    </template>
                </van-field>
            </div>
        </van-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    selectedStaff: [], // 选中的员工ID
                    staffList: [],
                    referralLinks: [], // 存储最终的推荐关系
                    showReferralDialog: false,
                    tempRecommenderId: null, // 弹窗中临时的推荐人ID
                    tempRecommendedIds: [], // 弹窗中临时的被推荐人ID数组
                }
            },
            created() {
                this.fetchStaffList();
            },
            computed: {
                selectedStaffDetails() {
                    return this.staffList.filter(s => this.selectedStaff.includes(s.id));
                },
                canSetReferral() {
                    return this.selectedStaff.length >= 2;
                }
            },
            methods: {
                fetchStaffList() {
                    this.staffList = [
                        { id: 1, name: '张三', position: '专员' },
                        { id: 2, name: '李四', position: '主任' },
                        { id: 3, name: '王五', position: '专员' },
                        { id: 4, name: '赵六', position: '专员' },
                        { id: 5, name: '钱七', position: '专员' },
                        { id: 6, name: '孙八', position: '非专员' },
                    ];
                },
                openReferralDialog() {
                    // 重置弹窗内的临时数据
                    this.tempRecommenderId = null;
                    this.tempRecommendedIds = [];
                    this.showReferralDialog = true;
                },
                onRecommenderChange(recommenderId) {
                    // 如果推荐人被选为被推荐人，则从被推荐人中移除
                    const index = this.tempRecommendedIds.indexOf(recommenderId);
                    if (index > -1) {
                        this.tempRecommendedIds.splice(index, 1);
                    }
                },
                confirmReferral() {
                    if (!this.tempRecommenderId) {
                        vant.Toast('请选择推荐人');
                        return;
                    }
                    if (this.tempRecommendedIds.length === 0) {
                        vant.Toast('请选择被推荐人');
                        return;
                    }
                    const recommender = this.selectedStaffDetails.find(s => s.id === this.tempRecommenderId);
                    const recommended = this.selectedStaffDetails.filter(s => this.tempRecommendedIds.includes(s.id));
                    
                    // 添加新的推荐关系
                    this.referralLinks.push({
                        recommender: { id: recommender.id, name: recommender.name },
                        recommended: recommended.map(r => ({ id: r.id, name: r.name }))
                    });
                    
                    vant.Toast.success('关系已添加');
                },
                removeReferralLink(index) {
                    this.referralLinks.splice(index, 1);
                }
            }
        });
    </script>
</body>
</html>