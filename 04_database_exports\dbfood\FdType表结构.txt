"Column_name"	"Type"	"Computed"	"Length"	"Prec"	"Scale"	"Nullable"	"TrimTrailingBlanks"	"FixedLenNullInSource"	"Collation"
"FtNo"	"varchar"	"no"	"2"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"FtCName"	"nvarchar"	"no"	"100"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"FtEName"	"varchar"	"no"	"30"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"PrnType"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"FPrnTypeA"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"FPrnIndexA"	"varchar"	"no"	"1"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"DPrnTypeA"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"DPrnIndexA"	"varchar"	"no"	"1"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"FPrnTypeB"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"FPrnIndexB"	"varchar"	"no"	"1"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"DPrnTypeB"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"DPrnIndexB"	"varchar"	"no"	"1"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"msrepl_tran_version"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"number"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"cashtype"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"iPadShow"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"iPadSort"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"iPadFtNo"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"iPadFtCName"	"nvarchar"	"no"	"100"	"     "	"     "	"yes"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
