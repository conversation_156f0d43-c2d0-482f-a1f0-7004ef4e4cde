/*
 Navicat Premium Dump SQL

 Source Server         : 名堂
 Source Server Type    : SQL Server
 Source Server Version : 11002100 (11.00.2100)
 Source Host           : *************:1433
 Source Catalog        : Dbfood
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11002100 (11.00.2100)
 File Encoding         : 65001

 Date: 25/07/2025 10:32:11
*/


-- ----------------------------
-- Table structure for Food
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Food]') AND type IN ('U'))
	DROP TABLE [dbo].[Food]
GO

CREATE TABLE [dbo].[Food] (
  [FdNo] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [FtNo] varchar(2) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [FdCName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [FdEName] varchar(40) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [FdQty] smallint  NULL,
  [FdLack] bit DEFAULT 0 NOT NULL,
  [FdPrice1] int DEFAULT -1 NOT NULL,
  [FdPrice2] int DEFAULT -1 NOT NULL,
  [FdPrice3] int DEFAULT -1 NOT NULL,
  [FdPrice4] int DEFAULT -1 NOT NULL,
  [FdPrice5] int DEFAULT -1 NOT NULL,
  [FdPrice6] int DEFAULT -1 NOT NULL,
  [FdPrice7] int DEFAULT -1 NOT NULL,
  [FdPrice8] int DEFAULT -1 NOT NULL,
  [FdPrice9] int DEFAULT -1 NOT NULL,
  [FdPrice10] int DEFAULT -1 NOT NULL,
  [FdSPrice1] int DEFAULT -1 NOT NULL,
  [FdSPrice2] int DEFAULT -1 NOT NULL,
  [FdSPrice3] int DEFAULT -1 NOT NULL,
  [FdSPrice4] int DEFAULT -1 NOT NULL,
  [FdSPrice5] int DEFAULT -1 NOT NULL,
  [FdSPrice6] int DEFAULT -1 NOT NULL,
  [FdSPrice7] int DEFAULT -1 NOT NULL,
  [FdSPrice8] int DEFAULT -1 NOT NULL,
  [FdSPrice9] int DEFAULT -1 NOT NULL,
  [FdSPrice10] int DEFAULT -1 NOT NULL,
  [FdMemberPrice1] int DEFAULT 0 NOT NULL,
  [FdMemberPrice2] int DEFAULT 0 NOT NULL,
  [FdMemberPrice3] int DEFAULT 0 NOT NULL,
  [FdMemberPrice4] int DEFAULT 0 NOT NULL,
  [FdMemberPrice5] int DEFAULT 0 NOT NULL,
  [FdMemberPrice6] int DEFAULT 0 NOT NULL,
  [FdMemberPrice7] int DEFAULT 0 NOT NULL,
  [FdMemberPrice8] int DEFAULT 0 NOT NULL,
  [FdMemberPrice9] int DEFAULT 0 NOT NULL,
  [FdMemberPrice10] int DEFAULT 0 NOT NULL,
  [InRmCost1] bit DEFAULT 1 NOT NULL,
  [InRmCost2] bit DEFAULT 1 NOT NULL,
  [InRmCost3] bit DEFAULT 1 NOT NULL,
  [InRmCost4] bit DEFAULT 1 NOT NULL,
  [InRmCost5] bit DEFAULT 1 NOT NULL,
  [InRmCost6] bit DEFAULT 1 NOT NULL,
  [InRmCost7] bit DEFAULT 1 NOT NULL,
  [InRmCost8] bit DEFAULT 1 NOT NULL,
  [InRmCost9] bit DEFAULT 1 NOT NULL,
  [InRmCost10] bit DEFAULT 1 NOT NULL,
  [ChangePrice1] bit DEFAULT 0 NOT NULL,
  [ChangePrice2] bit DEFAULT 0 NOT NULL,
  [ChangePrice3] bit DEFAULT 0 NOT NULL,
  [ChangePrice4] bit DEFAULT 0 NOT NULL,
  [ChangePrice5] bit DEFAULT 0 NOT NULL,
  [ChangePrice6] bit DEFAULT 0 NOT NULL,
  [ChangePrice7] bit DEFAULT 0 NOT NULL,
  [ChangePrice8] bit DEFAULT 0 NOT NULL,
  [ChangePrice9] bit DEFAULT 0 NOT NULL,
  [ChangePrice10] bit DEFAULT 0 NOT NULL,
  [MiniDisc1] int DEFAULT 0 NOT NULL,
  [MiniDisc2] int DEFAULT 0 NOT NULL,
  [MiniDisc3] int DEFAULT 0 NOT NULL,
  [MiniDisc4] int DEFAULT 0 NOT NULL,
  [MiniDisc5] int DEFAULT 0 NOT NULL,
  [MiniDisc6] int DEFAULT 0 NOT NULL,
  [MiniDisc7] int DEFAULT 0 NOT NULL,
  [MiniDisc8] int DEFAULT 0 NOT NULL,
  [MiniDisc9] int DEFAULT 0 NOT NULL,
  [MiniDisc10] int DEFAULT 0 NOT NULL,
  [Serv1] bit DEFAULT 1 NOT NULL,
  [Serv2] bit DEFAULT 1 NOT NULL,
  [Serv3] bit DEFAULT 1 NOT NULL,
  [Serv4] bit DEFAULT 1 NOT NULL,
  [Serv5] bit DEFAULT 1 NOT NULL,
  [Serv6] bit DEFAULT 1 NOT NULL,
  [Serv7] bit DEFAULT 1 NOT NULL,
  [Serv8] bit DEFAULT 1 NOT NULL,
  [Serv9] bit DEFAULT 1 NOT NULL,
  [Serv10] bit DEFAULT 1 NOT NULL,
  [CanDonate1] bit DEFAULT 1 NOT NULL,
  [CanDonate2] bit DEFAULT 1 NOT NULL,
  [CanDonate3] bit DEFAULT 1 NOT NULL,
  [CanDonate4] bit DEFAULT 1 NOT NULL,
  [CanDonate5] bit DEFAULT 1 NOT NULL,
  [CanDonate6] bit DEFAULT 1 NOT NULL,
  [CanDonate7] bit DEFAULT 1 NOT NULL,
  [CanDonate8] bit DEFAULT 1 NOT NULL,
  [CanDonate9] bit DEFAULT 1 NOT NULL,
  [CanDonate10] bit DEFAULT 1 NOT NULL,
  [Specs] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [Discount] int DEFAULT 0 NOT NULL,
  [GiveGifts] nvarchar(200) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [FoodSetType] int DEFAULT 0 NOT NULL,
  [Snack] int DEFAULT 0 NOT NULL,
  [SoftDrinks] int DEFAULT 0 NOT NULL,
  [MixedDrinks] int DEFAULT 0 NOT NULL,
  [iPadFtNo] nvarchar(2) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [iPadUnit] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [CommissionPrice] int DEFAULT 0 NOT NULL,
  [msrepl_tran_version] uniqueidentifier DEFAULT newid() NOT NULL,
  [CreateTime] datetime DEFAULT getdate() NULL
)
GO

ALTER TABLE [dbo].[Food] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table Food
-- ----------------------------
ALTER TABLE [dbo].[Food] ADD CONSTRAINT [PK_Food] PRIMARY KEY CLUSTERED ([FdNo])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Foreign Keys structure for table Food
-- ----------------------------
ALTER TABLE [dbo].[Food] ADD CONSTRAINT [FK_Food_FdType] FOREIGN KEY ([FtNo]) REFERENCES [dbo].[FdType] ([FtNo]) ON DELETE CASCADE ON UPDATE CASCADE
GO

