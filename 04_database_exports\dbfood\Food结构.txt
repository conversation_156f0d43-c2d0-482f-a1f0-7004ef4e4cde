"Column_name"	"Type"	"Computed"	"Length"	"Prec"	"Scale"	"Nullable"	"TrimTrailingBlanks"	"FixedLenNullInSource"	"Collation"
"FdNo"	"varchar"	"no"	"5"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"FtNo"	"varchar"	"no"	"2"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"FdCName"	"nvarchar"	"no"	"100"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"FdEName"	"varchar"	"no"	"40"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"FdQty"	"smallint"	"no"	"2"	"5    "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"FdLack"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"FdPrice1"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice2"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice3"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice4"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice5"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice6"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice7"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice8"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice9"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdPrice10"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice1"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice2"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice3"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice4"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice5"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice6"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice7"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice8"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice9"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdSPrice10"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice1"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice2"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice3"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice4"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice5"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice6"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice7"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice8"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice9"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"FdMemberPrice10"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"InRmCost1"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost2"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost3"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost4"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost5"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost6"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost7"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost8"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost9"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"InRmCost10"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice1"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice2"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice3"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice4"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice5"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice6"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice7"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice8"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice9"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"ChangePrice10"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc1"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc2"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc3"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc4"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc5"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc6"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc7"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc8"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc9"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MiniDisc10"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"Serv1"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv2"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv3"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv4"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv5"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv6"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv7"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv8"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv9"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Serv10"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate1"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate2"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate3"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate4"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate5"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate6"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate7"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate8"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate9"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanDonate10"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"msrepl_tran_version"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Specs"	"nvarchar"	"no"	"100"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"Discount"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"GiveGifts"	"nvarchar"	"no"	"400"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"FoodSetType"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"Snack"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"SoftDrinks"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MixedDrinks"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"iPadFtNo"	"nvarchar"	"no"	"4"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"iPadUnit"	"nvarchar"	"no"	"20"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"CommissionPrice"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
