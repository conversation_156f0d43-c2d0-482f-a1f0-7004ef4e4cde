"Column_name"	"Type"	"Computed"	"Length"	"Prec"	"Scale"	"Nullable"	"TrimTrailingBlanks"	"FixedLenNullInSource"	"Collation"
"MobileOrderKey"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"MobileOrderTitleKey"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"FdNo"	"nvarchar"	"no"	"100"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"FdQty"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"CashType"	"nvarchar"	"no"	"20"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"FdPrice"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"Ai"	"nvarchar"	"no"	"100"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"PackageNo"	"nvarchar"	"no"	"100"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"PackageGiveNo"	"nvarchar"	"no"	"100"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
