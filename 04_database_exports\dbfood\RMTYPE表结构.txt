"Column_name"	"Type"	"Computed"	"Length"	"Prec"	"Scale"	"Nullable"	"TrimTrailingBlanks"	"FixedLenNullInSource"	"Collation"
"RtNo"	"varchar"	"no"	"2"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"RtName"	"nvarchar"	"no"	"100"	"     "	"     "	"yes"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"MaxP"	"smallint"	"no"	"2"	"5    "	"0    "	"no"	"(n/a)"	"(n/a)"	
"NoServ"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"AccType"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"RmPrice"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"SRmPrice"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"WeekEndPrice"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"RealRoom"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"CanAutoZD"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"MaxZDRate"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"RmCostType"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"ServRate"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"RmPrice_Person"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"SRmPrice_Person"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"WeekEndPrice_Person"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"RmPrice_PerUnit"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"SRmPrice_PerUnit"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"WeekEndPrice_PerUnit"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"UnitMinutes"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MinMinutesOfTimeZone"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"MinMinutesOfTimeUnit"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"SetClearing"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"rowguid"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"msrepl_tran_version"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
