"Column_name"	"Type"	"Computed"	"Length"	"Prec"	"Scale"	"Nullable"	"TrimTrailingBlanks"	"FixedLenNullInSource"	"Collation"
"RmNo"	"varchar"	"no"	"4"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"RmName"	"nvarchar"	"no"	"100"	"     "	"     "	"yes"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"RtNo"	"varchar"	"no"	"2"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"AreaNo"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"InvNo"	"varchar"	"no"	"9"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"PriceNo"	"varchar"	"no"	"2"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"WorkDate"	"varchar"	"no"	"8"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"RsPos"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"RmStatus"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"IsSDate"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Rem"	"varchar"	"no"	"50"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"BookDate"	"varchar"	"no"	"8"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"BookTime"	"varchar"	"no"	"5"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"InDate"	"varchar"	"no"	"8"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"InTime"	"varchar"	"no"	"5"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"InNumbers"	"smallint"	"no"	"2"	"5    "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"OpenUserId"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"AccUserId"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"AccDate"	"varchar"	"no"	"8"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"AccTime"	"varchar"	"no"	"5"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"ContinueUserId"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"ContinueTime"	"varchar"	"no"	"13"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"MemberNo"	"varchar"	"no"	"12"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"CustName"	"varchar"	"no"	"10"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"OrderUserId"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"DiscRate"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"Serv"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"FdCost"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"RmCost"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"Disc"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"ZD"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"BeerZD"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"BeerCash"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"Tax"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"MorePayed"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"Tot"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"WC"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Dance"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"PrnFIndex"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"PrnDIndex"	"varchar"	"no"	"1"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"PInvCount"	"smallint"	"no"	"2"	"5    "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"FromRmNo"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"OpenCount"	"smallint"	"no"	"2"	"5    "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"ForceNoServ"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"Tag"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"FixedDisc"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"CarId"	"varchar"	"no"	"15"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"FdCost_InRmCost"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"FdCost_NotInRmCost"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"MembDisc"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"MembCard"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"Card_MNo"	"varchar"	"no"	"10"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"CardAmount"	"int"	"no"	"4"	"10   "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"CloseTime"	"varchar"	"no"	"16"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"CallAccount"	"bit"	"no"	"1"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"BadReason"	"varchar"	"no"	"30"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"BadUserId"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"AutoZD"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"rowguid"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"msrepl_tran_version"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
"th_RmCost"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"AccountManagerID"	"nvarchar"	"no"	"20"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"AccountManagerCName"	"nvarchar"	"no"	"20"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"CustomerServiceManagerID"	"nvarchar"	"no"	"20"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"CustomerServiceManagerName"	"nvarchar"	"no"	"20"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	"Chinese_PRC_Stroke_CI_AS"
"FromTime"	"varchar"	"no"	"5"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"ToTime"	"varchar"	"no"	"5"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
