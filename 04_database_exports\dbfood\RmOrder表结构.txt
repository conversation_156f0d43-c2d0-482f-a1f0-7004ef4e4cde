"Column_name"	"Type"	"Computed"	"Length"	"Prec"	"Scale"	"Nullable"	"TrimTrailingBlanks"	"FixedLenNullInSource"	"Collation"
"iKey"	"int"	"no"	"4"	"10   "	"0    "	"no"	"(n/a)"	"(n/a)"	
"RmNo"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"WorkDate"	"varchar"	"no"	"8"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"MemberNo"	"varchar"	"no"	"7"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"CustName"	"varchar"	"no"	"10"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"Sex"	"varchar"	"no"	"1"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"Qty"	"smallint"	"no"	"2"	"5    "	"0    "	"yes"	"(n/a)"	"(n/a)"	
"Tel"	"varchar"	"no"	"20"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"OrderUserId"	"varchar"	"no"	"4"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"ComeDate"	"varchar"	"no"	"8"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"ComeTime"	"varchar"	"no"	"5"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"BookDate"	"varchar"	"no"	"8"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"BookTime"	"varchar"	"no"	"5"	"     "	"     "	"no"	"no"	"no"	"Chinese_PRC_Stroke_CI_AS"
"OrderMem"	"varchar"	"no"	"40"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"UserId"	"varchar"	"no"	"4"	"     "	"     "	"yes"	"no"	"yes"	"Chinese_PRC_Stroke_CI_AS"
"msrepl_tran_version"	"uniqueidentifier"	"no"	"16"	"     "	"     "	"no"	"(n/a)"	"(n/a)"	
