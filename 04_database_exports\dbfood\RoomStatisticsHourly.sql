/*
 Navicat Premium Dump SQL

 Source Server         : 名堂
 Source Server Type    : SQL Server
 Source Server Version : 11002100 (11.00.2100)
 Source Host           : *************:1433
 Source Catalog        : Dbfood
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11002100 (11.00.2100)
 File Encoding         : 65001

 Date: 11/08/2025 16:30:17
*/


-- ----------------------------
-- Table structure for RoomStatisticsHourly
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RoomStatisticsHourly]') AND type IN ('U'))
	DROP TABLE [dbo].[RoomStatisticsHourly]
GO

CREATE TABLE [dbo].[RoomStatisticsHourly] (
  [LogID] int  IDENTITY(1,1) NOT NULL,
  [LogTime] datetime DEFAULT getdate() NOT NULL,
  [TotalRoomsBeforeFilter] int  NULL,
  [ValidRoomsCount] int  NULL,
  [BadRoomsCount] int  NULL,
  [AvailableRoomsCount] int  NULL,
  [Status_A_Count] int  NULL,
  [Status_B_Count] int  NULL,
  [Status_E_Count] int  NULL,
  [Status_U_Count] int  NULL
)
GO

ALTER TABLE [dbo].[RoomStatisticsHourly] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for RoomStatisticsHourly
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[RoomStatisticsHourly]', RESEED, 99)
GO


-- ----------------------------
-- Primary Key structure for table RoomStatisticsHourly
-- ----------------------------
ALTER TABLE [dbo].[RoomStatisticsHourly] ADD CONSTRAINT [PK__RoomStat__5E5499A8F178B5D1] PRIMARY KEY CLUSTERED ([LogID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

