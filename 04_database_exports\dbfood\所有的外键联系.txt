"table_name"	"column_name"	"referenced_table_name"	"referenced_column_name"
"Food"	"FtNo"	"FdType"	"FtNo"
"FdUserRights"	"UserId"	"FdUser"	"UserId"
"HotFdType"	"FtNo"	"FdType"	"FtNo"
"Room"	"RtNo"	"RmType"	"RtNo"
"HotFood"	"FdNo"	"Food"	"FdNo"
"MGradeFdDisc"	"MGradeId"	"MembSet"	"MGradeID"
"MGradeFdDisc"	"FdNo"	"Food"	"FdNo"
"Member"	"MGradeID"	"MembSet"	"MGradeID"
"RmOrder"	"RmNo"	"Room"	"RmNo"
"RtAuto"	"RtNo"	"RmType"	"RtNo"
"RtAuto"	"FdNo"	"Food"	"FdNo"
"UserAmountDetail"	"iKey"	"UserAmount"	"iKey"
"AddItem"	"ATNo"	"AiType"	"ATNo"
"RmFtPrnIndex"	"RmNo"	"Room"	"RmNo"
"RmFtPrnIndex"	"FtNo"	"FdType"	"FtNo"
"DeptBanSet"	"DeptId"	"Dept"	"DeptId"
"FdCashBak"	"InvNo"	"FdInv"	"InvNo"
"FdCashBak_Bak"	"InvNo"	"FdInv_Bak"	"InvNo"
"RtAutoZD"	"RtNo"	"RmType"	"RtNo"
"RtAutoZD"	"FdNo"	"Food"	"FdNo"
"FdInv_ExchangeLog"	"InvNo"	"FdInv"	"InvNo"
"FdCashBak_B"	"InvNo"	"FdInv_B"	"InvNo"
"FPrnData"	"RmNo"	"Room"	"RmNo"
"FdTimePrice"	"FdNo"	"Food"	"FdNo"
"FdTimePrice"	"PriceNo"	"PriceNo"	"PriceNo"
"UserZDItemDetail"	"ItemId"	"UserZDItem"	"ItemId"
"UserZDItemDetail"	"FdNo"	"Food"	"FdNo"
"UserFtZD"	"UserId"	"FdUser"	"UserId"
"UserFtZD"	"FtNo"	"FdType"	"FtNo"
"UserZDSet"	"UserId"	"FdUser"	"UserId"
"UserZDSet"	"ItemId"	"UserZDItem"	"ItemId"
"AmountLog"	"UserId"	"FdUser"	"UserId"
