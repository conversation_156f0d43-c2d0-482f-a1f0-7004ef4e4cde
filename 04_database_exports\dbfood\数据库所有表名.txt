"AddItem"
"AiType"
"AmountLog"
"CarLeaveLog"
"Cashier"
"ClearDataLog"
"DepositInfo"
"DepositInfo_Temp"
"Dept"
"DeptBanSet"
"dtproperties"
"EmpGift_CustRecord"
"EmpGift_Item"
"EmpGift_Record"
"FdCash"
"FdCashBak"
"FdCashBak_B"
"FdCashBak_Bak"
"FdCashOrder"
"FdDetType"
"FdImage"
"FdInv"
"FdInv_B"
"FdInv_Bak"
"FdInv_ExchangeLog"
"FdInvCashItem"
"FdInvDesc"
"FdTicket"
"FdTimePrice"
"FdTimeZone"
"FdType"
"FdUser"
"FdUserGrade"
"FdUserRights"
"festivaltime"
"Food"
"FoodCal"
"FoodLabel"
"FoodOrderM"
"FPrn"
"FPrnData"
"FPrnData_Bak"
"FtInfo"
"GDDB20Info"
"GDDBInfo"
"GiftAccount"
"GiftAccountOperationRecord"
"GiftAccountSceneAllocation"
"GiftRole"
"GiftScene"
"GiftSceneEquityConfig"
"GiftSceneRoleBinding"
"HappyRab"
"Holiday"
"HotFdType"
"HotFood"
"Inv_TimeSection"
"LanId"
"LanString"
"LastInvNo"
"LastRefNo"
"Limit_ConfigInfo"
"LogInfo"
"meal_distribution_info"
"Meal_info"
"MembAmountEditLog"
"Member"
"MemberCheckoutInfo"
"MemberGiveSet"
"MembSet"
"MGradeFdDisc"
"MobileFdGive"
"MobileFood"
"MobileFoodDisc"
"MobileFtType"
"MobilePackGive"
"MobilOrderItem"
"MobilOrderTitle"
"MobilUserOrderTitle"
"NewFdGive"
"NewFdType"
"NewFdTypeLink"
"NewMemberLog"
"OO"
"ParamSet"
"pre_order"
"PreOrderSendMsgInfo"
"PriceNo"
"QrInfo"
"RefToZDLog"
"Report_cg"
"Report_hs"
"RightSet"
"RmAccountInfo"
"RmArea"
"RmClearLog"
"RmCloseInfo"
"RmCloseInfo_Collect"
"RmExchangeDetail"
"RmExchangeLog"
"RmFtPrnIndex"
"RmOrder"
"RmOrderDelLog"
"RmOrderLog"
"rmslocal_RmLock"
"RmType"
"Room"
"Room_Consume_Number"
"Room_Consume_Number_Itme"
"RoomCloseLabel"
"RoomCloseLabelTypeDetail"
"RoomExtend"
"RoomLabel"
"RtAuto"
"RtAutoZD"
"RtTimePrice"
"S_AccType"
"S_CashItem"
"S_PrnType"
"S_RmStatus"
"SceneRole_Config"
"SceneRole_Config_Extra"
"SDate"
"Sh"
"ShareSetInfo"
"Sheet1"
"StarInfo"
"tb"
"TestTable"
"TimeZone"
"triggerRecord"
"tt"
"UserAmount"
"UserAmountDetail"
"UserFtZD"
"UserInfo_Binding"
"UserIO"
"UserZDItem"
"UserZDItemDetail"
"UserZDSet"
"Vesa"
"WebOrderTable"
"WeChatFoodOrderMsg"
"weekend"
"WindTicket"
"wx_shopmall_worktime"
"wxpay_FdCashOrder"
"wxPayCheckInfo"
"wxPayInfo"
"wxPayInfo_Del"
