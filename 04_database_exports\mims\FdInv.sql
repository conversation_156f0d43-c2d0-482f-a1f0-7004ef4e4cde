/*
 Navicat Premium Dump SQL

 Source Server         : 测试5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : Dbfood
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 09/07/2025 11:27:40
*/


-- ----------------------------
-- Table structure for FdInv
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[FdInv]') AND type IN ('U'))
	DROP TABLE [dbo].[FdInv]
GO

CREATE TABLE [dbo].[FdInv] (
  [InvNo] varchar(9) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [RmNo] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [BookDate] varchar(8) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [BookTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [InDate] varchar(8) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [InTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [InNumbers] smallint DEFAULT 1 NOT NULL,
  [MemberNo] varchar(12) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [MemberName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [OpenUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [OpenUserName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [AccUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [AccUserName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [AccDate] varchar(8) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [AccTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [CustName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [OrderUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [OrderUserName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [DiscRate] int DEFAULT 0 NOT NULL,
  [OutDate] varchar(8) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [OutTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CloseUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [CloseUserName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [Rem] varchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [FdCost] int  NOT NULL,
  [RmCost] int  NOT NULL,
  [ZD] int  NULL,
  [BeerZD] int  NULL,
  [BeerCash] int  NULL,
  [Serv] int DEFAULT 0 NOT NULL,
  [Disc] int DEFAULT 0 NOT NULL,
  [Tax] int  NULL,
  [Tot] int  NOT NULL,
  [Cash] int  NOT NULL,
  [Cash_Targ] int  NULL,
  [Vesa] int  NOT NULL,
  [VesaName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [Vesa_Targ] int  NULL,
  [VesaName_Targ] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [GZ] int DEFAULT '0' NOT NULL,
  [AccOkZD] int DEFAULT '0' NOT NULL,
  [MembCard] int DEFAULT 0 NOT NULL,
  [NoPayed] int DEFAULT 0 NOT NULL,
  [WorkDate] varchar(8) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Void] bit DEFAULT 0 NOT NULL,
  [VoidUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [VoidUserName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [GZOk] bit DEFAULT 0 NOT NULL,
  [GZOkDate] varchar(8) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [GZOkTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [MorePayed] int DEFAULT 0 NOT NULL,
  [Booked] bit DEFAULT 0 NOT NULL,
  [CardConsumeMNo] varchar(7) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [CardConsumeMName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [VesaNo] varchar(30) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [VesaNo_Targ] varchar(30) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [GZName] varchar(30) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [FixedDisc] int DEFAULT 0 NOT NULL,
  [msrepl_tran_version] uniqueidentifier DEFAULT newid() NOT NULL,
  [th_RmCost] int DEFAULT 0 NOT NULL,
  [AccountManagerID] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [AccountManagerCName] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [CustomerServiceManagerID] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [CustomerServiceManagerName] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL
)
GO

ALTER TABLE [dbo].[FdInv] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Indexes structure for table FdInv
-- ----------------------------
CREATE NONCLUSTERED INDEX [IX_FdInv_WorkDate]
ON [dbo].[FdInv] (
  [WorkDate] ASC
)
GO

CREATE NONCLUSTERED INDEX [index_InTime]
ON [dbo].[FdInv] (
  [InTime] ASC
)
GO

CREATE NONCLUSTERED INDEX [index_AccTime]
ON [dbo].[FdInv] (
  [AccTime] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table FdInv
-- ----------------------------
ALTER TABLE [dbo].[FdInv] ADD CONSTRAINT [PK_FdInv] PRIMARY KEY NONCLUSTERED ([InvNo])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

