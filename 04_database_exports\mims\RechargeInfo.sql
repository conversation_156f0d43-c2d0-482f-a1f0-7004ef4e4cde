/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : MIMS
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 11/07/2025 15:29:27
*/


-- ----------------------------
-- Table structure for RechargeInfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RechargeInfo]') AND type IN ('U'))
	DROP TABLE [dbo].[RechargeInfo]
GO

CREATE TABLE [dbo].[RechargeInfo] (
  [RechargeKey] uniqueidentifier DEFAULT newid() NOT NULL ROWGUIDCOL,
  [<PERSON><PERSON><PERSON>] uniqueidentifier  NOT NULL,
  [RechargeShopId] int DEFAULT 0 NOT NULL,
  [RechargeType] int DEFAULT 1 NOT NULL,
  [RechargeRmNo] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [InvNo] nvarchar(9) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [RechargeValue] int DEFAULT 0 NOT NULL,
  [RechargeUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [RechargeCheckUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [RechargeDate] datetime DEFAULT getdate() NOT NULL,
  [IsInvo] bit DEFAULT 0 NOT NULL,
  [IsDelete] bit DEFAULT 0 NOT NULL,
  [Val1] int DEFAULT '' NOT NULL,
  [Val2] int DEFAULT '' NOT NULL,
  [Val3] nvarchar(200) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [Val4] nvarchar(200) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [Val6] nvarchar(200) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL
)
GO

ALTER TABLE [dbo].[RechargeInfo] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'4倍充值',
'SCHEMA', N'dbo',
'TABLE', N'RechargeInfo',
'COLUMN', N'Val2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发票登记',
'SCHEMA', N'dbo',
'TABLE', N'RechargeInfo',
'COLUMN', N'Val6'
GO


-- ----------------------------
-- Indexes structure for table RechargeInfo
-- ----------------------------
CREATE NONCLUSTERED INDEX [IX_RechargeInfo_MemberKey]
ON [dbo].[RechargeInfo] (
  [MemberKey] ASC
)
GO


-- ----------------------------
-- Triggers structure for table RechargeInfo
-- ----------------------------
CREATE TRIGGER [dbo].[UpdateMemberInfoSetRechargeTotal]
ON [dbo].[RechargeInfo]
WITH EXECUTE AS CALLER
FOR INSERT, UPDATE
AS
Begin
	declare @RechargeTotal int,@MemberKey uniqueidentifier
	select @MemberKey=MemberKey from Inserted
	--Insert into Test (MemberKey) values (@MemberKey)
	select @RechargeTotal=IsNull(Sum(RechargeValue*RechargeType),0) from RechargeInfo where MemberKey=@MemberKey and IsDelete=0
	update MemberInfo set RechargeTotal=@RechargeTotal where MemberKey=@MemberKey
End
GO

CREATE TRIGGER [dbo].[UpdateMemberInfoSetRechargeTotalInDelete]
ON [dbo].[RechargeInfo]
WITH EXECUTE AS CALLER
FOR DELETE
AS
Begin
	declare @RechargeTotal int,@MemberKey uniqueidentifier
	select @MemberKey=MemberKey from Deleted
	select @RechargeTotal=IsNull(Sum(RechargeValue*RechargeType),0) from RechargeInfo where MemberKey=@MemberKey and IsDelete=0
	update MemberInfo set RechargeTotal=@RechargeTotal where MemberKey=@MemberKey
End
GO


-- ----------------------------
-- Primary Key structure for table RechargeInfo
-- ----------------------------
ALTER TABLE [dbo].[RechargeInfo] ADD CONSTRAINT [PK_RechargeInfo] PRIMARY KEY CLUSTERED ([RechargeKey])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

