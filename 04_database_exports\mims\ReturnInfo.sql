/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : MIMS
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 15/07/2025 14:38:37
*/


-- ----------------------------
-- Table structure for ReturnInfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[ReturnInfo]') AND type IN ('U'))
	DROP TABLE [dbo].[ReturnInfo]
GO

CREATE TABLE [dbo].[ReturnInfo] (
  [ReturnKey] uniqueidentifier DEFAULT newid() NOT NULL ROWGUIDCOL,
  [MemberK<PERSON>] uniqueidentifier  NOT NULL,
  [ReturnShopId] int DEFAULT 0 NOT NULL,
  [ReturnType] int DEFAULT 0 NOT NULL,
  [ReturnRmNo] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [InvNo] nvarchar(9) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [ReturnValue] int DEFAULT 0 NOT NULL,
  [ReturnUserName] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [ReturnDate] datetime DEFAULT getdate() NOT NULL,
  [IsDelete] bit DEFAULT 0 NOT NULL,
  [Val1] int DEFAULT '' NOT NULL,
  [Val2] int DEFAULT '' NOT NULL,
  [Val3] nvarchar(200) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [Val4] nvarchar(200) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [Val6] nvarchar(200) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL
)
GO

ALTER TABLE [dbo].[ReturnInfo] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Indexes structure for table ReturnInfo
-- ----------------------------
CREATE NONCLUSTERED INDEX [IX_ReturnInfo_MemberKey]
ON [dbo].[ReturnInfo] (
  [MemberKey] ASC
)
GO


-- ----------------------------
-- Triggers structure for table ReturnInfo
-- ----------------------------
CREATE TRIGGER [dbo].[TR_ReturnInfo_UpdateMemberTotal]
ON [dbo].[ReturnInfo]
WITH EXECUTE AS CALLER
FOR INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON; -- 提高性能，避免返回不必要的信息

    -- 使用CTE (通用表表达式) 获取所有受影响的会员ID，并去重
    -- 无论是插入、更新还是删除，所有相关的MemberKey都会被收集
    WITH AffectedMembers AS (
        SELECT MemberKey FROM inserted -- 包含新插入或更新后的行
        UNION
        SELECT MemberKey FROM deleted  -- 包含被删除或更新前的行
    )
    -- 批量更新 MemberInfo 表中所有受影响会员的 ReturnTotal
    UPDATE mi
    SET
        mi.ReturnTotal = ISNULL((
            -- 子查询：重新计算该会员在 ReturnInfo 表中所有未删除流水的总和
            SELECT SUM(ri.ReturnValue * ri.ReturnType)
            FROM dbo.ReturnInfo AS ri
            WHERE ri.MemberKey = am.MemberKey AND ri.IsDelete = 0
        ), 0)
    FROM
        dbo.MemberInfo AS mi
    JOIN
        AffectedMembers AS am ON mi.MemberKey = am.MemberKey;

    PRINT '触发器 TR_ReturnInfo_UpdateMemberTotal 已成功执行并更新MemberInfo。';
END
GO


-- ----------------------------
-- Primary Key structure for table ReturnInfo
-- ----------------------------
ALTER TABLE [dbo].[ReturnInfo] ADD CONSTRAINT [PK_ReturnInfo] PRIMARY KEY CLUSTERED ([ReturnKey])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

