/*
 Navicat Premium Dump SQL

 Source Server         : 测试5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : Dbfood
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 09/07/2025 11:28:08
*/


-- ----------------------------
-- Table structure for RmCloseInfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RmCloseInfo]') AND type IN ('U'))
	DROP TABLE [dbo].[RmCloseInfo]
GO

CREATE TABLE [dbo].[RmCloseInfo] (
  [InvNo] nvarchar(9) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Cash] int  NOT NULL,
  [Cash_Targ] int  NOT NULL,
  [Vesa] int  NOT NULL,
  [VesaName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [VesaNo] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Vesa_Targ] int  NOT NULL,
  [VesaName_Targ] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [VesaNo_Targ] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [GZ] int  NOT NULL,
  [GZName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [AccOkZD] int  NOT NULL,
  [ZDName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [NoPayed] int  NOT NULL,
  [NoPayedName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Check] int  NOT NULL,
  [CheckName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [WXPay] int  NOT NULL,
  [OpenId] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [wx_out_trade_no] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [AliPay] int  NOT NULL,
  [user_id] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Ali_out_trade_no] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [MTPay] int  NOT NULL,
  [MTPayNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [DZPay] int  NOT NULL,
  [DZPayNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [NMPay] int  NOT NULL,
  [NMPayNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Coupon] int  NOT NULL,
  [CouponName] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [RechargeAccount] int  NOT NULL,
  [RechargeMemberCardNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [ReturnAccount] int  NOT NULL,
  [ReturnMemberCardNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CloseDatetime] datetime DEFAULT getdate() NOT NULL,
  [MemberKey] uniqueidentifier  NOT NULL,
  [CloseUserName] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [CloseUserId] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [WechatDeposit] int DEFAULT 0 NOT NULL,
  [WechatShopping] int DEFAULT 0 NOT NULL,
  [Tot] int DEFAULT 0 NOT NULL
)
GO

ALTER TABLE [dbo].[RmCloseInfo] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Indexes structure for table RmCloseInfo
-- ----------------------------
CREATE NONCLUSTERED INDEX [index_InvNo]
ON [dbo].[RmCloseInfo] (
  [InvNo] ASC
)
GO

