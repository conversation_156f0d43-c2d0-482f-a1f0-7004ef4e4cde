/*
 Navicat Premium Dump SQL

 Source Server         : 名堂
 Source Server Type    : SQL Server
 Source Server Version : 11002100 (11.00.2100)
 Source Host           : *************:1433
 Source Catalog        : Dbfood
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11002100 (11.00.2100)
 File Encoding         : 65001

 Date: 11/07/2025 11:00:37
*/


-- ----------------------------
-- Table structure for wxPayInfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[wxPayInfo]') AND type IN ('U'))
	DROP TABLE [dbo].[wxPayInfo]
GO

CREATE TABLE [dbo].[wxPayInfo] (
  [iKey] int  IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  [ShopId] int DEFAULT 0 NOT NULL,
  [RmNo] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [InvNo] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [Tot] int DEFAULT 0 NOT NULL,
  [transaction_id] nvarchar(32) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [out_trade_no] nvarchar(32) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [TranDatetime] datetime DEFAULT getdate() NOT NULL,
  [IsAutoPrnt] int DEFAULT 1 NOT NULL,
  [wxPayTot] int DEFAULT 0 NOT NULL,
  [cash_fee] int DEFAULT 0 NOT NULL,
  [coupon_fee] int DEFAULT 0 NOT NULL,
  [coupon_count] int DEFAULT 0 NOT NULL,
  [PayType] int DEFAULT 0 NOT NULL,
  [th_minus_fee] int DEFAULT 0 NOT NULL,
  [payName] int DEFAULT '0' NOT NULL,
  [RechargeMoney] int DEFAULT 0 NOT NULL,
  [GroupAmount] int  NULL
)
GO

ALTER TABLE [dbo].[wxPayInfo] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'微信订单号',
'SCHEMA', N'dbo',
'TABLE', N'wxPayInfo',
'COLUMN', N'transaction_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'商户订单号',
'SCHEMA', N'dbo',
'TABLE', N'wxPayInfo',
'COLUMN', N'out_trade_no'
GO


-- ----------------------------
-- Auto increment value for wxPayInfo
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[wxPayInfo]', RESEED, 110526)
GO


-- ----------------------------
-- Primary Key structure for table wxPayInfo
-- ----------------------------
ALTER TABLE [dbo].[wxPayInfo] ADD CONSTRAINT [PK_wxPayInfo] PRIMARY KEY CLUSTERED ([iKey])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

