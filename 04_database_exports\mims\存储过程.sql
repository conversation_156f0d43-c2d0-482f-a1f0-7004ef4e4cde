GetBuffetCouponUseInfoRmCountByWorkDateAndFdQty	SQL_STORED_PROCEDURE	
Create Procedure GetBuffetCouponUseInfoRmCountByWorkDateAndFdQty
AS
Begin
	set nocount on
	select WorkDate,RmNo,a.InvNo,Tot,sum(FdQty) as FdQty into #BuffetCouponUseInfo from FdInv as a join FdCashBak as b on b.InvNo=a.InvNo where a.InvNo in (select InvNo from FdCashBak where fdno='2567' and CashType<>'X') and FdNo='0902' and CashType<>'X' group by WorkDate,RmNo,a.InvNo,Tot order by WorkDate desc,FdQty desc
	select WorkDate,FdQty,Count(1) as RmCount from #BuffetCouponUseInfo group by WorkDate,FdQty order by WorkDate desc,FdQty desc
	drop table #BuffetCouponUseInfo
	set nocount off
End

	2015-01-06 10:31:39.310	2024-05-21 10:22:12.207
web_bwxr_tj	SQL_STORED_PROCEDURE	CREATE PROCEDURE web_bwxr_tj  --房间布置提成统计
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin
	set nocount on
select *,(tt*commision)as tot from (select  top 1000 fdcname,cashusername,sum(fdqty)as tt,Case when fdprice=388 then 30 
 when fdprice=688  then 50 
 when fdprice=988  then 60  
 when fdprice=0  then 0  
 when fdprice=680  then 10  
 when fdprice=980  then 10 
 when fdprice=1288  then 80 
 End as commision from fdcashbak as a join fdinv as b on b.invno=a.invno
 where workdate between @BegDate and @EndDate  and cashtype='N'  and  fdcname like '%布置%' group by fdcname,cashusername,fdprice order by fdcname,cashusername) as tab


end	2018-08-24 17:54:49.030	2024-05-21 10:22:12.207
AddStarInfo	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddStarInfo
AS
Begin
	set nocount on
	declare @ConList nvarchar(2000),@InvNo nvarchar(9)
	set @ConList=''
	select a.WorkDate, 
	case 	when a.InTime between '10:00' and '13:39' then '10:30-13:30' 
		when a.InTime between '13:40' and '16:49' then '13:40-16:40'
		when a.InTime between '16:50' and '19:59' then '16:50-19:50'
		when a.InTime between '20:00' and '23:59' then '20点以后'
		when a.InTime between '00:00' and '00:59' then '20点以后'
		when a.InTime between '01:00' and '09:59' then '01点以后'
	End as TimeSection,a.InTime,OutTime,a.InvNo,a.RmNo,RtName,a.Tot,@ConList as ConList into StarInfo
	from FdInv as a join Room as b on b.RmNo=a.RmNo join RmType as c on c.RtNo=b.RtNo
	where a.InvNo in (select InvNo from FdCashbak where fdno between '2515' and '2516' and CashType<>'X')
	order by a.InvNo 

	declare StarInfo_Cursor cursor for select InvNo from StarInfo where len(ltrim(ConList))=0
	open StarInfo_cursor
	fetch next from StarInfo_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlistStar @InvNo
			fetch next from StarInfo_cursor into @InvNo
		End
	close StarInfo_cursor
	deallocate StarInfo_cursor

	set nocount off
End	2010-07-29 12:08:59.403	2024-05-21 10:22:12.210
web_ryzs_tj	SQL_STORED_PROCEDURE	CREATE PROCEDURE web_ryzs_tj --人员赠送明细统计
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin
	set nocount on

select  '白云店' as Shopid,workdate,rmno,a.invno,fdcname,fdprice,fdqty,cashusername from fdinv as a join fdcashbak as b on a.invno=b.invno 
where  workdate between @BegDate and @EndDate and cashtype='Z' and fdno<>'2602' 
and cashusername not in('会员赠送','套餐赠送','欢乐享赠送','买断赠送','生日赠送','取存酒','X销售赠送','唱享乐赠送','活动赠送','销售赠送','固消赠送','定食赠送')order by workdate

	set nocount off
end
	2018-08-24 18:04:25.420	2024-05-21 10:22:12.213
web_xhp_tj	SQL_STORED_PROCEDURE	CREATE PROCEDURE web_xhp_tj
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin
	set nocount on

select workdate,intime,rmno,a.invno,fdcname,fdprice,fdqty,cashtime,cashusername,tot from fdcashbak as a join fdinv as b on b.invno=a.invno where workdate between @BegDate and @EndDate  and cashtype<>'X' and  fdcname like '%消耗品%' order by workdate,a.invno

	set nocount off
end

	2018-09-11 15:57:00.327	2024-05-21 10:22:12.223
date_fdcashbak	SQL_STORED_PROCEDURE	
CREATE PROCEDURE  date_fdcashbak

@workdate  nvarchar(8)

AS
Begin
	set nocount on


      select  newid()as Bikey,8 as shopid, * from fdcashbak  where invno in(select invno from fdinv where workdate= @workdate)
	set nocount off
end	2018-11-13 18:30:45.653	2024-05-21 10:22:12.230
AddWeekEnd3tg	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd3tg
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime<'13:20' then '10:20-13:20' when Intime>'13:20' and InTime<'16:30' then '13:30-16:30' when intime>'16:30' and InTime<'19:40' then '16:40-19:40' End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '10:00' and '22:30' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'团购'and CashType<>'X'  and FdCName not like '%'+'跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='自由人团购' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and (fdno='0902'or fdno='2742' or fdno='2743' or fdno='2744' ) and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2011-04-27 10:34:31.113	2024-05-21 10:22:12.230
date_fdinv	SQL_STORED_PROCEDURE	
CREATE PROCEDURE  date_fdinv

@workdate  nvarchar(8)
AS
Begin
	set nocount on

    select newid()as ikey,8 as shopid, * from FdInv where workdate=@workdate order by workdate

	set nocount off
end	2018-11-13 18:30:45.747	2024-05-21 10:22:12.230
AddWeekEndxyk	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEndxyk
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when  InTime>'10:00' and  InTime<'13:20' then '10:20-13:20' when InTime>'13:19' and inTime<'16:30' then '13:30-16:30'  when InTime>'16:29'and inTime<'19:40'  then '16:40-19:40' when InTime>'19:39'and inTime<'23:59' then '20:00以后' when  InTime>'00:00' and inTime<'01:00'  then '20:00以后' when inTime>'00:59'and inTime<'06:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'中信信用卡房费九折优惠'+'%'and CashType<>'X'  and FdCName not like '%'+'跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='中信信用卡房费九折优惠' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2011-05-16 12:32:49.030	2024-05-21 10:22:12.233
date_rmcloseinfo	SQL_STORED_PROCEDURE	CREATE PROCEDURE  date_rmcloseinfo

@workdate  nvarchar(8)
AS
Begin
	set nocount on
  --    select newid()as ikey,8 as shopid, * from rmcloseinfo where invno in(select invno from fdinv where workdate=@workdate ) 
      select newid()as ikey,8 as shopid, 
	a.InvNo,
	a.Cash,
	a.Cash_Targ,
	a.Vesa,
	a.VesaName,
	a.VesaNo,
	a.Vesa_Targ,
	a.VesaName_Targ,
	a.VesaNo_Targ,
	a.GZ,
	a.GZName,
	a.AccOkZD,
	a.ZDName,
	a.NoPayed,
	[NoPayedName],
	[Check],
	[CheckName],
	[WXPay],
	[OpenId],
	[wx_out_trade_no],
	[AliPay],
	[user_id],
	[Ali_out_trade_no],
	[MTPay] [int] ,
	[MTPayNo],
	[DZPay],
	[DZPayNo],
	[NMPay],
	[NMPayNo],
	[Coupon],
	[CouponName],
	[RechargeAccount],
	[RechargeMemberCardNo],
	[ReturnAccount],
	[ReturnMemberCardNo],
	CloseDatetime,
	[MemberKey],
	a.CloseUserName,
	a.CloseUserId,
	[WechatDeposit],
	[WechatShopping],
	[workdate],
	a.tot
 	 from rmcloseinfo as a join fdinv as b on a.invno=b.invno  where a.invno in(select invno from fdinv where workdate=@workdate ) 
	set nocount off
	end	2018-11-13 18:30:45.827	2024-05-21 10:22:12.240
AddGDDBInfo	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddGDDBInfo
AS
Begin
	set nocount on

	if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[GDDBInfo]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	drop table [dbo].[GDDBInfo]

	declare @ConList nvarchar(2000),@InvNo nvarchar(9),@DiscRate int,@Numbers int
	set @ConList=''
	set @DiscRate=0
	set @Numbers=0
	select '天河店' as ShopName,a.WorkDate, 
	case 	when a.InTime between '10:00' and '13:39' then '10:30-13:30' 
		when a.InTime between '13:40' and '16:49' then '13:40-16:40'
		when a.InTime between '16:50' and '19:59' then '16:50-19:50'
		when a.InTime between '20:00' and '23:59' then '20点以后'
		when a.InTime between '00:00' and '00:59' then '20点以后'
		when a.InTime between '01:00' and '09:59' then '01点以后'
	End as TimeSection,a.InTime,a.AccTime,a.InvNo,a.RmNo,RtName,a.DiscRate,a.FixedDisc,@Numbers as Numbers,a.Tot,@ConList as ConList Into GDDBInfo
	from FdInv as a join Room as b on b.RmNo=a.RmNo join RmType as c on c.RtNo=b.RtNo
	where a.InvNo in (select InvNo from FdCashbak where fdno='2517' and CashType<>'X')  and a.WorkDate between  Convert(nvarchar(8),DateAdd(dd,-7,getdate()),112) and getdate() 
	order by a.InvNo 

	declare GDDBInfo_Cursor cursor for select InvNo from GDDBInfo where len(ltrim(ConList))=0
	open GDDBInfo_cursor
	fetch next from GDDBInfo_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlistGDDB @InvNo
			fetch next from GDDBInfo_cursor into @InvNo
		End
	close GDDBInfo_cursor
	deallocate GDDBInfo_cursor
	set nocount off
End	2010-08-11 16:30:28.530	2024-05-21 10:22:12.240
SendCashCount	SQL_STORED_PROCEDURE	
CREATE Procedure SendCashCount
AS
Begin
	set nocount on
	declare @WorkDate nvarchar(10),@Tot int,@Cash int,@Vesa int,@GZ int,@NoPayed int,@WXPay int,@AliPay int,@Msg nvarchar(1000),@M_Tot int,@MTPay int,@DZPay int,@NMPay int
	set @WorkDate=Convert(nvarchar(10),Dateadd(dd,-1,getdate()),112)
	select @M_Tot=Sum(Tot) from FdInv where WorkDate between left(@WorkDate,6)+'01' and @WorkDate
	select @Tot=Sum(Tot) from Fdinv where WorkDate=@WorkDate
	select @Cash=Sum(Cash),@Vesa=Sum(Vesa),@GZ=Sum(GZ),@NoPayed=Sum(NoPayed),@WXPay=Sum(WXPay),@AliPay=Sum(AliPay),@MTPay=Sum(MTPay),@DZPay=Sum(DZPay),@NMPay=Sum(NMPay) from RmCloseInfo where InvNo in (select InvNo from Fdinv where WorkDate=@WorkDate)
	set @Msg='白云店'+Convert(nvarchar(10),Dateadd(dd,-1,getdate()),120)+'营业情况：\n 1、营业额：'+ltrim(rtrim(str(@Tot)))+'元；\n 2、现金：'+ltrim(rtrim(str(@Cash)))+'元；\n 3、刷卡：'+ltrim(rtrim(str(@Vesa)))+'元；\n 4、微信支付：'+ltrim(rtrim(str(@WXPay)))+'元；\n 5、支付宝：'+ltrim(rtrim(str(@AliPay)))+'元；\n 6、美团：'+ltrim(rtrim(str(@MTPay)))+'元；\n 7、大众：'+ltrim(rtrim(str(@DZPay)))+'元；\n 8、糯米：'+ltrim(rtrim(str(@NMPay)))+'元；\n 9、挂帐：'+ltrim(rtrim(str(@GZ)))+'元；\n 10、免单：'+ltrim(rtrim(str(@NoPayed)))+'元；\n 11、本月累计营业额：'+ltrim(rtrim(str(@M_Tot)))+'元。'
	Insert into barmsg.dbo.WeChatMsg (id,type,message)values(1000007,0,@Msg)
	exec rms2009.dbo.sendmsg '13728015250',@Msg
	exec rms2009.dbo.sendmsg '13808882047',@Msg
	exec rms2009.dbo.sendmsg '13808860008',@Msg
	exec rms2009.dbo.sendmsg '13538888907',@Msg
	exec rms2009.dbo.sendmsg '13318709778',@Msg
set nocount off
End	2016-06-01 02:17:34.187	2024-05-21 10:22:12.240
AddWeekEndylg	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEndylg
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'10:00' and  InTime<'13:30' then '10:30-13:30' when InTime>'13:29' and inTime<'16:40' then '13:40-16:40'  when InTime>'16:39'and inTime<'19:50'  then '16:50-19:50' when InTime>'19:50' then '20:00以后' when InTime>'00:00' and inTime<'05:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'现金券500元'+'%'and CashType<>'X'  and FdCName not like '%'+'跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='现金券500元（机场店）' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2011-06-11 15:26:21.180	2024-05-21 10:22:12.243
AddGDDB20Info	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddGDDB20Info
AS
Begin
	set nocount on

	if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[GDDB20Info]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	drop table [dbo].[GDDB20Info]

	declare @ConList nvarchar(2000),@InvNo nvarchar(9),@DiscRate int,@Numbers int
	set @ConList=''
	set @DiscRate=0
	set @Numbers=0
	select '天河店' as ShopName,a.WorkDate, 
	case 	when a.InTime between '10:00' and '13:39' then '10:30-13:30' 
		when a.InTime between '13:40' and '16:49' then '13:40-16:40'
		when a.InTime between '16:50' and '19:59' then '16:50-19:50'
		when a.InTime between '20:00' and '23:59' then '20点以后'
		when a.InTime between '00:00' and '00:59' then '20点以后'
		when a.InTime between '01:00' and '09:59' then '01点以后'
	End as TimeSection,a.InTime,a.AccTime,a.InvNo,a.RmNo,RtName,a.DiscRate,a.FixedDisc,@Numbers as Numbers,a.Tot,@ConList as ConList Into GDDB20Info
	from FdInv as a join Room as b on b.RmNo=a.RmNo join RmType as c on c.RtNo=b.RtNo
	where a.DiscRate=90 and (a.VesaName='03060000' or a.VesaName='广东发展')  and a.WorkDate between  Convert(nvarchar(8),DateAdd(dd,-7,getdate()),112) and getdate() 
	order by a.InvNo 

	declare GDDB20Info_Cursor cursor for select InvNo from GDDB20Info where len(ltrim(ConList))=0
	open GDDB20Info_cursor
	fetch next from GDDB20Info_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlistGDDB20 @InvNo
			fetch next from GDDB20Info_cursor into @InvNo
		End
	close GDDB20Info_cursor
	deallocate GDDB20Info_cursor
	set nocount off
End	2010-08-12 16:46:42.890	2024-05-21 10:22:12.260
web_jb_tj	SQL_STORED_PROCEDURE	create PROCEDURE web_jb_tj   --酒吧日常酒水销售统计报表
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin
	set nocount on

select ftcname,b.fdno,b.fdcname,sum(b.fdqty),Case b.CashType when 'N' then '销售' when 'Z' then '赠送' end from fdinv  a join fdcashbak  b on a.invno=b.invno  join food c on c.fdno=b.fdno join fdtype d  on c.ftno=d.ftno
   where  workdate between @BegDate and @EndDate and prntype='D'and b.cashtype<>'X' group by d.ftcname,b.fdcname,b.cashtype,b.fdno order by b.fdno,b.fdcname


	set nocount off
end


-- exec web_jb_tj '20211201','20211201'

--酒吧酒水销售统计：
-- http://ktv2.tang-hui.com.cn:90//ExecUse.ASHX?Ex=web_jb_tj&BegDate=20211201&EndDate=20211202
  

	2021-12-24 15:19:58.810	2024-05-21 10:22:12.263
ftjtc	SQL_STORED_PROCEDURE	CREATE PROCEDURE ftjtc
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '天河店' as ShopName,WorkDate,Case when InTime>'19:49'and inTime<'23:59' then '20:00以后' when  InTime>='00:00' and inTime<'01:00'  then '20:00以后' when inTime>'00:59'and inTime<'06:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59'order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'伏特加套餐'+'%'and CashType<>'X'   group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='伏特加套餐' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End

	2011-08-04 17:04:21.123	2024-05-21 10:22:12.280
AddWeekEndzyr	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEndzyr
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime<'13:20' then '10:20-13:20' when Intime>'13:20' and InTime<'16:30' then '13:30-16:30' when intime>'16:30' and InTime<'19:40' then '16:40-19:40' End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '10:00' and '19:40' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2011-09-26 11:23:37.337	2024-05-21 10:22:12.297
AddTemp91926	SQL_STORED_PROCEDURE	

CREATE PROCEDURE AddTemp91926
AS
Begin
	set nocount on

	if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Temp91926]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
	drop table [dbo].[Temp91926]

	declare @ConList nvarchar(2000),@InvNo nvarchar(9),@DiscRate int,@Numbers int,@Free int,@Buffet int
	set @ConList=''
	set @DiscRate=0
	set @Numbers=0
	select '天河店' as ShopName,a.WorkDate, 
	case 	when a.InTime between '10:00' and '13:39' then '10:30-13:30' 
		when a.InTime between '13:40' and '16:49' then '13:40-16:40'
		when a.InTime between '16:50' and '19:59' then '16:50-19:50'
		when a.InTime between '20:00' and '23:59' then '20点以后'
		when a.InTime between '00:00' and '00:59' then '20点以后'
		when a.InTime between '01:00' and '09:59' then '01点以后'
	End as TimeSection,a.InTime,a.AccTime,a.InvNo,a.RmNo,RtName,a.DiscRate,a.FixedDisc,@Free as Free ,@Buffet as Buffet,@Numbers as Numbers,a.Tot,@ConList as ConList Into Temp91926
	from FdInv as a join Room as b on b.RmNo=a.RmNo join RmType as c on c.RtNo=b.RtNo
	where a.WorkDate between  '20101001' and '20101007' and a.InTime<'20:00' 
	order by a.InvNo 

	declare Temp91926_Cursor cursor for select InvNo from Temp91926 where len(ltrim(ConList))=0
	open Temp91926_cursor
	fetch next from Temp91926_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlistTemp91926 @InvNo
			fetch next from Temp91926_cursor into @InvNo
		End
	close Temp91926_cursor
	deallocate Temp91926_cursor
	set nocount off
End	2010-09-27 15:52:35.683	2024-05-21 10:22:12.300
AddWeekEnd_xdxx	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd_xdxx
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'10:00' and  InTime<'13:30' then '10:30-13:30' when InTime>'13:29' and inTime<'16:40' then '13:40-16:40'  when InTime>'16:39'and inTime<'19:50'  then '16:50-19:50'   End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '10:00' and '19:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'新店新喜'+'%'and CashType<>'X' and FdCName not like '%'+'跨'+'%'  group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='新店新喜' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2011-12-14 14:01:51.000	2024-05-21 10:22:12.303
AddWeekEnd_hphy	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd_hphy
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'10:00' and  InTime<'13:20' then '10:20-13:20' when InTime>'13:19' and inTime<'16:30' then '13:30-16:30'  when InTime>'16:29'and inTime<'19:40'  then '16:40-19:40' when InTime>'19:39'and inTime<'23:59' then '20:00以后' when  InTime>'00:00' and inTime<'01:00'  then '20:00以后' when inTime>'00:59'and inTime<'06:00' then '1:00以后'    End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like  '50元现金劵(团购)' and CashType<>'X' and FdCName not like '%'+'跨'+'%'  group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='50元现金劵(团购)' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2011-12-19 11:33:02.263	2024-05-21 10:22:12.303
AddWeekEndhlf	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEndhlf
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime<'13:30' then '10:30-13:30' when Intime>'13:30' and InTime<'16:40' then '13:40-16:40' when intime>'16:40' and InTime<'19:50' then '16:50-19:50' End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '10:00' and '20:30' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'欢乐汇'+'%'and CashType<>'X'  and FdCName not like '%'+'跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='欢乐汇' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and (fdno='0902'or fdno='2745' or fdno='2746' or fdno='2747' ) and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2011-12-26 15:22:53.060	2024-05-21 10:22:12.307
dbfood_FdInv	SQL_STORED_PROCEDURE	
CREATE PROCEDURE [dbo].[dbfood_FdInv]
	@t int=0,
	@StartTime varchar(10)='',			--开始时间
	@EndTime varchar(8)='',				--结束时间
	@RmNo varchar(4)='',				--房号
	@InvNo varchar(9)='',               --账单号
	@Shopid int=0,                      --门店id
	@IKey uniqueidentifier=null,		--账号Key
	@BillType int=0,					--操作类型(1为免单，2为挂账，3为招待)
	--分页
	@PageIndex int=1,                   --页码
	@PageSize int=50                    --每页容纳的记录数
AS
BEGIN
	declare @start int --当前页开始显示的No
	declare @end int--当前页结束显示的No
	declare @condition varchar(5000)--查询的条件
	declare @count int
	declare @sql  varchar(5000)--执行的SQL语句
	set @condition='1=1'
	if(@t=0)-- 查询
		begin
			if(@StartTime<>'' and @EndTime<>'')
				set @condition=@condition+' and InDate between '''+cast(@StartTime  as varchar(50))+''' and  '''+cast(@EndTime  as varchar(50))+''''
			if(@InvNo<>'')
				set @condition=@condition+' and InvNo='''+cast(@InvNo as varchar(50))+''' '
			if(@RmNo<>'')
				set @condition=@condition+' and RmNo='''+cast(@RmNo as varchar(50))+''' '	
			if(@BillType=1)
				set @condition=@condition+' and (NoPayed>0 or (NoPayed =0 and GZ=0 and AccOkZD=0))'	
			if(@BillType=2)
				set @condition=@condition+' and GZ>0'	
			if(@BillType=3)
				set @condition=@condition+' and AccOkZD>0'	
			--if(@PageIndex>0)
			--	begin
			--		set @start=(@PageIndex-1)*@pagesize+1
			--		set @end=@start+@PageSize-1
			--		set @sql='select * from (select * ,row_number() over(order by InDate desc , InTime desc) as record,(select COUNT(*) from FdInv where '+@condition+')as total from FdInv where '+@condition+')record where record between '+CAST(@start as varchar(50))+' and '+CAST(@end as varchar(50))+''
			--		set @sql=''
			--	end
			if(@PageIndex>0)
				begin
					set @start=(@PageIndex-1)*@pagesize
				end 
				select @sql='select top '+CAST(@pagesize as varchar(50))+' * from FdInv  where InvNo not in(select top '+CAST(@start as varchar(50))+'  InvNo from FdInv where '+@condition+' order by InDate desc,InTime desc) and  '+@condition+' order by InDate desc,InTime desc '
			exec(@sql)
			--select @sql
		end
	if(@t=1)--执行操作后删除数据
		begin
			delete from FdInv where InvNo=@InvNo
		end
END


	2019-08-23 09:38:36.170	2024-05-21 10:22:12.307
AddWeekEnd_zhs	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd_zhs
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'19:50'and inTime<'23:59' then '20:00以后' when  InTime>'00:00' and inTime<'01:00'  then '20:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and (InTime between '19:50' and '23:59')or (InTime between '00:00' and '1:00')  order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like  '%'+'芝华士威士忌赠劵'+'%' and CashType<>'X'  group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='芝华士威士忌赠劵' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2011-12-26 16:01:34.407	2024-05-21 10:22:12.310
GetInvNoInfo	SQL_STORED_PROCEDURE	-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
create PROCEDURE [dbo].[GetInvNoInfo] 
	@InvNo nvarchar(20)=''  --账单号
AS
BEGIN
	select FdCName,FdPrice,FdQty,Tot,CashUserName,CashType,CashTime,InDate,InTime,OutTime,a.InvNo,AccUserName,RmNo from FdInv as a join FdCashBak as b on  a.InvNo=b.InvNo  where a.InvNo=@InvNo
END
	2019-09-09 18:32:40.153	2024-05-21 10:22:12.310
AddWeekEnd_hll	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd_hll
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'19:00'and inTime<'06:59' then '20:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and (InTime between '19:00' and '23:55' ) order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like  '%生日party券%' and CashType<>'X'  group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='生日party券' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2012-01-09 12:02:41.670	2024-05-21 10:22:12.310
Ex_xhp_test	SQL_STORED_PROCEDURE	CREATE PROCEDURE Ex_xhp_test


@invno nvarchar(9), --帐单号
@FdNo NVARCHAR(20)='7935'

AS
Begin
 declare @number1 int,
	 @number2 int,
	 @cttype1 int,
	 @cttype2 int,
	 @cttype3 int,
	 @cttype4 int,
	@WorkDate INT,
	@RmNo NVARCHAR(20),
	@OredrCou int,
	@nowTime varchar(12),
	@workRoomCou  int,
	@intime varchar(5),
	@Peotot int,
	@ActBatch int,
	@ybfdno varchar(10),
	@FdNoTxt varchar(1000)--下单集合字符串
      --  select @workdate=CONVERT(varchar(8),GETDATE(),112)

	set nocount on

-- 统计人数
	select @number1=isnull(sum(fdqty),0) from fdcashbak  where invno=@invno and  cashtype='N' and  fdcname like '%消耗品%'
	select @number2=isnull(sum(fdqty),0) from fdcash a join room b on a.rmno=b.rmno where invno=@invno and cashtype='N' and fdno in( '0901','0902') 

/* 
-- 统计消费类型
	select @cttype1=isnull(count(1),0) from fdcashbak  where  invno=@invno and cashtype='N' and  fdcname like '%K+自助餐%' 
	select @cttype2=isnull(count(1),0) from fdcash a join room b on a.rmno=b.rmno where invno=@invno and cashtype='N' and (fdno like'09%' and fdno not in( '0901','0902')) 
             select @cttype3=isnull(count(1),0) from fdcashbak  where  invno=@invno and cashtype='N' and  (fdcname like '%团购%'or fdcname like '%新美大%')and (fdno like '27%' or fdno like '57%') and fdcname not like '%堂会%'
	select @cttype4=isnull(count(1),0) from fdcash a join room b on a.rmno=b.rmno where invno=@invno and cashtype='N' and (fdno like'27%' or fdno like'57%') and fdno not in('2762')

      if  @cttype1+@cttype2=0
         set @ActBatch = 2  --2是消费类型（ActBatch），PeoTot是人数
       else
          set @ActBatch = 1   --1是K+自助餐消费

*/

 set @Peotot= @number1+@number2

   select @WorkDate=WorkDate,@RmNo=RmNo,@intime=intime from Room  where InvNo=@invno
   
if @intime is null or @intime=''
    select @intime=intime from fdinv  where InvNo=@invno

  if @intime<'19:40' and  @intime>'09:00'
          set @ActBatch = 1
	else 
          set @ActBatch = 2


   	create table #FdNoTable
		(
			Fdno varchar(50) 
		)

IF (@WorkDate is null) 
begin--不是当前消费
        select @WorkDate=WorkDate,@intime=intime from FdInv where InvNo=@invno
        select @OredrCou=COUNT(1) from FdCashBak where FdNo=@Fdno and InvNo=@invno and cashtype<>'X'
		---拿取下单号集合拼接字符串
		--select @FdNoTxt=stuff((select ','+ FdNo from (select distinct(FdNo) from  FdCashBak where InvNo = @invno  and cashtype<>'X')a for xml path('')),1,1,'')
		insert into #FdNoTable select distinct(FdNo) from FdCashBak where InvNo = @invno  and cashtype<>'X'
end
else 
begin
        select  @OredrCou=COUNT(1) from FdCash where FdNo=@Fdno and RmNo=@RmNo and cashtype<>'X'
		---拿取下单号集合拼接字符串
	  -- select @FdNoTxt=stuff((select ','+ FdNo from (select distinct(FdNo) from  FdCash where  RmNo=@RmNo and cashtype<>'X')a for xml path('')),1,1,'')
	  insert into #FdNoTable select distinct(FdNo) from FdCash where  RmNo=@RmNo  and cashtype<>'X'
end
	set @FdNoTxt=''

--使用游标
	declare test_Cursor CURSOR SCROLL FOR
	select Fdno from #FdNoTable
	open test_Cursor
	FETCH NEXT FROM test_Cursor into @ybfdno
	while @@FETCH_STATUS =0
	begin
		set @FdNoTxt = @FdNoTxt+@ybfdno + ','
		FETCH NEXT FROM test_Cursor into @ybfdno
	end
	close test_Cursor
	deallocate test_Cursor
	drop table #FdNoTable
	if(len(@FdNoTxt)>4)
		set @FdNoTxt = left(@FdNoTxt,len(@FdNoTxt)-1)
	



if(DATENAME(HOUR,GETDATE())<6)
set @nowTime= convert(varchar(12),dateadd(day,-1,getdate()),112)
else
set  @nowTime=convert(varchar(12),getdate(),112)


select @workRoomCou =COUNT(1) from fdinv  where workdate=@nowTime and rmno not like '%B'
select @WorkDate as workdate,@nowTime as nowTime,ISNULL( @OredrCou,0) as oredrCou, @workRoomCou as workRoomCou,

(select COUNT(1) AS 开房 from Room where RmStatus in('U','C')and rmno not like '%B' )  as openCou , 
(select COUNT(1) AS 结账 from Room where RmStatus in('A')and rmno not like '%B' ) as  accountsCou ,
@intime as intime,@Peotot as Peotot,@ActBatch as ActBatch,@FdNoTxt as FdNoTxt
        
	set nocount off

end	2020-05-29 09:53:02.973	2024-05-21 10:22:12.313
GetAllCashUserCashTot	SQL_STORED_PROCEDURE	
CREATE PROCEDURE GetAllCashUserCashTot
@FromDate nvarchar(8),
@ToDate nvarchar(8)
AS
Begin
	select CloseUserId,CloseUserName,sum(Cash) as Cash ,sum(Vesa) as Vesa from vFdInv where WorkDate between @FromDate and @ToDate group by CloseUserId,CloseUserName order by CloseUserId,CloseUserName
End
	2012-03-09 12:14:25.513	2024-05-21 10:22:12.317
GetFdDetail	SQL_STORED_PROCEDURE	
CREATE PROCEDURE GetFdDetail
@FromDate nvarchar(8),
@ToDate nvarchar(8)
AS
Begin
	select '自助餐'  as FdName,sum(FdQty) as FdQty,Sum(FdQty*FdPrice)/sum(FdQty)as Price,Sum(FdQty*FdPrice)as Total from vBuffet where InvNo in (select InvNo from vFdInv where WorkDate between @FromDate and @ToDate) and CashType='N'
	union all
	select '食品'  as FdName,sum(FdQty) as FdQty,Sum(FdQty*FdPrice)/sum(FdQty)as Price,Sum(FdQty*FdPrice)as Total from vFood where InvNo in (select InvNo from vFdInv where WorkDate between @FromDate and @ToDate) and CashType='N'
	union all
	select '酒水'  as FdName,sum(FdQty) as FdQty,Sum(FdQty*FdPrice)/sum(FdQty)as Price,Sum(FdQty*FdPrice)as Total from vWine where InvNo in (select InvNo from vFdInv where WorkDate between @FromDate and @ToDate) and CashType='N'
End
	2012-03-09 12:14:25.607	2024-05-21 10:22:12.320
GetFdDetailOnType	SQL_STORED_PROCEDURE	
CREATE PROCEDURE GetFdDetailOnType
@FromDate nvarchar(8),
@ToDate nvarchar(8),
@TypeId int
AS
Begin
	if @TypeId=1
		select FdNo,FdCName,sum(FdPrice*FdQty)as Tot from vBuffet where InvNo in (select InvNo from vFdInv where WorkDate between @FromDate and @ToDate) group by FdNo,FdCName order by FdNo,FdCName
End
	2012-03-09 12:14:25.687	2024-05-21 10:22:12.320
GetTotCashVesa	SQL_STORED_PROCEDURE	
CREATE PROCEDURE GetTotCashVesa
@FromDate nvarchar(8),
@ToDate nvarchar(8)
AS
Begin
	declare @RmCostCash int,@RmCostCashVesa int,@TotCash_Vesa int,@TotCash_Cash int,@TotCash int,@RmCostVesa int,@TotVesa int
	select @RmCostCash=sum(RmCost)-sum(FixedDisc)  from vFdInv where WorkDate between @FromDate and @ToDate and Vesa=0 and Cash>0
	select @RmCostVesa=sum(RmCost)-sum(FixedDisc)  from vFdInv where WorkDate between @FromDate and @ToDate and Vesa>0 and Cash=0
	select @TotCash_Cash=sum(Cash),@TotCash_Vesa=sum(Vesa),@RmCostCashVesa=sum(RmCost-FixedDisc)  from vFdInv where WorkDate between @FromDate and @ToDate and Vesa>0 and Cash>0
	select @TotCash=sum(Cash) from vFdInv where WorkDate between @FromDate and @ToDate and Vesa=0 and Cash>0
	select @TotVesa=sum(Vesa) from vFdInv where WorkDate between @FromDate and @ToDate and Vesa>0 and Cash=0
/*	select @TotCash-@RmCostCash as FdCostCash,@RmCostCash as RmCostCash,@TotVesa-@RmCostVesa as FdCostVesa,@RmCostVesa as RmCostVesa*/
	select @TotCash-@RmCostCash+@TotCash_Cash-@RmCostCashVesa as FdCostCash,@RmCostCash+@RmCostCashVesa as RmCostCash,@TotVesa-@RmCostVesa+@TotCash_Vesa as FdCostVesa,@RmCostVesa as RmCostVesa
End
	2012-03-09 12:14:25.763	2024-05-21 10:22:12.320
web_gp_tj	SQL_STORED_PROCEDURE	CREATE PROCEDURE web_gp_tj   --果盘统计明细
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin
	set nocount on

select *, (tt*commision) as tot  from (select top 100 fdcname,sum(fdqty)as tt,Case when fdcname='缤纷果盘' then '0.5'
 when fdcname='贵宾果盘'  then 0.1
 when fdcname='VIP果盘'  then 0.5  
 when fdcname='大果盘'  then 0.3 
 when fdcname='升级为新春果盘'  then 0 
 when fdcname='新春喜庆果盘'  then 0.5 
 End as commision  from fdcashbak as a join fdinv as b on b.invno=a.invno 
where workdate between @BegDate and @EndDate  and cashtype<>'X' and  fdcname like '%果盘%' group by fdcname,fdprice order by fdcname) as tab

	set nocount off
end	2020-07-29 15:47:55.300	2024-05-21 10:22:12.330
web_ty_tj	SQL_STORED_PROCEDURE	CREATE PROCEDURE web_ty_tj   --特饮统计明细
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin
	set nocount on

select *,(tt*commision) as tot  from (select top 1000 fdcname,sum(fdqty)as tt,Case when fdcname like'%(杯)%'and fdcname not like'%员工%'  then 0.5
 when   (fdcname like'%渣%' or  fdcname like'%扎%')and fdcname not like'%员工%'  then 1
 when   fdcname  like'%员工%'  then 0
 End as commision from fdcashbak as a join fdinv as b on b.invno=a.invno 
where workdate between @BegDate and @EndDate  and cashtype<>'X' and (fdno like '41%') group by fdcname order by fdcname) as tab

	set nocount off
end


	2020-07-29 16:50:13.173	2024-05-21 10:22:12.330
web_zs_tj	SQL_STORED_PROCEDURE	CREATE PROCEDURE web_zs_tj --人员赠送统计汇总
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin
	set nocount on

select a.*,(fdprice*tt)as tot from (select top 1000 cashusername,fdcname,fdprice,sum(fdqty)as tt from fdinv as a join fdcashbak as b on a.invno=b.invno 
where  workdate between @BegDate and @EndDate and cashtype='Z' and fdno<>'2602' 
and cashusername not in('会员赠送','套餐赠送','欢乐享赠送','买断赠送','生日赠送','取存酒','X销售赠送','唱享乐赠送','活动赠送','销售赠送','固消赠送','定食赠送','积分支付','积分兑换','充值返赠','存酒卡赠送','低消赠送','取存酒/')
group by cashusername,fdcname,fdprice order by cashusername)as a

	set nocount off
end


	2020-08-04 11:50:47.113	2024-05-21 10:22:12.333
AddWeekEnd_tgtc	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd_tgtc
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case  when InTime>'19:40'and inTime<'23:59' then '20:00以后' when  InTime>'00:00' and inTime<'01:00'  then '20:00以后' when inTime>'00:59'and inTime<'06:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and (InTime between '16:40' and '23:59' or intime between '00:00'and '06:00')  order by InTime open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'团购'+'%'+'套餐'and CashType<>'X'   group by a.InvNo order by a.InvNo open SumFdQty_Cursor fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='团购套餐' where InvNo=@InvNo fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and 
(fdno='0902'or fdno='2739' or fdno='2740' or fdno='2741' )and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2012-03-16 15:03:10.840	2024-05-21 10:22:12.333
web_tg_tj	SQL_STORED_PROCEDURE	CREATE PROCEDURE [dbo].[web_tg_tj] --团购及劵类下单汇总
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin 
	set nocount on

select  fdcname,fdprice,sum(fdqty)as tt from fdinv as a join fdcashbak as b on a.invno=b.invno 
where  workdate between @BegDate and @EndDate and fdprice=0 and (fdno like 'C%' or fdno like 'D%' or fdno like '27%' or fdno like '34%' or fdno like '43%'or fdno like '44%'or fdno like '52%' or fdno like '53%' or fdno like '57%'or fdno like '80%'or fdcname like '%旅划算%'or fdcname like '%联联%'or fdcname like '%贪吃%'or fdcname like '%抖预%'or fdcname like '%美味%' or fdcname like '%剧本杀%'or fdcname like '%抖音%'or fdcname like '%美团%'or fdcname like '%美预%'or fdcname like '%在线预订%') and cashtype='N'
group by fdcname,fdprice order by fdcname
	set nocount off


end	2020-08-04 12:27:38.973	2025-04-27 15:38:20.920
Report2	SQL_STORED_PROCEDURE	CREATE PROCEDURE Report2
@BegDate nvarchar(8),
@EndDate nvarchar(8),
@ftno int
AS
Begin
  set nocount on
  -- declare @type int
  if @ftno=1  --全部
	select fdno,fdcname,fdprice,number,(fdprice*number)as tot,CashType from ( select top 10000 b.fdno,b.fdcname,b.fdprice,sum(b.fdqty)as number,case CashType when 'Z' then '赠送' when 'N' then '销售' End as CashType from fdinv  a join fdcashbak  b on a.invno=b.invno   
	where  workdate between @BegDate and @EndDate and cashtype<>'X'group by b.fdcname,b.fdprice,b.fdno,cashtype order by b.fdno)as tab		
	order by cashtype,fdno
		
  if @ftno=2  --酒水类
	select fdno,fdcname,fdprice,number,(fdprice*number)as tot,CashType from ( select top 10000 b.fdno,b.fdcname,b.fdprice,sum(b.fdqty)as number,case CashType when 'Z' then '赠送' when 'N' then '销售' End as CashType from fdinv  a join fdcashbak  b on a.invno=b.invno join food c on b.fdno=c.fdno   
	where  workdate between @BegDate and @EndDate and cashtype<>'X' and c.ftno  in('00','01','02','03','04','05','06','07','08','18','19','21','28','41') group by b.fdcname,b.fdprice,b.fdno,cashtype order by b.fdno) as tab		
	order by cashtype,fdno
	
  if @ftno=3  --食品类  
	select fdno,fdcname,fdprice,number,(fdprice*number)as tot,CashType from ( select top 10000 b.fdno,b.fdcname,b.fdprice,sum(b.fdqty)as number,case CashType when 'Z' then '赠送' when 'N' then '销售' End as CashType from fdinv  a join fdcashbak  b on a.invno=b.invno join food c on b.fdno=c.fdno   
	where  workdate between @BegDate and @EndDate and cashtype<>'X' and  c.ftno in('10','12','13','14','16','33') group by b.fdcname,b.fdprice,b.fdno,cashtype order by b.fdno) as tab		
	order by cashtype,fdno

  if @ftno=4  --套餐类  
	select fdno,fdcname,fdprice,number,(fdprice*number)as tot,CashType from ( select top 10000 b.fdno,b.fdcname,b.fdprice,sum(b.fdqty)as number,case CashType when 'Z' then '赠送' when 'N' then '销售' End as CashType from fdinv  a join fdcashbak  b on a.invno=b.invno join food c on b.fdno=c.fdno   
	where  workdate between @BegDate and @EndDate and cashtype<>'X' and c.ftno in('31','34','35','40','42','44','46','47','48','49','50','51','54','55','56')  group by b.fdcname,b.fdprice,b.fdno,cashtype order by b.fdno) as tab		
	order by cashtype,fdno

  if @ftno=5  --其它类 
	select fdno,fdcname,fdprice,number,(fdprice*number)as tot,CashType from ( select top 10000 b.fdno,b.fdcname,b.fdprice,sum(b.fdqty)as number,case CashType when 'Z' then '赠送' when 'N' then '销售' End as CashType from fdinv  a join fdcashbak  b on a.invno=b.invno join food c on b.fdno=c.fdno   
	where  workdate between @BegDate and @EndDate and cashtype<>'X' and c.ftno in('09','11','15','22','23','24','25','26','27','29','30','32','36','37','38','39','43','52','53','57','62','78','80') group by b.fdcname,b.fdprice,b.fdno,cashtype order by b.fdno) as tab		
	order by cashtype,fdno
	
	set nocount off
end

-- exec Report2 '20200812','20200812',5


	2020-08-19 17:26:33.717	2024-05-21 10:22:12.340
AddWeekEnd50	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd50
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'10:00' and  InTime<'13:20' then '10:20-13:20' when InTime>'13:19' and inTime<'16:30' then '13:30-16:30'  when InTime>'16:29'and inTime<'19:40'  then '16:40-19:40' when InTime>'19:40' then '20:00以后' when InTime>'00:00' and inTime<'05:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%光棍节100元现金券%'and CashType<>'X'  and FdCName not like '%光棍节100元现金券%'+'跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='光棍节100元现金券' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2012-04-18 11:12:49.640	2024-05-21 10:22:12.343
Report_hs_sp	SQL_STORED_PROCEDURE	



CREATE  PROCEDURE Report_hs_sp
@type int=0
AS
Begin
  set nocount on
  if @type=0
	Begin
		select ikey,a.invno,rmno,a.fdno,c.pice,fdcname,fdprice,fdqty,workdate from fdcashbak as a join fdinv as b on a.invno=b.invno join report_hs as c on c.fdno=a.fdno where workdate>=dbo.fun_get_date(getdate()) and cashtype='N'
		and a.fdno in (select fdno from Report_hs) order by ikey
	end
  if @type=1
	select a.fdno,fdcname,pice from Report_hs as a join food as b on a.fdno=b.fdno order by a.fdno

	set nocount off
end

-- exec Report_hs_sp	2020-08-28 11:26:21.250	2024-05-21 10:22:12.347
AddWeekEndzyr2	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEndzyr2
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime<'13:20' then '10:20-13:20' when Intime>'13:20' and InTime<'16:30' then '13:30-16:30' when intime>'16:30' and InTime<'19:40' then '16:40-19:40' End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '10:00' and '19:50' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'免费欢唱劵'+'%'and CashType<>'X'  and FdCName not like '%'+'跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='免费欢唱劵' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2012-04-18 11:33:39.077	2024-05-21 10:22:12.347
AddWeekEnd_bwj	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd_bwj
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'10:00' and  InTime<'13:20' then '10:20-13:20' when InTime>'13:19' and inTime<'16:30' then '13:30-16:30'  when InTime>'16:29'and inTime<'19:40'  then '16:40-19:40' when InTime>'19:40' then '20:00以后' when InTime>'00:00' and inTime<'05:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%畅饮百威100元现金券%'and CashType<>'X'  and FdCName not like '%畅饮百威100元现金券'+'跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='畅饮百威100元现金券' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2012-05-11 13:38:13.420	2024-05-21 10:22:12.350
Invno_info	SQL_STORED_PROCEDURE	CREATE PROCEDURE Invno_info
@invno nvarchar(9)='',
@typeid int,
@rmno nvarchar(5)=''
AS
Begin
  set nocount on
if @typeid=1
	--select ikey,fdno,fdcname,fdprice,fdqty from fdcashbak where invno=@invno order by cashtime
	select ikey,fdno,fdcname,fdprice,fdqty,case CashType when 'Z' then '赠送' when 'N' then '落单' End as CashType from fdcashbak where invno=@invno and cashtype<>'X' order by cashtime


if @typeid=2
	select workdate,invno,rmno,indate,intime,tot from fdinv where workdate>=dbo.fun_get_date(getdate()) and rmno=@rmno order by indate desc,intime desc
if @typeid=3
	select * from rmcloseinfo where invno=@invno

	set nocount off
end	2020-08-31 17:41:08.780	2024-05-21 10:22:12.350
web_yhhd_tj	SQL_STORED_PROCEDURE	CREATE PROCEDURE web_yhhd_tj   --银行活动统计明细
@BegDate nvarchar(8),
@EndDate nvarchar(8),
@fdcname nvarchar(8)
AS
Begin
	set nocount on

select workdate,rmno,a.invno,fdcname,fdprice,fdqty,cashusername,tot from fdcashbak as a join fdinv as b on b.invno=a.invno 
where workdate between @BegDate and @EndDate and cashtype<>'X' and fdcname like  '%'+@fdcname+'%' order by workdate

	set nocount off
end



	2020-10-14 14:18:49.560	2024-05-21 10:22:12.357
AddWeekEnd2	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd2
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'10:00' and  InTime<'13:30' then '10:30-13:30' when InTime>'13:29' and inTime<'16:40' then '13:40-16:40'  when InTime>'16:39'and inTime<'19:50'  then '16:50-19:50' when InTime>'19:50' then '20:00以后' when InTime>'00:00' and inTime<'05:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'光棍节'+'%'+'劵'and CashType<>'X'  and FdCName not like '%'+'光棍节%跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='光棍节100元现金券' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and (fdno='0902'or fdno='2739' or fdno='2740' or fdno='2741' ) and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2010-12-02 15:10:16.717	2024-05-21 10:22:12.360
tj31	SQL_STORED_PROCEDURE	CREATE procedure tj31
	 @ComeDate nvarchar(8),
	@ShopID int
AS
Begin
	set nocount on
	declare @ShopName nvarchar(20),@Msg nvarchar(1000),@Count int,@TotNumbers int,@TotRoom int,@DeNumbers int
	select @ShopName= Case @ShopId
		when 2 then '堂会缤缤店'
		when 3 then '堂会天河店'
		when 4 then '堂会英德店'
		when 5 then '堂会番禺店'
		when 6 then '堂会海印店'
		when 7 then '堂会开福店'
		when 8 then '堂会白云店'
		when 9 then '堂会区庄店'
		when 10 then '堂会罗湖店'
		when 12 then '堂会清远店'
		when 13 then '堂会黄岐店'
		else '' End
	--第一档开房数
	select @Count=count(1) from rms2009.dbo.opencacheinfo where comedate=@ComeDate and beg_key='1' and checkinstatus='3'and shopid=@ShopID

	--第一档总人数
	select @TotNumbers=sum(fdqty) from fdinv as a join fdcashbak as b on a.invno=b.invno where workdate=@ComeDate and fdno='0902'and cashtime between '12:00'and '14:59'and cashtype='N'

	--参与三免一房间数
	select @TotRoom=count(invno)  from fdinv where InvNo in (select InvNo from fdcashbak where workdate=@ComeDate and fdno='2762'and cashtype='N')

	--三免一减免人数
	select @DeNumbers=sum(fdqty) from fdinv as a join fdcashbak as b on a.invno=b.invno where workdate=@ComeDate and fdno='2762'and cashtype='N'
	set @Msg=@ShopName+left(@Comedate,4)+'年'+left(right(@ComeDate,4),2)+'月'+right(@ComeDate,2)+'第一档\n'+'开房'+ltrim(rtrim(str(@Count)))+'间；\n共有'+ltrim(rtrim(str(@TotNumbers)))+'人；\n会员日参与三免一'+ltrim(rtrim(str(@TotRoom)))+'间；\n减免'+ltrim(rtrim(str(@DeNumbers)))+'位。'
	Insert into barmsg.dbo.wechatMsg (id,message)values(1000012,@Msg)
	set nocount off
End	2017-01-24 14:52:47.537	2024-05-21 10:22:12.360
fdinv_track_report	SQL_STORED_PROCEDURE	CREATE PROCEDURE [dbo].[fdinv_track_report]
	@RmNo varchar(5)='',
	@RefNo varchar(50)='',
	@FdNo varchar(5)='',
	@State int=0,
	-------分页---------
	@PageIndex int=1, --页码
	@PageSize int=50--每页容纳的记录数
AS
BEGIN
	declare @sql varchar(1000) ---sql语句
	--declare @variable varchar(8000)='a.CashType<>''X'' and c.prntype<>''N'' ' --查询条件(出单未消单的数据)
	--declare @start int=0 --当前页开始显示的No
	--declare @end int=0--当前页结束显示的No
	declare @variable varchar(200) --查询条件(出单未消单的数据)
	declare @start int --当前页开始显示的No
	declare @end int --当前页结束显示的No
	--IF(@RmNo<>'')
	--	SET @variable+='and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' '
	--IF(@RefNo<>'')
	--	SET @variable+='and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''' '
	--IF(@FdNo<>'')
	--	SET @variable+='and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''' '
	--IF(@State<>'')
	--	SET @variable+='and TrackState='''+CAST(@State AS VARCHAR(50))+''' '
	IF(@RmNo<>'' and @RefNo<>'' and @FdNo<>'' and @State<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''' and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''' and TrackState='''+CAST(@State AS VARCHAR(50))+''''
	else IF(@RmNo<>'' and @RefNo<>'' and @FdNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''' and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''''
	else IF(@RmNo<>'' and @State<>'' and @FdNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' and TrackState='''+CAST(@State AS VARCHAR(50))+''' and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''''
	else IF(@RmNo<>'' and @RefNo<>'' and @State<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''' and TrackState='''+CAST(@State AS VARCHAR(50))+''''
	else IF(@FdNo<>'' and @RefNo<>'' and @State<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''' and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''' and TrackState='''+CAST(@State AS VARCHAR(50))+''''
	else IF(@RmNo<>'' and @RefNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''''
	else IF(@RmNo<>'' and @FdNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''''
	else IF(@RmNo<>'' and @State<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' and TrackState='''+CAST(@State AS VARCHAR(50))+''''
	else IF(@State<>'' and @RefNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackState='''+CAST(@State AS VARCHAR(50))+''' and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''''
	else IF(@FdNo<>'' and @RefNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''' and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''''
	else IF(@FdNo<>'' and @State<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''' and TrackState='''+CAST(@State AS VARCHAR(50))+''''
	else IF(@RmNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackRmNo='''+CAST(@RmNo AS VARCHAR(50))+''' '
	else IF(@RefNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and RefNo='''+CAST(@RefNo AS VARCHAR(50))+''' '
	else IF(@FdNo<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackFdNo='''+CAST(@FdNo AS VARCHAR(50))+''' '
	else IF(@State<>'')
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' and TrackState='''+CAST(@State AS VARCHAR(50))+''' '
	else 
		SET @variable='a.CashType<>''X'' and c.prntype<>''N'' '
	if(@PageSize!=0 and @PageIndex!=0)
		begin
			set @start=(@PageIndex-1)*@pagesize+1
			set @end=@start+@PageSize-1
		end
		--set @sql ='select * from (select (select count(0) from FdCash_Track a join Food b on a.trackfdno=b.FdNo join fdtype c on b.FtNo=c.FtNo where '+@variable+')as TotPage,a.*,b.FdCName,b.FdPrice1,isnull(a.TrackNum*b.fdprice1,0)as tot,ROW_NUMBER() over (order by trackrmno) as num  from FdCash_Track a join Food b on a.trackfdno=b.FdNo join fdtype c on b.FtNo=c.FtNo where '+@variable+')a where num between '+CAST(@start as varchar(10))+' and '+CAST(@end as varchar(10))+''
set @sql ='select (select count(0) from FdCash_Track a join Food b on a.trackfdno=b.FdNo join fdtype c on b.FtNo=c.FtNo where '+@variable+')as TotPage, a.*,b.FdCName,b.FdPrice1,isnull(a.TrackNum*b.fdprice1,0)as tot from FdCash_Track a join Food b on a.trackfdno=b.FdNo join fdtype c on b.FtNo=c.FtNo where '+@variable+''	
exec(@sql)
	--select @sql
	--select @variable
END


	2020-11-26 16:30:52.977	2020-11-26 16:30:52.977
AddWeekEnd3	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd3
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'10:00' and  InTime<'13:20' then '10:20-13:20' when InTime>'13:19' and inTime<'16:30' then '13:30-16:30'  when InTime>'16:29'and inTime<'19:40'  then '16:40-19:40' when InTime>'19:40' then '20:00以后' when InTime>'00:00' and inTime<'05:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'百威(纯生)'+'%'and CashType<>'X'  and FdCName not like '%'+'百威(纯生)%跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='百威纯生啤酒' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and( fdno='0163'or  fdno='0165') and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2013-04-16 16:40:25.280	2024-05-21 10:22:12.363
AddWeekEnd24	SQL_STORED_PROCEDURE	
CREATE PROCEDURE AddWeekEnd24
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case   when InTime>'19:40'and inTime<'23:59' then '20:00以后' when  InTime>'00:00' and inTime<'01:00'  then '20:00以后' when inTime>'00:59'and inTime<'06:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and (InTime between '19:30' and '23:59' or intime between '00:00'and '06:00')  order by InTime open WeekEnd_Cursor
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'特醇嘉士伯1箱劵'+'%'and CashType<>'X'  and FdCName not like '%'+'特醇嘉士伯1箱劵%跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='特醇嘉士伯1箱劵' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End
	2013-04-16 16:40:40.590	2024-05-21 10:22:12.370
AddWeekEndlxyl	SQL_STORED_PROCEDURE	
CREATE PROCEDURE AddWeekEndlxyl
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case when InTime>'10:00' and  InTime<'13:20' then '10:20-13:20' when InTime>'13:19' and inTime<'16:30' then '13:30-16:30'  when InTime>'16:29'and inTime<'19:40'  then '16:40-19:40' when InTime>'19:39'and inTime<'23:59' then '20:00以后' when  InTime>'00:00' and inTime<'01:00'  then '20:00以后' when inTime>'00:59'and inTime<'06:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '00:00' and '23:59' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'兴业信用卡满200减100优惠'and CashType<>'X'   group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='兴业信用卡满200减100优惠' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2013-04-16 16:40:48.263	2024-05-21 10:22:12.373
AddWeekEnd12	SQL_STORED_PROCEDURE	
CREATE PROCEDURE AddWeekEnd12
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case   when InTime>'19:40'and inTime<'23:59' then '20:00以后' when  InTime>'00:00' and inTime<'01:00'  then '20:00以后' when inTime>'00:59'and inTime<'06:00' then '1:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and (InTime between '19:51' and '23:59' or intime between '00:00'and '06:00')  order by InTime open WeekEnd_Cursor
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'消耗品'+'%'and CashType<>'X'  and FdCName not like '%'+'消耗品%跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='消耗品' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2013-04-16 17:23:11.827	2024-05-21 10:22:12.377
AddWeekEnd_yrr	SQL_STORED_PROCEDURE	

CREATE PROCEDURE AddWeekEnd_yrr
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case  when InTime>'22:59'  then '20:00以后' when InTime>'00:00'and inTime<'06:59' then '01:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and (InTime between '00:00' and '07:00')or(InTime between '20:37' and '23:59' ) order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like  '%'+'夜游人'+'%' and CashType<>'X'  group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='夜游人' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and (fdno='2621'or fdno='2622') and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2013-05-06 17:00:06.780	2024-05-21 10:22:12.383
AddWeekEnd_mty	SQL_STORED_PROCEDURE	

CREATE PROCEDURE AddWeekEnd_mty
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case  when InTime>'00:30'and inTime<'06:59' then '01:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and (InTime between '00:59' and '07:00')order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like  '%'+'猫头鹰'+'%' and CashType<>'X'  group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='猫头鹰' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and (fdno='2610'or fdno='2613') and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End

	2013-05-06 17:39:19.327	2024-05-21 10:22:12.387
AddWeekEnd_cxl	SQL_STORED_PROCEDURE	

CREATE PROCEDURE AddWeekEnd_cxl
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case  when InTime>'00:30'and inTime<'06:59' then '01:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and (InTime between '00:55' and '07:00')order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like  '%'+'唱享乐'+'%' and CashType<>'X'  group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='唱享乐' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and (fdno='3501'or fdno='3502'or fdno='3503'or fdno='3504'or fdno='3505') and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End

	2013-05-06 18:20:30.730	2024-05-21 10:22:12.390
AddWeekEnd_hlx	SQL_STORED_PROCEDURE	
CREATE PROCEDURE AddWeekEnd_hlx
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case  when InTime>'19:39'  then '20:00以后' when InTime>'00:00'and inTime<'06:59' then '01:00以后'  End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and(InTime between '00:20' and '23:59' ) order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like  '%'+'大美买断'+'%' and CashType<>'X'  group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='大美买断' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and (fdno like'52%'or fdno like'53%') and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2013-05-06 18:20:40.780	2024-05-21 10:22:12.390
RoomGiftAmountStatistics	SQL_STORED_PROCEDURE	CREATE PROCEDURE [dbo].[RoomGiftAmountStatistics]--代订房赠送金额统计
AS
BEGIN
	DECLARE @Ratio INT --金额统计比例
	DECLARE @OrderUserId VARCHAR(7); --员工工号
	DECLARE @OrderUserName VARCHAR(20);--员工名称
	DECLARE @Tot INT; --订单总金额
	DECLARE @SceneNum INT;--用户绑定场景数量
	DECLARE @Price INT;--当月代订平分金额
	DECLARE @UsePrice INT;--当月已使用金额
	DECLARE @OffLineUsePrice INT;--线下使用金额
	
	DECLARE @AccountId uniqueidentifier,@AllocationId uniqueidentifier, @SceneId INT;
	DECLARE @BeginTime Datetime,@EndTime DateTime;
	SET @BeginTime = DATEADD(MONTH, DATEDIFF(MONTH, 0, GETDATE()), 0);--当月第一天
	SET @EndTime = dateadd(ms, -2, dateadd(MONTH, datediff(MONTH, 0, GETDATE()) + 1, 0))--当月最后一天
	
	--各店统计金额占比
	SET @Ratio = 8;
	--查询前一天的开房下单数据
	DECLARE FdInv_Cursor CURSOR KEYSET FOR SELECT operator_Name,sum(b.a_tot + b.b_tot - b.c_tot) AS Tot FROM CLOUDMIMS.OperateData.dbo.reporting_basic as a 
	JOIN CLOUDMIMS.OperateData.dbo.reporting_ddf b ON a.Rb_No = b.Rb_No 
	JOIN CLOUDMIMS.OperateData.dbo.reporting_to_examine c ON b.Rb_No = c.Rte_apply_No 
	WHERE c.Rte_time BETWEEN @BeginTime AND @EndTime AND c.Rs_Id = 2 AND a.operator_Name != '' AND ShopID = 8 GROUP BY operator_Name

	--先清空所有人的账户余额，再重新统计
	UPDATE GiftAccount SET Amount = 0,ReAmount = 0;
	UPDATE GiftAccountSceneAllocation SET Amount = 0,ReAmount = 0;
	
	OPEN FdInv_Cursor
	IF(@@CURSOR_ROWS > 0)
	BEGIN
		FETCH NEXT FROM FdInv_Cursor INTO @OrderUserName,@Tot
		WHILE @@FETCH_STATUS=0
		BEGIN 
			SET @OrderUserId = ''
			--查询当前员工在天王的工号
			SELECT @OrderUserId = ISNULL(UserId, '') FROM FdUser WHERE CName LIKE '%'+ @OrderUserName +'%'
			IF(@OrderUserId != '')--查到当前员工工号，才继续操作
			BEGIN		
				--查询当前用户的所有可下单场景数量
				SELECT @SceneNum = COUNT(0) FROM UserInfo_Binding as a
				JOIN GiftRole b ON a.RoleId = b.Id
				JOIN GiftSceneRoleBinding c on b.Id = c.RoleId
				JOIN GiftScene d ON c.SceneId = d.Id
				JOIN GiftAccountSceneAllocation e ON d.Id = e.SceneId AND a.AccountId = e.AccountId
				WHERE a.UserId = @OrderUserId AND b.Status = 1 AND a.Status = 1 AND d.Status = 1 AND d.IsDeducte = 1
				GROUP BY d.Id
				/*计算平分到每个场景的金额（向下取整）*/
				SELECT @Price = ROUND(@Tot * @Ratio / @SceneNum / 100, 0)

				--计算线下使用金额，平分进每个场景
				SELECT @OffLineUsePrice = ISNULL(SUM(b.FdQty * b.FdPrice), 0)  FROM FdInv a
				JOIN FdCashBak b ON a.InvNo = b.InvNo
				WHERE a.WorkDate BETWEEN @BeginTime AND @EndTime
				AND b.CashType = 'Z' AND CashUserId = @OrderUserId
				SET @OffLineUsePrice = ROUND(@OffLineUsePrice / @SceneNum, 0);
				
				--查询当前用户的所有可下单的场景
				DECLARE Scene_Cursor CURSOR KEYSET FOR SELECT e.AccountId,e.Id,e.SceneId FROM UserInfo_Binding as a
				JOIN GiftRole b ON a.RoleId = b.Id
				JOIN GiftSceneRoleBinding c on b.Id = c.RoleId
				JOIN GiftScene d ON c.SceneId = d.Id
				JOIN GiftAccountSceneAllocation e ON d.Id = e.SceneId AND a.AccountId = e.AccountId
				WHERE a.UserId = @OrderUserId AND b.Status = 1 AND a.Status = 1 AND d.Status = 1 AND d.IsDeducte = 1
				OPEN Scene_Cursor
				FETCH NEXT FROM Scene_Cursor INTO @AccountId,@AllocationId,@SceneId
				WHILE @@FETCH_STATUS=0
				BEGIN
					SELECT @UsePrice = ISNULL(SUM(b.FdQty * b.FdPrice), 0) FROM EmpGift_Record a
					JOIN EmpGift_Item b ON a.Id = b.RecordId
					WHERE a.UserId = @OrderUserId AND a.CreateTime >= @BeginTime AND a.CreateTime <= @EndTime
					AND b.IsDelete = 0 AND a.AccountId = @AccountId  AND a.SceneId = @SceneId
					
					--修改数据进入赠送额度管理
					UPDATE GiftAccountSceneAllocation SET Amount = @Price,ReAmount = @Price - @UsePrice - @OffLineUsePrice,LastUpdateTime = GETDATE() WHERE Id = @AllocationId;
					UPDATE GiftAccount SET Amount = @Price,ReAmount = @Price - @UsePrice - @OffLineUsePrice,LastUpdateTime = GETDATE() WHERE Id = @AccountId
					--插入数据进入赠送额度管理操作表				
					INSERT INTO GiftAccountOperationRecord(AccountId,SceneId,Count,Amount,Type,Operator,Remark,CreateTime,ShopId)
					VALUES(@AccountId,@SceneId,0,@Price - @UsePrice - @OffLineUsePrice,1,@OrderUserId,'系统结算',GETDATE(),0)
					
					FETCH NEXT FROM Scene_Cursor INTO @AccountId,@AllocationId,@SceneId
				END
				CLOSE Scene_Cursor
				DEALLOCATE Scene_Cursor
			END			
			FETCH NEXT FROM FdInv_Cursor INTO @OrderUserName,@Tot
		END
	END
	CLOSE FdInv_Cursor
	DEALLOCATE FdInv_Cursor
END	2024-11-05 12:21:48.017	2024-12-24 11:09:04.147
api_rmstatus	SQL_STORED_PROCEDURE	CREATE PROCEDURE [dbo].[api_rmstatus]
@rmno varchar(50)=null,
@t varchar(50)='getall_uc',
@invno varchar(12)='',
@shopid int =0,
@token varchar(1000)='',
--2021-10-29 jjy 添加字段
@ermstatus varchar(10)='',
@soundckmsg nvarchar(50)='',
@membertype int=0,
@memberno varchar(50)='',
--2024-11-22 ly 添加字段
@IntegralRule INT = 0,
@PointRule INT = 0,
@ReturnScale INT = 0
AS
Begin
        --declare @sql nvarchar(1000)
-----------获取所有UC状态的房间列别,或根据房间
        IF(@t='getall_uc')
        BEGIN        
                IF @rmno is not null
                        select rmno,InvNo from room where rmno not like '%B' and rmstatus in('U','C','A')and  rmno=@rmno order by rmno
                ELSE
                        select rmno,InvNo from room  where  rmno not like '%B'   and  rmstatus in('U','C','A')  order by rmno        
        END
        ----------*****根据房号查询房间信息
        ELSE IF(@t='getrmno')
        BEGIN
             select *  from room where   rmno=@rmno     --2023.2.17 取服务费金额
        END
        ----------*****查询所有的房间
        ELSE IF(@t='getall')
        BEGIN
                select rmno,InvNo,rmstatus from room where  rmno not like '%B' 
        END
        ----------*****通过账单查询房间
        ELSE IF(@t='getrminvno')
        BEGIN
                select rmno,InvNo,rmstatus from room where  InvNo =@invno
        END
        ELSE IF(@t='getfdcash')
        BEGIN
        select a.*,b.Ftno,b.FdCName,FdPrice1,c.Cname,d.cname as Cashusername from FdCash as a 
		join food as b on a.fdno=b.fdno 
		left join fduser as c on a.userid=c.userid 
		left join fduser as d on a.cashuserid=d.userid 
		where RmNo = @rmno   --2023.2.17 增加Cashusername显示名称  微服务帐单
        END
        else if(@t='getmemberno')
        begin
                        select [ERmStatus],[MemberNo] from [RoomExtend] where RmNo =@rmno
        end
        else if(@t='insertrmextend')
        begin
                        if not exists(select * from [RoomExtend] where RmNo =@rmno)
                        INSERT INTO [dbfood].[dbo].[RoomExtend]
           ([RmNo]
           ,[ERmStatus]
           ,[SoundCkMsg]
           ,[SoundCkTime]
           ,[RmsUpTime]
           ,[MemberType]
           ,[MemberNo],
					 IntegralRule,
					 PointRule,
					 ReturnScale)
     VALUES
           (@rmno
           ,@ermstatus
           ,@soundckmsg
           ,GETDATE()
           ,GETDATE()
           ,@membertype
           ,@memberno,
					 @IntegralRule,
					 @PointRule,
					 @ReturnScale)
           else
           update [dbfood].[dbo].[RoomExtend] set [MemberType]=@MemberType,[MemberNo]=@MemberNo,IntegralRule=@IntegralRule,PointRule=@PointRule,ReturnScale=@ReturnScale where [RmNo]=@rmno
		  
		 UPDATE Room set MemberNo=@memberno where RmNo=@rmno
      select [ERmStatus],[MemberNo] from [RoomExtend] where RmNo =@rmno
	
        end
        --exec @sql;
	 ELSE IF (@t='getroomconfig')
        BEGIN
                         select PrnDIndex from Room where  RmNo =@rmno
        END

	 ELSE IF (@t='getbill') --获取帐单头信息
        BEGIN
               select a.rmno,a.indate,a.intime,b.innumbers,a.invno,a.memberno,a.custname,a.serv,a.fdcost,b.zd,a.rmcost,a.disc,a.fixeddisc,a.tot from qrinfo as a join fdinv as b on a.invno=b.invno where a.RmNo =@rmno
        END



End	2021-09-27 16:13:31.650	2024-11-26 10:18:12.823
SendTotMessage	SQL_STORED_PROCEDURE	
CREATE PROCEDURE SendTotMessage
@StoreID int
AS
Begin
	declare @StoreName nvarchar(10),@MsgStr nvarchar(1000),@Tot nvarchar(1000),@Str10 nvarchar(1000),@Str13 nvarchar(1000),@Str16 nvarchar(1000),@Str20 nvarchar(1000),@Str01 nvarchar(1000),@WeekDay nvarchar(10),@RoomTot int
	select @StoreName=Case @StoreID 
			when 2 then '缤缤店'
			when 3 then '天河店'
			when 4 then '英德店'
			when 5 then '番禺店'
			when 6 then '海印店'
			when 7 then '开福店'
			when 8 then '白云店'
		End
	select @WeekDay=Case ltrim(rtrim(str(DatePart(dw,Dateadd(dd,-1,getdate())))))
			when 1 then '周日：'
			when 2 then '周一：'
			when 3 then '周二：'
			when 4 then '周三：'
			when 5 then '周四：'
			when 6 then '周五：'
			when 7 then '周六：'
		End
	select @RoomTot=isNull(sum(RoomCount),0) from Rms2009.dbo.RoomStatistics where StatistDate=convert(nvarchar(10),Dateadd(dd,-1,getdate()),112) and StoreID=@StoreID
	select @Tot=@StoreName+ltrim(rtrim(str(DatePart(dd,Dateadd(dd,-1,getdate())))))+'号'+@WeekDay+'总开房'+ltrim(rtrim(str(@RoomTot)))+'间，总'+ltrim(rtrim(str(isNull(sum(Tot),0))))+'元' from fdinv where workdate=convert(nvarchar(10),Dateadd(dd,-1,getdate()),112)
	select @Str10='第一档：预'+ltrim(rtrim(str(ReservatCount)))+'，取'+ltrim(rtrim(str(CancelCount)))+'，自来'+ltrim(rtrim(str(StraightToCount)))+'，共开房'+ltrim(rtrim(str(RoomCount)))+'间'+Case IsFull when 0 then '' when 1 then '【'+Remarks+'开满房】' End from Rms2009.dbo.RoomStatistics where StatistDate=convert(nvarchar(10),Dateadd(dd,-1,getdate()),112) and Period='10:20-13:20' and StoreID=@StoreID
	select @Str13='第二档：预'+ltrim(rtrim(str(ReservatCount)))+'，取'+ltrim(rtrim(str(CancelCount)))+'，自来'+ltrim(rtrim(str(StraightToCount)))+'，直落'+ltrim(rtrim(str(StraightSetCount)))+'，共开房'+ltrim(rtrim(str(RoomCount)))+'间'+Case IsFull when 0 then '' when 1 then '【'+Remarks+'开满房】' End from Rms2009.dbo.RoomStatistics where StatistDate=convert(nvarchar(10),Dateadd(dd,-1,getdate()),112) and Period='13:30-16:30' and StoreID=@StoreID
	select @Str16='第三档：预'+ltrim(rtrim(str(ReservatCount)))+'，取'+ltrim(rtrim(str(CancelCount)))+'，自来'+ltrim(rtrim(str(StraightToCount)))+'，直落'+ltrim(rtrim(str(StraightSetCount)))+'，共开房'+ltrim(rtrim(str(RoomCount)))+'间'+Case IsFull when 0 then '' when 1 then '【'+Remarks+'开满房】' End from Rms2009.dbo.RoomStatistics where StatistDate=convert(nvarchar(10),Dateadd(dd,-1,getdate()),112) and Period='16:40-19:40' and StoreID=@StoreID
	select @Str20='20点后：预'+ltrim(rtrim(str(ReservatCount)))+'，取'+ltrim(rtrim(str(CancelCount)))+'，自来'+ltrim(rtrim(str(StraightToCount)))+'，直落'+ltrim(rtrim(str(StraightSetCount)))+'，共开房'+ltrim(rtrim(str(RoomCount)))+'间'+Case IsFull when 0 then '' when 1 then '【'+Remarks+'开满房】' End from Rms2009.dbo.RoomStatistics where StatistDate=convert(nvarchar(10),Dateadd(dd,-1,getdate()),112) and Period='20点后' and StoreID=@StoreID
	select @Str01='01点后：预'+ltrim(rtrim(str(ReservatCount)))+'，取'+ltrim(rtrim(str(CancelCount)))+'，自来'+ltrim(rtrim(str(StraightToCount)))+'，直落'+ltrim(rtrim(str(StraightSetCount)))+'，共开房'+ltrim(rtrim(str(RoomCount)))+'间'+Case IsFull when 0 then '' when 1 then '【'+Remarks+'开满房】' End from Rms2009.dbo.RoomStatistics where StatistDate=convert(nvarchar(10),Dateadd(dd,-1,getdate()),112) and Period='01点后' and StoreID=@StoreID
	select @MsgStr=@Tot+'；'+isNull(@Str10,'第一档：缺少数据')+'；'+isNull(@Str13,'第二档：缺少数据')+'；'+isNull(@Str16,'第三档：缺少数据')+'；'+isNull(@Str20,'20点后：缺少数据')+'；'+isNull(@Str01,'01点后：缺少数据')
	Exec Rms2009.dbo.SendMsg '13728015250', @MsgStr
	Exec Rms2009.dbo.SendMsg '13450270306', @MsgStr
End	2014-01-01 01:13:06.687	2024-05-21 10:22:12.420
OperateData	SQL_STORED_PROCEDURE	

-- =============================================
-- Author:		<Author,,Name>
-- ALTER  date: <ALTER  Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[OperateData]
	@ExType nvarchar(50),--查询类型    
	@Rmno nvarchar(50)='',
	@ShopID int =8,
	@CashUserid nvarchar(10)=NULL,
	@StartTime dateTime=NULL,
	@EndTime dateTime=NULL
	--房号 
AS
BEGIN
declare @StartTimeStr nvarchar(50),@EndTimeStr nvarchar(50),@StartTimeNum nvarchar(50),@EndTimeNum nvarchar(50)
if(@StartTime is NULL)
begin
	set @StartTime=GETDATE()
	set @EndTime=GETDATE()
end
set @StartTimeStr= CONVERT(varchar(100), @StartTime, 23)+' 09:59'
set @EndTimeStr= CONVERT(varchar(100), DATEADD(dd,1, @EndTime), 23)+' 10:00'
set @StartTimeNum= CONVERT(varchar(100), @StartTime, 112)
set @EndTimeNum= CONVERT(varchar(100), DATEADD(dd,1, @EndTime), 112)
--运营数据存储过程用于各店API调用
	if(@ExType='1')
	begin
	create table #fdcbak(FdNo varchar(5),FdQty smallint,CashType varchar(1),CashUserName varchar(10),WorkDate varchar(8),fdcname nvarchar(30))
		
		--预先获取指定时间段小吃，啤酒相关数据
		insert into #fdcbak select b.FdNo ,b.FdQty ,b.CashType ,CashUserName,a.WorkDate,fdcname from fdinv as a join fdcashbak as b on a.invno=b.invno where a.WorkDate>=@StartTimeNum and WorkDate< @EndTimeNum
		insert into #fdcbak select b.FdNo ,b.FdQty ,b.CashType,''as CashUserName ,''as WorkDate,fdcname from food as a join fdcash as b on a.fdno=b.fdno
		--insert into #fdcbak select b.FdNo ,b.FdQty ,b.CashType, '',a.WorkDate,'' from fdinv as a join fdcash as b on a.invno=b.invno where a.WorkDate>=@StartTimeNum and WorkDate< @EndTimeNum

select
isnull((select SUM(Tot) from fdinv where WorkDate>=@StartTimeNum and WorkDate< @EndTimeNum),0) as 营业额_1_元

,isnull(sum(Cash+Cash_Targ),0) as 现金_1_元,isnull(sum(Vesa+Vesa_Targ),0) as 刷卡_1_元,isnull(sum(GZ),0) as 挂账_3_元_s,isnull(sum(AccOkZD),0) as 招待总金额_3_元,isnull(sum(NoPayed),0) as 免单_3_元,isnull(sum(WXPay) ,0) as 微信支付_1_元,isnull(sum(AliPay) ,0) as 支付宝_1_元,isnull(sum(MTPay) ,0) as 美团_4_元_s,isnull(sum(DZPay) ,0) as 大众_4_元,isnull(sum(NMPay) ,0) as 糯米_4_元 
,(select  COUNT(*)  from rms2009.dbo.opencacheinfo  where checkinstatus='3' and case when DateName(hour,ComeTime)>9 then ComeDate else CONVERT(varchar(100), DATEADD(dd,-1,  ComeDate), 112) end >=@StartTimeNum and case when DateName(hour,ComeTime)>9 then ComeDate else CONVERT(varchar(100), DATEADD(dd,-1,  ComeDate), 112) end< @EndTimeNum) as 累计开房数_5_间
,(select COUNT(*) from room where rmstatus='A'and rmno not like '%B') as 当前结账_5_间
,(select COUNT(*) from fdinv where WorkDate>=@StartTimeNum and WorkDate< @EndTimeNum) as 会员消费_5_间
,(select COUNT(*) from rms2009.dbo.rminfo where shopid=@ShopID and( rmstatus='E' or  rmstatus='D')and rmno <>'9999') as 当前可用_5_间
,isnull( (select sum(fdqty) from #fdcbak where  (patindex('%自助餐%',fdcname)>0  or patindex('%团购%',fdcname)>0 )and cashtype='N'),0)  as 'K+自助餐总数_6_人'
,isnull( (select sum(fdqty) from #fdcbak where  cashtype='N'and patindex('%团购%',fdcname)>0),0)  as '团购_6_人'
,isnull( (select sum(fdqty) from #fdcbak where  cashtype='N'and patindex('%自助餐%券%',fdcname)>0 ),0)  as '自助餐券_6_人'
,isnull( (select sum(fdqty) from #fdcbak where  cashtype='N'and patindex('%10:20-13:20%自助餐%',fdcname)>0),0)  as  '10:20档自助餐_7_人'
,isnull( (select sum(fdqty) from #fdcbak where  cashtype='N'and (patindex('%12:00-15:00%自助餐%',fdcname)>0 or patindex('%自助餐直落%15:00-18:00%',fdcname)>0)),0)  as  '12:00档自助餐_7_人'
,isnull( (select sum(fdqty) from #fdcbak where  cashtype='N'and (patindex('%13:30-16:30%自助餐%',fdcname)>0 or patindex('%自助餐直落%13:30-16:30%',fdcname)>0)),0)  as  '13:30档自助餐_7_人'
,isnull( (select sum(fdqty) from #fdcbak where  cashtype='N'and (patindex('%16:40-19:40%自助餐%',fdcname)>0 or patindex('%自助餐直落%16:40-19:40%',fdcname)>0)),0)  as  '16:40档自助餐_7_人'
,isnull(( select sum(fdqty) from #fdcbak where  cashtype='N'and (patindex('%18:00-19:40%',fdcname)>0) and  patindex('09%',fdno)>0),0)  as '18:00档自助餐_7_人'

,isnull((select sum(fdqty) from #fdcbak where (patindex('12%',fdno)>0 or patindex('14%',fdno)>0or patindex('16%',fdno)>0or patindex('17%',fdno)>0)and cashtype<>'X'   ) ,0) as 小吃总数_8_份
,isnull( (select sum(fdqty) from #fdcbak where (patindex('12%',fdno)>0 or patindex('14%',fdno)>0or patindex('16%',fdno)>0or patindex('17%',fdno)>0)and cashtype='N'  ) ,0)  as 小吃销售_8_份
,isnull( (select sum(fdqty) from #fdcbak where (patindex('12%',fdno)>0 or patindex('14%',fdno)>0or patindex('16%',fdno)>0or patindex('17%',fdno)>0)and cashtype='Z'and  cashusername not like'%赠送'  ) ,0)  as 小吃常规赠送_8_份
,isnull( (select sum(fdqty) from #fdcbak where (patindex('12%',fdno)>0 or patindex('14%',fdno)>0or patindex('16%',fdno)>0or patindex('17%',fdno)>0)and cashtype='Z' and   cashusername like'%赠送') ,0)  as 小吃套餐赠送_8_份

,isnull((select sum(fdqty) from #fdcbak where patindex('01%',fdno)>0 and cashtype<>'X') ,0) as 啤酒总数_9_打
,isnull( (select sum(fdqty) from #fdcbak where patindex('01%',fdno)>0 and cashtype='N'  ) ,0)  as 啤酒销售数_9_打
,isnull( (select sum(fdqty) from #fdcbak where patindex('01%',fdno)>0 and cashtype='Z' and  cashusername like'%赠送' ) ,0)  as 啤酒套餐赠送_9_打

--,isnull((select sum(fdqty) from fdinv as a join fdcashbak as b on a.invno=b.invno where fdno='0902' and  WorkDate>=@StartTimeNum and WorkDate< @EndTimeNum),0) 消费人数_9
,(select COUNT(*) from MobilOrderTitle where PayPrice>0 and PayTime is not null and  TradingNo is not null and  @StartTimeStr<InputTime and @EndTimeStr>InputTime ) as 微信下单_10_笔
from dbo.RmCloseInfo where  @StartTimeStr<CloseDatetime and @EndTimeStr>CloseDatetime
	drop table #fdcbak
	end
	ELSE 
	if(@ExType='2')
	BEGIN
		select a.RmNo,a.RmStatus,CustName,AreaID from Room a join Rms2009.dbo.RmInfo b on a.RmNo=b.RmNo  where  a.RmNo!='302' and  a.RmNo!='9999' and b.ShopID=@ShopID  ORDER BY AreaID,a.RmNo
	END
	ELSE 
	if(@ExType='3')
	BEGIN
		exec th_GetFdCashbyRmNo @Rmno
	END
	ELSE 
	if(@ExType='4')
	BEGIN
		select *  from dbo.Room a join dbo.RmType b on a.RtNo=b.RtNo where RmNo=@Rmno
	END
	ELSE 
	if(@ExType='5')--统计小吃明细
	BEGIN
		select fdcname,fdprice
,sum(case when CashType='N' then fdQty else 0 end)as fdqty,(sum(case when CashType='N' then fdQty else 0 end)*fdprice)as tot  
,sum(case when CashType='Z' then fdQty else 0 end)as fdqtyGive ,(sum(case when CashType='Z' then fdQty else 0 end)*fdprice)as totGive 
from fdinv as a join fdcashbak as b on a.invno=b.invno where WorkDate>=@StartTimeNum and  (fdno like('12%')or fdno like('14%')or fdno like('16%')or fdno like('17%'))and cashtype='N'group by fdcname,fdprice,FdNo order by fdcname
	END
	ELSE 
	if(@ExType='6') --微信下单
	BEGIN
		select RmNo,CashUserId,InputUserId,OpenId,WebRequestKey,tot,th_minus_fee,TradingNo,PayPrice, CONVERT(nvarchar(50), PayTime,120)  as PayTime  from MobilOrderTitle where PayPrice>0 and PayTime is not null and  TradingNo is not null and  @StartTimeStr<InputTime and @EndTimeStr>InputTime order by  PayTime  desc					
	END
else
if(@ExType='10') --企业微信下单
	BEGIN
		select a.*,b.cname from MobilUserOrderTitle as a join fduser as b on a.cashuserid=b.userid  where @StartTimeStr<InputTime and @EndTimeStr>InputTime and IsOrdert=1 order by InputTime desc	
	END
else
if(@ExType='11') --查询企业微信指定用户下单信息
	BEGIN
		select a.*,b.cname from MobilUserOrderTitle as a join fduser as b on a.cashuserid=b.userid  where @StartTimeStr<InputTime and @EndTimeStr>InputTime and IsOrdert=1 and a.cashuserid=@CashUserid order by InputTime desc	
	END
ELSE 
	if(@ExType='7') --小吃赠送统计
	BEGIN
	select username,fdcname,sum(fdqty)as tt from fdcashbak as a join fdinv as b on a.invno=b.invno where WorkDate>=@StartTimeNum and WorkDate< @EndTimeNum and (fdno like '12%' or fdno like '13%' or fdno like '14%' or fdno like '16%' or fdno like '17%')and cashtype='Z' group by fdcname,username order by username
				
	END

ELSE 
	if(@ExType='8') --运营统计
	BEGIN
	
		select
		isnull((select SUM(Tot) from fdinv where WorkDate>=@StartTimeNum and WorkDate< @EndTimeNum),0) as 营业额
		,isnull(sum(Cash+Cash_Targ),0) as 现金,isnull(sum(Vesa+Vesa_Targ),0) as 刷卡,isnull(sum(GZ),0) as 挂账,isnull(sum(AccOkZD),0) as 招待总金额,isnull(sum(NoPayed),0) as 免单,isnull(sum(WXPay) ,0) as 微信支付,isnull(sum(AliPay) ,0) as 支付宝,isnull(sum(MTPay) ,0) as 美团,isnull(sum(DZPay) ,0) as 大众,isnull(sum(NMPay) ,0) as 糯米 
		,(select COUNT(*) from MobilOrderTitle where PayPrice>0 and PayTime is not null and  TradingNo is not null and  @StartTimeStr<InputTime and @EndTimeStr>InputTime ) as 微信下单笔
		from dbo.RmCloseInfo where  @StartTimeStr<CloseDatetime and @EndTimeStr>CloseDatetime
				
	END
	if(@ExType='9') --当月运营概况信息
	BEGIN
			create table #CountInfo
		(workdate nvarchar(50),Tot int,Cash int,Vesa int,GZ int,AccOkZD int,NoPayed int,WXPay int,AliPay int,MTPay int,DZPay int,NMPay int,fdinv int,opencache int,snackNum int,snackSaleNum int,snackCGiftNum int,snackTGiftNum int,beerNum int,beerSaleNum int,beerGiftNum int,MobilOrder int)
				
		declare @n int,@i int
		declare @sdate datetime,@edate datetime
		
		set @n=CONVERT(int, Datename(Day,GetDate()))
		set @i=0
		while @n>0
		BEGIN
			create table #dataInfo
			(Tot int,Cash int,Vesa int,GZ int,AccOkZD int,NoPayed int,WXPay int,AliPay int,MTPay int,DZPay int,NMPay int,rmnum int,fdinv int,nrmnum int,opencache int,snackNum int,snackSaleNum int,snackCGiftNum int,snackTGiftNum int,beerNum int,beerSaleNum int,beerGiftNum int,MobilOrder int)
			set @sdate=DATEADD(dd,-@i,GETDATE())
			--set @edate=DATEADD(dd,1,@sdate)
			
			insert into #dataInfo exec [dbo].[OperateData] @ExType='1',@StartTime=@sdate,@EndTime=@sdate
			insert #CountInfo select CONVERT(varchar(5),@sdate,101),Tot ,Cash ,Vesa ,GZ ,AccOkZD ,NoPayed ,WXPay ,AliPay ,MTPay ,DZPay ,NMPay ,fdinv ,opencache ,snackNum ,snackSaleNum ,snackCGiftNum ,snackTGiftNum ,beerNum ,beerSaleNum ,beerGiftNum ,MobilOrder from #dataInfo
		
			set @n=@n-1
			set @i=@i+1	
			drop table #dataInfo
		END
		select workdate as 日期, Tot as 营业额,Cash as 现金,Vesa as 刷卡,GZ as 挂账,AccOkZD as 招待总金额,NoPayed as 免单,WXPay as 微信支付,AliPay as 支付宝,MTPay as 美团,DZPay as 大众,NMPay as 糯米,fdinv as 结账数量,opencache as 开房数量,snackNum as 小吃数量,snackSaleNum as 小吃销售数,snackCGiftNum as 小吃常规赠送,snackTGiftNum as 小吃套餐赠送,beerNum as 啤酒数量,beerSaleNum as 啤酒销售数,beerGiftNum as 啤酒套餐赠送,MobilOrder as 微信下单笔 from #CountInfo order by workdate
		drop table #CountInfo	
		
	END
END	2017-06-27 10:26:23.030	2024-05-21 10:22:12.430
O_OrderSum	SQL_STORED_PROCEDURE	-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
---下单项目统计
--- ----   可根据分类/商品编号直接统计
--------	统计内容：常规下单，常规赠送，套餐赠送
CREATE PROCEDURE [dbo].[O_OrderSum]
	@No nvarchar(50),
	@Time_s DateTime,
	@Time_e DateTime,
	@SelectType int=1, --数据查询类型   1:ftNO查询    2:fdno查询      3:FoodCal表的fdno查询
	@IsList bit=0,--是否查询集合
	@dropTable bit=0--是否清除临时表，跟@IsList关联使用
AS
BEGIN

declare @TimeStr_s nvarchar(50),@TimeStr_e nvarchar(50)
set @TimeStr_s= CONVERT(nvarchar(50),@Time_s,112)
set @TimeStr_e= CONVERT(nvarchar(50),@Time_e,112)

if(@IsList=1)
begin
	declare @n int,@i int
		declare @sdate datetime,@edate datetime
				
		
		set @n=CONVERT(int, Datename(Day,GetDate()))
		
		while @n>1
		BEGIN
		 set @n=@n-1;
				set @sdate=DATEADD(dd,-@n,GETDATE())
				 exec [dbo].[O_OrderSum] @No,@sdate,@sdate,@SelectType,0,1
				
		END
		select * from ##temp
		 drop table ##temp
end
else
begin



			--数据源筛选
			SELECT * INTO #FoodInv
			FROM
			(
				 select InvNo from FdInv where WorkDate>=@TimeStr_s and  WorkDate <=@TimeStr_e
			)AS table_source 
			SELECT * INTO #FoodOrder
			FROM
			(
				select FdQty,FdPrice,CashType,cashusername,isnull(c.CalUnit,1) as CalUnit  from #FoodInv a join  FdCashBak b on a.InvNo=b.InvNo 
				left join FoodCal c on b.FdNo=c.FdNo
				where (@SelectType=1 and b.FdNo in(select  FdNo from Food where FtNo=@No))
				or	(@SelectType=2 and b.FdNo=@No )
				or	(@SelectType=3 and b.FdNo in(select FdNo from FoodCal where FtDetNo=@No))
			)AS table_source 

		--select * from  #FoodInv
			
			--常规下单数据解析
			SELECT * INTO #orderN
			FROM
			(
				select FdQty,FdPrice,CalUnit  from #FoodOrder where  CashType='N'
			)AS table_source 
			
			--常规赠送数据解析
			SELECT * INTO #orderZ
			FROM
			(
				select FdQty,FdPrice,CalUnit  from #FoodOrder  where  CashType='Z' and not ( cashusername   like '%赠送%' or cashusername ='取存酒')
			)AS table_source 
			
			--套餐赠送数据解析
			SELECT * INTO #orderZT
			FROM
			(
				select FdQty,FdPrice,CalUnit from #FoodOrder  where  CashType='Z' and ( cashusername   like '%赠送%' or cashusername ='取存酒')
			)AS table_source 
			
			if OBJECT_ID('tempdb..##temp') is  null
			create table ##temp
	(
	日期  nvarchar(50),
	常规下单 nvarchar(50),
	常规下单金额 nvarchar(50),
	常规赠送  nvarchar(50),
	常规赠送金额 nvarchar(50),
	套餐赠送  nvarchar(50),
	套餐赠送金额  nvarchar(50),
	)	
			insert into ##temp	
		select 
			CONVERT(nvarchar(50), @Time_s,111) as '日期'
			,isnull((select SUM(FdQty*CalUnit) from #orderN),0) as '常规下单'
			 ,isnull((select SUM(FdQty*FdPrice) from #orderN),0) as '常规下单金额'
			 ,isnull((select SUM(FdQty*CalUnit) from #orderZ),0) as '常规赠送'
			 ,isnull((select SUM(FdQty*FdPrice) from #orderZ),0) as '常规赠送金额'
			 ,isnull((select SUM(FdQty*CalUnit) from #orderZT ),0)  as '套餐赠送'
			  ,isnull((select SUM(FdQty*FdPrice) from #orderZT ),0)  as '套餐赠送金额'

			  if(@dropTable=0)
			  begin
			  select * from ##temp
			  drop table ##temp
			  end
			drop  table #orderN
			drop  table #orderZ
			drop  table #orderZT
		end	
END	2017-08-11 17:12:47.367	2024-05-21 10:22:12.467
Rm_Close	SQL_STORED_PROCEDURE	CREATE procedure [dbo].[Rm_Close]
(
  @RmNo varchar(4),
  @CloseUserId varchar(4),
  @Cash int,
  @Cash_Targ int,
  @Vesa int,
  @Vesa_Targ int,
  @VesaNo varchar(30),
  @VesaNo_Targ varchar(30),
  @VesaName varchar(10),
  @VesaName_Targ varchar(10),
  @AccOkZD int,
  @GZ int,
  @GZName varchar(30),
  @MembCard int,
  @CardConsumeMNo varchar(7),
  @NoPayed int,
  @CarId varchar(15)
)as


declare @RmStatus varchar(1), @AccType varchar(1)
DECLARE @IntegralRule INT,@ReturnScale INT,@IntegralValue INT,@ReturnValue INT,@Totle INT,@NonMember BIT,@PointRule INT,@PointValue INT,@ReturnStatus INT;
DECLARE @ExcludeAmount INT --排除计算金额
SET @IntegralValue = 0;
SET @ReturnValue = 0;
SET @PointValue = 0;
SET @ReturnStatus = 0;
SET @ExcludeAmount = 0;
DECLARE @MemberCardNo VARCHAR(50)
DECLARE @MemberCardTypeNo INT
select
   @RmStatus = A.RmStatus,
	 @Totle = A.Tot,
   @AccType = B.AccType,
	 @IntegralRule = C.IntegralRule,
	 @ReturnScale = C.ReturnScale,
	 @PointRule = C.PointRule,
	 @NonMember = ISNULL(C.NonMember,0),
	 @MemberCardNo = C.MemberNo,
	 @MemberCardTypeNo = C.MemberType
  from Room A inner join RmType B on(A.RtNo = B.RtNo)
	INNER JOIN RoomExtend C ON A.RmNo = C.RmNo
where A.RmNo = @RmNo
if ((@AccType = 'A') and (@RmStatus <> 'A')) or ((@AccType = 'B') and (@RmStatus <> 'U'))  return 1


declare @CloseDate varchar(8), @CloseTime varchar(5)
select @CloseDate = convert(varchar(8), getDate(), 112) 
select @CloseTime = convert(varchar(5), getDate(), 108)


declare @Booked bit, @OrderUserId varchar(4)
select @OrderUserId=OrderUserId from Room where RmNo=@RmNo
select @Booked=0
if exists(select UserId from FdUser where UserId=@OrderUserId)
  select @Booked = 1


declare @CloseUserName varchar(10)
select
  @CloseUserName = CName
from FdUser
where UserId=@CloseUserId

declare @CardConsumeMName varchar(10)
select @CardConsumeMName = ''
if (@CardConsumeMNo <> '') and (@MembCard <> 0)
begin
  select @CardConsumeMName = CName
    from Member
  where MemberNo = @CardConsumeMNo
end



declare @InvNo varchar(9)
select @InvNo = InvNo from Room where RmNo = @RmNo
if not exists(select * from FdInv where InvNo = @InvNo) begin
  insert into FdInv(
    InvNo, RmNo, BookDate, BookTime, InDate, InTime, InNumbers, MemberNo, MemberName, OpenUserId, OpenUserName,
    AccUserId, AccUserName, AccDate, AccTime, CustName, OrderUserId, OrderUserName, DiscRate, OutDate, OutTime,
    CloseUserId, CloseUserName, Rem, FdCost, RmCost, ZD, BeerZD, BeerCash, Serv, Disc, Tax, Tot, Cash, Cash_Targ,
    Vesa, VesaName, Vesa_Targ, VesaName_Targ, GZ, AccOKZD, MembCard, NoPayed, WorkDate, Void, VoidUserId,
    VoidUserName, GZOK, GZOKDate, GZOKTime, MorePayed, Booked, CardConsumeMNo, CardConsumeMName, VesaNo, VesaNo_Targ,
    GZName, FixedDisc,SubServ,IntegralRule,ReturnScale,IntegralValue,ReturnValue,NonMember,ReturnStatus,PointRule,PointValue,MemberCardTypeNo)
  values(
    @InvNo, @RmNo, '', '', '', '', 1, '', '', '', '',
    '', '', '', '', '', '', '', 0, '', '',
    '', '', '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, '', 0, '', 0, 0, 0, 0, '', 0, '',
    '', 0, '', '', 0, 0, '', '', '', '',
    '', 0,0,0,0,0,0,0,@ReturnStatus,0,0,@MemberCardTypeNo)
end

IF(@MemberCardNo IS NOT NULL AND @MemberCardNo != '')--是否是会员
BEGIN
	IF(@NonMember = 0)--是否勾选了不积不返
	BEGIN
		
		IF NOT EXISTS(SELECT b.FdNo FROM FdCash a JOIN FoodLabel b ON a.FdNo = b.FdNo WHERE a.RmNo = @RmNo AND b.VoucherType >= 1)--不存在整单不收取金额的商品
		BEGIN
			DECLARE @ReturnPayTot INT 
		  SELECT @ReturnPayTot = ISNULL(SUM(ReturnPayTot), 0) FROM MemberCheckoutInfo WHERE Invno = @InvNo
			IF(@ReturnPayTot <= 0)--未使用过房费返还的
			BEGIN
				--查不享受不积不返的金额，总金额减掉该部分金额
				SELECT  @ExcludeAmount = ISNULL(SUM(a.FdQty * a.FdPrice), 0)  FROM FdCash as a
				JOIN FoodLabel as b ON a.FdNo = b.FdNo
				WHERE a.RmNo = @RmNo AND b.PriceType >= 1
				SET @Totle = @Totle - @ExcludeAmount;
				IF(@Totle < 0) SET @Totle = 0;
								
				IF(@IntegralRule > 0)	SET @IntegralValue = ROUND(@Totle/@IntegralRule, 0)
				IF(@ReturnScale > 0) SET @ReturnValue = ROUND(@Totle*@ReturnScale/100, 0)
				IF(@PointRule > 0) SET @PointValue = ROUND(@Totle/@PointRule, 0)				
				IF(@IntegralRule <= 0 AND @ReturnScale <= 0 AND @PointRule <= 0) SET @ReturnStatus = 1;
			END
		END
		ELSE SET @ReturnStatus = 1;
	END
	ELSE SET @ReturnStatus = 1;
END

update FdInv
  set
    InvNo = @InvNo,
    RmNo = A.RmNo,
    BookDate = A.BookDate,
    BookTime = A.BookTime,
    InDate = A.InDate,
    InTime = A.InTime,
    OpenUserId = A.OpenUserId,
    OpenUserName = B.CName,
    InNumbers = A.InNumbers,
    MemberNo = A.MemberNo,
    MemberName = C.CName,
    CustName = A.CustName,
    Rem = A.Rem,
    OrderUserId = A.OrderUserId,
    OrderUserName = D.CName,
    AccDate = A.AccDate,
    AccTime = A.AccTime,
    AccUserId = A.AccUserId,
    AccUserName = E.CName,
    DiscRate = A.DiscRate,
    CloseUserId = @CloseUserId,
    CloseUserName = @CloseUserName,
    OutDate = @CloseDate,
    OutTime = @CloseTime,
    FdCost = A.FdCost,
    RmCost = A.RmCost,
    AccOkZD = @AccOkZD,
    ZD = A.ZD,
    BeerZD = A.BeerZD,
    BeerCash = A.BeerCash,
    Serv = A.Serv, 
    Disc = A.Disc,
    Tot = A.Tot,
    Cash = @Cash,
    Cash_Targ = @Cash_Targ,
    Vesa = @Vesa,
    VesaName = @VesaName,
    Vesa_Targ = @Vesa_Targ,
    VesaName_Targ = @VesaName_Targ,
    GZ = @GZ,
    MembCard = @MembCard,
    NoPayed = @NoPayed,
    WorkDate = A.WorkDate, 
    MorePayed = A.MorePayed,
    Booked = @Booked,
    CardConsumeMNo = @CardConsumeMNo,
    CardConsumeMName = @CardConsumeMName,
    VesaNo = @VesaNo,
    VesaNo_Targ = @VesaNo_Targ,
    GZName = @GZName,
		SubServ = A.SubServ,
		IntegralRule = @IntegralRule,
		ReturnScale = @ReturnScale,
		IntegralValue = @IntegralValue,
		ReturnValue = @ReturnValue,
		NonMember = @NonMember,
		PointRule = @PointRule,
		PointValue = @PointValue,
		ReturnStatus = @ReturnStatus,
		MemberCardTypeNo = @MemberCardTypeNo
  from Room A
  left outer join FdUser B on(A.OpenUserId = B.UserId)
  left outer join Member C on(A.MemberNo = C.MemberNo)
  left outer join FdUser D on(A.OrderUserId = D.UserId)
  left outer join FdUser E on(A.AccUserId = E.UserId)
  where A.RmNo = @RmNo and FdInv.InvNo = @InvNo




insert into FdInv_B(InvNo, RmNo, BookDate, BookTime, InDate, InTime, OpenUserId, OpenUserName, InNumbers,
  MemberNo, MemberName, CustName, Rem, OrderUserId, OrderUserName, AccDate, AccTime, AccUserId, AccUserName,
  DiscRate, CloseUserId, CloseUserName, OutDate, OutTime, FdCost, RmCost, AccOkZD, ZD, BeerZD, BeerCash, Serv, 
  Disc, Tot, Cash, Cash_Targ, Vesa, VesaName, Vesa_Targ, VesaName_Targ, GZ, MembCard, NoPayed, WorkDate, 
  MorePayed, Booked, CardConsumeMNo, CardConsumeMName, VesaNo, VesaNo_Targ, GZName,SubServ,IntegralRule,ReturnScale,IntegralValue,ReturnValue,NonMember,ReturnStatus,PointRule,PointValue,MemberCardTypeNo)
select
  A.InvNo, A.RmNo, A.BookDate, A.BookTime, A.InDate, A.InTime, A.OpenUserId, B.CName as OpenUserName, A.InNumbers, 
  A.MemberNo, C.CName as MemberName, A.CustName, A.Rem, A.OrderUserId, D.CName as OrderUserName, A.AccDate, A.AccTime,
  A.AccUserId, E.CName as AccUserName, A.DiscRate, @CloseUserId, @CloseUserName, @CloseDate, @CloseTime,
  A.FdCost, A.RmCost, @AccOkZD, A.ZD, A.BeerZD, A.BeerCash, A.Serv,
  A.Disc, A.Tot, @Cash, @Cash_Targ, @Vesa, @VesaName, @Vesa_Targ, @VesaName_Targ, @GZ, @MembCard, @NoPayed, 
  A.WorkDate, A.MorePayed, @Booked, @CardConsumeMNo, @CardConsumeMName, @VesaNo, @VesaNo_Targ, @GZName,A.SubServ,@IntegralRule,@ReturnScale,@IntegralValue,@ReturnValue,@NonMember,@PointRule,@PointValue,@ReturnStatus,@MemberCardTypeNo
  from Room A
  left outer join FdUser B on(A.OpenUserId = B.UserId)
  left outer join Member C on(A.MemberNo = C.MemberNo)
  left outer join FdUser D on(A.OrderUserId = D.UserId)
  left outer join FdUser E on(A.AccUserId = E.UserId)
  where A.RmNo = @RmNo

if @MembCard <> 0 
begin
  update Member set CardAmount = CardAmount - @MembCard where MemberNo = @CardConsumeMNo
end



declare @MemberNo varchar(7), @Tot int, @Amount int
select 
  @MemberNo = MemberNo,
  @Tot = Tot - @AccOkZD - @NoPayed
from Room where RmNo = @RmNo

select @MemberNo = isnull(@MemberNo, '')
if @MemberNo <> ''
begin
  
  declare @OtherMemberOrdered int
  select
    @OtherMemberOrdered = isnull(sum(FdQty * FdPrice), 0)
  from FdCash
  where RmNo = @RmNo and MemberNo <> @MemberNo and MemberNo <> '' and MemberNo is not null

  select @Amount = @Tot - @OtherMemberOrdered
  update Member set Amount = Amount + @Amount where MemberNo = @MemberNo
end


declare @MemberNo1 int, @Amount1 int
declare MemberCash_Cursor cursor keyset for
  select
    MemberNo,
    sum(FdPrice * FdQty)
  from FdCash
  where RmNo = @RmNo and MemberNo<>'' and MemberNo is not Null and CashType in ('A', 'N') and MemberNo<>@MemberNo
  group by MemberNo

open MemberCash_Cursor
fetch next from MemberCash_Cursor into @MemberNo1, @Amount1
while @@fetch_status = 0
begin
  update Member set Amount = Amount + @Amount1 where MemberNo = @MemberNo1
  fetch next from MemberCash_Cursor into @MemberNo1, @Amount1
end
close MemberCash_Cursor
deallocate MemberCash_Cursor


execute Local_SaveFdOrder @RmNo


if @CarId <> ''
begin
  declare @WorkDate varchar(8)
  select @WorkDate = WorkDate from Room where RmNo=@RmNo
  insert into CarLeaveLog(CarId, RmNo, WorkDate, CloseTime)
  values(@CarId, @RmNo, @WorkDate, getdate())
end

execute Rm_Empty @RmNo	2014-02-18 15:18:34.920	2024-12-20 11:42:51.937
CommissionConfig	SQL_STORED_PROCEDURE	CREATE  PROCEDURE [dbo].[CommissionConfig]
	@type int,
	@ftno nvarchar(10)=null,
	@startTime nvarchar(50)=null,
	@endTime nvarchar(50)=null,
	@Shopid nvarchar(50)=null
AS
BEGIN
	if(@type=1)
	begin
		select FtNo,FtCName from FdType  order by FtNo
	end
	else if(@type=2)
	begin
		select FdNo,FtNo,fdcname from Food where (@ftno IS NULL OR FtNo=@ftno  ) order by FdNo
	end
	else if(@type=3)
	begin
	
	select  NEWID() as Ikey,A.InvNo,CashUserId,isnull(CashUserName,'') as CashUserName,FdNo,WorkDate as CashDate,@shopid as shopid,AreaID,a.RmNo,b.FdQty from FdInv a 
join  FdCashBak b on a.InvNo=b.InvNo 
JOIN 
(select * from (
select RmNo+'B' as RmNo,AreaID,ShopID from Rms2009.dbo.RmInfo  where ShopID=@Shopid
union
select RmNo,AreaID,ShopID from Rms2009.dbo.RmInfo  where ShopID=@Shopid )  as  b1 ) as c
 on a.RmNo=c.RmNo
WHERE WorkDate between  @startTime and @endTime and c.ShopID=@Shopid and  b.CashType='N' order by CashUserName
	end
else if(@type=4)
	begin--获取所有房间分类
		select RtNo,RtName from RmType where rtname not like '%B' order by RtNo 
	end
else if(@type=5)
	begin--获取所有预约房间
select custtel,a.custname,bookno,a.rmno,a.rtno,rtname, replace(OpenMemory,'收费','') as OpenMemory,a.invno,ctname,bookmemory,a.orderuserid,orderusername,rmstatus from rms2009.dbo.opencacheinfo as a  join dbfood.dbo.room as b on a.invno=b.invno 
where checkinstatus='3' 
	end
END	2017-10-31 17:38:47.280	2024-05-21 10:22:12.480
AddWeekEnd	SQL_STORED_PROCEDURE	CREATE PROCEDURE AddWeekEnd
@Date nvarchar(8)
AS
Begin
	set nocount on
	declare @ShopName nvarchar(10),@WorkDate nvarchar(8),@TimeName nvarchar(50),@InTime nvarchar(8),@RmNo nvarchar(4),@InvNo nvarchar(9),
	@MemberCard nvarchar(50),@ConType nvarchar(50),@Tot int,@ConList nvarchar(1000),@TempCount int,@FdQty int
	declare WeekEnd_Cursor cursor for select '白云店' as ShopName,WorkDate,Case  when InTime<'13:20' then '10:20-13:20' when Intime>'13:20' and InTime<'16:30' then '13:30-16:30' when intime>'16:30' and InTime<'19:40' then '16:40-19:40' End as TimeName, InTime,RmNo,InvNo,
	Case when left(MemberNo,1)='J' then '积点卡' when left(MemberNo,1)='V' then 'VIP卡' else '' End as MemberCard,'' as ConType,Tot,'' as ConList from FdInv where WorkDate=@Date and InTime between '10:00' and '20:56' order by InTime
	open WeekEnd_Cursor
	fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
	while @@fetch_status=0
		Begin
			select @TempCount=Count(*) from WeekEnd where InvNo=@InvNo
			if @TempCount=0
				Insert into WeekEnd (ShopName,WorkDate,TimeName,InTime,RmNo,InvNo,MemberCard,ConType,Tot,ConList) values ( @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList)
			fetch next from WeekEnd_Cursor into @ShopName,@WorkDate,@TimeName,@InTime,@RmNo,@InvNo,@MemberCard,@ConType,@Tot,@ConList
		End
	close WeekEnd_Cursor
	deallocate WeekEnd_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and FdCName like '%'+'周末享'+'%'and CashType<>'X'  and FdCName not like '%'+'跨'+'%' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  WeekendCount=@FdQty,ConType='周末享' where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare SumFdQty_Cursor cursor for select a.InvNo,sum(FdQty) as FdQty from FdInv as a join fdcashbak as b on b.invno=a.invno where workdate=@Date and fdno='0902' and CashType<>'X' group by a.InvNo order by a.InvNo
	open SumFdQty_Cursor
	fetch next from SumFdQty_Cursor into @InvNo,@FdQty
	while @@fetch_status=0
		Begin
			update Weekend set  ConCount=@FdQty where InvNo=@InvNo
			fetch next from SumFdQty_Cursor into @InvNo,@FdQty
		End
	close SumFdQty_Cursor
	deallocate SumFdQty_Cursor

	declare Weekend_Cursor cursor for select InvNo from weekend where len(ltrim(ConList))=0
	open Weekend_cursor
	fetch next from Weekend_cursor into @InvNo
	while @@fetch_status=0
		Begin
			exec UpdateConlist @InvNo
			fetch next from Weekend_cursor into @InvNo
		End
	close Weekend_cursor
	deallocate Weekend_cursor
	set nocount off
End	2010-07-07 11:33:31.030	2024-05-21 10:22:12.490