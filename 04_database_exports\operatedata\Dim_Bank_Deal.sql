/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : ******** (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : OperateData
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : ******** (11.00.3000)
 File Encoding         : 65001

 Date: 13/08/2025 18:02:09
*/


-- ----------------------------
-- Table structure for Dim_Bank_Deal
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Dim_Bank_Deal]') AND type IN ('U'))
	DROP TABLE [dbo].[Dim_Bank_Deal]
GO

CREATE TABLE [dbo].[Dim_Bank_Deal] (
  [DealSK] int  IDENTITY(1,1) NOT NULL,
  [BankSK] int  NULL,
  [FdNo] varchar(5) COLLATE Chinese_PRC_CI_AS  NULL,
  [DealName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [DealAmount] decimal(18,2)  NULL,
  [SubsidyAmount] decimal(18,2)  NULL,
  [TotalAmount] decimal(18,2)  NULL,
  [ServiceFee] decimal(18,2)  NULL,
  [NetAmount] decimal(18,2)  NULL
)
GO

ALTER TABLE [dbo].[Dim_Bank_Deal] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'团购金额',
'SCHEMA', N'dbo',
'TABLE', N'Dim_Bank_Deal',
'COLUMN', N'DealAmount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'补贴金额',
'SCHEMA', N'dbo',
'TABLE', N'Dim_Bank_Deal',
'COLUMN', N'SubsidyAmount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'总金额',
'SCHEMA', N'dbo',
'TABLE', N'Dim_Bank_Deal',
'COLUMN', N'TotalAmount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'服务费',
'SCHEMA', N'dbo',
'TABLE', N'Dim_Bank_Deal',
'COLUMN', N'ServiceFee'
GO

EXEC sp_addextendedproperty
'MS_Description', N'实收金额',
'SCHEMA', N'dbo',
'TABLE', N'Dim_Bank_Deal',
'COLUMN', N'NetAmount'
GO


-- ----------------------------
-- Auto increment value for Dim_Bank_Deal
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[Dim_Bank_Deal]', RESEED, 21)
GO


-- ----------------------------
-- Primary Key structure for table Dim_Bank_Deal
-- ----------------------------
ALTER TABLE [dbo].[Dim_Bank_Deal] ADD CONSTRAINT [PK__Dim_Bank__E5B2D1805C87AA90] PRIMARY KEY CLUSTERED ([DealSK])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Foreign Keys structure for table Dim_Bank_Deal
-- ----------------------------
ALTER TABLE [dbo].[Dim_Bank_Deal] ADD CONSTRAINT [FK_Dim_Bank_Deal_BankSK] FOREIGN KEY ([BankSK]) REFERENCES [dbo].[Dim_Bank] ([BankSK]) ON DELETE NO ACTION ON UPDATE NO ACTION
GO

