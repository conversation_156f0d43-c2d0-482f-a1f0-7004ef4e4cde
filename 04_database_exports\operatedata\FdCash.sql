/*
 Navicat Premium Dump SQL

 Source Server         : 名堂
 Source Server Type    : SQL Server
 Source Server Version : 11002100 (11.00.2100)
 Source Host           : *************:1433
 Source Catalog        : Dbfood
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11002100 (11.00.2100)
 File Encoding         : 65001

 Date: 21/07/2025 15:40:09
*/


-- ----------------------------
-- Table structure for FdCash
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[FdCash]') AND type IN ('U'))
	DROP TABLE [dbo].[FdCash]
GO

CREATE TABLE [dbo].[FdCash] (
  [IKey] int  IDENTITY(1,1) NOT NULL,
  [RmNo] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [FdNo] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [DiscRate] int DEFAULT 0 NOT NULL,
  [FdPriceBeforeDisc] int DEFAULT 0 NOT NULL,
  [FdPrice] int  NOT NULL,
  [FdQty] smallint  NOT NULL,
  [CashType] varchar(1) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT 'N' NOT NULL,
  [CashDate] varchar(8) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CashTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CashUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [RefNo] varchar(9) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [PrnIndex] varchar(1) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Checked] bit DEFAULT 0 NOT NULL,
  [DeleUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [DeleTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [ToZDTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [UserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [MemberNo] varchar(12) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [InRmCost] bit DEFAULT 1 NOT NULL,
  [Ai] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [AiCost] int DEFAULT 0 NOT NULL,
  [Tag] int DEFAULT 0 NOT NULL,
  [CanServ] bit DEFAULT 1 NOT NULL,
  [CheckUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [CheckConfirmTime] datetime  NULL,
  [CheckConfirmUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [AutoZDFdNo] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [GiveMembNo] varchar(7) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [msrepl_tran_version] uniqueidentifier DEFAULT newid() NOT NULL,
  [OrderId] int DEFAULT 0 NULL,
  [PackageFdNo] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[FdCash] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for FdCash
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[FdCash]', RESEED, 13609371)
GO


-- ----------------------------
-- Indexes structure for table FdCash
-- ----------------------------
CREATE NONCLUSTERED INDEX [IX_FdCash_OrderId]
ON [dbo].[FdCash] (
  [OrderId] ASC
)
GO


-- ----------------------------
-- Triggers structure for table FdCash
-- ----------------------------
CREATE TRIGGER [dbo].[AutoShare_Insert]
ON [dbo].[FdCash]
WITH EXECUTE AS CALLER
FOR INSERT
AS
Begin
	set nocount on
	declare @RmNo nvarchar(50),@ShareFdNo nvarchar(50),@InputUserId nvarchar(50),@ShareFdQty int,@TempCount int,@Str nvarchar(1000),@CashUserId nvarchar(50),@CashType nvarchar(10)
	select @RmNo=RmNo,@ShareFdNo=FdNo,@InputUserId=UserId,@ShareFdQty=FdQty,@CashType=CashType,@CashUserId=CashUserId from Inserted
	if @InputUserId<>'9968' and @InputUserId<>'9969'and @InputUserId<>'在线预订'and @InputUserId<>'9924' and @CashUserId<>'9924' --20240729在线预订微服务下单重复修改  20240812换回@InputUserId
	Begin
		select @TempCount=Count(1) from ShareSetInfo where ShareFdNo=@ShareFdNo and Val1=1 and Val2=0
		if @TempCount>0 
			Begin
				declare @FdNo nvarchar(50),@FdQty int
				declare ShareCursor cursor for select FdNo,FdQty,Val3 from ShareSetInfo where ShareFdNo=@ShareFdNo and Val5<>'9966' union all select FdNo,FdQty,Val3 from ShareSetInfo where ShareFdNo=@ShareFdNo and Val5=@InputUserId--微信下单，当下单的用户ID是9966，则自动下单小食、软件
				open ShareCursor
				fetch next from ShareCursor into @FdNo,@FdQty,@CashUserId
				while @@Fetch_status=0
					Begin
						set @FdQty=@FdQty*@ShareFdQty
	
						declare   @FdPrice int
						execute GetFdTimePrice @FdNo, @RmNo, @FdPrice output
						insert into WebOrderTable (RmNo,IP, FdNo, FdQty, FdPrice, Ai, AiCost,PackageFdNo)values(@RmNo,'*************',@FdNo, @FdQty, @FdPrice, '',0,@ShareFdNo)
						fetch next from ShareCursor into @FdNo,@FdQty,@CashUserId
					End
				close ShareCursor
				deallocate ShareCursor
				exec AutoDoOrder @RmNo,'*************','Z',@CashUserId,@InputUserId
			End
		select @ShareFdQty=Case @ShareFdQty
			when 2 then 2
			when 3 then 2
			when 4 then 4
			when 5 then 4
			Else @ShareFdQty
		End
		select @TempCount=Count(1) from ShareSetInfo where ShareFdNo=@ShareFdNo and @ShareFdQty=Val2 and @CashType='N'
		if @TempCount>0
			Begin
				select @FdNo=FdNo,@FdQty=FdQty  from ShareSetInfo where ShareFdNo=@ShareFdNo and @ShareFdQty=Val2
				insert into WebOrderTable (RmNo,IP, FdNo, FdQty, FdPrice, Ai, AiCost,PackageFdNo)values(@RmNo,'*************',@FdNo, @FdQty, 0, '',0,@ShareFdNo)
				exec AutoDoOrder @RmNo,'*************','Z','9950',@InputUserId
			End
	End
	set nocount off
End
GO

CREATE TRIGGER [dbo].[FdCash_Delete]
ON [dbo].[FdCash]
WITH EXECUTE AS CALLER
FOR DELETE
AS
DELETE FPrnData  FROM Deleted WHERE FPrnData.RefNo=Deleted.RefNo
GO


-- ----------------------------
-- Primary Key structure for table FdCash
-- ----------------------------
ALTER TABLE [dbo].[FdCash] ADD CONSTRAINT [PK_FDCash] PRIMARY KEY NONCLUSTERED ([IKey])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

