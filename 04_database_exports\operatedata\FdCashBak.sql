/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : OperateData
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 23/07/2025 14:19:43
*/


-- ----------------------------
-- Table structure for FdCashBak
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[FdCashBak]') AND type IN ('U'))
	DROP TABLE [dbo].[FdCashBak]
GO

CREATE TABLE [dbo].[FdCashBak] (
  [Bikey] uniqueidentifier  NOT NULL,
  [ShopId] int DEFAULT 0 NOT NULL,
  [<PERSON><PERSON><PERSON>] int  IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  [InvNo] varchar(9) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [FdNo] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [FdCName] varchar(80) COLLATE Chinese_PRC_CI_AS  NULL,
  [DiscRate] int  NOT NULL,
  [FdPriceBeforeDisc] int  NOT NULL,
  [FdPrice] int  NOT NULL,
  [FdQty] smallint  NOT NULL,
  [CashType] varchar(1) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CashTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CashUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CashUserName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [RefNo] varchar(9) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [DeleUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [DeleUserName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [DeleTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [ToZDTime] varchar(5) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [UserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [UserName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [MemberNo] varchar(7) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [MemberName] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [PrnIndex] varchar(1) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [InRmCost] bit  NOT NULL,
  [Ai] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [AiCost] int  NOT NULL,
  [Checked] bit  NOT NULL,
  [Tag] int  NOT NULL,
  [CanServ] bit  NOT NULL,
  [CheckUserId] varchar(4) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [GiveMembNo] varchar(7) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [msrepl_tran_version] uniqueidentifier  NOT NULL,
  [OrderId] int DEFAULT 0 NULL,
  [PackageFdNo] varchar(5) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[FdCashBak] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for FdCashBak
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[FdCashBak]', RESEED, 45904330)
GO


-- ----------------------------
-- Primary Key structure for table FdCashBak
-- ----------------------------
ALTER TABLE [dbo].[FdCashBak] ADD CONSTRAINT [pk_bikey] PRIMARY KEY CLUSTERED ([Bikey])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

