/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : OperateData
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 25/07/2025 11:44:02
*/


-- ----------------------------
-- Table structure for KTV_Simplified_Daily_Report
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[KTV_Simplified_Daily_Report]') AND type IN ('U'))
	DROP TABLE [dbo].[KTV_Simplified_Daily_Report]
GO

CREATE TABLE [dbo].[KTV_Simplified_Daily_Report] (
  [Id] int  IDENTITY(1,1) NOT NULL,
  [ReportDate] date  NULL,
  [ShopName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [Weekday] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NULL,
  [TotalRevenue] decimal(18,2)  NULL,
  [DayTimeRevenue] decimal(18,2)  NULL,
  [NightTimeRevenue] decimal(18,2)  NULL,
  [TotalBatchCount] int  NULL,
  [DayTimeBatchCount] int  NULL,
  [NightTimeBatchCount] int  NULL,
  [FreeMeal_KPlus] int  NULL,
  [FreeMeal_Special] int  NULL,
  [FreeMeal_Meituan] int  NULL,
  [FreeMeal_Douyin] int  NULL,
  [FreeMeal_BatchCount] int  NULL,
  [FreeMeal_Revenue] decimal(18,2)  NULL,
  [Buyout_BatchCount] int  NULL,
  [Buyout_Revenue] decimal(18,2)  NULL,
  [Changyin_BatchCount] int  NULL,
  [Changyin_Revenue] decimal(18,2)  NULL,
  [FreeConsumption_BatchCount] int  NULL,
  [NonPackage_Special] int  NULL,
  [NonPackage_Meituan] int  NULL,
  [NonPackage_Douyin] int  NULL,
  [NonPackage_Others] int  NULL,
  [Night_Verify_BatchCount] int  NULL,
  [Night_Verify_Revenue] decimal(18,2)  NULL
)
GO

ALTER TABLE [dbo].[KTV_Simplified_Daily_Report] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'店名',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'ShopName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'日期',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'Weekday'
GO

EXEC sp_addextendedproperty
'MS_Description', N'晚间档营业额',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'NightTimeRevenue'
GO

EXEC sp_addextendedproperty
'MS_Description', N'总批次',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'TotalBatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'白天档批次',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'DayTimeBatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'晚间档批次',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'NightTimeBatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'自由餐_k+',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'FreeMeal_KPlus'
GO

EXEC sp_addextendedproperty
'MS_Description', N'自由餐_特权预约',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'FreeMeal_Special'
GO

EXEC sp_addextendedproperty
'MS_Description', N'自由餐_美团',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'FreeMeal_Meituan'
GO

EXEC sp_addextendedproperty
'MS_Description', N'自由餐_抖音',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'FreeMeal_Douyin'
GO

EXEC sp_addextendedproperty
'MS_Description', N'自由餐_批次',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'FreeMeal_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'自由餐_营业额',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'FreeMeal_Revenue'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_买断批次',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'Buyout_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_畅饮批次',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'Changyin_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_自由消批次',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'FreeConsumption_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_促销套餐_特权预约',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'NonPackage_Special'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_促销套餐_美团',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'NonPackage_Meituan'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_促销套餐_抖音',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'NonPackage_Douyin'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_其他',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'NonPackage_Others'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_批次小计',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'Night_Verify_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_消费金额',
'SCHEMA', N'dbo',
'TABLE', N'KTV_Simplified_Daily_Report',
'COLUMN', N'Night_Verify_Revenue'
GO


-- ----------------------------
-- Auto increment value for KTV_Simplified_Daily_Report
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[KTV_Simplified_Daily_Report]', RESEED, 11)
GO


-- ----------------------------
-- Primary Key structure for table KTV_Simplified_Daily_Report
-- ----------------------------
ALTER TABLE [dbo].[KTV_Simplified_Daily_Report] ADD CONSTRAINT [PK__KTV_Simp__3214EC0719BC9EFD] PRIMARY KEY CLUSTERED ([Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

