/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : OperateData
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 12/08/2025 11:03:20
*/


-- ----------------------------
-- Table structure for RmCloseInfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RmCloseInfo]') AND type IN ('U'))
	DROP TABLE [dbo].[RmCloseInfo]
GO

CREATE TABLE [dbo].[RmCloseInfo] (
  [Ikey] uniqueidentifier  NOT NULL,
  [Shopid] int DEFAULT 0 NOT NULL,
  [InvNo] nvarchar(9) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Cash] int  NOT NULL,
  [Cash_Targ] int  NOT NULL,
  [Vesa] int  NOT NULL,
  [VesaName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [VesaNo] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Vesa_Targ] int  NOT NULL,
  [VesaName_Targ] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [VesaNo_Targ] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [GZ] int  NOT NULL,
  [GZName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [AccOkZD] int  NOT NULL,
  [ZDName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [NoPayed] int  NOT NULL,
  [NoPayedName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Check] int  NOT NULL,
  [CheckName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [WXPay] int  NOT NULL,
  [OpenId] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [wx_out_trade_no] text COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [AliPay] int  NOT NULL,
  [user_id] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Ali_out_trade_no] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [MTPay] decimal(18,2)  NOT NULL,
  [MTPayNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [DZPay] decimal(18,2)  NOT NULL,
  [DZPayNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [NMPay] decimal(18,2)  NOT NULL,
  [NMPayNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [Coupon] int  NOT NULL,
  [CouponName] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [RechargeAccount] int  NOT NULL,
  [RechargeMemberCardNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [ReturnAccount] int  NOT NULL,
  [ReturnMemberCardNo] nvarchar(100) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CloseDatetime] datetime  NOT NULL,
  [MemberKey] uniqueidentifier  NOT NULL,
  [CloseUserName] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [CloseUserId] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [WechatDeposit] int  NOT NULL,
  [WechatShopping] int  NOT NULL,
  [WorkDate] varchar(8) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [Tot] int DEFAULT 0 NOT NULL,
  [OpenDateTime] datetime  NULL,
  [Numbers] int  NULL,
  [CtNo] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [Beg_Key] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [End_Key] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [WechatOfficialPay] decimal(18,2) DEFAULT 0 NULL,
  [WechatOfficialPayNo] nvarchar(100) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [IsDirectFall] bit DEFAULT 0 NOT NULL
)
GO

ALTER TABLE [dbo].[RmCloseInfo] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'开台时间',
'SCHEMA', N'dbo',
'TABLE', N'RmCloseInfo',
'COLUMN', N'OpenDateTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'客人数',
'SCHEMA', N'dbo',
'TABLE', N'RmCloseInfo',
'COLUMN', N'Numbers'
GO

EXEC sp_addextendedproperty
'MS_Description', N'消费类型',
'SCHEMA', N'dbo',
'TABLE', N'RmCloseInfo',
'COLUMN', N'CtNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'开始的时间段',
'SCHEMA', N'dbo',
'TABLE', N'RmCloseInfo',
'COLUMN', N'Beg_Key'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结束的时间段',
'SCHEMA', N'dbo',
'TABLE', N'RmCloseInfo',
'COLUMN', N'End_Key'
GO


-- ----------------------------
-- Indexes structure for table RmCloseInfo
-- ----------------------------
CREATE NONCLUSTERED INDEX [IX_RmCloseInfo_WorkDate_ShopId]
ON [dbo].[RmCloseInfo] (
  [WorkDate] ASC,
  [Shopid] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table RmCloseInfo
-- ----------------------------
ALTER TABLE [dbo].[RmCloseInfo] ADD CONSTRAINT [PK_RmCloseInfo] PRIMARY KEY CLUSTERED ([Ikey])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

