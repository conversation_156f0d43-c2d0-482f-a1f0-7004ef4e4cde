/*
 Navicat Premium Dump SQL

 Source Server         : 数据库2
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : OperateData
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 04/08/2025 11:48:06
*/


-- ----------------------------
-- Table structure for RmCloseInfo_Day
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RmCloseInfo_Day]') AND type IN ('U'))
	DROP TABLE [dbo].[RmCloseInfo_Day]
GO

CREATE TABLE [dbo].[RmCloseInfo_Day] (
  [Id] int  IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  [Shopid] int  NOT NULL,
  [Turnover] decimal(18,2)  NOT NULL,
  [Tot] decimal(18,2)  NOT NULL,
  [Cash] int  NOT NULL,
  [Cash_Targ] int  NOT NULL,
  [Vesa] int  NOT NULL,
  [GZ] int  NOT NULL,
  [AccOkZD] int  NOT NULL,
  [RechargeAccount] int  NOT NULL,
  [ReturnAccount] int  NOT NULL,
  [NoPayed] int  NOT NULL,
  [WXPay] int  NOT NULL,
  [AliPay] int  NOT NULL,
  [MTPay] decimal(18,2)  NOT NULL,
  [DZPay] decimal(18,2)  NOT NULL,
  [NMPay] decimal(18,2)  NOT NULL,
  [Check] int  NOT NULL,
  [WechatDeposit] int  NOT NULL,
  [WechatShopping] int  NOT NULL,
  [WorkDate] varchar(8) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [WechatOfficialPay] decimal(18,2) DEFAULT 0 NULL
)
GO

ALTER TABLE [dbo].[RmCloseInfo_Day] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for RmCloseInfo_Day
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[RmCloseInfo_Day]', RESEED, 45143)
GO


-- ----------------------------
-- Primary Key structure for table RmCloseInfo_Day
-- ----------------------------
ALTER TABLE [dbo].[RmCloseInfo_Day] ADD CONSTRAINT [PK_RmCloseInfo_Day] PRIMARY KEY CLUSTERED ([Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

