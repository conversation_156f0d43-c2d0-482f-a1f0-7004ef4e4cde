/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : OperateData
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 04/08/2025 11:48:15
*/


-- ----------------------------
-- Table structure for RmCloseInfo_Day
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RmCloseInfo_Day]') AND type IN ('U'))
	DROP TABLE [dbo].[RmCloseInfo_Day]
GO

CREATE TABLE [dbo].[RmCloseInfo_Day] (
  [Id] int  IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
  [Shopid] int DEFAULT 0 NOT NULL,
  [Turnover] int DEFAULT 0 NOT NULL,
  [Tot] int DEFAULT 0 NOT NULL,
  [Cash] int DEFAULT 0 NOT NULL,
  [Cash_Targ] int  NOT NULL,
  [Vesa] int DEFAULT 0 NOT NULL,
  [GZ] int DEFAULT 0 NOT NULL,
  [AccOkZD] int DEFAULT 0 NOT NULL,
  [RechargeAccount] int DEFAULT 0 NOT NULL,
  [ReturnAccount] int DEFAULT 0 NOT NULL,
  [NoPayed] int DEFAULT 0 NOT NULL,
  [WXPay] int DEFAULT 0 NOT NULL,
  [AliPay] int  NOT NULL,
  [MTPay] int DEFAULT 0 NOT NULL,
  [DZPay] int DEFAULT 0 NOT NULL,
  [NMPay] int DEFAULT 0 NOT NULL,
  [Check] int DEFAULT 0 NOT NULL,
  [WechatDeposit] int DEFAULT 0 NOT NULL,
  [WechatShopping] int DEFAULT 0 NOT NULL,
  [WorkDate] varchar(8) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [WechatOfficialPay] decimal(18,2)  NULL
)
GO

ALTER TABLE [dbo].[RmCloseInfo_Day] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for RmCloseInfo_Day
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[RmCloseInfo_Day]', RESEED, 45166)
GO


-- ----------------------------
-- Primary Key structure for table RmCloseInfo_Day
-- ----------------------------
ALTER TABLE [dbo].[RmCloseInfo_Day] ADD CONSTRAINT [PK_RmCloseInfo_Day] PRIMARY KEY CLUSTERED ([Id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

