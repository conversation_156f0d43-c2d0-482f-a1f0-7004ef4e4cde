/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : MIMS
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 22/07/2025 10:43:23
*/


-- ----------------------------
-- Table structure for ShopInfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[ShopInfo]') AND type IN ('U'))
	DROP TABLE [dbo].[ShopInfo]
GO

CREATE TABLE [dbo].[ShopInfo] (
  [ShopID] int DEFAULT 0 NOT NULL,
  [ShopName] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [address] nvarchar(80) COLLATE Chinese_PRC_CI_AS DEFAULT N'..' NOT NULL,
  [orderurl] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [GrouponShow] bit DEFAULT 1 NOT NULL,
  [OpenDate] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [CloseDate] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [UrlTitle] nvarchar(10) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [IsUse] bit DEFAULT 0 NOT NULL,
  [IsMemberCard] bit  NULL
)
GO

ALTER TABLE [dbo].[ShopInfo] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否办卡报表需要字段',
'SCHEMA', N'dbo',
'TABLE', N'ShopInfo',
'COLUMN', N'IsMemberCard'
GO

