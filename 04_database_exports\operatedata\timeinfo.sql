/*
 Navicat Premium Dump SQL

 Source Server         : 名堂
 Source Server Type    : SQL Server
 Source Server Version : 11002100 (11.00.2100)
 Source Host           : *************:1433
 Source Catalog        : rms2019
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11002100 (11.00.2100)
 File Encoding         : 65001

 Date: 17/07/2025 17:57:22
*/


-- ----------------------------
-- Table structure for timeinfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[timeinfo]') AND type IN ('U'))
	DROP TABLE [dbo].[timeinfo]
GO

CREATE TABLE [dbo].[timeinfo] (
  [Ikey] nvarchar(36) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [TimeNo] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [TimeName] nvarchar(25) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [BegTime] int DEFAULT 0 NOT NULL,
  [EndTime] int DEFAULT 0 NOT NULL,
  [IsClocks] bit  NULL,
  [IsSpecial] bit DEFAULT 0 NOT NULL
)
GO

ALTER TABLE [dbo].[timeinfo] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of timeinfo
-- ----------------------------
INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'03B244E2-4A89-462D-A9EC-26510D5C9CFC', N'25', N'17:00-20:00', N'1700', N'2000', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'0415AEF2-F4A1-48E0-B92F-2A5762AEB49A', N'52', N'15:00-19:00', N'1500', N'1900', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'0457be7e-b4b7-4dd8-b695-43836b1db1b6', N'18', N'22:00-end', N'2200', N'650', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'074c7ec4-334c-4e05-b5d8-4df2a5437027', N'05', N'20:00', N'2000', N'600', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'0eb1122f-2ca1-48fd-baa1-04fee770e626', N'16', N'23:00-end', N'2300', N'630', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'0eb41a2c-12f7-4872-b901-3305f45c5c07', N'08', N'11:00-14:00', N'1100', N'1400', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'10AD8A22-6062-4729-985E-59A1AC748B51', N'44', N'19:30-22:30', N'1930', N'2230', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'18d438fc-3461-457b-9b99-ad864220d17e', N'11', N'19:00-end', N'1900', N'500', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'230c9010-1cce-4279-ad8c-44259b4da7c9', N'03', N'16:40-19:40', N'1640', N'1940', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'2406E6FA-D56F-4103-BD0D-FC1083C68AEF', N'32', N'16:50-19:50', N'1650', N'1950', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'26c21cea-bc12-4e36-961a-2eaac7d864f8', N'15', N'19:00-20:30', N'1900', N'2030', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'29409EF0-FEC7-4601-A51E-5290DAE0510F', N'27', N'12:30-15:30', N'1230', N'1530', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'2D20E52F-E24A-417B-8649-EA7662927A79', N'28', N'11:50-14:50', N'1150', N'1450', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'41E0A4D2-FA2C-4979-9782-4760C2735D40', N'34', N'13:00-16:00', N'1300', N'1600', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'424fa7c1-248f-4c5e-8bcf-ad1ce9fa0da0', N'23', N'11:40-14:40', N'1140', N'1440', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'45A4316D-DEBE-4551-988B-331E6E43CFD9', N'40', N'22:00-01:00', N'2200', N'100', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'473417A8-5DAD-4EF7-8FA9-29AC943EF559', N'43', N'16:00-19:00', N'1600', N'1900', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'542BB23E-DC3B-4B5B-83F9-CC7CD77016B2', N'45', N'18:00-20:30', N'1800', N'2030', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'55205AD7-FCC5-4C5E-B6C0-0DFD09BDA42F', N'29', N'18:10-21:10', N'1810', N'2110', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'613cd5cc-a1d4-4d4f-87ee-b1c728bda931', N'12', N'17:10-20:10', N'1710', N'2010', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'62BA4BF3-8667-4279-A9B3-41B48ABF813A', N'37', N'18:00-21:00', N'1800', N'2100', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'6A8C9E3E-9163-4A97-9947-32D38FC845EC', N'39', N'19:00-22:00', N'1900', N'2200', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'6D5050A3-0473-405A-824F-C16CAC9FB2A4', N'47', N'22:00-02:00', N'2200', N'200', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'7AED56F0-D108-4F53-B6C3-FE8A3ED6B9E0', N'26', N'20:20', N'2020', N'600', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'814DBA50-BBD9-4B99-93D5-0710A6D7A0D2', N'49', N'13:00-18:00', N'1300', N'1800', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'88A4B507-94CC-41CA-ABAB-0FEA0C4877A3', N'36', N'12:00-16:30', N'1200', N'1630', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'8ba232c4-7b06-47e1-8adf-856db97ec71d', N'19', N'20:20-end', N'2020', N'600', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'8c025991-cdfd-4728-8d66-36107aff4186', N'04', N'18:00-19:40', N'1800', N'1940', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'9BF01137-1FB0-4270-8DC4-5682AA7787D2', N'48', N'20:00-End', N'2000', N'600', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'A0475D49-DD17-4908-993E-955F2A2A11E2', N'31', N'13:40-16:40', N'1340', N'1640', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'A1BBC95A-DD59-440B-AFF9-80C86837D963', N'35', N'18:00-20:40', N'1800', N'2040', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'a83d473f-061f-46f0-a293-125eb7debf52', N'07', N'15:00-18:00', N'1500', N'1800', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'bb716c8c-9a36-4a17-a4c9-b1dbe95a63d9', N'17', N'19:40-24:00', N'1940', N'0', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'BE26B8D0-0E9E-42EE-86E3-1B1FAA078A3D', N'51', N'12:00-18:00', N'1200', N'1800', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'c71e4aca-7188-45c9-8b04-39007f5d666e', N'13', N'18:40-20:40', N'1840', N'2040', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'cd70ec05-6e8f-42c1-accb-8a899e8b572a', N'06', N'12:00-15:00', N'1200', N'1500', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'cd87d180-b4db-422a-95d6-d4ccc2e2c584', N'10', N'10:50-13:50', N'1050', N'1350', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'D63C35E7-F204-471B-A133-3D2322A925D8', N'30', N'13:20-16:20', N'1320', N'1620', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'd7e1db14-48cd-43b1-bcb9-55ff8e5f3ddf', N'01', N'10:20-13:20', N'1020', N'1320', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'd8097b7c-50c5-4f6a-9e26-e4a03bd9bc4a', N'02', N'13:30-16:30', N'1330', N'1630', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'DB63078F-2799-4E61-BC82-DEB93AE29BA9', N'38', N'11:30-14:30', N'1130', N'1430', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'db8e5376-4d35-444d-aabb-b0d2bcf802b3', N'21', N'18:00-20:00', N'1800', N'2000', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'DE787E43-5413-43B1-BF5F-B739ECE9647E', N'50', N'14:00-17:00', N'1400', N'1700', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'e51015d7-7c6d-4312-a1a3-83fe43782849', N'09', N'14:10-17:10', N'1410', N'1710', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'E757D39A-4BC9-4034-ACD5-D6CB497409A0', N'41', N'15:50-18:50', N'1550', N'1850', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'E91EA937-2449-4DCF-8810-496535779CB5', N'33', N'20:10', N'2010', N'600', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'eece7bc0-c950-426e-91dc-26739dc6fe70', N'24', N'14:50-17:50', N'1450', N'1750', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'f2e09441-46d9-43c5-81ef-3bafe055234f', N'14', N'01:00', N'100', N'600', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'F580B185-4B84-4A42-9B2E-0A9F1D2FEDDE', N'42', N'20:00-24:00', N'2000', N'0', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'f5e4550d-6609-43ac-bb33-ec1271d89c1d', N'22', N'17:30-20:30', N'1730', N'2030', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'f7daf65d-9ab7-421d-a10d-198da4c7001a', N'20', N'21:00-end', N'2100', N'600', N'0', N'0')
GO

INSERT INTO [dbo].[timeinfo] ([Ikey], [TimeNo], [TimeName], [BegTime], [EndTime], [IsClocks], [IsSpecial]) VALUES (N'FEA8E429-46EC-424E-A6C8-AA7B5203550A', N'46', N'19:00-21:30', N'1900', N'2130', N'0', N'0')
GO


-- ----------------------------
-- Primary Key structure for table timeinfo
-- ----------------------------
ALTER TABLE [dbo].[timeinfo] ADD CONSTRAINT [PK_timeinfo] PRIMARY KEY CLUSTERED ([Ikey])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

