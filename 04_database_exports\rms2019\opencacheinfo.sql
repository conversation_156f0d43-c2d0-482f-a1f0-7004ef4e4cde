/*
 Navicat Premium Dump SQL

 Source Server         : 名堂
 Source Server Type    : SQL Server
 Source Server Version : 11002100 (11.00.2100)
 Source Host           : *************:1433
 Source Catalog        : rms2019
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11002100 (11.00.2100)
 File Encoding         : 65001

 Date: 18/07/2025 13:33:52
*/


-- ----------------------------
-- Table structure for opencacheinfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[opencacheinfo]') AND type IN ('U'))
	DROP TABLE [dbo].[opencacheinfo]
GO

CREATE TABLE [dbo].[opencacheinfo] (
  [Ikey] varchar(36) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [BookNo] varchar(4) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [ShopId] int DEFAULT 0 NOT NULL,
  [CustKey] varchar(36) COLLATE Chinese_PRC_CI_AS  NULL,
  [CustName] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [CustTel] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [ComeDate] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [ComeTime] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [Beg_Key] nvarchar(10) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NOT NULL,
  [Beg_Name] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [End_Key] nvarchar(10) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NOT NULL,
  [End_Name] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [Numbers] int DEFAULT 0 NOT NULL,
  [RtNo] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [RtName] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [CtNo] int DEFAULT 0 NOT NULL,
  [CtName] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [PtNo] int DEFAULT 0 NOT NULL,
  [PtName] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [BookMemory] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [BookStatus] int DEFAULT 0 NOT NULL,
  [CheckinStatus] int DEFAULT 0 NOT NULL,
  [BookShopId] int DEFAULT 0 NOT NULL,
  [BookUserId] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [BookUserName] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [BookDateTime] datetime  NOT NULL,
  [Invno] nvarchar(15) COLLATE Chinese_PRC_CI_AS  NULL,
  [Openmemory] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [OrderUserID] nvarchar(20) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [OrderUserName] nvarchar(20) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [RmNo] nvarchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [Val1] int  NOT NULL,
  [FromRmNo] nvarchar(10) COLLATE Chinese_PRC_CI_AS DEFAULT '' NOT NULL,
  [IsBirthday] bit DEFAULT 0 NOT NULL,
  [Remark] nvarchar(500) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
)
GO

ALTER TABLE [dbo].[opencacheinfo] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Indexes structure for table opencacheinfo
-- ----------------------------
CREATE NONCLUSTERED INDEX [IX_ShopId_OpenCacheInfo]
ON [dbo].[opencacheinfo] (
  [ShopId] ASC
)
GO

CREATE NONCLUSTERED INDEX [IX_BookDateTime_OpenCacheInfo]
ON [dbo].[opencacheinfo] (
  [BookDateTime] ASC
)
GO


-- ----------------------------
-- Triggers structure for table opencacheinfo
-- ----------------------------
CREATE TRIGGER [dbo].[delTrigger]
ON [dbo].[opencacheinfo]
WITH EXECUTE AS CALLER
FOR DELETE
AS
BEGIN
	
		---************将食品卡头打印记录移除
		declare @OpenKey  nvarchar(36)
		SELECT @OpenKey=Ikey  FROM deleted;
		delete PrintRecord  where OpenKey=@OpenKey

END
GO

CREATE TRIGGER [dbo].[insertTrigger]
ON [dbo].[opencacheinfo]
WITH EXECUTE AS CALLER
FOR INSERT
AS
BEGIN
		
		---************将食品卡头打印记录插入，用于打印程序使用
		declare @OpenKey varchar(36),@ShopId int,@ComeDate nvarchar(10)
		SELECT @OpenKey=Ikey,@ShopId=ShopId,@ComeDate=ComeDate  FROM inserted;
		insert PrintRecord (OpenKey,ShopId,ComeDate,IsPrint,InputTime)values(@OpenKey,@ShopId,@ComeDate,0,getdate())

END
GO


-- ----------------------------
-- Primary Key structure for table opencacheinfo
-- ----------------------------
ALTER TABLE [dbo].[opencacheinfo] ADD CONSTRAINT [PK_opencacheinfo] PRIMARY KEY CLUSTERED ([Ikey])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

