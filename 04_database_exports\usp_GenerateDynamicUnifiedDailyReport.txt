ALTER PROCEDURE dbo.usp_GenerateDynamicUnifiedDailyReport
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11,
    @lang NVARCHAR(10) = 'EN' -- 语言参数: 'EN' (英文), 'ZH' (中文)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX) = N'';
    DECLARE @pivot_columns NVARCHAR(MAX) = N'';
    DECLARE @static_columns NVARCHAR(MAX) = N''; -- 用于存放静态列

    -- 1. 动态生成需要展示的列名。
    -- 这里的逻辑是正确的，它根据 TimeMode = 1 来决定创建哪些列。
    SELECT @pivot_columns = @pivot_columns + 
        CASE @lang
            WHEN 'ZH' THEN
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.KPlus_Count END), 0) AS [' + ti.TimeName + '_K+]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Special_Count END), 0) AS [' + ti.TimeName + '_特权预约]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Meituan_Count END), 0) AS [' + ti.TimeName + '_美团]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Douyin_Count END), 0) AS [' + ti.TimeName + '_抖音]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.RoomFee_Count END), 0) AS [' + ti.TimeName + '_房费]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Subtotal_Count END), 0) AS [' + ti.TimeName + '_小计]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.PreviousSlot_DirectFall END), 0) AS [' + ti.TimeName + '_上档直落]'
            ELSE -- 默认为英文
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.KPlus_Count END), 0) AS [' + ti.TimeName + '_KPlus]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Special_Count END), 0) AS [' + ti.TimeName + '_SpecialReservation]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Meituan_Count END), 0) AS [' + ti.TimeName + '_Meituan]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Douyin_Count END), 0) AS [' + ti.TimeName + '_Douyin]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.RoomFee_Count END), 0) AS [' + ti.TimeName + '_RoomFee]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Subtotal_Count END), 0) AS [' + ti.TimeName + '_Subtotal]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.PreviousSlot_DirectFall END), 0) AS [' + ti.TimeName + '_PrevSlotDirectFall]'
        END
    FROM dbo.shoptimeinfo sti
    JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
    WHERE sti.Shopid = @ShopId 
      AND sti.TimeMode = 1 -- 只为 TimeMode = 1 的时段生成列
    ORDER BY ti.BegTime;

    -- 2. 根据语言参数，构建静态列的 SELECT 部分
    IF @lang = 'ZH'
    BEGIN
        SET @static_columns = N'
        h.ReportDate AS [日期], h.ShopName AS [门店], h.Weekday AS [星期],
        ISNULL(h.TotalRevenue, 0) AS [营收_总收入], ISNULL(h.DayTimeRevenue, 0) AS [营收_白天档], ISNULL(h.NightTimeRevenue, 0) AS [营收_晚上档],
        ISNULL(h.TotalBatchCount, 0) AS [全天总批数], ISNULL(h.DayTimeBatchCount, 0) AS [白天档_总批次], ISNULL(h.NightTimeBatchCount, 0) AS [晚上档_总批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [白天档_直落], ISNULL(h.NightTimeDropInBatch, 0) AS [晚上档_直落],
        ISNULL(h.BuffetGuestCount, 0) AS [自助餐人数], ISNULL(h.TotalDirectFallGuests, 0) AS [直落人数]'
        + @pivot_columns + N',
        ISNULL(h.DayTimeBatchCount, 0) AS [k+餐批次], ISNULL(h.DayTimeDropInBatch, 0) AS [k+餐直落批数], ISNULL(h.NightTimeDropInBatch, 0) AS [17点 18点 19点档直落],
        ISNULL(nd.FreeMeal_KPlus, 0) AS [k+自由餐_k+], ISNULL(nd.FreeMeal_Special, 0) AS [k+自由餐_特权预约], ISNULL(nd.FreeMeal_Meituan, 0) AS [k+自由餐_美团], ISNULL(nd.FreeMeal_Douyin, 0) AS [k+自由餐_抖音], ISNULL(nd.FreeMeal_BatchCount, 0) AS [k+自由餐_小计], ISNULL(nd.FreeMeal_Revenue, 0) AS [k+自由餐_营业额],
        ISNULL(nd.Buyout_BatchCount, 0) AS [20点后_买断批次], ISNULL(nd.Changyin_BatchCount, 0) AS [20点后_畅饮套餐], ISNULL(nd.FreeConsumption_BatchCount, 0) AS [20点后_自由消套餐],
        ISNULL(nd.NonPackage_Special, 0) AS [20点后_促销套餐_特权预约], ISNULL(nd.NonPackage_Meituan, 0) AS [20点后_促销套餐_美团], ISNULL(nd.NonPackage_Douyin, 0) AS [20点后_促销套餐_抖音],
        ISNULL(nd.NonPackage_RoomFee, 0) AS [20点后房费批次], ISNULL(nd.NonPackage_Others, 0) AS [20点后其他批次], ISNULL(nd.Night_Verify_BatchCount, 0) AS [20点后_批次小计], ISNULL(nd.Night_Verify_Revenue, 0) AS [20点后_营收金额],
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [招待批次], ISNULL(nd.DiscountFree_Revenue, 0) AS [招待金额]';
    END
    ELSE -- 默认为英文
    BEGIN
        SET @static_columns = N'
        h.ReportDate AS [ReportDate], h.ShopName AS [ShopName], h.Weekday AS [Weekday],
        ISNULL(h.TotalRevenue, 0) AS [Revenue_Total], ISNULL(h.DayTimeRevenue, 0) AS [Revenue_DayTime], ISNULL(h.NightTimeRevenue, 0) AS [Revenue_NightTime],
        ISNULL(h.TotalBatchCount, 0) AS [TotalBatchCount_AllDay], ISNULL(h.DayTimeBatchCount, 0) AS [DayTime_TotalBatch], ISNULL(h.NightTimeBatchCount, 0) AS [NightTime_TotalBatch],
        ISNULL(h.DayTimeDropInBatch, 0) AS [DayTime_DirectFallBatch], ISNULL(h.NightTimeDropInBatch, 0) AS [NightTime_DirectFallBatch],
        ISNULL(h.BuffetGuestCount, 0) AS [BuffetGuestCount], ISNULL(h.TotalDirectFallGuests, 0) AS [TotalDirectFallGuests]'
        + @pivot_columns + N',
        ISNULL(h.DayTimeBatchCount, 0) AS [DayTime_KPlusMeal_BatchCount], ISNULL(h.DayTimeDropInBatch, 0) AS [DayTime_KPlusMeal_DirectFallBatch], ISNULL(h.NightTimeDropInBatch, 0) AS [NightTime_EarlySlots_DirectFallBatch],
        ISNULL(nd.FreeMeal_KPlus, 0) AS [Night_FreeMeal_KPlus], ISNULL(nd.FreeMeal_Special, 0) AS [Night_FreeMeal_SpecialReservation], ISNULL(nd.FreeMeal_Meituan, 0) AS [Night_FreeMeal_Meituan], ISNULL(nd.FreeMeal_Douyin, 0) AS [Night_FreeMeal_Douyin], ISNULL(nd.FreeMeal_BatchCount, 0) AS [Night_FreeMeal_SubtotalBatch], ISNULL(nd.FreeMeal_Revenue, 0) AS [Night_FreeMeal_Revenue],
        ISNULL(nd.Buyout_BatchCount, 0) AS [After8PM_BuyoutBatch], ISNULL(nd.Changyin_BatchCount, 0) AS [After8PM_AllYouCanDrinkPackageBatch], ISNULL(nd.FreeConsumption_BatchCount, 0) AS [After8PM_FreeConsumptionPackageBatch],
        ISNULL(nd.NonPackage_Special, 0) AS [After8PM_Promo_SpecialReservation], ISNULL(nd.NonPackage_Meituan, 0) AS [After8PM_Promo_Meituan], ISNULL(nd.NonPackage_Douyin, 0) AS [After8PM_Promo_Douyin],
        ISNULL(nd.NonPackage_RoomFee, 0) AS [After8PM_RoomFeeBatch], ISNULL(nd.NonPackage_Others, 0) AS [After8PM_OtherBatch], ISNULL(nd.Night_Verify_BatchCount, 0) AS [After8PM_SubtotalBatch], ISNULL(nd.Night_Verify_Revenue, 0) AS [After8PM_Revenue],
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [Complimentary_BatchCount], ISNULL(nd.DiscountFree_Revenue, 0) AS [Complimentary_Revenue]';
    END

    -- 3. 组合成最终的动态 SQL 查询
    SET @sql = N'
    -- **** 新增的核心逻辑：使用CTE预先筛选出所有符合条件的时段详情数据 ****
    ;WITH FilteredTimeSlotDetails AS (
        SELECT
            d.ReportID,
            d.TimeSlotName,
            d.KPlus_Count,
            d.Special_Count,
            d.Meituan_Count,
            d.Douyin_Count,
            d.RoomFee_Count,
            d.Subtotal_Count,
            d.PreviousSlot_DirectFall
        FROM
            dbo.FullDailyReport_TimeSlotDetails AS d
        JOIN
            dbo.timeinfo AS ti ON d.TimeSlotName = ti.TimeName
        JOIN
            dbo.shoptimeinfo AS sti ON ti.TimeNo = sti.TimeNo
        WHERE
            sti.Shopid = ' + CAST(@ShopId AS NVARCHAR) + ' AND sti.TimeMode = 1
    )
    SELECT' + @static_columns + N'
    FROM
        dbo.FullDailyReport_Header AS h
    LEFT JOIN
        -- **** 将原始表的JOIN替换为对CTE的JOIN ****
        FilteredTimeSlotDetails AS d ON h.ReportID = d.ReportID
    LEFT JOIN
        dbo.FullDailyReport_NightDetails AS nd ON h.ReportID = nd.ReportID
    WHERE 
        h.ReportDate BETWEEN ''' + CONVERT(NVARCHAR, @BeginDate, 23) + ''' AND ''' + CONVERT(NVARCHAR, @EndDate, 23) + '''
        AND h.ShopID = ' + CAST(@ShopId AS NVARCHAR) + '
    GROUP BY
        h.ReportID, h.ReportDate, h.ShopName, h.Weekday, h.TotalRevenue, h.DayTimeRevenue, h.NightTimeRevenue,
        h.TotalBatchCount, h.DayTimeBatchCount, h.NightTimeBatchCount, h.DayTimeDropInBatch, 
        h.NightTimeDropInBatch, h.BuffetGuestCount, h.TotalDirectFallGuests,
        nd.FreeMeal_KPlus, nd.FreeMeal_Special, nd.FreeMeal_Meituan, nd.FreeMeal_Douyin,
        nd.FreeMeal_BatchCount, nd.FreeMeal_Revenue, nd.Buyout_BatchCount, nd.Buyout_Revenue,
        nd.Changyin_BatchCount, nd.Changyin_Revenue, nd.FreeConsumption_BatchCount,
        nd.NonPackage_Special, nd.NonPackage_Meituan, nd.NonPackage_Douyin, nd.NonPackage_RoomFee, nd.NonPackage_Others,
        nd.DiscountFree_BatchCount, nd.DiscountFree_Revenue, nd.Night_Verify_BatchCount, nd.Night_Verify_Revenue
    ORDER BY h.ReportDate';

    -- 4. 执行动态 SQL
    -- PRINT @sql; -- 调试时可取消注释以查看生成的SQL语句
    EXEC sp_executesql @sql;
END