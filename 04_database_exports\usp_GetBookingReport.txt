ALTER PROCEDURE dbo.usp_GetBookingReport
    @BeginDate DATE,  -- 开始日期参数
    @EndDate DATE,    -- 结束日期参数
    @ShopID INT,
    @lang NVARCHAR(10) = 'EN' -- 语言参数: 'EN' (英文), 'ZH' (中文)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX) = N'';
    DECLARE @pivot_columns NVARCHAR(MAX) = N'';
    DECLARE @static_columns NVARCHAR(MAX) = N'';
    
    -- 1. 动态生成时间段的列（Pivot Columns）
    SELECT @pivot_columns = @pivot_columns + 
        CASE @lang
            WHEN 'ZH' THEN
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.BookedRooms END), 0) AS [' + ti.TimeName + '_预订房间数]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.BookedGuests END), 0) AS [' + ti.TimeName + '_预订人数]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.OccupiedRooms END), 0) AS [' + ti.TimeName + '_待客房间数]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.OccupiedGuests END), 0) AS [' + ti.TimeName + '_待客人数]' + 
                ', FORMAT(ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.OccupancyRate END), 0), ''P2'') AS [' + ti.TimeName + '_开房率]'
            ELSE -- 默认为英文
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.BookedRooms END), 0) AS [' + ti.TimeName + '_BookedRooms]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.BookedGuests END), 0) AS [' + ti.TimeName + '_BookedGuests]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.OccupiedRooms END), 0) AS [' + ti.TimeName + '_OccupiedRooms]' + 
                ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.OccupiedGuests END), 0) AS [' + ti.TimeName + '_OccupiedGuests]' + 
                ', FORMAT(ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.OccupancyRate END), 0), ''P2'') AS [' + ti.TimeName + '_OccupancyRate]'
        END
    FROM dbo.shoptimeinfo sti
    JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
    WHERE sti.Shopid = @ShopID
    ORDER BY ti.BegTime;

    -- 2. 构建静态列的 SELECT 部分
    IF @lang = 'ZH'
    BEGIN
        SET @static_columns = N'
        h.ReportDate AS [报表日期],
        s.ShopName AS [店铺名称],
        h.Weekday AS [星期],
        ISNULL(h.TotalRevenue, 0) AS [总营业额]'
        + @pivot_columns; 
    END
    ELSE -- 默认为英文
    BEGIN
        SET @static_columns = N'
        h.ReportDate AS [ReportDate],
        s.ShopName AS [ShopName],
        h.Weekday AS [Weekday],
        ISNULL(h.TotalRevenue, 0) AS [TotalRevenue]'
        + @pivot_columns; 
    END

    -- 3. 构建完整的动态 SQL 查询
    SET @sql = N'
    SELECT ' + @static_columns + N'
    FROM 
        dbo.DynamicReport_Header h
    LEFT JOIN 
        dbo.DynamicReport_TimeSlotDetails d ON h.ReportID = d.HeaderReportID
    LEFT JOIN
        MIMS.DBO.SHOPINFO s ON h.ShopID = s.ShopID
    WHERE 
        h.ReportDate BETWEEN ''' + CONVERT(NVARCHAR, @BeginDate, 23) + ''' AND ''' + CONVERT(NVARCHAR, @EndDate, 23) + '''
        AND h.ShopID = ' + CAST(@ShopID AS NVARCHAR) + '
    GROUP BY
        h.ReportDate, h.ReportID, h.Weekday, s.ShopName, h.TotalRevenue
    ORDER BY
        h.ReportDate;'; 

    -- 4. 执行动态 SQL
    EXEC sp_executesql @sql;

END