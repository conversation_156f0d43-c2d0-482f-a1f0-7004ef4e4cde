# 派房提成系统 - API 实现指南

**版本**: 1.0
**最后更新**: 2025-06-24
**关联文档**: `database-design.md`, `commission-rules-qa-summary.md`

## 一、概述

本文档为后端开发人员提供了核心API的实现逻辑。所有实现都必须严格遵循已定义的数据库结构和业务规则。

## 二、API 实现逻辑

### 1. `GET /api/staff/list` - 获取员工列表

**功能**: 提供一个可搜索、可筛选的员工列表，用于前端派房人员选择界面。

**实现逻辑**:

1.  连接数据库。
2.  构建基础SQL查询: `SELECT id, name, position, avatar_url FROM employees WHERE status = 'active'`。
3.  **处理筛选参数**:
    *   如果请求中包含 `keyword` 参数，增加 `WHERE` 子句: `AND name LIKE '%<keyword>%'`。
    *   如果请求中包含 `position` 参数，增加 `WHERE` 子句: `AND position = '<position>'`。
4.  执行查询并返回JSON格式的员工列表。

**伪代码**:
```sql
SELECT id, name, position, avatar_url 
FROM employees 
WHERE status = 'active' 
  AND (?1 IS NULL OR name LIKE '%' || ?1 || '%')
  AND (?2 IS NULL OR position = ?2);
```

### 2. `POST /api/room/assign` - 执行派房操作

**功能**: 创建一次新的派房任务，并根据简化的业务规则计算所有相关费用。

**请求体结构 (简化后)**

```json
{
  "roomId": 101,
  "totalAmount": 2500.00,
  "commissionableAmount": 2150.00,
  "staff": [
    { "staffId": 1, "roles": ["预订"] },
    { "staffId": 2, "roles": ["服务"] },
    { "staffId": 3, "roles": ["服务"] }
  ],
  "referrals": [
    { "recommenderId": 4, "recommendedIds": [2, 3] }
  ],
  "operatorId": 5
}
```

**实现逻辑 (在一个数据库事务中执行)**:

1.  **开启事务**。
2.  **参数校验**: 验证所有传入ID的有效性。
3.  **清除旧数据**: (此步骤仅在修改接口 `PUT /api/room/assignment/:id` 中需要) 
    *   删除 `staff_earnings`、`assignment_staff`、`assignment_referrals` 中与该 `assignment_id` 相关的所有旧记录。
4.  **创建/更新主记录**: 在 `assignments` 表中插入或更新记录。
5.  **记录员工与角色**: 遍历 `staff` 数组，将信息存入 `assignment_staff` 表。
6.  **记录推荐关系**: 遍历 `referrals` 数组，将信息存入 `assignment_referrals` 表。
7.  **计算并插入收入 (核心逻辑)**:
    *   初始化一个空的 `earnings` 集合，用于防止重复计算。
    *   **A. 计算业绩提成**: 
        *   遍历 `staff` 数组，根据员工的角色组合和 `commissionableAmount` 计算业绩提成，并将结果插入 `staff_earnings`。
        *   例如：同时有“服务”和“预订”角色的员工，提成6%；只有其中一个角色的，提成2%。
    *   **B. 计算看房费**: 
        *   遍历 `staff` 数组，为所有角色包含“服务”的员工，计算200元看房费。
        *   在插入 `staff_earnings` 前，检查该员工是否已因其他规则（如下文的推荐）获得看房费，避免重复记入。
    *   **C. 计算推荐费**: 
        *   遍历 `referrals` 数组中的每个推荐关系。
        *   对于每个 `recommenderId`，统计其对应的 `recommendedIds` 数量 `n`。
        *   推荐人的推荐费为 `min(n, 2) * 100` 元（每推荐一人100元，最多200元封顶）。
        *   将推荐费插入 `staff_earnings`。
8.  **提交事务**。
9.  返回成功或失败状态。

---

## 三、相关数据库表结构 (更新后)

### 1. `assignments` - 派房主表

*`assignment_type` 字段已移除。*

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT (PK) | 唯一ID |
| `room_id` | INT (FK) | 关联的房间ID |
| `total_amount`| DECIMAL(10,2) | 消费总额 |
| `commissionable_amount` | DECIMAL(10,2) | 计提基数 |
| `created_at` | DATETIME | 创建时间 |

### 2. `assignment_staff` - 派房员工与角色关联表

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT (PK) | 唯一ID |
| `assignment_id` | INT (FK) | 关联的派房ID |
| `staff_id` | INT (FK) | 关联的员工ID |
| `role` | VARCHAR(50) | 员工在此次派房中的角色 ('服务', '预订') |

### 3. `assignment_referrals` - 推荐关系表 (简化后)

*`has_commission` 字段已移除。*

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT (PK) | 唯一ID |
| `assignment_id` | INT (FK) | 关联的派房ID |
| `recommender_id`| INT (FK) | 推荐人ID |
| `recommended_id`| INT (FK) | 被推荐人ID |
