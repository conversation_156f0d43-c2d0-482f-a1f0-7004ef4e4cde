
# 银行汇总报表 - 后端开发计划

---

## 1. 目标 (Objective)

- 创建一个遵循现有成熟架构的、高性能的 C# .NET 9.0 后端API。
- 该API能够根据前端传递的筛选条件（日期范围、一个或多个银行ID），查询我们已构建的数据仓库。
- API返回经过处理和重组（Pivoting）的JSON数据，其结构应高度匹配`银行汇总表.csv`的矩阵布局，以便Vue3前端能够轻松地直接渲染，最大程度减轻前端的计算压力。

## 2. 核心思路 (Core Idea)

我们将采用“**数据库负责聚合，C#负责塑形**”的策略：

- **数据库 (SQL Server):** 发挥其最擅长的能力，执行一个高效的 `JOIN` 和 `GROUP BY` 查询，以获取一个扁平化的、按“券名-店名”聚合的数据列表。
- **后端 (C# Service):** 接收到这个扁平化的数据列表后，在内存中进行快速的“数据透视”或“塑形”（Pivoting）操作，将其转换成前端需要的、带有层级结构的JSON对象。

## 3. API 端点设计 (API Endpoint Design)

- **端点 (Endpoint):** `GET /api/reports/bank-summary`
- **请求参数 (Request DTO):**
    - `StartDate` (string): "yyyy-MM-dd" 格式的开始日期。
    - `EndDate` (string): "yyyy-MM-dd" 格式的结束日期。
    - `BankSKs` (array of int): 一个包含银行代理键 (`Dim_Bank.BankSK`) 的数组。前端需要先提供一个获取所有银行列表的接口，让用户勾选。

- **响应结构 (Response DTO):** 这是设计的核心，API应返回一个复杂的JSON对象，直接映射报表结构。
    ```json
    {
      "columnHeaders": [ // 动态列头，即所有涉及的门店
        { "shopSK": 2, "shopName": "天河店" },
        { "shopSK": 3, "shopName": "缤缤店" }
        // ... more shops
      ],
      "rows": [ // 报表的每一行
        {
          "dealName": "xxx元xxx自助餐券",
          "dealSK": 101,
          "shopData": { // Key为ShopSK，Value为该门店该券的数据
            "2": { "count": 10, "amount": 1000.00, "subsidy": 200.00 },
            "3": { "count": 5, "amount": 500.00, "subsidy": 100.00 }
          },
          "rowTotal": { // 本行合计
            "count": 15, "amount": 1500.00, "subsidy": 300.00
          }
        }
        // ... more deals
      ],
      "grandTotal": { // 最下方的总计
        "totalAmount": 9999.99,
        "totalSubsidy": 888.88,
        "totalPlatformFee": 777.77,
        "totalNetAmount": 6666.66
      }
    }
    ```

## 4. 后端实现步骤 (Implementation Steps)

在现有架构中，添加以下几个部分：

- **步骤 1: 创建 DTO (Data Transfer Objects)**
    - 根据上面的JSON结构，创建 `BankSummaryRequestDto.cs` 和 `BankSummaryResponseDto.cs` 等一系列C# record或class。

- **步骤 2: 编写数据访问层 (Repository/DAL)**
    - 创建一个方法 `GetBankSummaryDataAsync(DateTime startDate, DateTime endDate, int[] bankSKs)`。
    - 此方法的核心是执行一个SQL查询。推荐使用 **Dapper**，因为它对于处理动态查询和返回结果集非常高效。
    - 这个SQL查询将返回一个 **扁平化的列表**，例如 `List<FlatReportDataDto>`，其中每个 `FlatReportDataDto` 对象包含 `ShopName`, `DealName`, `SumCount`, `SumAmount` 等字段。

- **步骤 3: 编写服务层 (Service)**
    - 创建一个 `ReportService.cs`。
    - 它有一个核心方法 `GenerateBankSummaryReportAsync(BankSummaryRequestDto request)`。
    - **此乃逻辑核心:**
        1.  调用DAL层获取扁平化的数据列表 `List<FlatReportDataDto>`。
        2.  在C#代码中，遍历这个列表，使用 `Dictionary` 或 `Linq.GroupBy` 将其塑造成我们在第3点设计的那个复杂的、有层级的 `BankSummaryResponseDto` 对象。这个过程就是在内存中构建那个JSON结构。
        3.  计算所有行合计与总合计的逻辑也在此层完成。

- **步骤 4: 编写控制器 (Controller)**
    - 创建一个 `ReportsController.cs`。
    - 添加一个 `[HttpGet("bank-summary")]` 的Action方法。
    - 该方法接收 `BankSummaryRequestDto`，调用 `ReportService`，然后将返回的 `BankSummaryResponseDto` 序列化为JSON并返回给前端。

## 5. 核心数据库查询 (Core DB Query Example)

这是DAL层需要执行的SQL查询的范本：

```sql
SELECT
    ds.ShopName,
    ds.ShopSK,
    dd.DealName,
    dd.DealSK,
    SUM(f.RedemptionCount) AS TotalCount,
    SUM(f.RedemptionAmount) AS TotalAmount,
    SUM(f.SubsidyAmount) AS TotalSubsidy,
    SUM(f.PlatformFee) AS TotalPlatformFee,
    SUM(f.NetAmount) AS TotalNetAmount
FROM
    dbo.Fact_Deal_Redemption f
JOIN
    dbo.Dim_Date dt ON f.DateSK = dt.DateSK
JOIN
    dbo.Dim_Shop ds ON f.ShopSK = ds.ShopSK
JOIN
    dbo.Dim_Bank_Deal dd ON f.DealSK = dd.DealSK
JOIN
    dbo.Dim_Bank db ON dd.BankSK = db.BankSK
WHERE
    dt.FullDate BETWEEN @StartDate AND @EndDate
    AND (@BankSKs IS NULL OR db.BankSK IN @BankSKs) -- Dapper可以轻松处理IN查询, @BankSKs为空则查询所有
GROUP BY
    ds.ShopName, ds.ShopSK, dd.DealName, dd.DealSK
ORDER BY
    dd.DealName, ds.ShopName;
```
