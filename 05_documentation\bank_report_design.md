
# 银行优惠券核销报表分析与事实表设计

---

## 第一部分：`银行汇总表.csv` 报表解构分析

`银行汇总表.csv` 文件是一个典型的交叉透视报表（Pivot Table），是数据分析的最终呈现结果。我们的目标是设计一个健壮的数据库模型，以便能够灵活、高效地生成此类报表。

### 1. 报表核心维度 (Dimensions)

维度是分析问题的视角，决定了报表数据的组织方式。

- **优惠券维度 (行):** 以`银行券名称`作为行，代表了分析的主体是“哪一种优惠券”。这直接对应我们已经创建的 `Dim_Bank_Deal` 维度表。
- **门店维度 (列):** 以`天河店`、`缤缤店`等作为列，代表了分析的空间维度是“哪一家门店”。这直接对应已有的 `Dim_Shop` 维度表。
- **时间维度 (筛选):** 报表头部的`日期...至...`表明，所有数据都需要能按任意时间范围进行筛选。这直接对应已有的 `Dim_Date` 维度表。

### 2. 报表核心度量 (Measures)

度量是我们需要量化的数值，是报表的核心内容。

- **基础度量:** `核销量` (优惠券被使用的次数), `核销额` (用户支付的金额), `补贴额` (银行或平台补贴的金额)。
- **计算度量:** `合计` (通常是 `核销额` + `补贴额`)。
- **汇总度量:** `平台服务费`, `合计实收金额` (通常是 `核销额` - `平台服务费`)。

### 3. 核心业务问题

此报表旨在回答：“在指定的时间范围内，任意一个或多个银行优惠券，在任意一个或多个门店的详细核销情况（数量、金额、补贴、费用等）是怎样的？”

---

## 第二部分：`Fact_Deal_Redemption` 事实表设计

为了支撑上述报表的生成，我们需要设计一张新的事实表，用于记录每日的优惠券核销流水。

- **建议表名:** `Fact_Deal_Redemption`
- **数据粒度 (Granularity):** `一券一店一日`。即，表中的每一行代表一个优惠券在一家门店一天内的所有核销活动的总和。

### 建议表结构

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) | 备注 |
| :--- | :--- | :--- | :--- |
| `RedemptionSK` | `BIGINT` (主键) | 代理键，自增 | 用于数据库内部关联 |
| `DateSK` | `INT` | 日期代理键 | 外键，关联到 `Dim_Date` 表 |
| `ShopSK` | `INT` | 门店代理键 | 外键，关联到 `Dim_Shop` 表 |
| `DealSK` | `INT` | 优惠券代理键 | 外键，关联到 `Dim_Bank_Deal` 表 |
| `RedemptionCount` | `INT` | 核销量 | 对应报表中的`核销量` |
| `RedemptionAmount` | `DECIMAL(18, 2)` | 核销额 | 对应报表中的`核销额` |
| `SubsidyAmount` | `DECIMAL(18, 2)` | 补贴金额 | 对应报表中的`补贴额` |
| `PlatformFee` | `DECIMAL(18, 2)` | 平台服务费 | 对应报表中的`平台服务费` |
| `NetAmount` | `DECIMAL(18, 2)` | 实收金额 | 对应报表中的`合计实收金额` |

### 设计价值

- **灵活性:** 通过这张事实表和三张维度表的关联查询，可以轻松地通过 `GROUP BY` 和 `PIVOT` 操作，生成与 `银行汇总表.csv` 完全一致的报表，并且可以支持未来更多样化的分析需求（如下钻、切片）。
- **性能:** 所有计算（如 `合计`）都可以在查询时动态完成，无需在表中冗余存储。事实表和维度表的关联查询在星型模型下性能很高。
- **可扩展性:** 未来如果增加新的维度（如“渠道”），只需在事实表中增加一个外键即可。

---

## 第三部分：下一步工作

此设计方案完成后，我们的下一个核心任务将是：**找到能够提供这张事实表所需数据的源系统和源数据表**。我们需要找到记录了每一笔优惠券核销流水的原始数据表，才能进行后续的ETL（数据抽取、转换、加载）开发。
