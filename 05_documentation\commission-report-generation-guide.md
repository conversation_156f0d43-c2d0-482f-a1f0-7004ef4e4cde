# 客服专员提成报表生成指南

## 1. 概述

本文档旨在说明如何基于《提成系统设计文档v3》中定义的数据库模型，生成月度的《客服专员提成报表》。

核心数据源是 `staff_earnings` 表，该表由后台提成计算引擎生成，包含了所有员工收益和扣除的明细记录。

## 2. 报表生成逻辑

报表生成的本质是一个 SQL 查询，该查询按员工 (`staff_id`) 分组，并对特定月份内的收益/扣除记录进行聚合计算。以下伪 SQL 将展示如何计算报表中的每一列。

**前提**：假设我们要生成 `2025-05` 月份的报表。

```sql
SELECT
    s.staff_name AS "员工姓名",

    -- 看房费部分
    SUM(CASE WHEN e.earning_type = '看房费' AND e.source_category = '自订自看-VIP房及以上' THEN 1 ELSE 0 END) AS "看房费-VIP房-判数",
    SUM(CASE WHEN e.earning_type = '看房费' AND e.source_category = '自订自看-VIP房及以上' THEN e.amount ELSE 0 END) AS "看房费-VIP房-金额",
    SUM(CASE WHEN e.earning_type = '看房费' AND e.source_category = '公司派房' THEN 1 ELSE 0 END) AS "看房费-公司派房-判数",
    SUM(CASE WHEN e.earning_type = '看房费' AND e.source_category = '公司派房' THEN e.amount ELSE 0 END) AS "看房费-公司派房-金额",
    SUM(CASE WHEN e.earning_type = '看房费' AND e.source_category = '同事推荐' THEN 1 ELSE 0 END) AS "看房费-同事推荐-判数",
    SUM(CASE WHEN e.earning_type = '看房费' AND e.source_category = '同事推荐' THEN e.amount ELSE 0 END) AS "看房费-同事推荐-金额",
    -- ... 其他房型（大房及以下）的逻辑类似，此处省略
    SUM(CASE WHEN e.earning_type = '看房费' THEN e.amount ELSE 0 END) AS "看房费小计",

    -- 业绩提成部分
    SUM(CASE WHEN e.earning_type = '业绩提成' AND e.source_category = '自订自看' THEN e.source_amount ELSE 0 END) AS "自订自看(6%)-营业额",
    SUM(CASE WHEN e.earning_type = '业绩提成' AND e.source_category = '自订自看' THEN e.amount ELSE 0 END) AS "自订自看(6%)-提成",
    SUM(CASE WHEN e.earning_type = '业绩提成' AND e.source_category = '公司派房' THEN e.source_amount ELSE 0 END) AS "公司派房(2%)-营业额",
    SUM(CASE WHEN e.earning_type = '业绩提成' AND e.source_category = '公司派房' THEN e.amount ELSE 0 END) AS "公司派房(2%)-提成",
    SUM(CASE WHEN e.earning_type = '业绩提成' AND e.source_category = '同事推荐' THEN e.source_amount ELSE 0 END) AS "同事推荐(2%)-营业额",
    SUM(CASE WHEN e.earning_type = '业绩提成' AND e.source_category = '同事推荐' THEN e.amount ELSE 0 END) AS "同事推荐(2%)-提成",
    SUM(CASE WHEN e.earning_type = '业绩提成' AND e.source_category = '自订非自看' THEN e.source_amount ELSE 0 END) AS "自订非自看(2%)-营业额",
    SUM(CASE WHEN e.earning_type = '业绩提成' AND e.source_category = '自订非自看' THEN e.amount ELSE 0 END) AS "自订非自看(2%)-提成",
    SUM(CASE WHEN e.earning_type = '推荐费' THEN e.amount ELSE 0 END) AS "推荐服务费",
    
    -- 业绩汇总与扣除
    SUM(CASE WHEN e.earning_type IN ('业绩提成', '推荐费') THEN e.amount ELSE 0 END) AS "业绩提成小计",
    ABS(SUM(CASE WHEN e.earning_type = '门店经费扣除' THEN e.amount ELSE 0 END)) AS "门店经费提取(10%)",
    (SUM(CASE WHEN e.earning_type IN ('业绩提成', '推荐费') THEN e.amount ELSE 0 END) + SUM(CASE WHEN e.earning_type = '门店经费扣除' THEN e.amount ELSE 0 END)) AS "个人业绩小计",

    -- 其他提成 (预留)
    SUM(CASE WHEN e.earning_type = '堂会代订房' THEN e.amount ELSE 0 END) AS "堂会代订房",
    SUM(CASE WHEN e.earning_type = '会员充值' THEN e.amount ELSE 0 END) AS "会员充值",
    -- ... 其他预留提成项
    SUM(CASE WHEN e.earning_type IN ('堂会代订房', '会员充值' ...) THEN e.amount ELSE 0 END) AS "其他提成小计",

    -- 最终合计
    SUM(e.amount) AS "个人提成合计"

FROM
    staff_earnings e
JOIN
    staffs s ON e.staff_id = s.staff_id
WHERE
    strftime('%Y-%m', e.created_at) = '2025-05' -- 此处为按月筛选的示例
GROUP BY
    e.staff_id, s.staff_name
ORDER BY
    e.staff_id;

```

## 3. 结论

如上所示，通过对 `staff_earnings` 表进行一次性的分组聚合查询，即可完整、准确地生成月度提成报表所需的全部数据。这证明了我们设计的提成计算引擎和数据模型的合理性与完备性。
