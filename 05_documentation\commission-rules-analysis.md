# 派房提成系统 - 后端规则实现分析

## 一、概述

根据提供的PDF文件及业务描述，提成规则是整个系统的核心与难点。其复杂性主要体现在后端逻辑的实现上。本分析旨在梳理后端实现的关键挑战，并提出设计思路与待确认的业务模糊点，以便指导后续开发。

## 二、后端实现的三大核心挑战

### 挑战一：精准的数据建模

数据模型是所有业务逻辑的基石。为了支撑复杂的提成规则，数据库设计必须精准反映业务细节。

**关键设计点**：
1.  **`Assignments` (派房记录表)**：
    *   必须包含 `AssignmentType` 字段，用于区分 **“自订自看”**、**“公司派房”**、**“同事推荐”** 等核心场景。这是所有规则判断的入口。
    *   需要关联 `RoomType` (房间类型)，以区分不同服务费标准（如VIP房350元 vs 普通房300元）。
    *   应有 `Status` 字段，追踪派房的整个生命周期（如：已指派、服务中、已结算、已取消）。

2.  **`AssignmentEmployees` (派房员工详情表)**：
    *   需要引入 `Role` 字段，用于区分员工在本次服务中的角色，例如：`Primary` (主服务专员), `Recommended` (被推荐专员), `NonSpecialist` (非专员)。
    *   必须记录关键时间戳：`ExpectedEntryTime` (应到时间), `ActualEntryTime` (实际进房时间)，这是计算超时的依据。

3.  **`Referrals` (推荐记录表)**：
    *   用于清晰记录 **“谁推荐了谁”**，以便准确计算100元/位的推荐费。

4.  **`Consumptions` (消费记录表)**：
    *   需要记录用于计算提成的 **“基础消费金额”**，即排除了看房费、打碟费等非提成项目后的金额。

### 挑战二：灵活的“提成规则引擎”

提成规则多变，直接在代码中使用硬编码（Hardcode）的方式会导致后期维护困难。因此，构建一个灵活的“规则引擎”至关重要。

**设计思路**：
1.  **引擎输入**：引擎的输入是一个完整的“派房上下文”对象，包含所有计算所需信息：派房类型、房间类型、消费金额、员工列表（含角色、进房时间）等。
2.  **规则库**：建立一个规则库，每条规则包含 **“条件（Condition）”** 和 **“动作（Action）”**。
    *   **示例规则1 (服务费)**：
        *   **条件**: `AssignmentType` 是 “自订自看” AND `RoomType` 是 “VIP房” AND `EmployeeRole` 是 `Primary`.
        *   **动作**: 计算服务费 `+350` 元。
    *   **示例规则2 (业绩提成)**：
        *   **条件**: `AssignmentType` 是 “公司派房” AND `EmployeeRole` 是 `Primary`.
        *   **动作**: 计算业绩提成 `基础消费金额 * 2%`。
    *   **示例规则3 (超时罚款)**：
        *   **条件**: `ActualEntryTime` 晚于 `ExpectedEntryTime` 超过30分钟。
        *   **动作**: 取消该员工的服务费，并可能调整其业绩提成比例。
3.  **优势**：未来若要调整提成比例（如2%变为2.5%），或增加新规则，只需修改或增加规则库中的条目，无需改动核心业务代码，系统扩展性和维护性极强。

### 挑战三：复杂的状态与时间管理

超时惩罚机制的引入，要求后端必须具备精准的状态和时间管理能力。

**设计思路**：
1.  **状态机**：为每一次派房任务和每一位参与的专员建立状态机，清晰地追踪其状态变化。
2.  **定时任务系统 (Scheduler/Cron Job)**：这是实现超时管理的核心技术。
    *   **流程示例**：当主任在 `14:00` 完成派房，要求专员在 `14:30` 到达时，系统会自动创建一个 **定时任务**，在 `14:40` (即超时10分钟后) 触发。
    *   **任务逻辑**：该任务会检查专员是否已在系统内“确认进房”。如果尚未确认，系统会自动执行惩罚逻辑（如记录罚款、发送通知给主任和专员等）。

## 三、已澄清的业务规则 (基于您的反馈)

根据您的解答，核心业务规则明确如下：

1.  **主任是最终决策者**：所有派房和提成分配的最终决定权和操作权都在“主任”手上。系统提供的是“建议值”，主任拥有最终的修改和审批权限。
2.  **“自订自看”多人服务**：主专员（自订者）获得服务费和6%的业绩提成。被主任拉进来的其他专员，可以获得服务费，但没有业绩提成。
3.  **“自订非自看”**：指A专员预订，但由主任指派B专员提供服务。
4.  **业绩提成上限由主任控制**：系统无需强制设定一个固定的总额上限（如6%），最终由主任来决定总提成如何分配。
5.  **“主任安排空闲的人”**：是“公司派房”的同义词。
6.  **非专员角色**：只能代订，不能参与看房服务，不享受看房服务费和业绩提成。
7.  **同事推荐**：推荐人只是向主任“建议”，最终人选和提成归属由主任决定。
8.  **团建费用**：暂时不纳入系统计算。

## 四、新的待确认问题 (请您优化)

基于以上澄清，我们对规则的理解更加深入。现在，我们需要确认以下几个具体场景下的操作细节：

1.  **主任的核心权力是“决定资格”，而非“修改比例”**
    *   **最终确认**：提成比例是固定的（6%或2%），主任不能修改比例数字。主任的核心权力是，在一个有多人服务的房间里，通过操作界面勾选出“谁有资格”获得这个固定比例的提成。因此，系统需要的是一个“提成资格”的开关或勾选功能，而非一个手动修改金额或比例的输入框。

2.  **“自订非自看”的提成归属**
    *   **场景**：A专员预订，B专员服务。
    *   **最终确认**：2%的业绩提成只给服务者B，预订者A不享受业绩提成。

3.  **推荐费的触发节点**
    *   **场景**：小红推荐了小张，主任也同意了。
    *   **最终确认**：推荐人的100元推荐费，在主任于系统中确认指派小张进入该房间的瞬间，即告生成。

## 五、全部规则已确认

1.  **“自订自看”时，被推荐专员的服务费标准**
    *   **场景**：小红“自订自看”（服务费300/350元），通过推荐增加了专员小张，且顾客同意。
    *   **最终确认**：被推荐的专员小张，其服务费按 **200元** 标准计算。

至此，所有关于派房和提成的业务规则均已澄清。本文档可作为后续技术设计的最终依据。
