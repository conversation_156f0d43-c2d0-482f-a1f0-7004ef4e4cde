# 提成系统设计文档 (V3.0) - 开发人员版

---

## 1. 核心设计思想：两阶段分离

本文档描述的V3.0设计，旨在解决一个核心业务问题：**服务关系的确立（派房）与消费金额的产生（结账）在时间上是分离的**。

因此，我们将整个流程拆分为两个独立阶段：

1.  **服务关系记录阶段**：在“派房”或“分配员工”时，系统只记录“谁、以什么角色、服务了哪个订单”。此阶段完全不涉及金额计算。
2.  **收益计算阶段**：在客户“结账”之后，系统根据最终的消费账单和此前记录的服务关系，进行提成、看房费等收益的计算。

这种分离式设计，使得系统架构更清晰、更健壮，并完美贴合实际业务流程。

---

## 2. 数据模型

系统围绕以下核心表进行构建，通过 `booking_id` (订单ID) 作为关键枢纽进行关联。

-   **`assignments` (服务分配表)**: `assignment_id`, `booking_id`, `staff_id`, `has_commission` (BOOLEAN), `created_at`
-   **`bills` (账单表)**: `bill_id`, `booking_id`, `room_id`, `booking_agent_id` (INT, FK), `total_amount`, `commissionable_amount`, `checkout_time`, `is_commission_calculated` (BOOLEAN, 默认为 `false`)
-   **`bill_items` (账单明细表)**: `item_id`, `bill_id`, `item_name`, `price`, `quantity`
-   **`non_commissionable_items` (不可计提项目表)**: `id`, `item_name`, `is_active` (BOOLEAN)
-   **`staff_earnings` (员工收益明细表)**: `earning_id`, `staff_id`, `booking_id`, `earning_type` (枚举: '业绩提成', '看房费', '推荐费', '门店经费扣除', '会员充值', '主题布置' 等), `amount` (DECIMAL, 扣除项为负数), `source_category` (来源类别), `source_amount` (来源金额), `created_at`
-   **`rooms` (房间表)**: `room_id`, `room_type` (`'VIP房及以上'`, `'大房及以下'`)
-   **`booking_referrals` (推荐关系表)**: `id`, `booking_id`, `recommender_id`, `recommended_staff_id`

---

## 3. 核心业务流程与伪代码实现

### 3.1. 阶段一与二：服务分配与结账

-   **服务分配 API (`POST /assignments`)**: 接收 `booking_id` 和一个包含 `staff_id` 及 `has_commission` 状态的服务人员列表，写入 `assignments` 表。
-   **代订人指定**: 代订人 `booking_agent_id` 应在创建或更新 `bills` 记录时被指定。
-   **推荐关系 API (`POST /bookings/{id}/referrals`)**: 接收 `recommenderId` 和 `recommendedIds` 列表，用于创建或更新 `booking_referrals` 表中的推荐关系记录。
-   **结账 API (`POST /bills/{id}/checkout`)**: 记录账单详情 `bill_items`，更新 `bills` 表中的 `total_amount` 和 `checkout_time`。此时 `is_commission_calculated` 保持为 `false`。

### 3.2. 阶段三：后台计算引擎伪代码实现

后台计算引擎通过一个定时任务（如每分钟执行一次）来处理所有未计算收益的账单。

```pseudocode
// 主任务入口
function process_uncalculated_bills_job():
    // 1. 查找所有未计算的、且已结账的账单
    bills_to_process = query("SELECT * FROM bills WHERE is_commission_calculated = false AND checkout_time IS NOT NULL")

    // 2. 循环处理每个账单
    for bill in bills_to_process:
        calculate_earnings_for_bill(bill)

// 为单个账单计算所有相关收益
function calculate_earnings_for_bill(bill):
    // 步骤 0: 计算并存储计提基数
    commissionable_amount = calculate_and_store_commissionable_amount(bill)
    if commissionable_amount <= 0:
        print(f"账单 {bill.id} 无计提金额，跳过计算。")
        update("UPDATE bills SET is_commission_calculated = true WHERE bill_id = ?", bill.id)
        return

    // 准备数据：获取代订人和所有服务分配记录
    booking_agent_id = bill.booking_agent_id
    assignments = query("SELECT staff_id, has_commission FROM assignments WHERE booking_id = ?", bill.booking_id)

    // 步骤 1 & 2: 为每个服务人员计算收益
    service_staff_ids = []
    for assignment in assignments:
        staff_id = assignment.staff_id
        service_staff_ids.append(staff_id)
        has_commission = assignment.has_commission
        is_booking_agent = (staff_id == booking_agent_id)

        // 1. 计算业绩提成 (仅当 has_commission 为 true)
        if has_commission:
            calculate_commission_for_staff(staff_id, is_booking_agent, commissionable_amount, bill)
        
        // 2. 计算看房费 (所有服务人员都有)
        calculate_viewing_fee_for_staff(staff_id, is_booking_agent, bill)

    // 步骤 3: 额外处理“自订非自看”的代订人提成
    if booking_agent_id and (booking_agent_id not in service_staff_ids):
        db_insert("staff_earnings", {
            staff_id: booking_agent_id,
            booking_id: bill.booking_id,
            earning_type: "业绩提成",
            amount: commissionable_amount * 0.02,
            source_category: "自订非自看",
            source_amount: commissionable_amount
        })

    // 步骤 4: 计算推荐费
    calculate_referral_fee_for_booking(bill)

    // 完成：标记账单为已计算
    update("UPDATE bills SET is_commission_calculated = true WHERE bill_id = ?", bill.id)
    print(f"账单 {bill.id} 的收益计算已完成。")

// --- 步骤 0: 计算计提基数 ---
function calculate_and_store_commissionable_amount(bill):
    excluded_items = query("SELECT item_name FROM non_commissionable_items WHERE is_active = true")
    bill_items = query("SELECT item_name, price, quantity FROM bill_items WHERE bill_id = ?", bill.id)
    
    excluded_amount = 0
    for item in bill_items:
        if item.item_name in excluded_items:
            excluded_amount += item.price * item.quantity
            
    commissionable_amount = bill.total_amount - excluded_amount
    
    // 将计算结果存回 bills 表
    update("UPDATE bills SET commissionable_amount = ? WHERE bill_id = ?", commissionable_amount, bill.id)
    return commissionable_amount

// --- 步骤 1: 计算服务人员的业绩提成 (仅为'有提成'的员工) ---
function calculate_commission_for_staff(staff_id, is_booking_agent, commissionable_amount, bill):
    rate = 0
    source_category = ""

    if is_booking_agent: // 情况A: 自订自看
        rate = 0.06
        source_category = "自订自看"
    else: // 情况B: 公司派房
        rate = 0.02
        source_category = "公司派房"

    if rate > 0:
        amount = commissionable_amount * rate
        db_insert("staff_earnings", {
            staff_id: staff_id,
            booking_id: bill.booking_id,
            earning_type: "业绩提成",
            amount: amount,
            source_category: source_category,
            source_amount: commissionable_amount
        })

// --- 步骤 2: 计算看房费 (只给服务人员) ---
function calculate_viewing_fee_for_staff(staff_id, is_booking_agent, bill):
    fee = 0
    source_category = ""
    room = query_one("SELECT r.room_type FROM rooms r WHERE r.room_id = ?", bill.room_id)

    if is_booking_agent: // 情况A: 自订自看 (服务人员且是代订人)
        if room.room_type == "VIP房及以上":
            fee = 350
            source_category = room.room_type
        else: // 大房及以下
            fee = 300
            source_category = room.room_type
    else: // 情况B: 公司派房 (服务人员但不是代订人)
        fee = 200
        source_category = "公司派房标准看房费"

    if fee > 0:
        db_insert("staff_earnings", {
            staff_id: staff_id,
            booking_id: bill.booking_id,
            earning_type: "看房费",
            amount: fee,
            source_category: source_category,
            source_amount: null
        })

// --- 步骤 3: 计算推荐费 ---
function calculate_referral_fee_for_booking(bill):
    // 从新的 booking_referrals 表中获取所有推荐人
    referrers = query("SELECT DISTINCT recommender_id FROM booking_referrals WHERE booking_id = ?", bill.booking_id)
    
    if not referrers:
        return

    // 获取被推荐的总人数，用于记录 source_amount
    total_recommended_count = query_scalar("SELECT COUNT(id) FROM booking_referrals WHERE booking_id = ?", bill.booking_id)

    fee_per_person = 100
    max_total_fee = 200
    paid_total = 0

    // 按推荐人ID循环，支付推荐费
    for referrer in referrers:
        if paid_total >= max_total_fee:
            break
        
        fee_to_pay = min(fee_per_person, max_total_fee - paid_total)
        
        db_insert("staff_earnings", {
            staff_id: referrer.recommender_id,
            booking_id: bill.booking_id,
            earning_type: "推荐费",
            amount: fee_to_pay,
            source_category: "员工推荐",
            source_amount: total_recommended_count // 记录总被推荐人数
        })
        paid_total += fee_to_pay
```

### 3.3. 辅助函数与实现说明

-   **`group_roles_by_staff(assignments)`**: 这是一个辅助函数，需要将数据库查询出的 `assignments` 列表（可能包含同一员工的多条记录）转换成以 `staff_id` 为键，角色列表为值的字典，方便后续逻辑处理。
-   **推荐费分配逻辑**: 上述伪代码中，推荐费按每人100元、总额上限200元的规则分配。如果推荐人超过2位，则按顺序只给前两位发放，总计200元。如果需要均分，此部分逻辑可调整。
-   **数据库事务**: 整个 `calculate_earnings_for_bill` 函数应在一个数据库事务中执行，以确保所有收益计算和状态更新的原子性。如果中途出错，所有操作应回滚。
-   **结账 API (`POST /bills/{id}/checkout`)**: 完成结账逻辑，创建 `bills` 和 `bill_items` 记录。`is_commission_calculated` 保持 `false`。

### 阶段三：后台收益计算任务 (核心)

**入口伪代码: `mainCommissionJob()`**
```javascript
// 整个后台任务的主函数
function mainCommissionJob() {
    // 1. 查找所有需要计算提成的账单
    const billsToProcess = query("SELECT * FROM bills WHERE is_commission_calculated = false");
    
    // 2. 循环处理每一张账单，并确保事务性
    for (const bill of billsToProcess) {
        // 使用数据库事务确保操作的原子性
        db_transaction_start();
        try {
            calculate_earnings_for_bill(bill);
            // 如果成功，提交事务
            db_transaction_commit();
        } catch (error) {
            // 如果失败，回滚事务
            db_transaction_rollback();
            log_error(`Failed to process bill ${bill.id}:`, error);
        }
    }
}
```

**核心计算伪代码: `calculate_earnings_for_bill()`**
```javascript
// 为单个账单计算所有相关人员的收益
function calculate_earnings_for_bill(bill) {
    // 步骤 0: 计算可计提基数
    const commissionable_amount = calculate_commissionable_amount(bill);
    if (commissionable_amount <= 0) {
        // 如果没有可计提金额，直接标记为已处理并结束
        update("UPDATE bills SET is_commission_calculated = true WHERE bill_id = ?", bill.id)
        return
    }

    // 准备数据：获取代订人和所有服务分配记录
    const booking_agent_id = bill.booking_agent_id;
    const assignments = query("SELECT staff_id, has_commission FROM assignments WHERE booking_id = ?", bill.booking_id);

    // 用于累加当前订单产生的总业绩提成（不含看房费）
    let total_commission_for_this_bill = 0;
    let commission_earners = {}; // 记录谁产生了多少业绩提成

    // 步骤 1 & 2: 计算服务人员的业绩提成和看房费
    const service_staff_ids = [];
    for (const assignment of assignments) {
        const staff_id = assignment.staff_id;
        service_staff_ids.push(staff_id);
        const has_commission = assignment.has_commission;
        const is_booking_agent = (staff_id == booking_agent_id);

        // 1. 计算业绩提成 (仅当 has_commission 为 true)
        if (has_commission) {
            const commission_amount = calculate_commission_for_staff(staff_id, is_booking_agent, commissionable_amount, bill);
            total_commission_for_this_bill += commission_amount;
            commission_earners[staff_id] = (commission_earners[staff_id] || 0) + commission_amount;
        }
        
        // 2. 计算看房费 (所有服务人员都有，不计入经费扣除基数)
        calculate_viewing_fee_for_staff(staff_id, is_booking_agent, bill);
    }

    // 步骤 3: 额外处理“自订非自看”的代订人提成
    if (booking_agent_id && !service_staff_ids.includes(booking_agent_id)) {
        const commission_rate = 0.02;
        const commission_amount = commissionable_amount * commission_rate;
        db_insert("staff_earnings", {
            staff_id: booking_agent_id,
            booking_id: bill.booking_id,
            earning_type: "业绩提成",
            amount: commission_amount,
            source_category: "自订非自看",
            source_amount: commissionable_amount
        });
        total_commission_for_this_bill += commission_amount;
        commission_earners[booking_agent_id] = (commission_earners[booking_agent_id] || 0) + commission_amount;
    }

    // 步骤 4: 计算推荐费 (计入经费扣除基数)
    const referral_commissions = calculate_referral_fee_for_booking(bill);
    for(const r_commission of referral_commissions) {
        total_commission_for_this_bill += r_commission.amount;
        commission_earners[r_commission.staff_id] = (commission_earners[r_commission.staff_id] || 0) + r_commission.amount;
    }

    // 步骤 5: 计算并插入门店经费扣除
    // 报表逻辑显示，经费扣除是按月按人统计的，此处我们按订单模拟
    // 注意：最终实现时，可能需要一个单独的月度任务来计算总扣除
    // 此处按每个员工在本次订单中产生的业绩提成比例，分摊本次订单产生的总经费扣除
    const total_deduction = total_commission_for_this_bill * 0.10;
    if (total_deduction > 0) {
        for(const staff_id in commission_earners) {
            const staff_commission = commission_earners[staff_id];
            const staff_deduction_share = (staff_commission / total_commission_for_this_bill) * total_deduction;
            db_insert("staff_earnings", {
                staff_id: staff_id,
                booking_id: bill.booking_id,
                earning_type: "门店经费扣除",
                amount: -staff_deduction_share, // 金额为负
                source_category: "业绩提成总额扣除",
                source_amount: staff_commission // 基于该员工贡献的提成
            });
        }
    }

    // 完成：标记账单为已计算
    update("UPDATE bills SET is_commission_calculated = true WHERE bill_id = ?", bill.id);
}
```

### 3.3. 辅助函数与实现说明

```javascript
// --- 计算可计提基数 ---
function calculate_commissionable_amount(bill) {
    const bill_items = query("SELECT * FROM bill_items WHERE bill_id = ?", bill.id);
    const non_commissionable_names = query("SELECT item_name FROM non_commissionable_items WHERE is_active = true").map(item => item.item_name);
    
    let commissionable_amount = 0;
    for (const item of bill_items) {
        if (!non_commissionable_names.includes(item.item_name)) {
            commissionable_amount += item.price * item.quantity;
        }
    }
    
    // 将计算出的基数存回 bills 表，方便追溯
    update("UPDATE bills SET commissionable_amount = ? WHERE bill_id = ?", commissionable_amount, bill.id);
    return commissionable_amount;
}

// --- 计算服务人员的业绩提成 (仅为'有提成'的员工) ---
function calculate_commission_for_staff(staff_id, is_booking_agent, commissionable_amount, bill) {
    let rate = 0;
    let source_category = "";

    if (is_booking_agent) { // 情况A: 自订自看
        rate = 0.06;
        source_category = "自订自看";
    } else { // 情况B: 公司派房或同事推荐
        rate = 0.02;
        // 查询是否为被推荐者
        const is_recommended = query_one("SELECT 1 FROM booking_referrals WHERE booking_id = ? AND recommended_staff_id = ?", bill.booking_id, staff_id);
        if (is_recommended) {
            source_category = "同事推荐";
        } else {
            source_category = "公司派房";
        }
    }

    const amount = commissionable_amount * rate;
    if (amount > 0) {
        db_insert("staff_earnings", {
            staff_id: staff_id,
            booking_id: bill.booking_id,
            earning_type: "业绩提成",
            amount: amount,
            source_category: source_category,
            source_amount: commissionable_amount
        });
    }
    return amount; // 返回计算出的金额，用于累加
}

// --- 计算服务人员的看房费 ---
function calculate_viewing_fee_for_staff(staff_id, is_booking_agent, bill) {
    const room = query_one("SELECT room_type FROM rooms WHERE room_id = ?", bill.room_id);
    let fee = 0;
    let source_category = "";

    if (is_booking_agent) { // 自订自看
        if (room.room_type == 'VIP房及以上') {
            fee = 350;
            source_category = "自订自看-VIP房及以上";
        } else {
            fee = 300;
            source_category = "自订自看-大房及以下";
        }
    } else { // 公司派房或同事推荐
        fee = 200;
        const is_recommended = query_one("SELECT 1 FROM booking_referrals WHERE booking_id = ? AND recommended_staff_id = ?", bill.booking_id, staff_id);
        if (is_recommended) {
            source_category = "同事推荐";
        } else {
            source_category = "公司派房";
        }
    }

    if (fee > 0) {
        db_insert("staff_earnings", {
            staff_id: staff_id,
            booking_id: bill.booking_id,
            earning_type: "看房费",
            amount: fee,
            source_category: source_category,
            source_amount: 1 // 代表次数
        });
    }
}

// --- 计算推荐费 ---
function calculate_referral_fee_for_booking(bill) {
    const referrals = query("SELECT DISTINCT recommender_id FROM booking_referrals WHERE booking_id = ?", bill.booking_id);
    const fee_per_person = 100; // 每人100
    const max_total_fee = 200; // 上限200
    let paid_total = 0;
    let commission_records = [];

    for (const referrer of referrals) {
        if (paid_total >= max_total_fee) {
            break;
        }
        const fee_to_pay = Math.min(fee_per_person, max_total_fee - paid_total);
        const record = {
            staff_id: referrer.recommender_id,
            booking_id: bill.booking_id,
            earning_type: "推荐费",
            amount: fee_to_pay,
            source_category: "员工推荐",
            source_amount: referrals.length // 记录总被推荐人数
        };
        db_insert("staff_earnings", record);
        commission_records.push(record);
        paid_total += fee_to_pay;
    }
    return commission_records; // 返回生成的记录，用于累加
}
```

---

## 4. 附录：规则配置文件 (`rules.json`)

```json
{
  "commission_rates": {
    "service_and_booking": 0.06, 
    "booking_only": 0.02,       
    "service_only": 0.02        
  },
  "viewing_fees": {
    "service_and_booking": {
      "vip_room": 350,
      "large_room": 300
    },
    "default": 200
  },
  "referral_fees": {
    "per_person": 100,
    "max_cap": 200
  }
}
```
