# 营业数据统计平台需求分析概要

本文档综合了对 `营业数据统计平台开发需求.csv` 和 `月度总表.csv` 两个文件的分析，旨在明确数据统计平台的核心需求。

---

## 模块一：每日营业分析报表

*源自: `营业数据统计平台开发需求.csv`*

该模块关注 **每日** 的 **运营过程指标**，用于一线管理和日常运营监控。

### 核心功能

1.  **多门店选择**：可选择一个或多个门店的数据进行查询。
2.  **日期范围选择**：可自由选择报表的起止日期。
3.  **精细化时段分析**：核心功能，将一天划分为多个 **特殊且可能重叠的时间段** 进行数据统计，例如：
    - `10:50-13:50`
    - `11:50-14:50`
    - `20:20-END`
4.  **导出功能**：可以将当前查询结果导出为Excel/CSV文件。

### 关键指标 (按特殊时间段统计)

- 预订人数
- 预订房间数
- 待客人数
- 待客房间数
- 开房率
- 营业额

---

## 模块二：月度营收分析报表

*源自: `月度总表.csv`*

该模块关注 **每月** 的 **财务结果指标**，用于管理层进行趋势分析和战略决策。

### 核心功能

1.  **门店/集团范围**：可按单个门店或整个集团进行数据汇总。
2.  **增长趋势分析**：核心功能，所有主要收入项都包含 **同比** 和 **环比** 增长率的计算。
3.  **收入渠道拆分**：将总收入按来源渠道进行分解，深入分析构成。
4.  **成本费用分析**：对支付给第三方平台的各项手续费进行明细拆分。

### 关键指标

- **总览指标**:
  - 月度总营业收入
  - 同比 / 环比
- **收入大项 (按渠道)**:
  - 线下门店营收
  - 特权预约
  - 公众号
  - 美团 / 抖音
  - 银行 / 其他
- **收入小项 (特权预约)**:
  - 按不同收费金额（0元, 5元, 10元...）统计执行次数。
- **平台手续费**:
  - 按平台（抖音/美团）和业务（预约/团购）细分。
  - 按不同银行（广发/中信/银联）细分。

---

## 总结

这两个模块共同构成了一个功能全面的数据平台，既满足了 **日常运营监控** 的需求，也满足了 **月度战略复盘** 的需求。

---

## 现有技术资产分析 (Stored Procedure Analysis)

通过分析 `usp_GetBookingReport.txt` 和 `usp_GenerateDynamicUnifiedDailyReport.txt`，我们得出以下关键结论：

### 1. 关键发现：存在预处理机制

- 两个存储过程都不是直接查询原始流水表（如 `openhistory`），而是查询名为 `DynamicReport_*` 和 `FullDailyReport_*` 的 **结果表/汇总表**。
- 这表明系统中已存在一个ETL（数据提取、转换、加载）过程，它定时将原始数据预处理成结构化的报表数据。
- **这是一个非常重要的正面发现**，意味着新平台可以基于这些汇总表进行开发，从而极大地提升报表查询性能和简化查询逻辑。

### 2. 现有程序与新需求匹配度

- **`usp_GetBookingReport`**:
  - **功能**: 已实现“每日营业分析报表”中关于 **预订量、待客量、开房率** 按动态时间段统计的核心功能。
  - **复用性**: **可直接复用**。后端可直接调用此程序获取每日报表中的运营过程指标。

- **`usp_GenerateDynamicUnifiedDailyReport`**:
  - **功能**: 已实现“每日营业分析报表”中关于 **各渠道（美团/抖音/特权）收入、批次** 按动态时间段和时段（白天/晚上）统计的核心功能。
  - **复用性**: **可直接复用**。它与上一个程序互为补充，二者结合基本覆盖了每日报表的全部数据需求。

### 3. 结论与开发建议

- **“每日报表”模块**: 开发工作量将**大大降低**。后端的主要工作是分别调用这两个存储过程，然后将两份数据聚合在一起，提供给前端。
- **“月度报表”模块**: 现有程序**无法满足**月度汇总和同比/环比的计算需求。
  - **建议**: 我们需要**新建一个存储过程**专门用于月度报表的计算。这个新程序也应该基于现有的 `FullDailyReport_*` 等汇总表进行开发，以确保性能和数据一致性。