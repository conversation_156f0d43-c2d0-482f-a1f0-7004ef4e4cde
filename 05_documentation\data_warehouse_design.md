
# 统一分析数据仓设计方案

本文档定义了为KTV营业数据统计平台设计的核心数据仓库结构，旨在为各类报表提供一个高性能、高扩展性的统一数据源。

---

## 核心设计思想

采用业界标准的 **星型模型 (Star Schema)** 进行设计，其核心思想是将数据分为“维度”和“事实”。

- **维度 (Dimension):** 分析问题的角度，如时间、地点。
- **事实 (Fact):** 需要分析的数值，如收入、人数。

为了解决不同指标的分析层级（粒度）不同的问题（例如：某些指标按时段统计，某些指标按天统计），我们最终采用 **“3个维度表 + 2个事实表”** 的结构。

---

## 数据表结构

### 维度表 (Dimension Tables)

维度表是分析的基石，提供背景信息。

1.  **`Dim_Date` (日期维度表)**
    - **用途:** 提供丰富的日期属性（如年、月、日、星期、是否周末、是否节假日），是进行时间序列分析（如同比、环比）的基础。

2.  **`Dim_Shop` (门店维度表)**
    - **用途:** 提供所有门店的详细信息（如名称、城市、区域）。

3.  **`Dim_TimeSlot` (时段维度表)**
    - **用途:** 提供业务上定义的特殊时间段的详细信息（如名称、起止时间、时段分组）。

### 事实表 (Fact Tables)

事实表存放具体的数值（度量），并与维度表关联。我们设计了两张事实表以应对不同层级的分析需求。

1.  **`Fact_Daily_TimeSlot_Summary` (时段级事实表)**
    - **粒度:** `1门店 x 1日期 x 1时段` (表中最精细的数据)。
    - **用途:** 存放与 **每个具体时间段强相关** 的指标，完美支撑“每日营业分析报表”中的时段细节分析。
    - **示例数据:** `该时段的待客人数`, `该时段的开房率` 等。

2.  **`Fact_Daily_Shop_Summary` (门店级事实表)**
    - **粒度:** `1门店 x 1日期` (按天汇总的数据)。
    - **用途:** 存放 **不区分时段、只跟“天”相关** 的汇总指标，用于宏观分析和“月度营收分析报表”的数据基础。
    - **示例数据:** `全天总营业额`, `白天档总营业额`, `每日平台总手续费`, `自助餐单价` 等。

---

## 数据处理流程 (ETL)

- 将创建一个新的核心存储过程 `usp_Populate_Analytics_Daily_Summary`。
- 该程序会 **每日定时执行一次**，从各个源数据库（本地、云端、门店）拉取原始数据。
- 经过计算、转换和汇总后，将结果分别填充到上述的两张事实表中。
- 所有前端报表都将直接查询这两张已经处理好的结果表，从而保证查询的高性能和数据的统一性。
