
# 统一分析数据仓 - 数据表详细设计 (V2 - 已根据源表优化)

本文档详细描述了为“新一代营业数据智能分析平台”设计的5个核心数据表的结构与字段含义，旨在作为数据库建设的技术蓝图。

---

## 第一部分：维度表 (Dimension Tables)

维度表为我们提供了分析的上下文和角度，即“从哪些角度看数据”。

### 1. `Dim_Date` (日期维度表)

- **用途:** 提供统一的、丰富的日期维度信息，是所有时间相关分析（同比、环比、工作日/周末对比）的基础。
- **粒度:** 一天一行。

> #### 设计思想与价值
> **为什么需要日期表?** 虽然可以在查询时通过函数计算月份、年份，但当数据量巨大时，反复计算会严重拖慢查询速度。而预先建立日期表，就相当于为数据分析创建了高效的“时间索引”。
> **可以提升什么?** 
> 1.  **查询性能:** 查询可以直接关联本表，无需实时计算，速度提升数十倍以上。
> 2.  **分析能力:** 可轻松加入“是否周末”、“是否节假日”等复杂业务维度，让报表可以一键对比“节假日与平时”的业绩差异，这是简单日期列无法实现的。
> 3.  **逻辑统一:** 所有关于时间的规则（如财年定义）都在此表中统一，保证全公司分析口径一致。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) |
| :--- | :--- | :--- |
| `DateSK` | `INT` (主键) | 代理键，用于数据库内部关联。 |
| `FullDate` | `DATE` | 完整的日期, 如 '2025-08-11'。 |
| `Year` | `INT` | 该日期所属的年份, 如 2025。 |
| `Month` | `INT` | 该日期所属的月份, 如 8。 |
| `Day` | `INT` | 该日期是几号, 如 11。 |
| `DayOfWeek` | `INT` | 一周中的第几天 (1=周一, 7=周日)。 |
| `WeekdayName_ZH` | `NVARCHAR(10)` | 中文的星期名, 如 '星期一'。 |
| `IsWeekend` | `BIT` | 是否为周末 (1=是, 0=否)。 |
| `IsHoliday` | `BIT` | 是否为法定节假日 (1=是, 0=否)。 |
| `HolidayName` | `NVARCHAR(50)` | 节假日名称, 如 '国庆节'。 |


### 2. `Dim_Shop` (门店维度表)

- **用途:** 提供所有门店的静态信息，确保门店名称、区域等信息的统一性。
- **粒度:** 一个门店一行。

> #### 设计思想与价值
> **为什么需要门店表?** 如果没有独立的门店表，那在事实表中每一行都需要存入“天河店”、“广州市”这样的文本信息，造成海量数据冗余。独立的维度表解决了这个问题。
> **可以提升什么?** 
> 1.  **降低冗余:** 几百万行的数据，只需保存门店ID，而不是重复保存门店名称，极大节省了存储空间。
> 2.  **信息统一:** 当门店改名或修改区域信息时，只需修改本表一行，所有历史报表的数据都会自动更新，保证了数据的一致性。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) |
| :--- | :--- | :--- |
| `ShopSK` | `INT` (主键) | 代理键，用于数据库内部关联。 |
| `ShopID` | `INT` | 业务主键，来自源系统 `ShopInfo` 表的 `ShopID`。 |
| `ShopName` | `NVARCHAR(100)` | 门店的官方名称。 |
| `Address` | `NVARCHAR(100)` | 门店的完整地址，来自源表 `address` 字段。 |
| `City` | `NVARCHAR(50)` | 门店所在的城市 (可考虑从地址中提取或单独维护)。 |
| `Region` | `NVARCHAR(50)` | 门店所在的区域 (可考虑从地址中提取或单独维护)。 |
| `OpenDate` | `DATE` | 门店的开业日期 (需将源表 `OpenDate` 的文本格式转换为日期格式)。 |
| `IsActive` | `BIT` | 门店当前是否在用，来自源表 `IsUse` 字段，用于筛选在营门店。 |


### 3. `Dim_TimeSlot` (时段维度表)

- **用途:** 统一定义业务上使用的所有特殊分析时段。
- **粒度:** 一个业务时段一行。

> #### 设计思想与价值
> **为什么需要时段表?** 业务中用于分析的“时段”（如黄金档）是核心的业务规则。将它固化成一个维度表，等于将这条业务规则标准化、产品化了。
> **可以提升什么?** 
> 1.  **规则标准化:** 保证所有报表对于“黄金档”的定义（起止时间）都是完全一致的。
> 2.  **管理便捷:** 未来若要新增或修改一个分析时段，只需操作本表，所有报表即可自动应用新规则，无需修改任何代码。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) |
| :--- | :--- | :--- |
| `TimeSlotSK` | `INT` (主键) | 代理键，用于数据库内部关联。 |
| `TimeSlotBusinessKey` | `NVARCHAR(10)` | 业务主键, 来自源系统 `timeinfo` 表的 `TimeNo`。 |
| `TimeSlotName` | `NVARCHAR(50)` | 时段的名称, 如 '10:50-13:50'。 |
| `StartTime` | `TIME` | 时段的开始时间 (需将源表 `BegTime` 的整数格式如1700转换为时间格式)。 |
| `EndTime` | `TIME` | 时段的结束时间 (需将源表 `EndTime` 的整数格式如2000转换为时间格式)。 |
| `TimeSlotGroup` | `NVARCHAR(50)` | 时段所属的大类, 如 '白天档', '晚上档'。 |
| `IsSpecial` | `BIT` | 是否为特殊时段，来自源表 `IsSpecial` 字段。 |

---

## 第二部分：事实表 (Fact Tables)

事实表存放需要分析的数值（度量），并与维度表关联。

### 4. `Fact_Daily_TimeSlot_Summary` (时段级事实表)

- **用途:** 存放最精细的、与每个具体“时段”相关的运营指标。
- **粒度:** `1门店 x 1日期 x 1时段`。

> #### 设计思想与价值
> **为什么需要时段级事实表?** 这是整个数据仓库中 **最精细的数据**，是所有上层分析的基础。它记录了业务运营的每一个“细胞”的状况。
> **可以提升什么?** 
> 1.  **细节钻取:** 完美支撑“每日营业分析报表”的需求，可以下钻到任意一天、任意一个时段的经营细节，用于一线运营分析。
> 2.  **数据基石:** 所有更宏观的数据（如每日、每月汇总）都可以由这张表聚合而成，保证了所有报表的数据同源、可追溯。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) |
| :--- | :--- | :--- |
| `TimeSlotSummarySK` | `BIGINT` (主键) | 代理键。 |
| `DateSK` | `INT` | 外键，关联到 `Dim_Date`。 |
| `ShopSK` | `INT` | 外键，关联到 `Dim_Shop`。 |
| `TimeSlotSK` | `INT` | 外键，关联到 `Dim_TimeSlot`。 |
| `BookedRooms` | `INT` | 该时段的预订房间数。 |
| `BookedGuests` | `INT` | 该时段的预订人数。 |
| `OccupiedRooms` | `INT` | 该时段的待客房间数。 |
| `OccupiedGuests` | `INT` | 该时段的待客人数。 |
| `OccupancyRate` | `DECIMAL(5, 4)` | 该时段的开房率 (计算得出)。 |
| `Revenue` | `DECIMAL(18, 2)` | 该时段产生的总营业额。 |
| `Revenue_By_Channel_KPlus` | `DECIMAL(18, 2)` | 该时段内，K+渠道产生的收入。 |
| `Revenue_By_Channel_Special` | `DECIMAL(18, 2)` | 该时段内，特权预约渠道产生的收入。 |
| `Revenue_By_Channel_Meituan` | `DECIMAL(18, 2)` | 该时段内，美团渠道产生的收入。 |
| `Revenue_By_Channel_Douyin` | `DECIMAL(18, 2)` | 该时段内，抖音渠道产生的收入。 |
| `Revenue_By_Channel_RoomFee` | `DECIMAL(18, 2)` | 该时段内，纯房费产生的收入。 |
| `DirectFall_Batches` | `INT` | 该时段的直落批次数。 |


### 5. `Fact_Daily_Shop_Summary` (门店级事实表)

- **用途:** 存放按“天”汇总的全局性、总结性指标。
- **粒度:** `1门店 x 1日期`。

> #### 设计思想与价值
> **为什么需要门店日总表?** 像“月度报表”这类宏观报表，如果每次都从最细的时段数据开始算，会非常慢。我们预先把按天汇总的结果存入此表，相当于为管理层创建了一个“数据高速公路”。
> **可以提升什么?** 
> 1.  **宏观报表性能:** 月报、年报的查询将直接基于这张日汇总表，速度极快，能做到即时响应。
> 2.  **简化计算:** 计算同比、环比时，直接操作这张表，逻辑会比操作时段级数据简单得多。
> 3.  **存放特殊指标:** 像“全天总收入”、“自助餐单价”这类与具体时段无关、只与天相关的指标，存放在这里最合适，避免了在时段表中重复存储。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) |
| :--- | :--- | :--- |
| `ShopSummarySK` | `BIGINT` (主键) | 代理键。 |
| `DateSK` | `INT` | 外键，关联到 `Dim_Date`。 |
| `ShopSK` | `INT` | 外键，关联到 `Dim_Shop`。 |
| `TotalRevenue` | `DECIMAL(18, 2)` | 全天总营业额。 |
| `DayTimeRevenue` | `DECIMAL(18, 2)` | 白天档总营业额 (由各时段数据汇总)。 |
| `NightTimeRevenue` | `DECIMAL(18, 2)` | 晚上档总营业额 (由各时段数据汇总)。 |
| `TotalBatches` | `INT` | 全天总批次数。 |
| `BuffetGuestCount` | `INT` | 全天自助餐总人数。 |
| `TotalDirectFallGuests` | `INT` | 全天直落总人数。 |
| `ComplimentaryBatches` | `INT` | 全天招待总批次数。 |
| `ComplimentaryRevenue` | `DECIMAL(18, 2)` | 全天招待总金额。 |
| `PrivilegeBooking_Count_0Yuan` | `INT` | 0元特权预约的执行次数。 |
| `PrivilegeBooking_Count_5Yuan` | `INT` | 5元特权预约的执行次数。 |
| `PrivilegeBooking_Count_10Yuan` | `INT` | 10元特权预约的执行次数。 |
| `PrivilegeBooking_Count_15Yuan` | `INT` | 15元特权预约的执行次数。 |
| `Fee_Meituan_Booking` | `DECIMAL(18, 2)` | 支付给美团的预约类总手续费。 |
| `Fee_Meituan_GroupBuy` | `DECIMAL(18, 2)` | 支付给美团的团购类总手续费。 |
| `Fee_Douyin_Booking` | `DECIMAL(18, 2)` | 支付给抖音的预约类总手续费。 |
| `Fee_Douyin_GroupBuy` | `DECIMAL(18, 2)` | 支付给抖音的团购类总手续费。 |
| `Fee_Bank_GF` | `DECIMAL(18, 2)` | 支付给广发银行的总手续费。 |
| `Fee_Bank_CITIC` | `DECIMAL(18, 2)` | 支付给中信银行的总手续费。 |
| `Fee_Bank_UnionPay` | `DECIMAL(18, 2)` | 支付给银联的总手续费。 |
