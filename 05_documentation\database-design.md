# 派房提成系统 - 数据库设计方案

**版本**: 1.0
**最后更新**: 2025-06-24

## 一、设计原则

本数据库设计旨在支持派房系统的核心业务逻辑，特别是复杂的提成和费用计算规则。设计遵循以下原则：

1.  **数据一致性**: 通过外键约束确保关联数据的完整性。
2.  **逻辑清晰**: 表结构清晰地反映业务实体及其关系。
3.  **可扩展性**: 方便未来增加新的规则和功能，如超时罚款模块。
4.  **查询效率**: 为核心查询场景（如历史记录、财务报表）建立索引。

## 二、数据实体关系图 (E-R Diagram)

```mermaid
erDiagram
    EMPLOYEES ||--o{ ASSIGNMENTS : "is operator of"
    ROOMS ||--o{ ASSIGNMENTS : "is assigned to"
    ASSIGNMENTS ||--|{ ASSIGNMENT_DETAILS : "has"
    EMPLOYEES ||--|{ ASSIGNMENT_DETAILS : "participates in"
    ASSIGNMENTS ||--o{ COMMISSION_RECORDS : "generates"
    EMPLOYEES ||--o{ COMMISSION_RECORDS : "earns"
    NON_COMMISSIONABLE_ITEMS
```

## 三、表结构定义

### 1. `employees` - 员工表

存储所有员工的基本信息。

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| `id` | `INT` | `PRIMARY KEY`, `AUTO_INCREMENT` | 员工唯一ID |
| `name` | `VARCHAR(255)` | `NOT NULL` | 员工姓名 |
| `position` | `ENUM('专员', '主任', '财务', '非专员')` | `NOT NULL` | 职位 |
| `avatar_url` | `VARCHAR(255)` | | 头像URL |
| `status` | `ENUM('active', 'inactive')` | `DEFAULT 'active'` | 员工状态 |
| `created_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP` | 创建时间 |

### 2. `rooms` - 房间表

存储房间信息。

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| `id` | `INT` | `PRIMARY KEY`, `AUTO_INCREMENT` | 房间唯一ID |
| `room_number` | `VARCHAR(50)` | `NOT NULL`, `UNIQUE` | 房间号 |
| `type` | `VARCHAR(100)` | | 房间类型（大房、小房） |
| `status` | `ENUM('available', 'occupied', 'maintenance')` | `DEFAULT 'available'` | 房间状态 |

### 3. `assignments` - 派房主记录表

核心业务表，记录每一次派房操作。

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| `id` | `INT` | `PRIMARY KEY`, `AUTO_INCREMENT` | 派房记录唯一ID |
| `room_id` | `INT` | `FOREIGN KEY (rooms.id)` | 关联的房间ID |
| `operator_id` | `INT` | `FOREIGN KEY (employees.id)` | 操作人（主任）ID |
| `assignment_type` | `ENUM('自订自看', '自订非自看', '公司派房', '同事推荐')` | `NOT NULL` | 派房类型 |
| `total_consumption` | `DECIMAL(10, 2)` | `DEFAULT 0.00` | 房间总消费金额 |
| `non_commissionable_amount` | `DECIMAL(10, 2)` | `DEFAULT 0.00` | 不计提成项目的总金额 |
| `effective_spend` | `DECIMAL(10, 2)` | `DEFAULT 0.00` | 有效业绩 (总消费 - 不计提成) |
| `status` | `ENUM('pending', 'confirmed', 'completed', 'cancelled')` | `DEFAULT 'pending'` | 派房状态 |
| `note` | `TEXT` | | 备注信息 |
| `assignment_time` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP` | 派房操作时间 |
| `service_start_time` | `TIMESTAMP` | | 服务实际开始时间 |
| `service_end_time` | `TIMESTAMP` | | 服务实际结束时间 |

### 4. `assignment_details` - 派房详情表

记录一次派房中有哪些员工参与，以及他们的角色和提成状态。

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| `id` | `INT` | `PRIMARY KEY`, `AUTO_INCREMENT` | 详情唯一ID |
| `assignment_id` | `INT` | `FOREIGN KEY (assignments.id)` | 关联的派房记录ID |
| `employee_id` | `INT` | `FOREIGN KEY (employees.id)` | 参与的员工ID |
| `role` | `ENUM('服务者', '预订者', '被推荐者')` | `NOT NULL` | 员工在本次任务中的角色 |
| `has_commission` | `BOOLEAN` | `NOT NULL DEFAULT FALSE` | 是否享受业绩提成（由主任勾选） |
| `referrer_id` | `INT` | `FOREIGN KEY (employees.id)` | 推荐人ID（如果适用） |

**复合唯一键**: `(assignment_id, employee_id)` 确保一个员工在一次任务中只出现一次。

### 5. `commission_records` - 财务记录表

记录所有最终产生的费用，方便财务查询和统计。

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| `id` | `INT` | `PRIMARY KEY`, `AUTO_INCREMENT` | 记录唯一ID |
| `assignment_id` | `INT` | `FOREIGN KEY (assignments.id)` | 关联的派房记录ID |
| `employee_id` | `INT` | `FOREIGN KEY (employees.id)` | 收益员工ID |
| `record_type` | `ENUM('业绩提成', '服务费', '推荐费', '代订费')` | `NOT NULL` | 费用类型 |
| `amount` | `DECIMAL(10, 2)` | `NOT NULL` | 金额 |
| `base_amount` | `DECIMAL(10, 2)` | | 计算基数（如有效业绩） |
| `rate` | `DECIMAL(5, 4)` | | 计算比率（如0.02） |
| `created_at` | `TIMESTAMP` | `DEFAULT CURRENT_TIMESTAMP` | 记录生成时间 |

### 6. `non_commissionable_items` - 不计提成项目表

存储不参与提成计算的消费项目。

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| `id` | `INT` | `PRIMARY KEY`, `AUTO_INCREMENT` | 项目唯一ID |
| `item_name` | `VARCHAR(255)` | `NOT NULL`, `UNIQUE` | 项目名称（如“自助餐”） |
| `description`| `TEXT` | | 描述 |
| `is_active` | `BOOLEAN` | `DEFAULT TRUE` | 是否启用 |

## 四、索引建议

-   在 `assignments` 表的 `room_id`, `operator_id`, `assignment_time` 字段上创建索引。
-   在 `assignment_details` 表的 `assignment_id`, `employee_id` 上创建索引。
-   在 `commission_records` 表的 `employee_id`, `created_at` 上创建索引。
