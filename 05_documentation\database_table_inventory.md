# 数据库表清单
*报告生成时间: 2025-08-08 17:05:00*

## 本地服务器: 192.168.2.5

### 数据库: `dbfood`
- `dbo.AddItem`
- `dbo.AiType`
- `dbo.AmountLog`
- `dbo.CarLeaveLog`
- `dbo.Cashier`
- `dbo.ClearDataLog`
- `dbo.DepositInfo`
- `dbo.DepositInfo_Temp`
- `dbo.Dept`
- `dbo.DeptBanSet`
- `dbo.dtproperties`
- `dbo.EmpGift_CustRecord`
- `dbo.EmpGift_Item`
- `dbo.EmpGift_Record`
- `dbo.FdCash`
- `dbo.FdCashBak`
- `dbo.FdCashBak_B`
- `dbo.FdCashBak_Bak`
- `dbo.FdCashOrder`
- `dbo.FdDetType`
- `dbo.FdImage`
- `dbo.FdInv`
- `dbo.FdInv_B`
- `dbo.FdInv_Bak`
- `dbo.FdInv_ExchangeLog`
- `dbo.FdInvCashItem`
- `dbo.FdInvDesc`
- `dbo.FdTicket`
- `dbo.FdTimePrice`
- `dbo.FdTimeZone`
- `dbo.FdType`
- `dbo.FdType_bak_20250724`
- `dbo.FdUser`
- `dbo.FdUserGrade`
- `dbo.FdUserRights`
- `dbo.festivaltime`
- `dbo.Food`
- `dbo.food_bak_20250724`
- `dbo.FoodCal`
- `dbo.FoodLabel`
- `dbo.foodlabel_bak_20250724`
- `dbo.FoodOrderM`
- `dbo.FPrn`
- `dbo.FPrnData`
- `dbo.FPrnData_Bak`
- `dbo.FtInfo`
- `dbo.GDDB20Info`
- `dbo.GDDBInfo`
- `dbo.GiftAccount`
- `dbo.GiftAccountOperationRecord`
- `dbo.GiftAccountSceneAllocation`
- `dbo.GiftRole`
- `dbo.GiftScene`
- `dbo.GiftSceneEquityConfig`
- `dbo.GiftSceneRoleBinding`
- `dbo.HappyRab`
- `dbo.Holiday`
- `dbo.HotFdType`
- `dbo.HotFood`
- `dbo.Inv_TimeSection`
- `dbo.LanId`
- `dbo.LanString`
- `dbo.LastInvNo`
- `dbo.LastRefNo`
- `dbo.Limit_ConfigInfo`
- `dbo.LogInfo`
- `dbo.meal_distribution_info`
- `dbo.Meal_info`
- `dbo.MembAmountEditLog`
- `dbo.Member`
- `dbo.MemberCheckoutInfo`
- `dbo.MemberGiveSet`
- `dbo.MembSet`
- `dbo.MGradeFdDisc`
- `dbo.MobileFdGive`
- `dbo.MobileFood`
- `dbo.MobileFoodDisc`
- `dbo.MobileFtType`
- `dbo.MobilePackGive`
- `dbo.MobilOrderItem`
- `dbo.MobilOrderTitle`
- `dbo.MobilUserOrderTitle`
- `dbo.NewFdGive`
- `dbo.NewFdType`
- `dbo.NewFdTypeLink`
- `dbo.NewMemberLog`
- `dbo.OO`
- `dbo.ParamSet`
- `dbo.pre_order`
- `dbo.PreOrderSendMsgInfo`
- `dbo.PriceNo`
- `dbo.QrInfo`
- `dbo.RefToZDLog`
- `dbo.Report_cg`
- `dbo.Report_hs`
- `dbo.RightSet`
- `dbo.RmAccountInfo`
- `dbo.RmArea`
- `dbo.RmClearLog`
- `dbo.RmCloseInfo`
- `dbo.RmCloseInfo_Collect`
- `dbo.RmExchangeDetail`
- `dbo.RmExchangeLog`
- `dbo.RmFtPrnIndex`
- `dbo.RmOrder`
- `dbo.RmOrderDelLog`
- `dbo.RmOrderLog`
- `dbo.rmslocal_RmLock`
- `dbo.RmType`
- `dbo.Room`
- `dbo.Room_Consume_Number`
- `dbo.Room_Consume_Number_Itme`
- `dbo.RoomCloseLabel`
- `dbo.RoomCloseLabelTypeDetail`
- `dbo.RoomExtend`
- `dbo.RoomLabel`
- `dbo.RtAuto`
- `dbo.RtAutoZD`
- `dbo.RtTimePrice`
- `dbo.S_AccType`
- `dbo.S_CashItem`
- `dbo.S_PrnType`
- `dbo.S_RmStatus`
- `dbo.SceneRole_Config`
- `dbo.SceneRole_Config_Extra`
- `dbo.SDate`
- `dbo.Sh`
- `dbo.ShareSetInfo`
- `dbo.Sheet1`
- `dbo.StarInfo`
- `dbo.tb`
- `dbo.TestTable`
- `dbo.TimeZone`
- `dbo.triggerRecord`
- `dbo.tt`
- `dbo.UserAmount`
- `dbo.UserAmountDetail`
- `dbo.UserFtZD`
- `dbo.UserInfo_Binding`
- `dbo.UserIO`
- `dbo.UserZDItem`
- `dbo.UserZDItemDetail`
- `dbo.UserZDSet`
- `dbo.Vesa`
- `dbo.WebOrderTable`
- `dbo.WeChatFoodOrderMsg`
- `dbo.weekend`
- `dbo.WindTicket`
- `dbo.wx_shopmall_worktime`
- `dbo.wxpay_FdCashOrder`
- `dbo.wxPayCheckInfo`
- `dbo.wxPayInfo`
- `dbo.wxPayInfo_Del`

### 数据库: `mims`
- `dbo.Activity`
- `dbo.appraise_record`
- `dbo.appraise_record_score`
- `dbo.BillOffset`
- `dbo.BlackListInfo`
- `dbo.cash_coupon`
- `dbo.CashTicketInfo`
- `dbo.CheckoutInfo`
- `dbo.ConsumeInfo`
- `dbo.CouponInfo`
- `dbo.CouponIssueInfo`
- `dbo.datasynchronization_mainproject`
- `dbo.datasynchronization_mainproject_details`
- `dbo.datasynchronization_mainproject_progress`
- `dbo.DeptIT`
- `dbo.DeptITDutyInfo`
- `dbo.dtproperties`
- `dbo.EmployeeDutyInfo`
- `dbo.ExGiftInfo`
- `dbo.ExIntegralInfo`
- `dbo.FdUserWeChat`
- `dbo.FdUserWeChatJurisdiction`
- `dbo.FIFATeamInfo`
- `dbo.FrozenMoneyInfo`
- `dbo.Game_WeChatGameUserInfo`
- `dbo.Game_WeChatGameUserInfo_ShowGood`
- `dbo.GameInfo`
- `dbo.GamePadInfo`
- `dbo.GetGiftInfo`
- `dbo.GiftInfo`
- `dbo.GiftModelInfo`
- `dbo.GrouponInfo`
- `dbo.IC_FdUser`
- `dbo.IntegralInfo`
- `dbo.IntegralInfo_History`
- `dbo.Invoice`
- `dbo.LeaseHistory`
- `dbo.LeaseItems`
- `dbo.LevelTicketInfo`
- `dbo.LiveQCode`
- `dbo.ManagerCardInfo`
- `dbo.ManagerInfo`
- `dbo.marketingShops_deduction_record`
- `dbo.member_uplog`
- `dbo.MemberAbnormal`
- `dbo.MemberActivationLog`
- `dbo.MemberBatch`
- `dbo.MemberBillErroInfo`
- `dbo.MemberCardInfo`
- `dbo.MemberCardInfo_WithoutEasy`
- `dbo.MemberCardInfoJoin`
- `dbo.MemberCardInfoRemove`
- `dbo.MemberCardNoInfo`
- `dbo.MemberCardOrderRecord`
- `dbo.MemberCardPresentInfo`
- `dbo.MemberCardTypeInfo`
- `dbo.MemberCheckoutInfo`
- `dbo.MemberEmpowerPay`
- `dbo.MemberEmpowerShop`
- `dbo.MemberGiftRecord`
- `dbo.MemberGive`
- `dbo.MemberInfo`
- `dbo.MemberInfo_WithoutEasy`
- `dbo.MemberInfoExtend`
- `dbo.MemberInfoRemove`
- `dbo.MemBerRiskControl`
- `dbo.MemBerRiskRule`
- `dbo.MemberRollBackLog`
- `dbo.MemBerRule`
- `dbo.MemBerSccountRecord`
- `dbo.MemberSystemEdition`
- `dbo.MemberUpdateRecord`
- `dbo.Mims_Log`
- `dbo.Mims_Text`
- `dbo.mimspublic`
- `dbo.MSpeer_conflictdetectionconfigrequest`
- `dbo.MSpeer_conflictdetectionconfigresponse`
- `dbo.MSpeer_lsns`
- `dbo.MSpeer_originatorid_history`
- `dbo.MSpeer_request`
- `dbo.MSpeer_response`
- `dbo.MSpeer_topologyrequest`
- `dbo.MSpeer_topologyresponse`
- `dbo.MSpub_identity_range`
- `dbo.NBrandConfig`
- `dbo.NBrandShop`
- `dbo.NBrandSubConfig`
- `dbo.NCoupon`
- `dbo.NCoupon_ChangeLog`
- `dbo.NCouponSubInfo`
- `dbo.NCouponSubInfo_ChangeLog`
- `dbo.NGiftType`
- `dbo.NUse`
- `dbo.NUse_ChangeLog`
- `dbo.NUse_JoinMoney`
- `dbo.NUseAndNCoupon`
- `dbo.NUseBrandInfo`
- `dbo.OfflineRechargeInfo`
- `dbo.old_memberrechargeinfo`
- `dbo.OpenAllAreaInfo`
- `dbo.OpenAreaInfo`
- `dbo.OpenAreaInfo_200813`
- `dbo.Openhistory`
- `dbo.OpenPhoneInfo_200813`
- `dbo.PCSystemEdition`
- `dbo.PhoneMsg`
- `dbo.PointInfo`
- `dbo.PointInfo_1`
- `dbo.PointInfo_History`
- `dbo.PointInfo_History_Bak`
- `dbo.PointInfo_Temp`
- `dbo.PointInfo_WithoutEasy`
- `dbo.PointInfoTemp`
- `dbo.PointState`
- `dbo.RechargeBatchInfo`
- `dbo.RechargeChangeNCouponInfo`
- `dbo.RechargedataInfo`
- `dbo.RechargeDataState`
- `dbo.RechargeFlowInfo`
- `dbo.RechargeInfo`
- `dbo.RechargeInfo_History`
- `dbo.RechargeJoinUser`
- `dbo.RechargePurviewInfo`
- `dbo.RegMemberCardInfo`
- `dbo.report_loss_membercard`
- `dbo.ReturnAnnualClosing`
- `dbo.ReturnInfo`
- `dbo.ReturnInfo_History`
- `dbo.RoomCommission`
- `dbo.ScoreDelRecord`
- `dbo.ServiceState`
- `dbo.ShareLogInfo`
- `dbo.ShopInfo`
- `dbo.SolarData`
- `dbo.SolarDatasx`
- `dbo.SpecMemberCardInfo`
- `dbo.SpecMemberInfo`
- `dbo.SpecRechargeInfo`
- `dbo.sysarticlecolumns`
- `dbo.sysarticles`
- `dbo.sysarticleupdates`
- `dbo.syspublications`
- `dbo.sysreplservers`
- `dbo.sysschemaarticles`
- `dbo.syssubscriptions`
- `dbo.systranschemas`
- `dbo.TeamInfo`
- `dbo.TEL`
- `dbo.Test`
- `dbo.test_MembererroOpen`
- `dbo.TestB`
- `dbo.TestC`
- `dbo.TestD`
- `dbo.Th_ActivityUser`
- `dbo.Th_MemberRegisterPay`
- `dbo.Th_MemberRules`
- `dbo.Th_MemberShopRecord`
- `dbo.Th_RoomCommissionAllot`
- `dbo.TuneShop`
- `dbo.UserCardInfo`
- `dbo.vip_report_singlecou_project`
- `dbo.vip_report_singlecou_project_item`
- `dbo.vip_report_singlecou_project_item_record_day`
- `dbo.VIPMemberInfo`
- `dbo.Visit`
- `dbo.WeChatBookUserInfo`
- `dbo.WeChatCardRecord`
- `dbo.WeChatCouponConsumeInfo`
- `dbo.WeChatEmployeeAllInfo`
- `dbo.WeChatEmployeeInfo`
- `dbo.WeChatGameUserInfo`
- `dbo.WeChatGameWinerInfo`
- `dbo.WeChatInfo`
- `dbo.WeChatInfoLog`
- `dbo.WeChatUserInfo`
- `dbo.WrongRechargeInfo`
- `dbo.wx_rechargeinfo`
- `dbo.WxPayInfo`
- `dbo.Ypoint`

### 数据库: `operatedata`
- `dbo.AbnormalBill`
- `dbo.AllOperatingincomeByWorkDate`
- `dbo.BarFdType`
- `dbo.BarFood`
- `dbo.BeerUp4`
- `dbo.bookcacheinfo`
- `dbo.BookOrder_CadrePhone`
- `dbo.BookOrder_RmConfig`
- `dbo.BookOrder_RoyaltyResult`
- `dbo.BookOrder_RoyaltySubResult`
- `dbo.BookOrder_SchemeConfig`
- `dbo.BookOrder_UserConfig`
- `dbo.ChartTypeInfo`
- `dbo.CommissionConfig`
- `dbo.Costallocation`
- `dbo.DefaultConfigStaInfo`
- `dbo.depositinfo`
- `dbo.Dept`
- `dbo.DrinksInfo`
- `dbo.DynamicReport_Header`
- `dbo.DynamicReport_TimeSlotDetails`
- `dbo.fd`
- `dbo.FdCash_xh`
- `dbo.FdCashBak`
- `dbo.fdcashbak_2018`
- `dbo.fdcashbak_2019`
- `dbo.fdcashbak_2020`
- `dbo.FdInv`
- `dbo.FdInv_2018`
- `dbo.FdInv_2019`
- `dbo.FdInv_2020`
- `dbo.Fdtype`
- `dbo.FdUser`
- `dbo.Food`
- `dbo.Food_xh`
- `dbo.FullDailyReport_Header`
- `dbo.FullDailyReport_NightDetails`
- `dbo.FullDailyReport_TimeSlotDetails`
- `dbo.GroupBy`
- `dbo.GrouponAndType`
- `dbo.JobExecutionLog`
- `dbo.Kboss_ResultInfo`
- `dbo.KTV_Simplified_Daily_Report`
- `dbo.lg_memberinfo`
- `dbo.MemberCheckoutInfo`
- `dbo.MemberFd`
- `dbo.miniprom_Buffet_Groupby`
- `dbo.MsgInfo`
- `dbo.MSpeer_conflictdetectionconfigrequest`
- `dbo.MSpeer_conflictdetectionconfigresponse`
- `dbo.MSpeer_lsns`
- `dbo.MSpeer_originatorid_history`
- `dbo.MSpeer_request`
- `dbo.MSpeer_response`
- `dbo.MSpeer_topologyrequest`
- `dbo.MSpeer_topologyresponse`
- `dbo.MSpub_identity_range`
- `dbo.new_result_info`
- `dbo.new_result_userinfo`
- `dbo.new_result_userinfo_01`
- `dbo.new_result_userinfo_bak`
- `dbo.opencacheinfo`
- `dbo.OperatingIncome`
- `dbo.OrderInfo`
- `dbo.other`
- `dbo.PartyInfoTest`
- `dbo.Pay`
- `dbo.PreFourCodeInfo`
- `dbo.reporting_atmosphere`
- `dbo.reporting_basic`
- `dbo.reporting_bzf`
- `dbo.reporting_commission_record`
- `dbo.reporting_ddf`
- `dbo.reporting_djing`
- `dbo.reporting_emptyBottle`
- `dbo.reporting_HouseWatching`
- `dbo.reporting_HouseWatching_type`
- `dbo.reporting_membershipCard`
- `dbo.reporting_Print`
- `dbo.reporting_state`
- `dbo.reporting_to_examine`
- `dbo.reporting_type`
- `dbo.reporting_wine`
- `dbo.reporting_xf`
- `dbo.ResultChild`
- `dbo.RmCloseInfo`
- `dbo.RmCloseInfo_Day`
- `dbo.RmCloseInfo_Test`
- `dbo.Rmtype`
- `dbo.season1`
- `dbo.ShopResult`
- `dbo.shoptimeinfo`
- `dbo.special_electronization_bill`
- `dbo.SpecialBillAccInfo`
- `dbo.SpecialBillInfo`
- `dbo.SpecialBillShopower`
- `dbo.SpecialBillState`
- `dbo.SpecialBillType`
- `dbo.StaSonInfo`
- `dbo.StatisticsTypeInfo`
- `dbo.sysarticlecolumns`
- `dbo.sysarticles`
- `dbo.sysarticleupdates`
- `dbo.syspublications`
- `dbo.sysreplservers`
- `dbo.sysschemaarticles`
- `dbo.syssubscriptions`
- `dbo.systranschemas`
- `dbo.TempFd`
- `dbo.Test_bydq`
- `dbo.Test_hqdq`
- `dbo.Test_lhdq20211020`
- `dbo.Test_ozdq20230208`
- `dbo.Test_ozdy`
- `dbo.Test_pydq20211119`
- `dbo.Test_thyd20211217`
- `dbo.timeinfo`
- `dbo.TimeInfo2`
- `dbo.TotalResult`
- `dbo.Waiter_Order_Info`
- `dbo.Web_Dqtj_log`
- `dbo.wxPayInfo`

### 数据库: `rms2019`
- `dbo.bookcacheinfo`
- `dbo.bookhistory`
- `dbo.CustInfo`
- `dbo.CustInfoMark`
- `dbo.CustZsPhoneInfo`
- `dbo.opencacheinfo`
- `dbo.openhistory`
- `dbo.rminfo`

---
## 远程服务器: ************* (通过链接服务器: cloudRms2019)

### 数据库: `rms2019`
- `dbo._bak_rminfo`
- `dbo.api_message`
- `dbo.areainfo`
- `dbo.bak_rminfo`
- `dbo.behapartinfo`
- `dbo.Behatypeinfo`
- `dbo.billstatus`
- `dbo.birthday_gift_record`
- `dbo.birthday_gift_record_detailed`
- `dbo.birthday_virtual_order`
- `dbo.bookcacheinfo`
- `dbo.bookcacheinfo_preorder`
- `dbo.bookhistory`
- `dbo.bookhistory_bak`
- `dbo.bookhistory_bak20231208`
- `dbo.bookmessage`
- `dbo.contypeinfo`
- `dbo.contypetimeinfo`
- `dbo.custbehainfo`
- `dbo.custdemand`
- `dbo.custdemandtitle`
- `dbo.CustInfo`
- `dbo.CustZsPhoneInfo`
- `dbo.deposit_food`
- `dbo.depositinfo`
- `dbo.exchangewait`
- `dbo.heartbeat`
- `dbo.luserinfo`
- `dbo.msms`
- `dbo.MSpeer_conflictdetectionconfigrequest`
- `dbo.MSpeer_conflictdetectionconfigresponse`
- `dbo.MSpeer_lsns`
- `dbo.MSpeer_originatorid_history`
- `dbo.MSpeer_request`
- `dbo.MSpeer_response`
- `dbo.MSpeer_topologyrequest`
- `dbo.MSpeer_topologyresponse`
- `dbo.MSpub_identity_range`
- `dbo.mvodmsg`
- `dbo.mvodroom`
- `dbo.NumberManage`
- `dbo.opencacheinfo`
- `dbo.openhistory`
- `dbo.openhistory_bak`
- `dbo.operation`
- `dbo.orderuser`
- `dbo.Price`
- `dbo.pricemodel`
- `dbo.printattribute`
- `dbo.printrecord`
- `dbo.rmexchange`
- `dbo.rminfo`
- `dbo.rminfoChanged`
- `dbo.RmOperation`
- `dbo.RmOperation_History`
- `dbo.rmsmemberinfo`
- `dbo.rtinfo`
- `dbo.rtinfobak`
- `dbo.rtprice`
- `dbo.RtPrice_rms2009`
- `dbo.shopbookusedInfo`
- `dbo.shopinfo`
- `dbo.shoporderinfo`
- `dbo.shoporderitem`
- `dbo.shoptimeinfo`
- `dbo.SpecPrice`
- `dbo.SummaryStoreTimeSlotDaily`
- `dbo.sysarticlecolumns`
- `dbo.sysarticles`
- `dbo.sysarticleupdates`
- `dbo.syspublications`
- `dbo.sysreplservers`
- `dbo.sysschemaarticles`
- `dbo.syssubscriptions`
- `dbo.systranschemas`
- `dbo.timeinfo`
- `dbo.triggerRecord`
- `dbo.userinfo`
- `dbo.workbookout`
- `dbo.workconfig`
- `dbo.worknotcontype`
- `dbo.worknotrtinfo`
- `dbo.worknotshop`
- `dbo.worknotshoptime`
