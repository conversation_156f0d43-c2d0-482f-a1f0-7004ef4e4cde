# Claude Code Subagent 使用示例

本文档展示了如何使用Claude Code中创建的subagent来执行各种任务。

## 文档生成subagent使用示例

### 自动化API文档生成
```
// 在Python脚本中添加适当的docstring
def calculate_revenue(start_date, end_date, shop_id):
    """
    计算指定时间段内指定门店的收入
    
    Args:
        start_date (str): 开始日期，格式为'YYYY-MM-DD'
        end_date (str): 结束日期，格式为'YYYY-MM-DD'
        shop_id (int): 门店ID
        
    Returns:
        dict: 包含总收入、日均收入等信息的字典
        {
            'total_revenue': 125000.00,
            'daily_average': 15625.00,
            'shop_id': 11
        }
        
    Raises:
        ValueError: 当日期格式不正确时抛出
        ConnectionError: 当数据库连接失败时抛出
    """
    # 函数实现...
```

然后让Claude Code调用文档生成subagent来创建API文档：
```
请为这个calculate_revenue函数生成API文档，并包含使用示例。
```

### 项目README自动生成
```
// 让Claude Code调用文档生成subagent来创建项目概述
请基于项目的CLAUDE.md文件和代码结构，生成一个详细的README.md文件，包含：
1. 项目概述
2. 安装说明
3. 使用方法
4. 目录结构说明
5. 贡献指南
```

## SQL优化subagent使用示例

### 复杂查询优化
```sql
-- 原始查询
SELECT s.ShopName, d.DateKey, 
       SUM(f.Revenue) as TotalRevenue, 
       COUNT(f.TransactionID) as TransactionCount
FROM Fact_Daily_Shop_Summary f
JOIN Dim_Shop s ON f.ShopSK = s.ShopSK
JOIN Dim_Date d ON f.DateSK = d.DateSK
WHERE d.DateKey BETWEEN '2025-01-01' AND '2025-12-31'
  AND s.City = '广州'
GROUP BY s.ShopName, d.DateKey
ORDER BY TotalRevenue DESC;
```

让Claude Code调用SQL优化subagent来分析和优化此查询：
```
请分析这个SQL查询的性能，并提供优化建议。考虑索引、执行计划和查询结构。
```

### 存储过程性能分析
```sql
-- 让Claude Code调用SQL优化subagent来审查存储过程
请分析usp_GenerateDynamicUnifiedDailyReport存储过程的性能，特别关注以下方面：
1. 循环中的查询优化
2. 临时表使用效率
3. 索引使用情况
4. 可能的并行处理机会
```

## 最佳实践

1. **明确任务描述**：在调用subagent时，提供清晰、具体的任务描述和上下文信息
2. **提供足够上下文**：包含相关代码、查询或文档片段，以便subagent能够准确理解需求
3. **指定输出格式**：如果需要特定格式的输出，请明确说明
4. **迭代改进**：根据subagent的输出，可以进一步细化需求或要求更多细节

## 注意事项

1. Subagent会自动在需要时被调用，无需手动启动
2. 确保项目中的subagent配置文件保持最新
3. 可以根据项目需求创建更多专门的subagent
4. 定期审查和更新subagent的配置以适应项目发展