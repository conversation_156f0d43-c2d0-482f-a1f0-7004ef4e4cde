# 日废总表数据结构

## 📋 文档概述
- **文档名称**: 日废总表
- **业务类型**: KTV连锁店收入分析对比表
- **分析维度**: 同比、环比收入对比分析
- **文档来源**: https://docs.qq.com/sheet/DUEJWT3FpaGx3SWdq?tab=BB08J2

## 📊 数据字段结构

### 主要维度分类
该表格采用横向展开的数据结构，每个收入类别都包含同比和环比两个对比维度。

### 核心数据字段

#### 1. 基础信息
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| 门店集团 | VARCHAR(50) | 店铺或集团标识 |

#### 2. 营业收入分析
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| 营业收入_同比 | DECIMAL(15,2) | 与去年同期对比的营业收入 |
| 营业收入_环比 | DECIMAL(15,2) | 与上期对比的营业收入 |

#### 3. 餐饮消费收入（合资信管制）
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| 餐饮消费收入_同比 | DECIMAL(15,2) | 餐饮收入同比数据 |
| 餐饮消费收入_环比 | DECIMAL(15,2) | 餐饮收入环比数据 |

#### 4. 各权预约收入
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| 各权预约_同比 | DECIMAL(15,2) | 特权预约收入同比 |
| 各权预约_环比 | DECIMAL(15,2) | 特权预约收入环比 |

#### 5. 公众号收入
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| 公众号_同比 | DECIMAL(15,2) | 微信公众号渠道收入同比 |
| 公众号_环比 | DECIMAL(15,2) | 微信公众号渠道收入环比 |

#### 6. 美团收入
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| 美团_同比 | DECIMAL(15,2) | 美团平台收入同比 |
| 美团_环比 | DECIMAL(15,2) | 美团平台收入环比 |

#### 7. 收入大项组成
| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| 收入大项组成 | TEXT | 收入构成详细说明 |

## 📈 数据分析维度

### 对比分析类型
1. **同比分析** (Year-over-Year)
   - 与去年同期数据对比
   - 反映业务年度增长趋势
   - 消除季节性因素影响

2. **环比分析** (Month-over-Month/Period-over-Period)
   - 与上一期间数据对比
   - 反映业务短期变化趋势
   - 及时发现经营异常

### 收入渠道分类
1. **传统营业收入**: 基础KTV服务收入
2. **餐饮消费**: 合资信管制下的餐饮服务收入
3. **特权预约**: VIP或特殊服务预约收入
4. **线上渠道**: 
   - 微信公众号预订收入
   - 美团等第三方平台收入

## 🛠️ 系统实现建议

### 数据表设计
```sql
CREATE TABLE daily_revenue_summary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    store_group VARCHAR(50) NOT NULL,
    report_date DATE NOT NULL,
    
    -- 营业收入
    business_revenue_yoy DECIMAL(15,2),
    business_revenue_mom DECIMAL(15,2),
    
    -- 餐饮消费收入
    catering_revenue_yoy DECIMAL(15,2),
    catering_revenue_mom DECIMAL(15,2),
    
    -- 特权预约收入
    vip_booking_revenue_yoy DECIMAL(15,2),
    vip_booking_revenue_mom DECIMAL(15,2),
    
    -- 公众号收入
    wechat_revenue_yoy DECIMAL(15,2),
    wechat_revenue_mom DECIMAL(15,2),
    
    -- 美团收入
    meituan_revenue_yoy DECIMAL(15,2),
    meituan_revenue_mom DECIMAL(15,2),
    
    -- 收入构成说明
    revenue_composition TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_store_date (store_group, report_date),
    INDEX idx_report_date (report_date)
);
```

### 计算逻辑
1. **同比计算**: (本期数据 - 去年同期数据) / 去年同期数据 * 100%
2. **环比计算**: (本期数据 - 上期数据) / 上期数据 * 100%

### 数据更新策略
- **更新频率**: 日更新
- **数据来源**: 从各业务系统汇总
- **计算时机**: 每日营业结束后统一计算

## 💡 业务价值

### 经营分析价值
1. **趋势监控**: 及时发现收入变化趋势
2. **渠道分析**: 各收入渠道贡献度分析
3. **对比分析**: 多维度收入对比分析
4. **决策支持**: 为经营决策提供数据支撑

### 管理应用场景
1. **月度经营分析会**: 提供详细的收入分析数据
2. **渠道效果评估**: 评估各营销渠道的收入贡献
3. **预算执行监控**: 监控实际收入与预算的差异
4. **连锁店对比**: 不同门店收入表现对比

## 🔄 数据集成要求

### 数据来源系统
1. **POS系统**: 基础营业收入数据
2. **餐饮系统**: 餐饮消费数据
3. **预订系统**: 特权预约数据
4. **微信平台**: 公众号订单数据
5. **美团平台**: 第三方平台订单数据

### 数据质量要求
- **准确性**: 确保各渠道数据准确无误
- **完整性**: 覆盖所有收入来源
- **及时性**: 数据更新及时，支持实时分析
- **一致性**: 各系统数据口径保持一致

## 📝 备注
- 该表格为KTV收入分析的核心对比表
- 支持多维度收入分析和趋势监控
- 需要与各业务系统建立稳定的数据接口
- 建议定期进行数据校验和质量检查
