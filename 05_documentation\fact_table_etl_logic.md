
# 事实表 `Fact_Deal_Redemption` ETL 业务逻辑说明

---

## 1. 目标 (Objective)

本ETL（数据抽取、转换、加载）流程的核心目标是，每日计算按 **优惠券-门店-天** 聚合的核销汇总数据，并将其填充到 `Fact_Deal_Redemption` 事实表中，为 `银行汇总表` 的前端报表提供统一、准确、高效的数据源。

---

## 2. 数据源 (Data Sources)

本流程的数据来源于 `operatedata` 数据库中的两张核心业务表：

1.  **`dbo.fdcashbak`**: 提供了每一笔商品消费的流水记录，是度量的主要来源（如 `FdQty` 商品数量）。
2.  **`dbo.rmcloseinfo`**: 提供了结算信息，通过 `InvNo` (发票/账单号) 与 `fdcashbak` 关联，主要用于获取每笔流水的 **`workdate` (营业日期)**。

---

## 3. 核心处理逻辑 (ETL Logic)

ETL过程通过一个SQL查询完成所有计算和聚合，其核心步骤如下：

### 步骤 3.1: 关联源数据 (Joining Sources)

- 通过 `fdcashbak.InvNo = rmcloseinfo.InvNo` 将流水表和结算表关联起来，确保每条流水都能匹配到其发生的营业日期。

### 步骤 3.2: 关联维度表 (Dimension Lookups)

为了将业务数据转换为数据仓库中的标准化维度，查询会进一步关联所有相关的维度表：

- **关联优惠券维度:** `JOIN Dim_Bank_Deal ON fdcashbak.FdNo = Dim_Bank_Deal.FdNo`
  - **目的:** 筛选出属于银行优惠券的流水记录，并获取优惠券的详细信息（如 `DealAmount`, `SubsidyAmount`）和代理键 `DealSK`。
  - **注意:** 此处需处理 `COLLATE` (排序规则) 冲突。
- **关联门店维度:** `JOIN Dim_Shop ON fdcashbak.ShopId = Dim_Shop.ShopID`
  - **目的:** 获取门店的代理键 `ShopSK`。
- **关联日期维度:** `JOIN Dim_Date ON rmcloseinfo.workdate = Dim_Date.FullDate`
  - **目的:** 获取日期的代理键 `DateSK`。

### 步骤 3.3: 数据聚合 (Aggregation)

- 为了达到 `一券一店一日` 的数据粒度，查询使用 `GROUP BY dt.DateSK, s.ShopSK, d.DealSK` 对所有流水数据进行聚合。

### 步骤 3.4: 度量计算 (Measure Calculation)

在聚合过程中，对每个组计算以下核心度量：

- **`RedemptionCount` (核销量):** `SUM(fdcashbak.FdQty)`
- **`RedemptionAmount` (核销额):** `SUM(Dim_Bank_Deal.DealAmount * fdcashbak.FdQty)`
- **`SubsidyAmount` (补贴金额):** `SUM(Dim_Bank_Deal.SubsidyAmount * fdcashbak.FdQty)`
- **`PlatformFee` (平台服务费):** `SUM(Dim_Bank_Deal.ServiceFee * fdcashbak.FdQty)`
- **`NetAmount` (实收金额):** `SUM(Dim_Bank_Deal.NetAmount * fdcashbak.FdQty)`

---

## 4. 数据加载策略 (Loading Strategy)

- **`MERGE` 语句:** 为了保证数据的准确性和ETL脚本的可重复执行性（幂等性），加载操作采用 `MERGE` 语句。
- **逻辑:**
  - 以 `(DateSK, ShopSK, DealSK)` 作为联合业务主键进行匹配。
  - **`WHEN MATCHED`:** 如果当天该门店该优惠券的记录已存在，则执行 `UPDATE`，用最新的计算结果覆盖旧数据。
  - **`WHEN NOT MATCHED`:** 如果记录不存在，则执行 `INSERT`，插入新计算出的聚合数据行。
- **实现方式:** 通过将聚合结果先存入临时表 (`#temp_fact`)，再从临时表 `MERGE` 到最终事实表的方式来执行，以获得最佳性能和稳定性。
