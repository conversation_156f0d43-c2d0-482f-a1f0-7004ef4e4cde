# KTV数据同步系统分析报告

## 连接193数据库的结果

我们成功连接到了193.112.2.229服务器上的rms2019数据库，并找到了两个与同步相关的存储过程：

1. **O_Synchro** - 用于从193服务器同步预订和开台数据的存储过程
2. **trigger_synchro_rms2019_room** - 用于同步房间信息的存储过程

## 数据同步作业

在总部服务器上，我们找到了几个关键的数据同步作业：

1. **"每天同步预订跟开台数据到本地数据库"** 作业：
   - 执行 `rms2019.dbo.usp_Sync_RMS_DailyOpenData` 存储过程
   - 执行 `rms2019.dbo.usp_Sync_RMS_DailyBookData` 存储过程

2. **"数据导入"** 作业：
   - 执行 `date_Rms2019` 存储过程

## 存储过程详细信息

### usp_Sync_RMS_DailyOpenData
这个存储过程负责同步开台数据：
- 从 `cloudRms2019.rms2019.dbo.opencacheinfo` 和 `cloudRms2019.rms2019.dbo.openhistory` 表中获取数据
- 使用 `MERGE` 语句将数据同步到本地表中
- 处理前一天的业务数据（基于9小时偏移）

### usp_Sync_RMS_DailyBookData
这个存储过程负责同步预订数据：
- 从 `cloudRms2019.rms2019.dbo.bookcacheinfo` 和 `cloudRms2019.rms2019.dbo.bookhistory` 表中获取数据
- 使用 `MERGE` 语句将数据同步到本地表中
- 处理前一天的自然日期数据

### date_Rms2019
这个存储过程通过HTTP API调用从193服务器获取数据：
- 调用 `http://r.tang-hui.com.cn/api/Synchro/DataPull_Public.ashx` 接口
- 使用 `O_Synchro` 存储过程获取预订数据和开台数据
- 通过 `ex_public1` 存储过程执行HTTP请求

## 数据同步流程

系统的完整数据同步流程如下：
1. 通过HTTP API从193服务器获取数据
2. 将数据存储到总部服务器的rms2019数据库中
3. 通过SQL Agent作业定期执行同步存储过程
4. 使用链接服务器 `cloudRms2019` 访问分店数据

这个同步机制确保了总部能够获取所有分店的最新预订和开台数据，为后续的数据分析和报表生成提供基础数据。