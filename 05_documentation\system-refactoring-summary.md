# 系统重构变更总结

**版本**: 1.0
**日期**: 2025-06-25

# 系统重构变更总结 (最终版)

**版本**: 2.0
**日期**: 2025-06-25

本文档旨在清晰、简洁地总结本次员工角色与提成系统重构所涉及的核心数据库和API变更，作为后端开发的最终依据。该设计已确认可以支撑最终的薪酬统计报表。

---

## 1. 数据库表结构变更

### 1.1. `assignments` - 派房主表

- **移除字段**: `assignment_type` (VARCHAR)
- **说明**: 系统不再依赖预设的“派房类型”。

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT (PK) | 唯一ID |
| `room_id` | INT (FK) | 关联的房间ID |
| `total_amount`| DECIMAL(10,2) | 顾客消费总额 |
| `commissionable_amount` | DECIMAL(10,2) | **后端计算得出**的计提基数 |
| `created_at` | DATETIME | 创建时间 |
| `operator_id` | INT (FK) | 操作员ID |

### 1.2. `non_commissionable_items` - 不计提成项目表 (新增)

- **说明**: 用于动态管理不参与业绩提成计算的项目。此表为业务规则配置表，灵活性高。

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT (PK) | 唯一ID |
| `item_name` | VARCHAR(100) | 项目名称 (例如: "VIP服务费") |
| `is_active` | BOOLEAN | 是否当前生效 (true/false) |

### 1.3. `assignment_staff` - 派房员工与角色关联表 (新增或确认)

- **说明**: 用于存储一名员工在一次派房中可能拥有的多个角色。

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT (PK) | 唯一ID |
| `assignment_id` | INT (FK) | 关联的派房ID |
| `staff_id` | INT (FK) | 关联的员工ID |
| `role` | VARCHAR(50) | 员工角色 ('服务', '预订') |

### 1.4. `assignment_referrals` - 推荐关系表

- **移除字段**: `has_commission` (BOOLEAN)
- **说明**: 推荐关系不再与特定提成挂钩。

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT (PK) | 唯一ID |
| `assignment_id` | INT (FK) | 关联的派房ID |
| `recommender_id`| INT (FK) | 推荐人ID |
| `recommended_id`| INT (FK) | 被推荐人ID |

### 1.5. `staff_earnings` - 员工收入记录表 (最终版)

- **说明**: **系统的核心流水表，也是生成最终报表的关键**。记录了每一笔收入的详细信息。

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | INT (PK) | 唯一ID |
| `assignment_id` | INT (FK) | 关联的派房ID |
| `staff_id` | INT (FK) | 收入所属的员工ID |
| `earning_type` | VARCHAR(50) | 收入类型 ('业绩提成', '看房费', '推荐费') |
| `source_category` | VARCHAR(50) | **(新增)** 来源类别 (如: 'VIP房', '大房', '主订主服') |
| `source_amount` | DECIMAL(10,2)| **(新增)** 贡献收入的源金额 (即报表中的“营业额”) |
| `amount` | DECIMAL(10,2) | 最终计算出的收入金额 (即报表中的“提成”) |
| `calculation_note` | VARCHAR(255) | (可选) 文本备注 |

---

## 2. API 接口变更

- `POST /api/room/assign` (新增派房)
- `PUT /api/room/assignment/:id` (修改派房)

### 最新请求体 (Request Body) 结构

```json
{
  "roomId": 101,
  "totalAmount": 5000.00,
  "consumptionDetails": [
    { "itemName": "房间费", "amount": 4000.00 },
    { "itemName": "VIP服务费", "amount": 500.00 }
  ],
  "operatorId": 5,
  "staff": [
    { "staffId": 1, "roles": ["服务", "预订"] }
  ],
  "referrals": [
    { "recommenderId": 3, "recommendedIds": [1] }
  ]
}
```

---

## 3. 核心业务逻辑总结 (V2.1 - 精确版)

后端计算引擎需实现以下核心收入计算规则，**严格按照顺序执行**：

**步骤 0: 计算计提基数 (`commissionableAmount`)**
1.  从 `non_commissionable_items` 表中获取所有 `is_active = true` 的排除项名称。
2.  遍历前端传入的 `consumptionDetails` 数组，累加所有匹配到排除项的金额，得到 `excluded_amount`。
3.  计算 `commissionableAmount` = `totalAmount` - `excluded_amount`。
4.  将计算出的 `commissionableAmount` 存入 `assignments` 表。

**步骤 1: 计算业绩提成**
1.  对每个员工，根据其 `roles` 组合确定 **提成比例** 和 **`source_category`**：
    -   `roles` = ["服务", "预订"]  => 提成比例: **6%**, `source_category`: **'自订自看'**
    -   `roles` = ["预订"]          => 提成比例: **2%**, `source_category`: **'自订非自看'**
    -   `roles` = ["服务"]          => 提成比例: **2%**, `source_category`: **'公司派房'**
2.  计算 `提成金额` = `commissionableAmount` * `提成比例`。
3.  **写入 `staff_earnings` 表**: 
    -   `earning_type`: '业绩提成'
    -   `source_category`: 根据上述规则确定
    -   `source_amount`: `commissionableAmount`
    -   `amount`: `提成金额`

**步骤 2: 计算看房费**
-   **仅当**员工的 `roles` 数组中包含“服务”时，才计算看房费。
1.  **判断条件**: 检查员工角色组合。
    -   **情况A: 自订自看** (`roles` = ["服务", "预订"])
        -   根据房间类型确定看房费金额：
            -   VIP房及以上: **350元**
            -   大房及以下: **300元**
        -   `source_category` 设为房间类型 (如 'VIP房及以上')。
        -   `amount` 设为对应的金额 (350 或 300)。
    -   **情况B: 其他服务** (如 `roles` = ["服务"])
        -   看房费固定为 **200元**。
        -   `source_category` 设为 '标准看房费'。
        -   `amount` 设为 200。
2.  **写入 `staff_earnings` 表**: 
    -   `earning_type`: '看房费'
    -   `source_category`: 根据上述规则确定
    -   `source_amount`: 0
    -   `amount`: 根据上述规则确定

**步骤 3: 计算推荐费**
-   对每个推荐人，计算推荐费（每人100，上限200）。
-   **写入 `staff_earnings` 表**: `earning_type`='推荐费', `source_category`='员工推荐', `source_amount`=被推荐人数, `amount`=推荐费金额。

---

## 4. (可选) 动态规则引擎设计

为了实现极致的业务灵活性，允许非开发人员（如运营、财务）随时调整提成、看房费等核心业务数值，可以引入一个动态规则引擎。这套方案虽然会增加初期开发成本，但能一劳永逸地解决未来规则变更的需求。

**核心设计：`business_rules` 表**

| 字段名 | 类型 | 描述 | 示例值 |
| :--- | :--- | :--- | :--- |
| `id` | INT (PK) | 唯一ID | |
| `rule_name` | VARCHAR(100) | **规则名称 (给运营看)** | `自订自看提成` |
| `rule_key` | VARCHAR(50) | **规则键 (给程序用)** | `COMMISSION_RATE_SERVICE_BOOKING` |
| `conditions` | JSON | **规则生效的条件** | `{"roles": ["服务", "预订"]}` |
| `value` | VARCHAR(255) | **规则的结果值** | `0.06` |
| `description` | VARCHAR(255)| 规则的详细文字描述 | `当员工同时拥有服务和预订角色时的提成比例` |
| `is_active` | BOOLEAN | 是否生效 | `true` |

**工作流程**

1.  **后台管理**：需开发一个后台管理界面，用于增删改查 `business_rules` 表中的规则。
2.  **后端改造**：
    *   **加载规则**：服务启动时，将所有激活的规则加载到内存缓存。
    *   **匹配规则**：计算费用时，根据当前场景（如角色、房型）去内存中查找匹配的规则。
    *   **应用规则**：找到规则后，使用其 `value` 进行计算。

**工程建议**

-   可以将此模块作为**二期功能**。项目初期先采用**配置文件方案**（将规则写入独立的配置文件中），在保证上线速度的同时，也为未来升级到此动态规则引擎做好准备。

### 附录：配置文件方案实现细则

对于项目初期，推荐采用此方案以平衡开发速度与未来维护性。

**1. 创建配置文件 (`rules.json`)**

在后端项目中创建此文件，并填入以下结构化规则：

```json
{
  "commission_rates": {
    "service_and_booking": 0.06, 
    "booking_only": 0.02,       
    "service_only": 0.02        
  },
  "viewing_fees": {
    "service_and_booking": {
      "vip_room": 350,
      "large_room": 300
    },
    "default": 200
  },
  "referral_fees": {
    "per_person": 100,
    "max_cap": 200
  }
}
```

**2. 后端代码实现逻辑**

-   **启动时加载**：后端服务启动时，读取 `rules.json` 文件内容到内存中的一个全局配置对象（如 `rulesConfig`）。
-   **计算时使用**：在业务逻辑中，通过查询此 `rulesConfig` 对象来获取规则数值，而非硬编码。

**伪代码示例：**

*计算业绩提成*
```javascript
function calculateCommission(roles, commissionableAmount, rulesConfig) {
    let rateKey;
    if (roles.includes('服务') && roles.includes('预订')) rateKey = 'service_and_booking';
    else if (roles.includes('预订')) rateKey = 'booking_only';
    else if (roles.includes('服务')) rateKey = 'service_only';
    else return 0;

    const rate = rulesConfig.commission_rates[rateKey];
    return commissionableAmount * rate;
}
```

*计算看房费*
```javascript
function calculateViewingFee(roles, roomType, rulesConfig) {
    if (!roles.includes('服务')) return 0;

    if (roles.includes('预订')) {
        const feeKey = (roomType === 'VIP房及以上') ? 'vip_room' : 'large_room';
        return rulesConfig.viewing_fees.service_and_booking[feeKey];
    } else {
        return rulesConfig.viewing_fees.default;
    }
}
```

