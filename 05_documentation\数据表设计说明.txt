-------------------------------------------------------------------
第一部分：维度表 (Dimension Tables)
-------------------------------------------------------------------
维度表为数据分析提供业务上下文，定义了分析的视角。

### 1. Dim_Date (日期维度表)

- 用途: 提供统一的、丰富的日期维度信息，是所有时间序列分析（同比、环比、工作日/周末对比）的基础。
- 粒度: 每日一行。

设计思想与价值:
  职责: 预先计算并存储各类日期属性，供事实表关联。
  价值:  1. 提升性能: 避免在大型事实表上重复执行日期函数，通过索引关联提高查询效率。
         2. 增强分析能力: 支持按节假日、周末等预定义维度进行过滤与分组。
         3. 确保一致性: 集中管理日期维度，确保所有报表的口径统一。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description)                 |
| -------------------- | -------------------- | -------------------------------------- |
| DateSK               | INT (主键)           | 代理键，用于数据库内部关联。           |
| FullDate             | DATE                 | 完整的日期, 如 '2025-08-11'。          |
| Year                 | INT                  | 该日期所属的年份, 如 2025。            |
| Month                | INT                  | 该日期所属的月份, 如 8。               |
| Day                  | INT                  | 该日期是几号, 如 11。                  |
| DayOfWeek            | INT                  | 一周中的第几天 (1=周一, 7=周日)。      |
| WeekdayName_ZH       | NVARCHAR(10)         | 中文的星期名, 如 '星期一'。            |
| IsWeekend            | BIT                  | 是否为周末 (1=是, 0=否)。              |
| IsHoliday            | BIT                  | 是否为法定节假日 (1=是, 0=否)。        |
| HolidayName          | NVARCHAR(50)         | 节假日名称, 如 '国庆节'。              |


### 2. Dim_Shop (门店维度表)

- 用途: 提供所有门店的静态信息，确保门店名称、区域等信息的统一性。
- 粒度: 一个门店一行。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description)                                         |
| -------------------- | -------------------- | -------------------------------------------------------------- |
| ShopSK               | INT (主键)           | 代理键，用于数据库内部关联。                                   |
| ShopID               | INT                  | 业务主键，来自源系统 ShopInfo 表的 ShopID。                    |
| ShopName             | NVARCHAR(100)        | 门店的官方名称。                                               |
| Address              | NVARCHAR(100)        | 门店的完整地址，来自源表 address 字段。                        |
| City                 | NVARCHAR(50)         | 门店所在的城市 (可考虑从地址中提取或单独维护)。                |
| Region               | NVARCHAR(50)         | 门店所在的区域 (可考虑从地址中提取或单独维护)。                |
| OpenDate             | DATE                 | 门店的开业日期 (需将源表 OpenDate 的文本格式转换为日期格式)。    |
| IsActive             | BIT                  | 门店当前是否在用，来自源表 IsUse 字段，用于筛选在营门店。      |


### 3. Dim_TimeSlot (时段维度表)

- 用途: 统一定义业务上使用的所有特殊分析时段。
- 粒度: 一个业务时段一行。

| 字段名 (Column Name)  | 数据类型 (Data Type) | 中文含义 (Description)                                               |
| --------------------- | -------------------- | -------------------------------------------------------------------- |
| TimeSlotSK            | INT (主键)           | 代理键，用于数据库内部关联。                                         |
| TimeSlotBusinessKey   | NVARCHAR(10)         | 业务主键, 来自源系统 timeinfo 表的 TimeNo。                          |
| TimeSlotName          | NVARCHAR(50)         | 时段的名称, 如 '10:50-13:50'。                                      |
| StartTime             | TIME                 | 时段的开始时间 (需将源表 BegTime 的整数格式如1700转换为时间格式)。 |
| EndTime               | TIME                 | 时段的结束时间 (需将源表 EndTime 的整数格式如2000转换为时间格式)。 |
| TimeSlotGroup         | NVARCHAR(50)         | 时段所属的大类, 如 '白天档', '晚上档'。                              |
| IsSpecial             | BIT                  | 是否为特殊时段，来自源表 IsSpecial 字段。                            |

-------------------------------------------------------------------
第二部分：事实表 (Fact Tables)
-------------------------------------------------------------------
事实表存放量化的业务度量，并与维度表关联。

### 4. Fact_Daily_TimeSlot_Summary (时段级事实表)

- 用途: 存放与每个具体“时段”相关的运营指标。
- 粒度: 1门店 x 1日期 x 1时段。

| 字段名 (Column Name)          | 数据类型 (Data Type) | 中文含义 (Description)                     |
| ----------------------------- | -------------------- | ------------------------------------------ |
| TimeSlotSummarySK             | BIGINT (主键)        | 代理键。                                   |
| DateSK                        | INT                  | 外键，关联到 Dim_Date。                      |
| ShopSK                        | INT                  | 外键，关联到 Dim_Shop。                      |
| TimeSlotSK                    | INT                  | 外键，关联到 Dim_TimeSlot。                  |
| BookedRooms                   | INT                  | 该时段的预订房间数。                       |
| BookedGuests                  | INT                  | 该时段的预订人数。                         |
| OccupiedRooms                 | INT                  | 该时段的待客房间数。                       |
| OccupiedGuests                | INT                  | 该时段的待客人数。                         |
| OccupancyRate                 | DECIMAL(5, 4)        | 该时段的开房率 (计算得出)。                |
| Revenue                       | DECIMAL(18, 2)       | 该时段产生的总营业额。                     |
| Revenue_By_Channel_KPlus      | DECIMAL(18, 2)       | 该时段内，K+渠道产生的收入。               |
| Revenue_By_Channel_Special    | DECIMAL(18, 2)       | 该时段内，特权预约渠道产生的收入。         |
| Revenue_By_Channel_Meituan    | DECIMAL(18, 2)       | 该时段内，美团渠道产生的收入。             |
| Revenue_By_Channel_Douyin     | DECIMAL(18, 2)       | 该时段内，抖音渠道产生的收入。             |
| Revenue_By_Channel_RoomFee    | DECIMAL(18, 2)       | 该时段内，纯房费产生的收入。               |
| DirectFall_Batches            | INT                  | 该时段的直落批次数。                       |


### 5. Fact_Daily_Shop_Summary (门店级事实表)

- 用途: 存放按“天”汇总的全局性、总结性指标。
- 粒度: 1门店 x 1日期。

| 字段名 (Column Name)          | 数据类型 (Data Type) | 中文含义 (Description)                     |
| ----------------------------- | -------------------- | ------------------------------------------ |
| ShopSummarySK                 | BIGINT (主键)        | 代理键。                                   |
| DateSK                        | INT                  | 外键，关联到 Dim_Date。                      |
| ShopSK                        | INT                  | 外键，关联到 Dim_Shop。                      |
| TotalRevenue                  | DECIMAL(18, 2)       | 全天总营业额。                             |
| DayTimeRevenue                | DECIMAL(18, 2)       | 白天档总营业额 (由各时段数据汇总)。        |
| NightTimeRevenue              | DECIMAL(18, 2)       | 晚上档总营业额 (由各时段数据汇总)。        |
| TotalBatches                  | INT                  | 全天总批次数。                             |
| BuffetGuestCount              | INT                  | 全天自助餐总人数。                         |
| TotalDirectFallGuests         | INT                  | 全天直落总人数。                           |
| ComplimentaryBatches          | INT                  | 全天招待总批次数。                         |
| ComplimentaryRevenue          | DECIMAL(18, 2)       | 全天招待总金额。                           |
| PrivilegeBooking_Count_0Yuan  | INT                  | 0元特权预约的执行次数。                    |
| PrivilegeBooking_Count_5Yuan  | INT                  | 5元特权预约的执行次数。                    |
| PrivilegeBooking_Count_10Yuan | INT                  | 10元特权预约的执行次数。                   |
| PrivilegeBooking_Count_15Yuan | INT                  | 15元特权预约的执行次数。                   |
| Fee_Meituan_Booking           | DECIMAL(18, 2)       | 支付给美团的预约类总手续费。               |
| Fee_Meituan_GroupBuy          | DECIMAL(18, 2)       | 支付给美团的团购类总手续费。               |
| Fee_Douyin_Booking            | DECIMAL(18, 2)       | 支付给抖音的预约类总手续费。               |
| Fee_Douyin_GroupBuy           | DECIMAL(18, 2)       | 支付给抖音的团购类总手续费。               |
| Fee_Bank_GF                   | DECIMAL(18, 2)       | 支付给广发银行的总手续费。                 |
| Fee_Bank_CITIC                | DECIMAL(18, 2)       | 支付给中信银行的总手续费。                 |
| Fee_Bank_UnionPay             | DECIMAL(18, 2)       | 支付给银联的总手续费。                     |