# CLAUDE.md

此文件为Claude Code (claude.ai/code)在本代码库中工作时提供指导。

## 项目概述

这是一个KTV（卡拉OK）业务数据分析系统，旨在分析预订、结账和交易数据。该系统能够检测时间段重叠，识别直落客户（客人续单），执行渠道分析，并为KTV运营生成业务报告。

## 架构

### 数据库基础设施
- **总部服务器 (192.168.2.5)**: 包含operatedata（运营数据库）、mims（会员数据库）、rms2019（同步目标数据库）和本地dbfood数据库
- **分店服务器 (例如：193.112.2.229)**: 拥有自己的rms2019（同步源）和dbfood数据库
- **链接服务器**: 用于通过'cloudRms2019'等名称将分店rms2019数据库连接到总部
- **MySQL服务器**: 用于微信相关数据的外部MySQL数据库，位于yy.tang-hui.com.cn

### 核心数据流
1. **数据同步**: 通过SQL Agent作业从分店rms2019每日同步到总部rms2019
2. **数据处理**: SQL存储过程转换和分析运营数据
3. **报表生成**: Python和SQL脚本生成全面的业务报告
4. **银行交易集成**: 银行交易数据集成，用于全面的财务分析

### 关键组件
- **存储过程**: 时间段分析、直落客户检测、报表生成
- **SQL Agent作业**: 自动化数据同步和报表生成
- **Python脚本**: 数据处理、ETL操作和报表生成
- **维度建模**: 用于分析的事实/维度表数据仓库设计

### 数据仓库结构
- **维度表**: Dim_Date, Dim_Shop, Dim_TimeSlot, Dim_Bank_Deal
- **事实表**: Fact_Daily_TimeSlot_Summary（粒度：门店 x 日期 x 时间段）, Fact_Daily_Shop_Summary（粒度：门店 x 日期）, Fact_Deal_Redemption
- **ETL流程**: 每日执行usp_Populate_Analytics_Daily_Summary以填充事实表

## 目录结构

```
├── 00_deployment/          # 生产部署脚本
├── 01_sql_scripts/         # SQL脚本（核心逻辑）
│   ├── reports/           # 报表生成过程
│   ├── maintenance/       # 数据库维护脚本
│   └── ...                # 核心表创建和ETL逻辑
├── 02_python_scripts/     # Python数据处理脚本
├── 03_reports/           # 生成的报表和文档
├── 04_database_exports/   # 数据库模式导出
├── 05_documentation/     # 系统文档
└── archive/              # 历史和已弃用文件
```

## 业务逻辑

### 房间状态代码
- A=结账, B=坏房, C=续单, D=清洁
- E=空房, H=预转, L=慢带, U=占用
- R=留房, W=派房, V=微信, F=预结

### 核心分析
- **时间段重叠检测**: 识别与业务时间段重叠的客户消费时段
- **直落客户识别**: 区分真正的直落客户（客人续单）和延时付款
- **渠道分析**: 按来源（K+、美团、抖音、特权预约）对客户进行分类
- **收入分析**: 基于时间的收入统计，区分白天和晚上时段

### 数据处理规则
- **有效预订**: 通过isdelete字段过滤（有效=0）
- **客户计数**: opencacheinfo/openhistory中的Source字段为Numbers（不在FdCashBak中）
- **时间段统计**: 使用Beg_Key和End_Key字段
- **工作日期计算**: 运营数据的营业日从上午9:00开始

## 开发命令

### 环境设置
```bash
pip install -r requirements.txt
```

### SQL执行
对复杂查询使用`sqlcmd`：
```bash
sqlcmd -S server_name -d database_name -i script.sql -o output.csv
```

测试存储过程：
```sql
EXEC usp_GenerateDynamicUnifiedDailyReport @BeginDate='2025-07-01', @EndDate='2025-07-31', @ShopId=11
```

### Python脚本执行
```bash
python script_name.py
```

### 测试和调试
- **测试数据库连接**: 
  - `python test_193_connection.py` (测试到分店RMS服务器的连接)
  - `python test_mysql_connection.py` (测试到MySQL服务器的连接)
- **验证数据同步**: `python check_sync_job_status.py`
- **调试存储过程**: `python debug_sp_execution.py`

### 数据库操作
- **测试连接**: `test_193_connection.py`, `test_mysql_connection.py`
- **生成模式**: `generate_db_inventory.py`, `get_table_schemas_from_both_servers.py`
- **数据同步**: `daily_sync_transactions.py`, `populate_fact_deal_redemption.py`
- **银行交易导入**: `import_bank_deals_from_json_v5.py`, `final_import_bank_deals.py`

### 常见SQL Agent作业
- `RMS_Daily_Data_Sync_to_HQ`: 主同步作业
- `BankDeal_Daily_ETL_Job`: 银行交易数据ETL作业

## 技术指南

### 文件组织
- 新文件遵循基于类型的编号目录结构
- 遗留文件移至`archive/`目录
- Python脚本在`02_python_scripts/`中，SQL脚本在`01_sql_scripts/`中

### 数据库访问
- **分店总部同步**: 使用链接服务器`cloudRms2019.rms2019.dbo.table_name`
- **本地处理**: 直接连接到本地数据库
- **连接测试**: 在数据操作前始终验证连接

### 代码标准
- 所有业务逻辑使用中文和UTF-8编码
- 当脚本修改多次失败时，创建新文件而不是继续修改现有文件
- 存储过程遵循命名约定：`usp_Generate*`用于报表，`usp_Sync_*`用于数据同步
- 对复杂SQL执行使用sqlcmd的`-i`参数以避免引号转义问题

### 关键存储过程
- `usp_GenerateDynamicUnifiedDailyReport`: 主报表生成
- `usp_Sync_RMS_DailyOpenData`: 每日开台数据同步
- `usp_Sync_RMS_DailyBookData`: 每日预订数据同步
- `usp_Populate_Analytics_Daily_Summary`: 填充数据仓库事实表
- `RMS_Daily_Data_Sync_to_HQ`: 主同步作业

### 常见问题
- 如果每日同步作业缺失，使用`00_deployment/04_create_sql_agent_job.sql`重新创建
- 核心消费数据在本地`operatedata.dbo.FdCashBak`中，不在远程`dbfood`中
- 有效预订过滤需要`isdelete = 0`条件

always response in Chinese utf-8