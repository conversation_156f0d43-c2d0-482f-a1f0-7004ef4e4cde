- Always respond in 中文 with utf-8 encoding.
- 如果一个脚本或文件执行或修改失败，并且尝试修复后再次失败，我应该放弃继续修改，并创建一个全新的文件来执行任务，以避免潜在的文件缓存或状态问题。
- KTV房间状态码中文含义：A=结帐, B=坏房, C=续单, D=清洁, E=空房, H=预转, L=慢带, U=占用, R=留房, W=派房, V=微信, F=预结。
- 从总部服务器(***********)访问门店服务器(*************)的rms2019数据库时，应使用名为 'cloudRms2019' 的链接服务器。例如：cloudRms2019.rms2019.dbo.opencacheinfo。
- KTV项目的数据同步新方案：1. 目标是同步opencacheinfo, openhistory, bookcacheinfo, bookhistory四个表。2. 数据源是链接服务器cloudRms2019。3. 开台数据需增加并计算'WorkDate'(营业日，早上9点为界)，预订数据按自然日同步。4. 通过两个新的独立存储过程(usp_Sync_RMS_DailyOpenData, usp_Sync_RMS_DailyBookData)和新的定时作业(RMS_Daily_Data_Sync)实现。
- KTV_Data_Analysis项目文件结构规范：新文件应根据类型放入对应的数字编号文件夹：00_deployment (部署脚本), 01_sql_scripts (SQL脚本, 内含reports, maintenance, tests_and_adhoc子文件夹), 02_python_scripts (Python脚本), 03_reports (报告, 内含analysis_results, final_reports_and_docs子文件夹), 04_database_exports (数据库导出), 05_documentation (文档)。旧文件应移至archive/文件夹。
- KTV_Data_Analysis项目信息获取优先级：1. 首先，分析项目中的 .md 和 .sql 文件以了解背景和历史。2. 其次，如果文件信息不足，则连接数据库 (***********) 获取实时数据和对象结构。
- KTV数据库架构：总部服务器(***********)拥有库：operatedata(运营库)、mims(会员库)、rms2019(同步目标库)和一个本地dbfood库。每个远程门店(如*************)拥有自己的rms2019(同步源)和dbfood库。总部通过链接服务器(如cloudRms2019)连接门店rms2019。核心任务是将门店rms2019数据同步到总部rms2019。各dbfood库相互独立，不进行同步。
- 核心诊断：总部服务器(***********)的每日数据同步作业'RMS_Daily_Data_Sync_to_HQ'若未执行，极有可能是被意外删除。应首先检查作业是否存在，如不存在，则使用'00_deployment/04_create_sql_agent_job.sql'脚本进行重建。
- 所有新的 .py 脚本文件应创建在 `02_python_scripts/` 目录下。
- KTV报表核心业务逻辑：有效预订需过滤isdelete字段；待客人数的唯一来源是opencacheinfo/openhistory的Numbers字段，因为FdCashBak中不存在“消费人数”项；时间段统计应使用Beg_Key和End_Key。
- 在KTV项目中，包含消费项详情的核心表是 FdCashBak，它位于本地的 operatedata 数据库，而不是远程的 Dbfood 数据库。
- 技术准则：使用sqlcmd执行复杂查询时，应优先将SQL语句写入.sql文件，然后使用 -i 参数执行，以避免-Q参数的引号和转义问题。