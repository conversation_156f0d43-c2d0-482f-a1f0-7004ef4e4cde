import pyodbc
import sys

# --- 连接信息 ---
server = "192.168.2.2"
user = "sa"
password = "Musicbox123"

def find_staff_report_info():
    """连接到服务器并查找与'staffreport'相关的所有信息。"""
    conn = None
    try:
        # --- 更稳健地构建连接字符串 ---
        conn_str = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            f"SERVER={server};"
            "DATABASE=msdb;"  # 先连接到msdb查找作业
            f"UID={user};"
            f"PWD={password};"
            "TrustServerCertificate=yes;"
            "LoginTimeout=5;"
        )
        
        print(f"--- 正在连接到服务器 {server}...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("--- 连接成功！正在查询相关作业...")

        # --- 查找相关的 SQL Agent 作业 ---
        job_query = """
        SELECT j.name, j.description, s.step_name, s.command
        FROM dbo.sysjobs j JOIN dbo.sysjobsteps s ON j.job_id = s.job_id
        WHERE s.command LIKE N'%staffreport%' OR j.name LIKE N'%staffreport%';
        """
        cursor.execute(job_query)
        jobs = cursor.fetchall()

        if jobs:
            print("--- 找到了以下相关作业信息: ---")
            for job in jobs:
                print("\n----------------------------------------")
                print(f"作业名称:   {job.name}")
                print(f"作业描述:   {job.description}")
                print(f"步骤名称:   {job.step_name}")
                print(f"执行命令:   {job.command}")
                print("----------------------------------------")
        else:
            print("--- 在 msdb 中未找到与 'staffreport' 相关的SQL Agent作业。---")

        # --- 查找相关的存储过程 ---
        print("\n--- 正在 operatedata 数据库中查找相关存储过程... ---")
        # 必须使用 conn.execute 来确保数据库上下文切换
        conn.execute("USE operatedata;")
        proc_query = "SELECT name FROM sys.procedures WHERE name LIKE '%staffreport%'"
        cursor.execute(proc_query)
        procs = cursor.fetchall()

        if procs:
            print("--- 找到了以下可能相关的存储过程: ---")
            for proc in procs:
                print(f"  - {proc.name}")
        else:
            print("--- 在 operatedata 中也未找到相关存储过程。---")

    except pyodbc.Error as ex:
        print(f"数据库操作失败: {ex}", file=sys.stderr)
    except Exception as e:
        print(f"发生未知错误: {e}", file=sys.stderr)
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

# --- 执行 ---
if __name__ == "__main__":
    find_staff_report_info()