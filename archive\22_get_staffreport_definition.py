

import pyodbc
import sys

# --- 连接信息 ---
server = "192.168.2.2"
user = "sa"
password = "Musicbox123"
database = "operatedata"

def get_staff_report_definition():
    """连接到数据库并获取 StaffReport 存储过程的定义。"""
    conn = None
    try:
        conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password};TrustServerCertificate=yes;LoginTimeout=5;"
        
        print(f"--- 正在连接到 {server}/{database}...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("--- 连接成功！正在获取 StaffReport 的定义...")

        sql_query = "EXEC sp_helptext 'StaffReport'"
        
        cursor.execute(sql_query)
        rows = cursor.fetchall()
        
        if not rows:
            print("--- 错误: 无法找到名为 'StaffReport' 的存储过程。---")
            return

        print("--- StaffReport 存储过程定义如下: ---")
        definition = "".join([row.Text for row in rows])
        print(definition)

    except pyodbc.Error as ex:
        print(f"数据库操作失败: {ex}", file=sys.stderr)
    except Exception as e:
        print(f"发生未知错误: {e}", file=sys.stderr)
    finally:
        if conn:
            conn.close()
            print("\n--- 数据库连接已关闭。---")

# --- 执行 ---
if __name__ == "__main__":
    get_staff_report_definition()

