

import pyodbc
import sys
from collections import defaultdict

# --- 连接信息 ---
server = "192.168.2.5"
user = "sa"
password = "Musicbox123"
database = "operatedata"

# --- 用于识别旧版本的关键字 ---
OLD_VERSION_KEYWORDS = ['_old', '_v1', '_v2', '_v3', '_v4', '_v5', '_backup', '_test', '_temp', '_fixed', '_fix', '_simple', '_bak']

def analyze_stored_procedures():
    """连接到数据库，分析所有usp_开头的存储过程，并提供清理建议。"""
    conn = None
    try:
        conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password};TrustServerCertificate=yes;LoginTimeout=10;"
        
        print(f"--- 正在连接到 {server}/{database}...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("--- 连接成功！正在获取存储过程列表和依赖关系...")

        # 使用 sys.sql_expression_dependencies 兼容 SQL Server 2012
        sql_query = """
        SELECT 
            p.name AS procedure_name,
            p.create_date,
            p.modify_date,
            OBJECT_NAME(d.referencing_id) AS referencing_entity_name
        FROM sys.procedures p
        LEFT JOIN sys.sql_expression_dependencies d 
            ON p.object_id = d.referenced_id
        WHERE p.name LIKE 'usp_%'
        ORDER BY p.name;
        """
        
        cursor.execute(sql_query)
        rows = cursor.fetchall()

        # --- 数据处理 ---
        # 使用字典来聚合每个存储过程的引用者
        procedures = defaultdict(lambda: {'create_date': None, 'modify_date': None, 'references': set()})
        for row in rows:
            procedures[row.procedure_name]['create_date'] = row.create_date
            procedures[row.procedure_name]['modify_date'] = row.modify_date
            if row.referencing_entity_name:
                procedures[row.procedure_name]['references'].add(row.referencing_entity_name)

        # --- 分类 ---
        high_priority_cleanup = []
        medium_priority_cleanup = []
        low_priority_active = []

        for name, data in procedures.items():
            is_old_by_name = any(keyword in name.lower() for keyword in OLD_VERSION_KEYWORDS)
            has_no_references = not data['references']

            if has_no_references and is_old_by_name:
                high_priority_cleanup.append(name)
            elif has_no_references:
                medium_priority_cleanup.append(name)
            else:
                # 只要有引用，就认为是活跃的
                low_priority_active.append((name, sorted(list(data['references']))))

        # --- 打印报告 ---
        print("\n" + "="*80)
        print("存储过程清理分析报告")
        print("="*80)

        print("\n[高优先级清理建议 (High Priority)]")
        print("说明: 这些存储过程既有旧版本命名特征，又无任何内部依赖，是最安全的清理对象。")
        if high_priority_cleanup:
            for sp in sorted(high_priority_cleanup):
                print(f"  - {sp}")
        else:
            print("  (无)")

        print("\n[中优先级清理建议 (Medium Priority)]")
        print("说明: 这些存储过程无任何内部依赖，但命名不明确。请在删除前确认无外部程序调用。")
        if medium_priority_cleanup:
            for sp in sorted(medium_priority_cleanup):
                print(f"  - {sp}")
        else:
            print("  (无)")

        print("\n[低优先级/请勿清理 (Active & In-Use)]")
        print("说明: 这些存储过程正被其他数据库对象调用，请勿删除。")
        if low_priority_active:
            for sp, refs in sorted(low_priority_active):
                print(f"  - {sp} (被 {len(refs)} 个对象引用: {refs})")
        else:
            print("  (无)")

        print("\n" + "="*80)

    except pyodbc.Error as ex:
        print(f"数据库操作失败: {ex}", file=sys.stderr)
    except Exception as e:
        print(f"发生未知错误: {e}", file=sys.stderr)
    finally:
        if conn:
            conn.close()
            print("\n--- 数据库连接已关闭。---")

# --- 执行 ---
if __name__ == "__main__":
    analyze_stored_procedures()

