

import pyodbc
import sys
from datetime import datetime

# --- 连接信息 ---
server = "192.168.2.5"
user = "sa"
password = "Musicbox123"
database = "operatedata"

def get_function_creation_dates():
    """连接到数据库，查询所有函数的创建和修改日期，并按创建日期倒序排列。"""
    conn = None
    try:
        conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password};TrustServerCertificate=yes;LoginTimeout=10;"
        
        print(f"--- 正在连接到 {server}/{database}...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("--- 连接成功！正在查询函数信息...")

        # 查询 sys.objects 视图，筛选出所有类型的函数
        # FN = SQL_SCALAR_FUNCTION, IF = SQL_INLINE_TABLE_VALUED_FUNCTION, TF = SQL_TABLE_VALUED_FUNCTION
        sql_query = """
        SELECT 
            name,
            create_date,
            modify_date,
            type_desc
        FROM sys.objects
        WHERE type IN ('FN', 'IF', 'TF')
        ORDER BY create_date DESC;
        """
        
        cursor.execute(sql_query)
        rows = cursor.fetchall()

        if not rows:
            print("--- 在此数据库中未找到任何用户自定义函数。---")
            return

        # --- 打印结果 ---
        print("\n" + "-"*100)
        print(f"{'函数名称':<50} {'类型':<30} {'创建日期':<20} {'最后修改日期':<20}")
        print("-"*100)
        for row in rows:
            # 格式化日期以提高可读性
            create_dt = row.create_date.strftime('%Y-%m-%d %H:%M:%S')
            modify_dt = row.modify_date.strftime('%Y-%m-%d %H:%M:%S')
            print(f"{row.name:<50} {row.type_desc:<30} {create_dt:<20} {modify_dt:<20}")
        print("-"*100)

    except pyodbc.Error as ex:
        print(f"数据库操作失败: {ex}", file=sys.stderr)
    except Exception as e:
        print(f"发生未知错误: {e}", file=sys.stderr)
    finally:
        if conn:
            conn.close()
            print("\n--- 数据库连接已关闭。---")

# --- 执行 ---
if __name__ == "__main__":
    get_function_creation_dates()

