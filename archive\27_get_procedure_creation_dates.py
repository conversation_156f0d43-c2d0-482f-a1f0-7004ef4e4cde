

import pyodbc
import sys
from datetime import datetime

# --- 连接信息 ---
server = "192.168.2.5"
user = "sa"
password = "Musicbox123"
database = "operatedata"

def get_procedure_creation_dates():
    """连接到数据库，查询所有usp_开头的存储过程的创建和修改日期，并按创建日期倒序排列。"""
    conn = None
    try:
        conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password};TrustServerCertificate=yes;LoginTimeout=10;"
        
        print(f"--- 正在连接到 {server}/{database}...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("--- 连接成功！正在查询存储过程信息...")

        # 查询 sys.procedures 视图
        sql_query = """
        SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures
        WHERE name LIKE 'usp_%'
        ORDER BY create_date DESC;
        """
        
        cursor.execute(sql_query)
        rows = cursor.fetchall()

        if not rows:
            print("--- 在此数据库中未找到任何以 usp_ 开头的存储过程。---")
            return

        # --- 打印结果 ---
        print("\n" + "-"*90)
        print(f"{'存储过程名称':<60} {'创建日期':<20} {'最后修改日期':<20}")
        print("-"*90)
        for row in rows:
            create_dt = row.create_date.strftime('%Y-%m-%d %H:%M:%S')
            modify_dt = row.modify_date.strftime('%Y-%m-%d %H:%M:%S')
            print(f"{row.name:<60} {create_dt:<20} {modify_dt:<20}")
        print("-"*90)

    except pyodbc.Error as ex:
        print(f"数据库操作失败: {ex}", file=sys.stderr)
    except Exception as e:
        print(f"发生未知错误: {e}", file=sys.stderr)
    finally:
        if conn:
            conn.close()
            print("\n--- 数据库连接已关闭。---")

# --- 执行 ---
if __name__ == "__main__":
    get_procedure_creation_dates()

