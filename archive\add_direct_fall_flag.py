import pyodbc
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'Dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 新增列名 ---
NEW_COLUMN_NAME = 'IsDirectFall'

def add_and_update_flag():
    """在food表中添加IsDirectFall列，并根据组合逻辑更新其值。"""
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} ---")
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("连接成功！")

        # 1. 步骤A: 检查并添加新列
        print(f"\n--- 步骤 A: 检查并添加新列 '{NEW_COLUMN_NAME}' 到 food 表 ---")
        # 使用系统视图检查列是否存在
        check_column_sql = f"SELECT COUNT(*) FROM sys.columns WHERE Name = N'{NEW_COLUMN_NAME}' AND Object_ID = Object_ID(N'dbo.food')"
        cursor.execute(check_column_sql)
        column_exists = cursor.fetchone()[0] > 0

        if not column_exists:
            print(f"列 '{NEW_COLUMN_NAME}' 不存在，正在添加... (BIT, DEFAULT 0)")
            add_column_sql = f"ALTER TABLE dbo.food ADD {NEW_COLUMN_NAME} BIT NOT NULL DEFAULT 0;"
            cursor.execute(add_column_sql)
            cnxn.commit()
            print("添加成功！")
        else:
            print(f"列 '{NEW_COLUMN_NAME}' 已存在，无需添加。")

        # 2. 步骤B: 使用组合逻辑更新标签
        print(f"\n--- 步骤 B: 使用组合逻辑更新所有‘直落’项目的 '{NEW_COLUMN_NAME}' 标志位 ---")
        update_sql = f"""UPDATE f
                       SET f.{NEW_COLUMN_NAME} = 1
                       FROM 
                           dbo.food f
                       LEFT JOIN 
                           dbo.foodlabel fl ON f.FdNo = fl.FdNo
                       WHERE 
                           f.FdCName LIKE N'%直落%' 
                           OR f.FdCName LIKE N'%跨时段%'
                           OR fl.Type LIKE N'%直落%'
                           OR fl.Category1 LIKE N'%直落%'
                           OR fl.CtTypeName LIKE N'%直落%'
                           OR fl.Type LIKE N'%跨时段%'
                           OR fl.Category1 LIKE N'%跨时段%'
                           OR fl.CtTypeName LIKE N'%跨时段%';"""
        
        cursor.execute(update_sql)
        updated_rows = cursor.rowcount
        cnxn.commit()
        print(f"更新完成！共 {updated_rows} 个商品的‘直落’标志被更新为 1。")

        # 3. 步骤C: 验证结果
        print(f"\n--- 步骤 C: 验证被打上标签的商品列表 ---")
        verify_sql = f"SELECT FdNo, FdCName FROM dbo.food WHERE {NEW_COLUMN_NAME} = 1 ORDER BY FdNo;"
        df_verify = pd.read_sql_query(verify_sql, cnxn)
        print(f"查询到 {len(df_verify)} 个被打上标签的商品，部分示例如下：")
        print(df_verify.head(15).to_string(index=False)) # 打印前15条作为示例

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        # 如果出错，尝试回滚
        if cnxn and cnxn.autocommit == False:
            try:
                cnxn.rollback()
                print("事务已回滚。")
            except:
                pass
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    add_and_update_flag()
