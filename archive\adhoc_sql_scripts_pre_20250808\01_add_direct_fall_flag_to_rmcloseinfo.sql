
-- 步骤 1: 修改 RmCloseInfo 表，增加直落标志位和性能索引
-- 本脚本可以安全重复执行

BEGIN TRANSACTION;

BEGIN TRY

    -- 1. 添加 IsDirectFall 字段 (BIT类型, 默认为0)
    -- 检查字段是否存在，不存在则添加
    IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE Name = N'IsDirectFall' AND Object_ID = OBJECT_ID(N'dbo.RmCloseInfo'))
    BEGIN
        PRINT 'Adding [IsDirectFall] column to dbo.RmCloseInfo...';
        ALTER TABLE dbo.RmCloseInfo ADD IsDirectFall BIT NOT NULL CONSTRAINT DF_RmCloseInfo_IsDirectFall DEFAULT 0;
        PRINT 'Column [IsDirectFall] added successfully.';
    END
    ELSE
    BEGIN
        PRINT 'Column [IsDirectFall] already exists in dbo.RmCloseInfo.';
    END

    -- 2. 为 (WorkDate, ShopId) 创建索引以极大提升更新和查询性能
    -- 检查索引是否存在，不存在则创建
    IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE Name = 'IX_RmCloseInfo_WorkDate_ShopId' AND object_id = OBJECT_ID('dbo.RmCloseInfo'))
    BEGIN
        PRINT 'Creating index [IX_RmCloseInfo_WorkDate_ShopId] on dbo.RmCloseInfo...';
        CREATE NONCLUSTERED INDEX IX_RmCloseInfo_WorkDate_ShopId ON dbo.RmCloseInfo(WorkDate, ShopId);
        PRINT 'Index [IX_RmCloseInfo_WorkDate_ShopId] created successfully.';
    END
    ELSE
    BEGIN
        PRINT 'Index [IX_RmCloseInfo_WorkDate_ShopId] already exists.';
    END

    COMMIT TRANSACTION;
    PRINT 'Step 1 completed successfully.';

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT 'An error occurred during Step 1. Transaction rolled back.';
    -- 抛出原始错误信息
    THROW;
END CATCH
GO

