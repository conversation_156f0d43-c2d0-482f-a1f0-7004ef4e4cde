
-- 步骤 2 (修正版): 创建用于更新“直落”标志的专用存储过程
-- 版本 V2, 修正了无法找到直落项的逻辑

IF OBJECT_ID('dbo.usp_UpdateDirectFallFlag_ByName', 'P') IS NOT NULL
BEGIN
    PRINT 'Dropping existing procedure [usp_UpdateDirectFallFlag_ByName]...';
    DROP PROCEDURE dbo.usp_UpdateDirectFallFlag_ByName;
END
GO

CREATE PROCEDURE dbo.usp_UpdateDirectFallFlag_ByName
    @TargetDate VARCHAR(8), -- 接受 YYYYMMDD 格式的字符串
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;

    PRINT N'Starting direct fall flag update (FIXED) for ShopID: ' + CAST(@ShopId AS NVARCHAR(10)) + N' on Date: ' + @TargetDate;

    -- 步骤 1: 先将目标范围内所有记录的标志位重置为0
    UPDATE dbo.RmCloseInfo
    SET IsDirectFall = 0
    WHERE WorkDate = @TargetDate AND ShopId = @ShopId;
    PRINT N'Reset IsDirectFall flag for all records on the target date and shop.';

    -- 步骤 2: 高效地更新 RmCloseInfo 表
    -- **【修正点】** 不再使用JOIN，而是改用更健壮的 EXISTS 子查询来避免关联问题。
    -- 这个查询会更新所有在 FdCashBak 中能找到相应“直落”消费项的 RmCloseInfo 记录。
    UPDATE rci
    SET rci.IsDirectFall = 1
    FROM dbo.RmCloseInfo AS rci
    WHERE rci.WorkDate = @TargetDate
      AND rci.ShopId = @ShopId
      AND EXISTS (
          SELECT 1
          FROM operatedata.dbo.FdCashBak fcb
          WHERE fcb.InvNo = rci.InvNo COLLATE Chinese_PRC_CI_AS -- 明确指定排序规则以防万一
            AND fcb.ShopId = @ShopId
            AND fcb.FdCName LIKE N'%直落%'
      );

    DECLARE @UpdateCount INT = @@ROWCOUNT;
    PRINT N'Successfully updated ' + CAST(@UpdateCount AS NVARCHAR(10)) + N' records in RmCloseInfo with the IsDirectFall flag.';

    -- 增加一个验证步骤
    IF @UpdateCount = 0
    BEGIN
        PRINT N'WARNING: No records were flagged as direct fall. Please double-check source data and logic.';
    END

    PRINT N'Direct fall flag update (FIXED) completed for ShopID: ' + CAST(@ShopId AS NVARCHAR(10)) + N' on Date: ' + @TargetDate;

END
GO

PRINT 'FIXED Procedure [usp_UpdateDirectFallFlag_ByName] created successfully.';
GO
