
-- 步骤 3: 创建总调度存储过程
-- 这个过程将协调“打标签”和“生成报表”两个步骤，并为所有门店循环执行

IF OBJECT_ID('dbo.usp_RunNightlyKTVReportJob', 'P') IS NOT NULL
BEGIN
    PRINT 'Dropping existing procedure [usp_RunNightlyKTVReportJob]...';
    DROP PROCEDURE dbo.usp_RunNightlyKTVReportJob;
END
GO

CREATE PROCEDURE dbo.usp_RunNightlyKTVReportJob
AS
BEGIN
    SET NOCOUNT ON;

    -- 定义需要处理的门店ID列表
    -- 使用一个表变量来存储ShopId，便于管理和扩展
    DECLARE @ShopList TABLE (ShopId INT PRIMARY KEY);
    INSERT INTO @ShopList (ShopId) VALUES (2), (3), (4), (5), (6), (8), (9), (10), (11);

    -- 定义目标日期，通常是昨天
    DECLARE @TargetDate DATE = CAST(DATEADD(day, -1, GETDATE()) AS DATE);
    
    -- 声明用于循环的变量
    DECLARE @CurrentShopId INT;
    DECLARE @JobLogMessage NVARCHAR(1000);

    PRINT N'--------------------------------------------------';
    PRINT N'Starting Nightly KTV Report Job for Date: ' + CONVERT(NVARCHAR, @TargetDate, 120);
    PRINT N'--------------------------------------------------';

    -- 使用游标来安全地循环每一个门店
    DECLARE ShopCursor CURSOR FOR
    SELECT ShopId FROM @ShopList ORDER BY ShopId;

    OPEN ShopCursor;
    FETCH NEXT FROM ShopCursor INTO @CurrentShopId;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        BEGIN TRY
            PRINT N'';
            PRINT N'Processing ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            PRINT N'------------------------';

            -- 步骤 3a: 调用“直落”标签更新程序
            PRINT N'Step 3a: Updating direct fall flags...';
            EXEC dbo.usp_UpdateDirectFallFlag_ByName @TargetDate = @TargetDate, @ShopId = @CurrentShopId;
            PRINT N'Step 3a: Direct fall flags updated successfully.';

            -- 步骤 3b: 调用现有的统一日报表生成程序
            PRINT N'Step 3b: Generating unified daily report...';
            EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected @TargetDate = @TargetDate, @ShopId = @CurrentShopId;
            PRINT N'Step 3b: Unified daily report generated successfully.';

            -- 记录成功的日志
            SET @JobLogMessage = N'Successfully processed ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10)) + N' for date: ' + CONVERT(NVARCHAR, @TargetDate, 120);
            PRINT @JobLogMessage;
            -- (可选) 您可以在这里将日志插入到一个永久的日志表中
            -- INSERT INTO dbo.GlobalJobLog (ShopId, ReportDate, Status, Message) VALUES (@CurrentShopId, @TargetDate, 'Success', @JobLogMessage);

        END TRY
        BEGIN CATCH
            -- 如果任何一个步骤失败，记录错误并继续处理下一个门店
            DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
            SET @JobLogMessage = N'FAILED to process ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10)) + N' for date: ' + CONVERT(NVARCHAR, @TargetDate, 120) + N'. Error: ' + @ErrorMessage;
            
            -- 使用 RAISERROR 将错误打印到SQL Server错误日志中，便于监控
            RAISERROR(@JobLogMessage, 10, 1) WITH NOWAIT;
            -- (可选) 您也可以在这里将失败日志插入到永久的日志表中
            -- INSERT INTO dbo.GlobalJobLog (ShopId, ReportDate, Status, Message) VALUES (@CurrentShopId, @TargetDate, 'Failure', @JobLogMessage);

        END CATCH

        FETCH NEXT FROM ShopCursor INTO @CurrentShopId;
    END

    CLOSE ShopCursor;
    DEALLOCATE ShopCursor;

    PRINT N'';
    PRINT N'--------------------------------------------------';
    PRINT N'Nightly KTV Report Job finished.';
    PRINT N'--------------------------------------------------';

END
GO

PRINT 'Master scheduler procedure [usp_RunNightlyKTVReportJob] created successfully.';
GO
