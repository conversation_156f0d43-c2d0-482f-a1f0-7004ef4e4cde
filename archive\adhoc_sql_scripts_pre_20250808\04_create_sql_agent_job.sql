
-- 步骤 4: 创建 SQL Server 代理作业以实现每日自动化执行
-- 请确保 SQL Server Agent 服务正在运行

USE msdb;
GO

BEGIN TRANSACTION;

DECLARE @ReturnCode INT = 0;

-- 1. 作业分类 (可选，但推荐)
IF NOT EXISTS (SELECT 1 FROM msdb.dbo.syscategories WHERE name = N'KTV Business Reports' AND category_class = 1)
BEGIN
    EXEC @ReturnCode = msdb.dbo.sp_add_category @class=N'JOB', @type=N'LOCAL', @name=N'KTV Business Reports';
    IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;
END

-- 2. 定义作业名称
DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'KTV - Nightly Unified Report Generation';

-- 首先检查作业是否存在，如果存在则先删除，确保我们创建的是最新配置
SELECT @jobId = job_id FROM msdb.dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    PRINT N'Job "' + @jobName + N'" already exists. Deleting it to recreate.';
    EXEC msdb.dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
END

PRINT N'Creating job "' + @jobName + N'"...';

-- 3. 添加作业
EXEC @ReturnCode = msdb.dbo.sp_add_job @job_name=@jobName, 
        @enabled=1, 
        @notify_level_eventlog=0, 
        @notify_level_email=0, 
        @notify_level_netsend=0, 
        @notify_level_page=0, 
        @delete_level=0, 
        @description=N'每日自动执行KTV统一日报表生成任务。该作业会为所有指定门店循环执行：1. 更新直落标志；2. 生成统一日报表。',
        @category_name=N'KTV Business Reports', 
        @owner_login_name=N'sa';
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

-- 4. 添加作业步骤 (核心执行内容)
EXEC @ReturnCode = msdb.dbo.sp_add_jobstep @job_name=@jobName, @step_name=N'Run Master Scheduler Procedure', 
        @step_id=1, 
        @cmdexec_success_code=0, 
        @on_success_action=1, -- 成功后转到下一步 (或退出)
        @on_success_step_id=0, 
        @on_fail_action=2,    -- 失败时退出作业
        @on_fail_step_id=0, 
        @retry_attempts=0, 
        @retry_interval=0, 
        @os_run_priority=0, @subsystem=N'TSQL', 
        @command=N'EXEC dbo.usp_RunNightlyKTVReportJob;',
        @database_name=N'operatedata';
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

-- 5. 更新作业的起始步骤
EXEC @ReturnCode = msdb.dbo.sp_update_job @job_name=@jobName, @start_step_id = 1;
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

-- 6. 创建执行计划 (Schedule)
DECLARE @scheduleName NVARCHAR(128) = N'Daily at 8:00 AM';

EXEC @ReturnCode = msdb.dbo.sp_add_jobschedule @job_name=@jobName, @name=@scheduleName, 
        @enabled=1, 
        @freq_type=4, -- 每日
        @freq_interval=1, -- 每 1 天
        @freq_subday_type=1, -- 在指定时间
        @freq_subday_interval=0, 
        @freq_relative_interval=0, 
        @freq_recurrence_factor=0, 
        @active_start_date=20250730, -- 作业开始日期 (YYYYMMDD)
        @active_end_date=99991231, 
        @active_start_time=80000, -- 早上 8:00:00 (HHMMSS)
        @active_end_time=235959;
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

-- 7. 将作业附加到服务器 (可选，但推荐)
EXEC @ReturnCode = msdb.dbo.sp_add_jobserver @job_name = @jobName, @server_name = N'(local)';
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

COMMIT TRANSACTION;
PRINT N'SQL Agent Job "' + @jobName + N'" created and scheduled successfully.';
GOTO EndSave;

QuitWithRollback:
    IF (@@TRANCOUNT > 0) ROLLBACK TRANSACTION;
    PRINT N'An error occurred. Job creation rolled back.';

EndSave:
GO
