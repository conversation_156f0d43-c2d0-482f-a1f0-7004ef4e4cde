
-- 步骤 5b: 创建优化版的“时段详情”存储过程
-- 版本 V2, 使用 IsDirectFall 标志进行优化

IF OBJECT_ID('dbo.usp_GetTimeSlotDetails_WithDirectFall_V2_Optimized', 'P') IS NOT NULL
BEGIN
    PRINT 'Dropping existing procedure [usp_GetTimeSlotDetails_WithDirectFall_V2_Optimized]...';
    DROP PROCEDURE dbo.usp_GetTimeSlotDetails_WithDirectFall_V2_Optimized;
END
GO

CREATE PROCEDURE dbo.usp_GetTimeSlotDetails_WithDirectFall_V2_Optimized
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;

    -- **【优化点】** 整个存储过程的逻辑被极大简化。
    -- 不再需要动态SQL，不再需要复杂的 TrueDropInData CTE 和 LEAD/LAG 函数来计算跨时段。
    -- 我们现在只关心在每个时段开房的批次，以及在这些批次中有多少是“直落”的。

    -- CTE 用于聚合每个时段的数据
    WITH TimeSlotAggregation AS (
        SELECT
            rt.Beg_Key, -- 按开房时段分组
            
            -- 分类计数 (逻辑不变)
            COUNT(rt.InvNo) - COUNT(CASE WHEN rt.MTPay > 0 OR rt.DZPay > 0 OR rt.AliPay > 0 OR rt.CtNo = 1 THEN 1 END) AS KPlus_Count,
            COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS Special_Count,
            COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS Meituan_Count,
            COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS Douyin_Count,
            COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS RoomFee_Count,
            COUNT(rt.InvNo) AS Subtotal_Count,
            
            -- **【优化点】** 直落计数变得非常简单
            -- 直接统计在这个时段开房的批次中，有多少已经被标记为 IsDirectFall = 1
            SUM(CASE WHEN rt.IsDirectFall = 1 THEN 1 ELSE 0 END) AS PreviousSlot_DirectFall_Optimized

        FROM dbo.RmCloseInfo AS rt
        WHERE rt.Shopid = @ShopId 
          AND rt.WorkDate = @TargetDate 
          AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.Beg_Key
    )
    -- 最终查询，关联 timeinfo 获取时段名称和顺序
    SELECT
        ti.TimeName AS TimeSlotName,
        ROW_NUMBER() OVER (ORDER BY ti.BegTime) AS TimeSlotOrder,
        agg.KPlus_Count,
        agg.Special_Count,
        agg.Meituan_Count,
        agg.Douyin_Count,
        agg.RoomFee_Count,
        agg.Subtotal_Count,
        agg.PreviousSlot_DirectFall_Optimized AS PreviousSlot_DirectFall -- 列名保持一致以兼容主表
    FROM TimeSlotAggregation agg
    JOIN dbo.timeinfo ti ON agg.Beg_Key = ti.TimeNo
    ORDER BY ti.BegTime;

END
GO

PRINT 'Optimized procedure [usp_GetTimeSlotDetails_WithDirectFall_V2_Optimized] created successfully.';
GO
