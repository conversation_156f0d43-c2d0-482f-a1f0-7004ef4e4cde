
-- 步骤 6: 创建最终的、完全优化的主调度存储过程
-- 版本 V2, 调用所有优化版的子过程

IF OBJECT_ID('dbo.usp_RunUnifiedDailyReportJob_Corrected_V2_Optimized', 'P') IS NOT NULL
BEGIN
    PRINT 'Dropping existing procedure [usp_RunUnifiedDailyReportJob_Corrected_V2_Optimized]...';
    DROP PROCEDURE dbo.usp_RunUnifiedDailyReportJob_Corrected_V2_Optimized;
END
GO

CREATE PROCEDURE dbo.usp_RunUnifiedDailyReportJob_Corrected_V2_Optimized
    @TargetDate DATE = NULL,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report_Corrected_V2_Optimized';
    DECLARE @ReportID INT;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- Step 0: 清理旧数据 (逻辑不变)
        PRINT N'Step 0: Deleting existing data for ' + CONVERT(NVARCHAR, @TargetDate) + N'...';
        SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
        IF @ReportID IS NOT NULL
        BEGIN
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;
        END

        -- Step 1: 生成表头数据 (调用V4优化版)
        PRINT N'Step 1: Generating Header data using V4_Optimized...';
        CREATE TABLE #TempHeader (
            WorkDate DATE, ShopName NVARCHAR(100), WeekdayName NVARCHAR(20),
            DayTimeRevenue DECIMAL(18,2), NightTimeRevenue DECIMAL(18,2), TotalRevenue DECIMAL(18,2),
            DayTimeBatchCount INT, NightTimeBatchCount INT, TotalBatchCount INT,
            TotalGuestCount INT, BuffetGuestCount INT,
            DayTimeDropInBatch INT, NightTimeDropInBatch INT, TotalDirectFallGuests INT
        );
        -- **【调用优化版】**
        INSERT INTO #TempHeader EXEC dbo.usp_GenerateDayTimeReport_Simple_V4_Optimized @ShopId = @ShopId, @TargetDate = @TargetDate;
        
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        ) SELECT @TargetDate, @ShopId, ShopName, WeekdayName, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests FROM #TempHeader;
        SET @ReportID = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- Step 2: 生成夜间详情 (此过程无直落逻辑，无需优化，继续使用V7)
        PRINT N'Step 2: Generating COMPLETE Night Details data (using V7_Final_Corrected)...';
        CREATE TABLE #TempNightDetails (
            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
            FreeConsumption_BatchCount int,
            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_RoomFee int, NonPackage_Others int,
            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
            DiscountFree_BatchCount INT, DiscountFree_Revenue DECIMAL(18, 2)
        );
        -- **【无需优化，保持原样】**
        INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Corrected @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;
        
        INSERT INTO dbo.FullDailyReport_NightDetails (
            ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_RoomFee, NonPackage_Others,
            Night_Verify_BatchCount,Night_Verify_Revenue, DiscountFree_BatchCount, DiscountFree_Revenue
        ) SELECT 
            @ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin,NonPackage_RoomFee, NonPackage_Others,
            Night_Verify_BatchCount,Night_Verify_Revenue, DiscountFree_BatchCount, DiscountFree_Revenue
        FROM #TempNightDetails;
        PRINT N'Complete Night Details data inserted.';

        -- Step 3: 生成时段详情 (调用V2优化版)
        PRINT N'Step 3: Generating Time Slot Details data using V2_Optimized...';
        CREATE TABLE #TempTimeSlotDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        -- **【调用优化版】**
        INSERT INTO #TempTimeSlotDetails EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall_V2_Optimized @TargetDate = @TargetDate, @ShopId = @ShopId;
        
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        ) SELECT @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall FROM #TempTimeSlotDetails;
        PRINT N'Time Slot Details data inserted.';

        -- 清理临时表
        DROP TABLE #TempHeader;
        DROP TABLE #TempNightDetails;
        DROP TABLE #TempTimeSlotDetails;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: OPTIMIZED complete unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempNightDetails') IS NOT NULL DROP TABLE #TempNightDetails;
        IF OBJECT_ID('tempdb..#TempTimeSlotDetails') IS NOT NULL DROP TABLE #TempTimeSlotDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing OPTIMIZED report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT 'Final optimized master procedure [usp_RunUnifiedDailyReportJob_Corrected_V2_Optimized] created successfully.';
GO
