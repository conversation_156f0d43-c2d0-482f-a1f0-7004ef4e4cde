
-- 步骤 8 (最终修正版): 端到端测试脚本
-- 修正了调用 usp_UpdateDirectFallFlag_ByName 时的日期格式问题

USE operatedata;
GO

PRINT N'=====================================================================';
PRINT N'=           STARTING END-TO-END TEST SCRIPT (FINAL FIX)           =';
PRINT N'= Target Shop: 11                                               =';
PRINT N'= Target Date: 20250724                                         =';
PRINT N'=====================================================================';
GO

DECLARE @TestShopId INT = 11;
DECLARE @TestDateAsDate DATE = '2025-07-24'; -- 用于调用需要 DATE 类型的存储过程
DECLARE @TestDateAsVarchar VARCHAR(8) = '20250724'; -- 用于调用需要 VARCHAR 类型的存储过程

-- **测试前准备**
PRINT N'
--- PRE-TEST CLEANUP ---';
DECLARE @ReportID_Pre INT;
SELECT @ReportID_Pre = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TestDateAsDate AND ShopID = @TestShopId;
IF @ReportID_Pre IS NOT NULL
BEGIN
    PRINT N'Found existing report data for the target date. Deleting it now...';
    DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID_Pre;
    DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID_Pre;
    DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID_Pre;
    PRINT N'Cleanup complete.';
END
ELSE
BEGIN
    PRINT N'No existing report data found for the target date. Good to go.';
END
GO

-- **测试步骤 1: 执行“直落”标签更新程序 (使用正确的日期格式)**
PRINT N'
--- TEST STEP 1 of 2: UPDATING DIRECT FALL FLAGS (with correct date format) ---';
-- **【修正点】** 传递 '20250724' 而不是 '2025-07-24'
EXEC dbo.usp_UpdateDirectFallFlag_ByName @TargetDate = @TestDateAsVarchar, @ShopId = @TestShopId;
PRINT N'--- FINISHED STEP 1 ---';
GO

-- **测试步骤 2: 执行最终的、完全优化的主调度程序**
PRINT N'
--- TEST STEP 2 of 2: RUNNING THE FINAL OPTIMIZED MASTER PROCEDURE ---';
EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected_V2_Optimized @TargetDate = @TestDateAsDate, @ShopId = @TestShopId;
PRINT N'--- FINISHED STEP 2 ---';
GO

-- **测试结果验证**
PRINT N'
=====================================================================';
PRINT N'=                     TEST VERIFICATION RESULTS                     =';
PRINT N'=====================================================================';
GO

DECLARE @ReportID_Post INT;
SELECT @ReportID_Post = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TestDateAsDate AND ShopID = @TestShopId;

IF @ReportID_Post IS NOT NULL
BEGIN
    PRINT N'SUCCESS: Report generated. ReportID is ' + CAST(@ReportID_Post AS NVARCHAR(10));
    
    PRINT N'
--- Final Result: FullDailyReport_Header ---';
    SELECT * FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID_Post;
END
ELSE
BEGIN
    PRINT N'FAILURE: No report data was generated. Please check the logs above for errors.';
END

PRINT N'
=====================================================================';
PRINT N'=                   END OF TEST SCRIPT                          =';
PRINT N'=====================================================================';
GO
