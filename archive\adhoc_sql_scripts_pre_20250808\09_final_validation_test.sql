
-- 步骤 9: 最终的、完全自包含的验证脚本
-- 移除了所有GO语句，确保变量在整个脚本生命周期内都有效

USE operatedata;

BEGIN
    -- 1. 声明所有需要的变量
    PRINT N'=====================================================================';
    PRINT N'=           STARTING FINAL VALIDATION SCRIPT (SELF-CONTAINED)       =';
    PRINT N'=====================================================================';

    DECLARE @TestShopId INT = 11;
    DECLARE @TestDateAsDate DATE = '2025-07-24';
    DECLARE @TestDateAsVarchar VARCHAR(8) = '20250724';
    DECLARE @ReportID INT;

    -- 2. 清理旧数据
    PRINT N'
--- Step 1: PRE-TEST CLEANUP ---';
    SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TestDateAsDate AND ShopID = @TestShopId;
    IF @ReportID IS NOT NULL
    BEGIN
        PRINT N'Found existing report data (ReportID: ' + CAST(@ReportID AS NVARCHAR(10)) + N'). Deleting it now...';
        DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID;
        DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;
        DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;
        PRINT N'Cleanup complete.';
    END
    ELSE
    BEGIN
        PRINT N'No existing report data found for the target date. Good to go.';
    END

    -- 3. 执行“直落”标签更新程序
    PRINT N'
--- Step 2: UPDATING DIRECT FALL FLAGS ---';
    EXEC dbo.usp_UpdateDirectFallFlag_ByName @TargetDate = @TestDateAsVarchar, @ShopId = @TestShopId;
    PRINT N'--- FINISHED STEP 2 ---';

    -- 4. 执行最终的、完全优化的主调度程序
    PRINT N'
--- Step 3: RUNNING THE FINAL OPTIMIZED MASTER PROCEDURE ---';
    EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected_V2_Optimized @TargetDate = @TestDateAsDate, @ShopId = @TestShopId;
    PRINT N'--- FINISHED STEP 3 ---';

    -- 5. 验证结果
    PRINT N'
=====================================================================';
    PRINT N'=                     FINAL VERIFICATION RESULTS                    =';
    PRINT N'=====================================================================';
    
    -- 重置变量并重新获取新的ReportID
    SET @ReportID = NULL;
    SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TestDateAsDate AND ShopID = @TestShopId;

    IF @ReportID IS NOT NULL
    BEGIN
        PRINT N'SUCCESS: Report generated. New ReportID is ' + CAST(@ReportID AS NVARCHAR(10));
        
        PRINT N'
--- Final Result: FullDailyReport_Header ---';
        SELECT * FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;

        PRINT N'
--- Final Result: FullDailyReport_NightDetails ---';
        SELECT * FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;

        PRINT N'
--- Final Result: FullDailyReport_TimeSlotDetails ---';
        SELECT * FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID ORDER BY TimeSlotOrder;
    END
    ELSE
    BEGIN
        PRINT N'FAILURE: No report data was generated. Please check the logs above for errors.';
    END

    PRINT N'
=====================================================================';
    PRINT N'=                   END OF VALIDATION SCRIPT                      =';
    PRINT N'=====================================================================';
END;
