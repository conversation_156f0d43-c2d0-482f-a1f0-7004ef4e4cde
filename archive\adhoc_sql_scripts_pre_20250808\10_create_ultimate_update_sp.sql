
-- 步骤 10: 创建“双日期格式”的终极版更新存储过程
-- 它会同时尝试用 DATE 类型和 VARCHAR 类型去匹配 WorkDate

IF OBJECT_ID('dbo.usp_UpdateDirectFallFlag_ByName_Ultimate', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_UpdateDirectFallFlag_ByName_Ultimate;
END
GO

CREATE PROCEDURE dbo.usp_UpdateDirectFallFlag_ByName_Ultimate
    @TargetDateAsDate DATE,
    @TargetDateAsVarchar VARCHAR(8),
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;

    PRINT N'Starting ULTIMATE direct fall flag update for ShopID: ' + CAST(@ShopId AS NVARCHAR(10));

    -- 先重置
    UPDATE dbo.RmCloseInfo
    SET IsDirectFall = 0
    WHERE (WorkDate = @TargetDateAsVarchar OR CAST(WorkDate AS DATE) = @TargetDateAsDate) AND ShopId = @ShopId;

    -- **【终极修正】**
    -- 使用 OR 来同时匹配两种可能的日期格式，解决WorkDate的类型不确定性问题
    UPDATE rci
    SET rci.IsDirectFall = 1
    FROM dbo.RmCloseInfo AS rci
    WHERE 
        (rci.WorkDate = @TargetDateAsVarchar OR CAST(rci.WorkDate AS DATE) = @TargetDateAsDate)
        AND rci.ShopId = @ShopId
        AND EXISTS (
            SELECT 1
            FROM operatedata.dbo.FdCashBak fcb
            WHERE fcb.InvNo = rci.InvNo COLLATE Chinese_PRC_CI_AS
              AND fcb.ShopId = @ShopId
              AND fcb.FdCName LIKE N'%直落%'
        );

    DECLARE @UpdateCount INT = @@ROWCOUNT;
    PRINT N'>>> ULTIMATE UPDATE: Flagged ' + CAST(@UpdateCount AS NVARCHAR(10)) + N' records as direct fall. <<<';

END
GO
