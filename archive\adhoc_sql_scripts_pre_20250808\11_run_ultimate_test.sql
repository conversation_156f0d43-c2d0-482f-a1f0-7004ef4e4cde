
-- 步骤 11: 执行终极版测试脚本

USE operatedata;

BEGIN
    DECLARE @TestShopId INT = 11;
    DECLARE @TestDateAsDate DATE = '2025-07-24';
    DECLARE @TestDateAsVarchar VARCHAR(8) = '20250724';

    PRINT N'--- RUNNING ULTIMATE TEST ---';
    
    -- 调用新的“双日期格式”更新程序
    EXEC dbo.usp_UpdateDirectFallFlag_ByName_Ultimate 
        @TargetDateAsDate = @TestDateAsDate, 
        @TargetDateAsVarchar = @TestDateAsVarchar, 
        @ShopId = @TestShopId;

    -- 再次运行报表生成
    EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected_V2_Optimized 
        @TargetDate = @TestDateAsDate, 
        @ShopId = @TestShopId;

    PRINT N'--- CHECKING FINAL RESULTS ---';
    SELECT * FROM dbo.FullDailyReport_Header WHERE ReportDate = @TestDateAsDate AND ShopID = @TestShopId;
END;
