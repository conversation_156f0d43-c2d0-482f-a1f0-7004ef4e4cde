
-- 步骤 12: 创建最终版的“日间报表”存储过程
-- 版本 V5, 采纳用户建议，结合 IsDirectFall 和 RevenueClassificationMode 进行精确分类

IF OBJECT_ID('dbo.usp_GenerateDayTimeReport_Simple_V5_Final', 'P') IS NOT NULL
BEGIN
    PRINT 'Dropping existing procedure [usp_GenerateDayTimeReport_Simple_V5_Final]...';
    DROP PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V5_Final;
END
GO

CREATE PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V5_Final
    @ShopId int,
    @TargetDate date
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @BeginDate datetime = DATEADD(hour, 8, CAST(@TargetDate AS datetime));
    DECLARE @EndDate datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @TargetDate) AS datetime));

    -- CTE: 逻辑与之前版本保持一致，用于区分日夜场收入和批次
    WITH RecordsWithTimeMode AS (
        SELECT
            rt.*,
            sti.TimeMode,
            (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
            CASE
                WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                ELSE 2
            END AS RevenueClassificationMode
        FROM dbo.RmCloseInfo AS rt
        LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
        WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @BeginDate AND @EndDate
    )
    -- 最终聚合
    SELECT
        @TargetDate AS WorkDate,
        (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
        DATENAME(weekday, @TargetDate) AS WeekdayName,
        
        -- 收入和批次统计 (逻辑不变)
        ISNULL(SUM(CASE WHEN rt.RevenueClassificationMode = 1 THEN rt.Revenue ELSE 0 END), 0) AS DayTimeRevenue,
        ISNULL(SUM(CASE WHEN rt.RevenueClassificationMode = 2 THEN rt.Revenue ELSE 0 END), 0) AS NightTimeRevenue,
        ISNULL(SUM(rt.Revenue), 0) AS TotalRevenue,
        COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
        COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
        (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,

        -- 客人统计 (逻辑不变)
        SUM(rt.Numbers) AS TotalGuestCount,
        SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,

        -- **【最终修正逻辑】**
        -- 根据用户建议，结合 IsDirectFall 和 RevenueClassificationMode 进行精确统计
        ISNULL(SUM(CASE WHEN rt.IsDirectFall = 1 AND rt.RevenueClassificationMode = 1 THEN 1 ELSE 0 END), 0) AS DayTimeDropInBatch,
        ISNULL(SUM(CASE WHEN rt.IsDirectFall = 1 AND rt.RevenueClassificationMode = 2 THEN 1 ELSE 0 END), 0) AS NightTimeDropInBatch,
        ISNULL(SUM(CASE WHEN rt.IsDirectFall = 1 THEN rt.Numbers ELSE 0 END), 0) AS TotalDirectFallGuests

    FROM RecordsWithTimeMode rt
    GROUP BY rt.ShopId;

END
GO

PRINT 'Final procedure [usp_GenerateDayTimeReport_Simple_V5_Final] created successfully.';
GO
