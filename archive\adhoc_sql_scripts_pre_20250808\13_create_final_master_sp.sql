
-- 步骤 13: 创建最终版的“主调度”存储过程
-- 版本 V3, 调用V5的表头程序，并恢复使用原始的时段详情程序

IF OBJECT_ID('dbo.usp_RunUnifiedDailyReport_V3_Final', 'P') IS NOT NULL
BEGIN
    PRINT 'Dropping existing procedure [usp_RunUnifiedDailyReport_V3_Final]...';
    DROP PROCEDURE dbo.usp_RunUnifiedDailyReport_V3_Final;
END
GO

CREATE PROCEDURE dbo.usp_RunUnifiedDailyReport_V3_Final
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report_V3_Final';
    DECLARE @ReportID INT;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- Step 0: 清理旧数据
        PRINT N'Step 0: Deleting existing data...';
        SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
        IF @ReportID IS NOT NULL
        BEGIN
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;
        END

        -- Step 1: 生成表头数据 (调用V5最终版)
        PRINT N'Step 1: Generating Header data using V5_Final...';
        CREATE TABLE #TempHeader (
            WorkDate DATE, ShopName NVARCHAR(100), WeekdayName NVARCHAR(20),
            DayTimeRevenue DECIMAL(18,2), NightTimeRevenue DECIMAL(18,2), TotalRevenue DECIMAL(18,2),
            DayTimeBatchCount INT, NightTimeBatchCount INT, TotalBatchCount INT,
            TotalGuestCount INT, BuffetGuestCount INT,
            DayTimeDropInBatch INT, NightTimeDropInBatch INT, TotalDirectFallGuests INT
        );
        INSERT INTO #TempHeader EXEC dbo.usp_GenerateDayTimeReport_Simple_V5_Final @ShopId = @ShopId, @TargetDate = @TargetDate;
        
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        ) SELECT @TargetDate, @ShopId, ShopName, WeekdayName, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests FROM #TempHeader;
        SET @ReportID = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- Step 2: 生成夜间详情 (继续使用V7)
        PRINT N'Step 2: Generating Night Details data using V7_Final_Corrected...';
        CREATE TABLE #TempNightDetails (
            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
            FreeConsumption_BatchCount int,
            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_RoomFee int, NonPackage_Others int,
            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
            DiscountFree_BatchCount INT, DiscountFree_Revenue DECIMAL(18, 2)
        );
        INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Corrected @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;
        
        INSERT INTO dbo.FullDailyReport_NightDetails (
            ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_RoomFee, NonPackage_Others,
            Night_Verify_BatchCount,Night_Verify_Revenue, DiscountFree_BatchCount, DiscountFree_Revenue
        ) SELECT 
            @ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin,NonPackage_RoomFee, NonPackage_Others,
            Night_Verify_BatchCount,Night_Verify_Revenue, DiscountFree_BatchCount, DiscountFree_Revenue
        FROM #TempNightDetails;
        PRINT N'Night Details data inserted.';

        -- Step 3: 生成时段详情 (恢复使用原始的、正确的存储过程)
        PRINT N'Step 3: Generating Time Slot Details data using the ORIGINAL procedure...';
        CREATE TABLE #TempTimeSlotDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        -- **【恢复使用原始逻辑】**
        INSERT INTO #TempTimeSlotDetails EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;
        
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        ) SELECT @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall FROM #TempTimeSlotDetails;
        PRINT N'Time Slot Details data inserted.';

        -- 清理临时表
        DROP TABLE #TempHeader;
        DROP TABLE #TempNightDetails;
        DROP TABLE #TempTimeSlotDetails;

        COMMIT TRANSACTION;

        PRINT N'Success: FINAL unified report processed successfully.';

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
        -- ... (Error handling logic remains the same)
        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT 'Final master procedure [usp_RunUnifiedDailyReport_V3_Final] created successfully.';
GO
