
-- 步骤 14: 最终的、决定性的测试脚本

USE operatedata;

BEGIN
    DECLARE @TestShopId INT = 11;
    DECLARE @TestDateAsDate DATE = '2025-07-24';
    DECLARE @TestDateAsVarchar VARCHAR(8) = '20250724';

    PRINT N'--- RUNNING FINAL TEST ---';
    
    -- 步骤 1: 预先更新“直落”标志 (使用我们修正过的程序)
    PRINT N'Step 1: Updating direct fall flags...';
    EXEC dbo.usp_UpdateDirectFallFlag_ByName @TargetDate = @TestDateAsVarchar, @ShopId = @TestShopId;

    -- 步骤 2: 运行最终的主调度程序
    PRINT N'Step 2: Running the final master procedure...';
    EXEC dbo.usp_RunUnifiedDailyReport_V3_Final @TargetDate = @TestDateAsDate, @ShopId = @TestShopId;

    PRINT N'--- CHECKING FINAL RESULTS ---';
    PRINT N'
--- Header ---';
    SELECT * FROM dbo.FullDailyReport_Header WHERE ReportDate = @TestDateAsDate AND ShopID = @TestShopId;
    PRINT N'
--- TimeSlot Details ---';
    SELECT * FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = (SELECT ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TestDateAsDate AND ShopID = @TestShopId) ORDER BY TimeSlotOrder;

END;
