
-- 步骤 15: 创建最终的、供代理作业调用的总调度存储过程

IF OBJECT_ID('dbo.usp_RunNightlyKTVReportJob_Final', 'P') IS NOT NULL
BEGIN
    PRINT 'Dropping existing procedure [usp_RunNightlyKTVReportJob_Final]...';
    DROP PROCEDURE dbo.usp_RunNightlyKTVReportJob_Final;
END
GO

CREATE PROCEDURE dbo.usp_RunNightlyKTVReportJob_Final
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ShopList TABLE (ShopId INT PRIMARY KEY);
    INSERT INTO @ShopList (ShopId) VALUES (2), (3), (4), (5), (6), (8), (9), (10), (11);

    -- 日期定义
    DECLARE @TargetDateAsDate DATE = CAST(DATEADD(day, -1, GETDATE()) AS DATE);
    DECLARE @TargetDateAsVarchar VARCHAR(8) = CONVERT(VARCHAR(8), @TargetDateAsDate, 112); -- YYYYMMDD format
    
    DECLARE @CurrentShopId INT;
    DECLARE @JobLogMessage NVARCHAR(1000);

    PRINT N'--- Starting FINAL Nightly KTV Report Job for Date: ' + CONVERT(NVARCHAR, @TargetDateAsDate, 120) + N' ---';

    DECLARE ShopCursor CURSOR FOR SELECT ShopId FROM @ShopList ORDER BY ShopId;
    OPEN ShopCursor;
    FETCH NEXT FROM ShopCursor INTO @CurrentShopId;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        BEGIN TRY
            PRINT N'
Processing ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            PRINT N'------------------------';

            -- 步骤 1: 调用“直落”标签更新程序 (我们修正过的版本)
            PRINT N'Step 1: Updating direct fall flags...';
            EXEC dbo.usp_UpdateDirectFallFlag_ByName @TargetDate = @TargetDateAsVarchar, @ShopId = @CurrentShopId;
            PRINT N'Step 1: Direct fall flags updated successfully.';

            -- 步骤 2: 调用最终的、正确的统一日报表生成程序
            PRINT N'Step 2: Generating the final unified daily report (V3_Final)...';
            EXEC dbo.usp_RunUnifiedDailyReport_V3_Final @TargetDate = @TargetDateAsDate, @ShopId = @CurrentShopId;
            PRINT N'Step 2: Final unified daily report generated successfully.';

            SET @JobLogMessage = N'Successfully processed ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            PRINT @JobLogMessage;

        END TRY
        BEGIN CATCH
            DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
            SET @JobLogMessage = N'FAILED to process ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10)) + N'. Error: ' + @ErrorMessage;
            RAISERROR(@JobLogMessage, 10, 1) WITH NOWAIT;
        END CATCH

        FETCH NEXT FROM ShopCursor INTO @CurrentShopId;
    END

    CLOSE ShopCursor;
    DEALLOCATE ShopCursor;

    PRINT N'
--- FINAL Nightly KTV Report Job finished. ---';

END
GO

PRINT 'Final nightly job procedure [usp_RunNightlyKTVReportJob_Final] created successfully.';
GO
