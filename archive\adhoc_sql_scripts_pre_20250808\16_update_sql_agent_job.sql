
-- 步骤 16: 更新现有的 SQL Server 代理作业，使其调用最终的调度程序

USE msdb;
GO

DECLARE @jobName NVARCHAR(128) = N'KTV - Nightly Unified Report Generation';
DECLARE @jobId BINARY(16);

-- 查找作业ID
SELECT @jobId = job_id FROM msdb.dbo.sysjobs WHERE name = @jobName;

IF (@jobId IS NOT NULL)
BEGIN
    PRINT N'Updating job step for job: "' + @jobName + N'"...';

    -- 使用 sp_update_jobstep 来更新作业步骤的命令
    -- 我们只更新命令内容，其他设置保持不变
    EXEC msdb.dbo.sp_update_jobstep 
        @job_id = @jobId,
        @step_id = 1, -- 作业的第一个（也是唯一一个）步骤
        @command = N'EXEC dbo.usp_RunNightlyKTVReportJob_Final;'; -- 将命令更新为我们最终的调度程序

    PRINT N'Job step updated successfully.';
    PRINT N'The job will now execute the final, correct logic every day at 8:00 AM.';
END
ELSE
BEGIN
    PRINT N'Error: Job "' + @jobName + N'" not found. Please create it first.';
END
GO
