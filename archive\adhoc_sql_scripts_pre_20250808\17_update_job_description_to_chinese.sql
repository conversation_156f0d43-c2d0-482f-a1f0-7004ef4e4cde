
-- 步骤 17: 将代理作业的描述更新为纯中文

USE msdb; -- 必须在 msdb 数据库下执行
GO

-- 检查作业是否存在，然后更新其 @description 字段
IF EXISTS (SELECT 1 FROM msdb.dbo.sysjobs WHERE name = N'KTV - Nightly Unified Report Generation')
BEGIN
    EXEC dbo.sp_update_job
        @job_name = N'KTV - Nightly Unified Report Generation',
        @description = N'此作业每日自动执行KTV统一日报表的生成流程。它会为所有已配置的门店，按顺序完成两个核心步骤：第一，更新结账单的‘直落’业务标志；第二，调用最终版的报表存储过程，生成完整的三张核心报表（表头、夜间详情、时段详情）。';
    
    PRINT N'作业 "KTV - Nightly Unified Report Generation" 的描述已成功更新为中文。';
END
ELSE
BEGIN
    PRINT N'错误：未找到名为 "KTV - Nightly Unified Report Generation" 的作业。';
END
GO
