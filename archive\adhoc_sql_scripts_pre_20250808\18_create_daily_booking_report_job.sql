
-- =================================================================================================
-- 脚本: 18_create_daily_booking_report_job.sql
-- 作者: Gemini AI
-- 日期: 2025-08-04
-- 描述: 创建一个新的调度存储过程，并为其设置一个每日执行的SQL Server Agent作业，
--       用于为所有门店生成“预订及代订客到”日报表。
-- =================================================================================================

-- =================================================================================================
-- 步骤 1: 创建一个新的“调度器”存储过程，用于循环调用日报表过程
--         (仿照 usp_RunNightlyKTVReportJob_Final 的逻辑)
-- =================================================================================================

USE operatedata;
GO

IF OBJECT_ID('dbo.usp_RunDailyBookingReportForAllShops', 'P') IS NOT NULL
BEGIN
    PRINT '存储过程 [usp_RunDailyBookingReportForAllShops] 已存在，正在删除...';
    DROP PROCEDURE dbo.usp_RunDailyBookingReportForAllShops;
END
GO

PRINT '正在创建存储过程 [usp_RunDailyBookingReportForAllShops]...';
GO

CREATE PROCEDURE dbo.usp_RunDailyBookingReportForAllShops
AS
BEGIN
    SET NOCOUNT ON;

    -- 为了与现有夜间作业保持一致，我们在这里硬编码门店列表
    -- 如果未来需要更动态的方式，可以修改为从 dbo.shopinfo 读取
    DECLARE @ShopList TABLE (ShopId INT PRIMARY KEY);
    INSERT INTO @ShopList (ShopId) VALUES (2), (3), (4), (5), (6), (8), (9), (10), (11);

    -- 定义目标日期为昨天，以确保数据完整性
    DECLARE @TargetDate DATE = DATEADD(day, -1, GETDATE());
    
    DECLARE @CurrentShopId INT;
    DECLARE @LogMessage NVARCHAR(1000);

    PRINT N'--- 开始执行“预订及代订客到”日报表生成任务，日期: ' + CONVERT(NVARCHAR, @TargetDate, 120) + N' ---';

    DECLARE ShopCursor CURSOR FOR SELECT ShopId FROM @ShopList ORDER BY ShopId;
    OPEN ShopCursor;
    FETCH NEXT FROM ShopCursor INTO @CurrentShopId;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        BEGIN TRY
            PRINT N'
正在处理门店ID: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            PRINT N'------------------------';

            -- 调用核心的动态日报表生成程序
            EXEC dbo.usp_GenerateDynamicDailyReport @TargetDate = @TargetDate, @ShopID = @CurrentShopId;

            SET @LogMessage = N'成功处理门店ID: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            PRINT @LogMessage;

        END TRY
        BEGIN CATCH
            DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
            SET @LogMessage = N'处理门店ID失败: ' + CAST(@CurrentShopId AS NVARCHAR(10)) + N'. 错误: ' + @ErrorMessage;
            -- 使用 RAISERROR 将错误信息打印到日志中，但不足以使整个作业失败
            RAISERROR(@LogMessage, 10, 1) WITH NOWAIT;
        END CATCH

        FETCH NEXT FROM ShopCursor INTO @CurrentShopId;
    END

    CLOSE ShopCursor;
    DEALLOCATE ShopCursor;

    PRINT N'
--- “预订及代订客到”日报表生成任务已全部完成. ---';

END
GO

PRINT '存储过程 [usp_RunDailyBookingReportForAllShops] 已成功创建。';
GO

-- =================================================================================================
-- 步骤 2: 创建一个新的SQL Server Agent作业，用于定时执行上述“调度器”过程
-- =================================================================================================

USE msdb;
GO

DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'KTV - Daily Booking and Arrival Report';

-- 首先检查作业是否已存在，如果存在则删除，确保脚本可以重复执行
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    PRINT '作业 "' + @jobName + '" 已存在，正在删除...';
    EXEC dbo.sp_delete_job @job_id = @jobId, @delete_history = 1;
END

PRINT '正在创建作业 "' + @jobName + '"...';

-- 添加作业
EXEC dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @notify_level_eventlog = 2, -- 失败时写入事件日志
    @description = N'[Gemini自动创建] 每日定时执行，为所有有效门店生成“预订及代订客到”日报表。',
    @category_name = N'[Uncategorized (Local)]',
    @owner_login_name = N'sa', -- 请根据您的环境确认此登录名
    @job_id = @jobId OUTPUT;

-- 添加作业步骤
EXEC dbo.sp_add_jobstep
    @job_id = @jobId,
    @step_name = N'Execute Daily Booking Report For All Shops',
    @step_id = 1,
    @cmdexec_success_code = 0,
    @on_success_action = 1, -- 成功后转到下一步 (或退出)
    @on_fail_action = 2,    -- 失败时退出作业
    @retry_attempts = 0,
    @subsystem = N'TSQL',
    @command = N'USE operatedata; EXEC dbo.usp_RunDailyBookingReportForAllShops;',
    @database_name = N'operatedata';

-- 更新作业的起始步骤
EXEC dbo.sp_update_job @job_id = @jobId, @start_step_id = 1;

-- 创建一个每日执行的调度
EXEC dbo.sp_add_jobschedule
    @job_id = @jobId,
    @name = N'Daily Report Schedule (Early Morning)',
    @enabled = 1,
    @freq_type = 4, -- 每天
    @freq_interval = 1, -- 每1天发生一次
    @freq_subday_type = 1, -- 在指定的时间
    @active_start_time = 80000; -- 早上 08:00:00

PRINT '作业 "' + @jobName + '" 已成功创建并调度在每日凌晨2点执行。';
GO
