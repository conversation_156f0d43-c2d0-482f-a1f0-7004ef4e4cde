
-- =================================================================================================
-- 脚本: 19_manual_backfill_report_data.sql
-- 作者: Gemini AI
-- 日期: 2025-08-04
-- 描述: 手动回填指定日期范围内的日报表数据。
-- =================================================================================================

USE operatedata;
GO

SET NOCOUNT ON;

-- 1. 定义需要回填的日期列表
DECLARE @DateList TABLE (TargetDate DATE PRIMARY KEY);
INSERT INTO @DateList (TargetDate) VALUES
('2025-08-01'),
('2025-08-02'),
('2025-08-03');

-- 2. 定义需要执行的门店列表 (与定时任务保持一致)
DECLARE @ShopList TABLE (ShopId INT PRIMARY KEY);
INSERT INTO @ShopList (ShopId) VALUES (2), (3), (4), (5), (6), (8), (9), (10), (11);

-- 3. 声明循环变量
DECLARE @CurrentDate DATE;
DECLARE @CurrentShopId INT;
DECLARE @LogMessage NVARCHAR(1000);

PRINT N'--- 开始手动回填日报表数据 ---';

-- 4. 外层循环: 遍历日期
DECLARE DateCursor CURSOR FOR SELECT TargetDate FROM @DateList ORDER BY TargetDate;
OPEN DateCursor;
FETCH NEXT FROM DateCursor INTO @CurrentDate;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT N'
=========================================================';
    PRINT N'开始处理日期: ' + CONVERT(NVARCHAR, @CurrentDate, 120);
    PRINT N'=========================================================';

    -- 内层循环: 遍历门店
    DECLARE ShopCursor CURSOR FOR SELECT ShopId FROM @ShopList ORDER BY ShopId;
    OPEN ShopCursor;
    FETCH NEXT FROM ShopCursor INTO @CurrentShopId;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        BEGIN TRY
            SET @LogMessage = N'正在为门店ID: ' + CAST(@CurrentShopId AS NVARCHAR(10)) + N' 生成 ' + CONVERT(NVARCHAR, @CurrentDate, 23) + N' 的报表...';
            PRINT @LogMessage;

            -- 调用核心存储过程
            EXEC dbo.usp_GenerateDynamicDailyReport @TargetDate = @CurrentDate, @ShopID = @CurrentShopId;

            PRINT N'  -> 成功。';

        END TRY
        BEGIN CATCH
            DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
            SET @LogMessage = N'  -> 失败! 错误: ' + @ErrorMessage;
            RAISERROR(@LogMessage, 10, 1) WITH NOWAIT;
        END CATCH

        FETCH NEXT FROM ShopCursor INTO @CurrentShopId;
    END

    CLOSE ShopCursor;
    DEALLOCATE ShopCursor;

    FETCH NEXT FROM DateCursor INTO @CurrentDate;
END

CLOSE DateCursor;
DEALLOCATE DateCursor;

PRINT N'
--- 手动回填数据任务完成. ---';
GO
