
-- =================================================================================================
-- 脚本: 20_compare_guest_count_methods.sql
-- 作者: Gemini AI
-- 日期: 2025-08-04
-- 描述: 对比两种不同的“待客人数”计算方法：
--       1. 报表法 (Fuzzy): 基于模糊查询 FdCashBak 中的“消费人数”项，结果已在报表中。
--       2. 直接法 (Direct): 直接使用 opencacheinfo/openhistory 中的 Numbers 字段。
--       以门店ID 11 在指定日期范围内的数据为例。
-- =================================================================================================

USE operatedata;
GO

SET NOCOUNT ON;

-- 定义要分析的门店和日期范围
DECLARE @ShopID INT = 11;
DECLARE @StartDate DATE = '2025-08-01';
DECLARE @EndDate DATE = '2025-08-03';

PRINT N'正在为门店ID ' + CAST(@ShopID AS NVARCHAR) + N' 对比 ' + CONVERT(NVARCHAR, @StartDate) + N' 至 ' + CONVERT(NVARCHAR, @EndDate) + N' 的待客人数...';

WITH
-- CTE 1: 从已生成的报表中获取“报表法”的待客人数
ReportedGuests AS (
    SELECT
        h.ReportDate,
        d.TimeSlotName,
        d.OccupiedGuests AS Guests_From_Report_Fuzzy
    FROM dbo.DynamicReport_Header h
    JOIN dbo.DynamicReport_TimeSlotDetails d ON h.ReportID = d.HeaderReportID
    WHERE h.ShopID = @ShopID
      AND h.ReportDate BETWEEN @StartDate AND @EndDate
),

-- CTE 2: 从远程数据库直接获取“直接法”的待客人数
-- 注意: OPENQUERY 不支持变量，因此我们为每个日期分别查询然后 UNION ALL
DirectGuests AS (
    -- 2025-08-01
    SELECT
        CAST('2025-08-01' AS DATE) AS ReportDate,
        Beg_Key,
        SUM(ISNULL(Numbers, 0)) AS Guests_From_Numbers_Direct
    FROM (
        SELECT Beg_Key, Numbers FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, Numbers FROM rms2019.dbo.opencacheinfo WHERE ComeDate = ''20250801'' AND ShopID = 11 AND Beg_Key = End_Key')
        UNION ALL
        SELECT Beg_Key, Numbers FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, Numbers FROM rms2019.dbo.openhistory WHERE ComeDate = ''20250801'' AND ShopID = 11 AND Beg_Key = End_Key')
    ) AS CombinedOccupancy
    GROUP BY Beg_Key

    UNION ALL

    -- 2025-08-02
    SELECT
        CAST('2025-08-02' AS DATE) AS ReportDate,
        Beg_Key,
        SUM(ISNULL(Numbers, 0))
    FROM (
        SELECT Beg_Key, Numbers FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, Numbers FROM rms2019.dbo.opencacheinfo WHERE ComeDate = ''20250802'' AND ShopID = 11 AND Beg_Key = End_Key')
        UNION ALL
        SELECT Beg_Key, Numbers FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, Numbers FROM rms2019.dbo.openhistory WHERE ComeDate = ''20250802'' AND ShopID = 11 AND Beg_Key = End_Key')
    ) AS CombinedOccupancy
    GROUP BY Beg_Key

    UNION ALL

    -- 2025-08-03
    SELECT
        CAST('2025-08-03' AS DATE) AS ReportDate,
        Beg_Key,
        SUM(ISNULL(Numbers, 0))
    FROM (
        SELECT Beg_Key, Numbers FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, Numbers FROM rms2019.dbo.opencacheinfo WHERE ComeDate = ''20250803'' AND ShopID = 11 AND Beg_Key = End_Key')
        UNION ALL
        SELECT Beg_Key, Numbers FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, Numbers FROM rms2019.dbo.openhistory WHERE ComeDate = ''20250803'' AND ShopID = 11 AND Beg_Key = End_Key')
    ) AS CombinedOccupancy
    GROUP BY Beg_Key
)

-- 最终查询: 将两种方法的结果连接在一起进行对比
SELECT
    ISNULL(r.ReportDate, d.ReportDate) AS ReportDate,
    t.TimeName,
    ISNULL(r.Guests_From_Report_Fuzzy, 0) AS Guests_From_Report_Fuzzy,
    ISNULL(d.Guests_From_Numbers_Direct, 0) AS Guests_From_Numbers_Direct,
    (ISNULL(d.Guests_From_Numbers_Direct, 0) - ISNULL(r.Guests_From_Report_Fuzzy, 0)) AS Difference
FROM dbo.timeinfo t
-- 确保我们只选择该门店有效的时间段
JOIN dbo.shoptimeinfo st ON t.timeno = st.timeno AND st.shopid = @ShopID
-- 使用 FULL OUTER JOIN 来确保两种方法中任何一方有数据都能显示出来
FULL OUTER JOIN ReportedGuests r ON t.TimeName = r.TimeSlotName
FULL OUTER JOIN DirectGuests d ON t.timeno = d.Beg_Key AND r.ReportDate = d.ReportDate
WHERE
    -- 只显示至少有一种方法统计到人数的时间段，避免全是0的行
    ISNULL(r.Guests_From_Report_Fuzzy, 0) <> 0 OR ISNULL(d.Guests_From_Numbers_Direct, 0) <> 0
ORDER BY
    ReportDate, t.timeno;
GO
