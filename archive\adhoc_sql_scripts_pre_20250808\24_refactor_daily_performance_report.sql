
-- =================================================================================================
-- 脚本: 24_refactor_daily_performance_report.sql
-- 作者: Gemini AI
-- 日期: 2025-08-04
-- 描述: 对“每日业绩报表”相关的存储过程进行重命名重构，以提高可读性和可维护性。
--       此脚本为幂等设计，可以安全地重复执行。
-- =================================================================================================

USE msdb;
GO

-- =================================================================================================
-- 步骤 1: 更新 SQL Server Agent 作业
-- =================================================================================================
PRINT N'步骤 1: 正在更新 SQL Server Agent 作业 [KTV - Nightly Report Job]...';

-- 检查作业是否存在
IF EXISTS (SELECT 1 FROM dbo.sysjobs WHERE name = N'KTV - Nightly Report Job')
BEGIN
    -- 更新作业步骤中的命令，使其调用新的主调度存储过程
    EXEC dbo.sp_update_jobstep
        @job_name = N'KTV - Nightly Report Job',
        @step_id = 1,
        @command = N'USE operatedata; EXEC dbo.usp_Job_GenerateDailyPerformanceReport;';
    PRINT N' -> 作业 [KTV - Nightly Report Job] 的命令已更新为执行 [usp_Job_GenerateDailyPerformanceReport]。';
END
ELSE
BEGIN
    PRINT N' -> 警告: 未找到名为 [KTV - Nightly Report Job] 的作业，跳过更新。';
END
GO

USE operatedata;
GO

-- =================================================================================================
-- 步骤 2: 删除旧的存储过程 (自顶向下)
-- =================================================================================================
PRINT N'步骤 2: 正在删除旧的每日业绩报表流程相关的存储过程...';

-- 删除顺序与依赖关系相反，确保不会因为依赖而失败
IF OBJECT_ID('dbo.usp_RunNightlyKTVReportJob_Final', 'P') IS NOT NULL BEGIN DROP PROCEDURE dbo.usp_RunNightlyKTVReportJob_Final; PRINT N' -> 已删除 [usp_RunNightlyKTVReportJob_Final]'; END
IF OBJECT_ID('dbo.usp_RunUnifiedDailyReport_V3_Final', 'P') IS NOT NULL BEGIN DROP PROCEDURE dbo.usp_RunUnifiedDailyReport_V3_Final; PRINT N' -> 已删除 [usp_RunUnifiedDailyReport_V3_Final]'; END
IF OBJECT_ID('dbo.usp_UpdateDirectFallFlag_ByName', 'P') IS NOT NULL BEGIN DROP PROCEDURE dbo.usp_UpdateDirectFallFlag_ByName; PRINT N' -> 已删除 [usp_UpdateDirectFallFlag_ByName]'; END
IF OBJECT_ID('dbo.usp_GenerateDayTimeReport_Simple_V5_Final', 'P') IS NOT NULL BEGIN DROP PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V5_Final; PRINT N' -> 已删除 [usp_GenerateDayTimeReport_Simple_V5_Final]'; END
IF OBJECT_ID('dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Corrected', 'P') IS NOT NULL BEGIN DROP PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Corrected; PRINT N' -> 已删除 [usp_GenerateSimplifiedDailyReport_V7_Final_Corrected]'; END
IF OBJECT_ID('dbo.usp_GetTimeSlotDetails_WithDirectFall', 'P') IS NOT NULL BEGIN DROP PROCEDURE dbo.usp_GetTimeSlotDetails_WithDirectFall; PRINT N' -> 已删除 [usp_GetTimeSlotDetails_WithDirectFall]'; END
GO

-- =================================================================================================
-- 步骤 3: 创建新的存储过程 (自底向上)
-- =================================================================================================
PRINT N'步骤 3: 正在用新名称创建新的存储过程...';

-- 3.1 创建 usp_Util_GetTimeSlotDetailsWithDirectFall (原 usp_GetTimeSlotDetails_WithDirectFall)
PRINT N' -> 正在创建 [usp_Util_GetTimeSlotDetailsWithDirectFall]...';
GO
CREATE PROCEDURE dbo.usp_Util_GetTimeSlotDetailsWithDirectFall
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @SqlStatement NVARCHAR(MAX);
    DECLARE @ParamDefinition NVARCHAR(500);
    SET @ParamDefinition = N'@pShopId INT, @pTargetDate DATE';
    SET @SqlStatement = N'
        WITH TimeSlots AS (
            SELECT ti.TimeNo, ti.TimeName, ti.BegTime,
                   DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@pTargetDate AS datetime))) AS SlotStartDateTime,
                   LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@pTargetDate AS datetime))), 1, ''2999-12-31'') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
            FROM dbo.shoptimeinfo AS sti JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo WHERE sti.Shopid = @pShopId
        ),
        TrueDropInData AS (
            SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
            FROM dbo.RmCloseInfo AS rt
            JOIN dbo.shoptimeinfo AS sti_beg ON rt.Shopid = sti_beg.Shopid AND rt.Beg_Key = sti_beg.TimeNo
            JOIN dbo.shoptimeinfo AS sti_end ON rt.Shopid = sti_end.Shopid AND rt.End_Key = sti_end.TimeNo
            WHERE rt.Shopid = @pShopId AND rt.WorkDate = @pTargetDate AND rt.OpenDateTime IS NOT NULL
              AND rt.Beg_Key <> rt.End_Key
              AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
              AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
        )
        SELECT
            ti_main.TimeName AS TimeSlotName,
            ROW_NUMBER() OVER (ORDER BY ti_main.BegTime) AS TimeSlotOrder,
            (COUNT(rt.InvNo) - COUNT(CASE WHEN rt.MTPay > 0 OR rt.DZPay > 0 OR rt.AliPay > 0 OR rt.CtNo = 1 THEN 1 END)) AS KPlus_Count,
            COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS Special_Count,
            COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS Meituan_Count,
            COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS Douyin_Count,
            COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS RoomFee_Count,
            COUNT(rt.InvNo) AS Subtotal_Count,
            ISNULL((SELECT COUNT(tdi.InvNo) FROM TrueDropInData AS tdi JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo WHERE ts_beg.BegTime < ti_main.BegTime AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime), 0) AS PreviousSlot_DirectFall
        FROM dbo.RmCloseInfo AS rt
        JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
        JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
        WHERE rt.Shopid = @pShopId AND rt.WorkDate = @pTargetDate AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
        ORDER BY ti_main.BegTime;';
    EXEC sp_executesql @SqlStatement, @ParamDefinition, @pShopId = @ShopId, @pTargetDate = @TargetDate;
END
GO

-- 3.2 创建 usp_Util_CalculateSimplifiedMetrics (原 usp_GenerateSimplifiedDailyReport_V7_Final_Corrected)
PRINT N' -> 正在创建 [usp_Util_CalculateSimplifiedMetrics]...';
GO
CREATE PROCEDURE dbo.usp_Util_CalculateSimplifiedMetrics
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;
    CREATE TABLE #DailyReports (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int,NonPackage_RoomFee int,NonPackage_Others int,
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
        DiscountFree_BatchCount int, DiscountFree_Revenue decimal(18, 2)
    );
    DECLARE @CurrentDate DATE = @BeginDate;
    WHILE @CurrentDate <= @EndDate
    BEGIN
        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));
        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));
        WITH RecordsWithTimeMode AS (
            SELECT
                rt.*,
                sti.TimeMode,
                (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
                CASE
                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                    WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                    ELSE 2
                END AS RevenueClassificationMode
            FROM dbo.RmCloseInfo AS rt
            LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
        ),
        PackageData AS (
            SELECT
                r.InvNo,
                fdc.FdCName,
                (fdc.FdPrice * fdc.FdQty) as ItemRevenue
            FROM RecordsWithTimeMode r
            JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE r.TimeMode = 2
            AND fdc.ShopId = @ShopId
            AND (fdc.FdCName LIKE N'%买断%' OR fdc.FdCName LIKE N'%畅饮%')
        ),
        YearCardData AS (
            SELECT DISTINCT
                r.InvNo
            FROM RecordsWithTimeMode r
            JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE r.TimeMode = 2
            AND fdc.ShopId = @ShopId
            AND fdc.FdCName LIKE N'%年卡%'
        )
        INSERT INTO #DailyReports
        SELECT
            @CurrentDate AS ReportDate,
            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
            DATENAME(weekday, @CurrentDate) AS Weekday,
            ISNULL(SUM(Revenue), 0) AS TotalRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,
            (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
            COUNT(DISTINCT CASE WHEN  rt.CtNo = 19 AND rt.CtNo = 2 THEN rt.InvNo END) AS FreeMeal_KPlus,
            COUNT(DISTINCT CASE WHEN  rt.CtNo = 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS FreeMeal_Special,
            COUNT(DISTINCT CASE WHEN  rt.CtNo = 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS FreeMeal_Meituan,
            COUNT(DISTINCT CASE WHEN   rt.CtNo = 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS FreeMeal_Douyin,
            COUNT(DISTINCT CASE WHEN   rt.CtNo = 19 THEN rt.InvNo END) AS FreeMeal_BatchCount,
            ISNULL(SUM(CASE WHEN  rt.CtNo = 19 THEN rt.Revenue ELSE 0 END), 0) AS FreeMeal_Revenue,
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') AS Buyout_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%买断%'), 0) AS Buyout_Revenue,
            ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) AS Changyin_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%畅饮%' AND FdCName NOT LIKE N'%自由畅饮%'), 0) AS Changyin_Revenue,
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') AS FreeConsumption_BatchCount,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS NonPackage_Special,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS NonPackage_Meituan,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS NonPackage_Douyin,
            (COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) - 
            (
                COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.InvNo END) + 
                (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') + 
                ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) + 
                (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') + 
                COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.AliPay > 0 THEN rt.InvNo END) + 
                COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.MTPay > 0 THEN rt.InvNo END) + 
                COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.DZPay > 0 THEN rt.InvNo END) +
                (SELECT COUNT(DISTINCT InvNo) FROM YearCardData)
            ) AS NonPackage_RoomFee,
             (SELECT COUNT(DISTINCT InvNo) FROM YearCardData) AS NonPackage_Others,
            (COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) - COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.InvNo END)) AS Night_Verify_BatchCount,
            (ISNULL(SUM(CASE WHEN rt.TimeMode = 2 THEN rt.Revenue ELSE 0 END), 0) - ISNULL(SUM(CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.Revenue ELSE 0 END), 0)) AS Night_Verify_Revenue,
            COUNT(CASE WHEN rt.AccOkZD > 0 THEN 1 ELSE NULL END) AS DiscountFree_BatchCount,
            ISNULL(SUM(CASE WHEN rt.AccOkZD > 0 THEN rt.AccOkZD ELSE 0 END), 0) AS DiscountFree_Revenue
        FROM RecordsWithTimeMode rt;
        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END
    SELECT * FROM #DailyReports ORDER BY ReportDate;
    DROP TABLE #DailyReports;
END
GO

-- 3.3 创建 usp_Util_CalculateDayTimeMetrics (原 usp_GenerateDayTimeReport_Simple_V5_Final)
PRINT N' -> 正在创建 [usp_Util_CalculateDayTimeMetrics]...';
GO
CREATE PROCEDURE dbo.usp_Util_CalculateDayTimeMetrics
    @ShopId int,
    @TargetDate date
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @BeginDate datetime = DATEADD(hour, 8, CAST(@TargetDate AS datetime));
    DECLARE @EndDate datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @TargetDate) AS datetime));
    WITH RecordsWithTimeMode AS (
        SELECT
            rt.*,
            sti.TimeMode,
             ti_beg.BegTime,
            (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
            CASE
                WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                ELSE 2
            END AS RevenueClassificationMode
        FROM dbo.RmCloseInfo AS rt
        LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti.TimeNo = ti_beg.TimeNo
        WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @BeginDate AND @EndDate
    )
    SELECT
        @TargetDate AS WorkDate,
        (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
        DATENAME(weekday, @TargetDate) AS WeekdayName,
        ISNULL(SUM(CASE WHEN rt.RevenueClassificationMode = 1 THEN rt.Revenue ELSE 0 END), 0) AS DayTimeRevenue,
        ISNULL(SUM(CASE WHEN rt.RevenueClassificationMode = 2 THEN rt.Revenue ELSE 0 END), 0) AS NightTimeRevenue,
        ISNULL(SUM(rt.Revenue), 0) AS TotalRevenue,
        COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
        COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
        (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,
        SUM(rt.Numbers) AS TotalGuestCount,
        SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,
        COUNT(DISTINCT CASE WHEN rt.IsDirectFall = 1 AND rt.BegTime IS NOT NULL AND rt.BegTime < 1650 THEN rt.Beg_Key ELSE NULL END) AS DayTimeDropInBatch,
        COUNT(DISTINCT CASE WHEN rt.IsDirectFall = 1 AND rt.BegTime IS NOT NULL AND rt.BegTime >= 1650 AND rt.BegTime < 2000 THEN rt.Beg_Key ELSE NULL END) AS NightTimeDropInBatch,
        ISNULL(SUM(CASE WHEN rt.IsDirectFall = 1 THEN rt.Numbers ELSE 0 END), 0) AS TotalDirectFallGuests
    FROM RecordsWithTimeMode rt
    GROUP BY rt.ShopId;
END
GO

-- 3.4 创建 usp_Util_UpdateDirectFallFlags (原 usp_UpdateDirectFallFlag_ByName)
PRINT N' -> 正在创建 [usp_Util_UpdateDirectFallFlags]...';
GO
CREATE PROCEDURE dbo.usp_Util_UpdateDirectFallFlags
    @TargetDate VARCHAR(8),
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;
    PRINT N'Starting direct fall flag update for ShopID: ' + CAST(@ShopId AS NVARCHAR(10)) + N' on Date: ' + @TargetDate;
    UPDATE dbo.RmCloseInfo
    SET IsDirectFall = 0
    WHERE WorkDate = @TargetDate AND ShopId = @ShopId;
    UPDATE rci
    SET rci.IsDirectFall = 1
    FROM dbo.RmCloseInfo AS rci
    WHERE rci.WorkDate = @TargetDate
      AND rci.ShopId = @ShopId
      AND EXISTS (
          SELECT 1
          FROM operatedata.dbo.FdCashBak fcb
          WHERE fcb.InvNo = rci.InvNo COLLATE Chinese_PRC_CI_AS
            AND fcb.ShopId = @ShopId
            AND fcb.FdCName LIKE N'%直落%'
      );
    DECLARE @UpdateCount INT = @@ROWCOUNT;
    PRINT N'Successfully updated ' + CAST(@UpdateCount AS NVARCHAR(10)) + N' records with the IsDirectFall flag.';
    IF @UpdateCount = 0
    BEGIN
        PRINT N'WARNING: No records were flagged as direct fall.';
    END
    PRINT N'Direct fall flag update completed for ShopID: ' + CAST(@ShopId AS NVARCHAR(10)) + N' on Date: ' + @TargetDate;
END
GO

-- 3.5 创建 usp_Report_CreateDailyPerformance (原 usp_RunUnifiedDailyReport_V3_Final)
PRINT N' -> 正在创建 [usp_Report_CreateDailyPerformance]...';
GO
CREATE PROCEDURE dbo.usp_Report_CreateDailyPerformance
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @ReportID INT;
    BEGIN TRANSACTION;
    BEGIN TRY
        PRINT N'Step 0: Deleting existing data...';
        SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
        IF @ReportID IS NOT NULL
        BEGIN
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;
        END

        PRINT N'Step 1: Generating Header data using [usp_Util_CalculateDayTimeMetrics]...';
        CREATE TABLE #TempHeader (
            WorkDate DATE, ShopName NVARCHAR(100), WeekdayName NVARCHAR(20),
            DayTimeRevenue DECIMAL(18,2), NightTimeRevenue DECIMAL(18,2), TotalRevenue DECIMAL(18,2),
            DayTimeBatchCount INT, NightTimeBatchCount INT, TotalBatchCount INT,
            TotalGuestCount INT, BuffetGuestCount INT,
            DayTimeDropInBatch INT, NightTimeDropInBatch INT, TotalDirectFallGuests INT
        );
        INSERT INTO #TempHeader EXEC dbo.usp_Util_CalculateDayTimeMetrics @ShopId = @ShopId, @TargetDate = @TargetDate;
        
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        ) SELECT @TargetDate, @ShopId, ShopName, WeekdayName, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests FROM #TempHeader;
        SET @ReportID = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        PRINT N'Step 2: Generating Night Details data using [usp_Util_CalculateSimplifiedMetrics]...';
        CREATE TABLE #TempNightDetails (
            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
            FreeConsumption_BatchCount int,
            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_RoomFee int, NonPackage_Others int,
            Night_Verify_BatchCount int,Night_Verify_Revenue decimal(18, 2), DiscountFree_BatchCount INT, DiscountFree_Revenue DECIMAL(18, 2)
        );
        INSERT INTO #TempNightDetails EXEC dbo.usp_Util_CalculateSimplifiedMetrics @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;
        
        INSERT INTO dbo.FullDailyReport_NightDetails (
            ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_RoomFee, NonPackage_Others,
            Night_Verify_BatchCount,Night_Verify_Revenue, DiscountFree_BatchCount, DiscountFree_Revenue
        ) SELECT 
            @ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin,NonPackage_RoomFee, NonPackage_Others,
            Night_Verify_BatchCount,Night_Verify_Revenue, DiscountFree_BatchCount, DiscountFree_Revenue
        FROM #TempNightDetails;
        PRINT N'Night Details data inserted.';

        PRINT N'Step 3: Generating Time Slot Details data using [usp_Util_GetTimeSlotDetailsWithDirectFall]...';
        CREATE TABLE #TempTimeSlotDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        INSERT INTO #TempTimeSlotDetails EXEC dbo.usp_Util_GetTimeSlotDetailsWithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;
        
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        ) SELECT @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall FROM #TempTimeSlotDetails;
        PRINT N'Time Slot Details data inserted.';

        DROP TABLE #TempHeader;
        DROP TABLE #TempNightDetails;
        DROP TABLE #TempTimeSlotDetails;

        COMMIT TRANSACTION;
        PRINT N'Success: Daily Performance Report processed successfully.';
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- 3.6 创建 usp_Job_GenerateDailyPerformanceReport (原 usp_RunNightlyKTVReportJob_Final)
PRINT N' -> 正在创建 [usp_Job_GenerateDailyPerformanceReport]...';
GO
CREATE PROCEDURE dbo.usp_Job_GenerateDailyPerformanceReport
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @ShopList TABLE (ShopId INT PRIMARY KEY);
    INSERT INTO @ShopList (ShopId) VALUES (2), (3), (4), (5), (6), (8), (9), (10), (11);
    DECLARE @TargetDateAsDate DATE = CAST(DATEADD(day, -1, GETDATE()) AS DATE);
    DECLARE @TargetDateAsVarchar VARCHAR(8) = CONVERT(VARCHAR(8), @TargetDateAsDate, 112);
    DECLARE @CurrentShopId INT;
    DECLARE @JobLogMessage NVARCHAR(1000);

    PRINT N'--- Starting Daily Performance Report Job for Date: ' + CONVERT(NVARCHAR, @TargetDateAsDate, 120) + N' ---';
    DECLARE ShopCursor CURSOR FOR SELECT ShopId FROM @ShopList ORDER BY ShopId;
    OPEN ShopCursor;
    FETCH NEXT FROM ShopCursor INTO @CurrentShopId;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        BEGIN TRY
            PRINT N'
Processing ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            PRINT N'------------------------';

            PRINT N'Step 1: Updating direct fall flags using [usp_Util_UpdateDirectFallFlags]...';
            EXEC dbo.usp_Util_UpdateDirectFallFlags @TargetDate = @TargetDateAsVarchar, @ShopId = @CurrentShopId;
            PRINT N'Step 1: Direct fall flags updated successfully.';

            PRINT N'Step 2: Generating the daily performance report using [usp_Report_CreateDailyPerformance]...';
            EXEC dbo.usp_Report_CreateDailyPerformance @TargetDate = @TargetDateAsDate, @ShopId = @CurrentShopId;
            PRINT N'Step 2: Daily performance report generated successfully.';

            SET @JobLogMessage = N'Successfully processed ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            PRINT @JobLogMessage;
        END TRY
        BEGIN CATCH
            DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
            SET @JobLogMessage = N'FAILED to process ShopID: ' + CAST(@CurrentShopId AS NVARCHAR(10)) + N'. Error: ' + @ErrorMessage;
            RAISERROR(@JobLogMessage, 10, 1) WITH NOWAIT;
        END CATCH
        FETCH NEXT FROM ShopCursor INTO @CurrentShopId;
    END

    CLOSE ShopCursor;
    DEALLOCATE ShopCursor;
    PRINT N'
--- Daily Performance Report Job finished. ---';
END
GO

PRINT N'=================================================================================================';
PRINT N'重构完成！所有相关的存储过程和SQL Agent作业都已更新为新名称。';
PRINT N'=================================================================================================';
GO
