
-- =================================================================================================
-- 脚本: 25_test_refactored_report.sql
-- 作者: Gemini AI
-- 日期: 2025-08-04
-- 描述: 测试重构后的“每日业绩报表”流程是否能为指定日期和门店成功生成数据。
-- =================================================================================================

USE operatedata;
GO

PRINT N'--- 开始测试重构后的每日业绩报表流程 ---';

-- 1. 定义测试参数
DECLARE @TestDate DATE = '2025-08-03';
DECLARE @TestShopID INT = 11;
DECLARE @TestDateAsVarchar VARCHAR(8) = CONVERT(VARCHAR(8), @TestDate, 112);

PRINT N'测试参数: ShopID = ' + CAST(@TestShopID AS NVARCHAR) + N', Date = ' + CONVERT(NVARCHAR, @TestDate, 23);

-- 2. 直接调用重构后的核心子程序进行测试
--    我们不调用顶层的Job存储过程，因为它有自己的日期逻辑(昨天)。
--    我们直接调用它下面的两个核心步骤，传入我们指定的参数。
BEGIN TRY
    PRINT N'
Step 1: Updating direct fall flags using [usp_Util_UpdateDirectFallFlags]...';
    EXEC dbo.usp_Util_UpdateDirectFallFlags @TargetDate = @TestDateAsVarchar, @ShopId = @TestShopID;
    PRINT N'Step 1: Direct fall flags updated successfully.';

    PRINT N'
Step 2: Generating the daily performance report using [usp_Report_CreateDailyPerformance]...';
    EXEC dbo.usp_Report_CreateDailyPerformance @TargetDate = @TestDate, @ShopId = @TestShopID;
    PRINT N'Step 2: Daily performance report generated successfully.';

    PRINT N'
--- 测试执行成功 --- ';

END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
    PRINT N'
--- 测试执行失败! 错误: ' + @ErrorMessage + N' ---';
END CATCH
GO

-- 3. 验证数据是否已生成
PRINT N'
--- 正在验证数据... ---';
SELECT TOP 1 *
FROM dbo.FullDailyReport_Header
WHERE ReportDate = '2025-08-03' AND ShopID = 11;
GO
