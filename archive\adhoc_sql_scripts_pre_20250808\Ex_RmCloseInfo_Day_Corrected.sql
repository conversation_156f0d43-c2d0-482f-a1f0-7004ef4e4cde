ALTER PROC [dbo].[Ex_RmCloseInfo_Day]
	@t int =0,--参数
	@ShopId int =0,
	@Sort VARCHAR(50)='WorkDate', --排序字段及规则,不用加order by
	@BeginDate datetime = null,--开始日期
	@EndDate  datetime = null, --结束日期
	@IsMerge bit =1,--是否合并
	@PageIndex int=1,
	@PageSize int=50000000
AS
----对RmCloseInfo_Day表的查询，并可酌情进行合并
----参数t
----    0：查询明细
----	1:将数据导出成csv报表
BEGIN
	SET NOCOUNT ON;

	DECLARE @sql NVARCHAR(MAX);
	DECLARE @params NVARCHAR(1000);
	DECLARE @whereClause NVARCHAR(MAX);
	DECLARE @start int;
	DECLARE @end int;
	DECLARE @SafeSort NVARCHAR(100);
	DECLARE @OrderByClause NVARCHAR(200);

	-- 日期默认值处理
	IF(@BeginDate IS NULL)
		SET @BeginDate = CONVERT(date, DATEADD(day,-1,GETDATE()));
	IF(@EndDate IS NULL)
		SET @EndDate = CONVERT(date, GETDATE());

	-- 安全地处理动态排序字段（白名单验证）
	SET @SafeSort = CASE 
						WHEN @Sort = 'ShopName' THEN 'b.ShopName'
						WHEN @Sort = 'Turnover' THEN 'a.Turnover DESC'
						WHEN @Sort = 'Tot' THEN 'a.Tot DESC'
						WHEN @Sort = 'WorkDate' THEN 'a.WorkDate DESC'
						ELSE 'a.WorkDate DESC' -- 默认排序
					END;

	-- 构造ORDER BY子句
	SET @OrderByClause = CASE 
						WHEN @Sort = 'ShopName' THEN 'b.ShopName'
						WHEN @Sort = 'Turnover' THEN 'a.Turnover DESC'
						WHEN @Sort = 'Tot' THEN 'a.Tot DESC'
						WHEN @Sort = 'WorkDate' THEN 'a.WorkDate DESC'
						ELSE 'a.WorkDate DESC' -- 默认排序
					END;

	-- 构造安全的WHERE条件
	SET @whereClause = N' WHERE 1=1 ';
	IF(@BeginDate IS NOT NULL)
		SET @whereClause += ' AND a.WorkDate >= CONVERT(varchar(8), @p_BeginDate, 112) ';
	IF(@EndDate IS NOT NULL)
		SET @whereClause += ' AND a.WorkDate <= CONVERT(varchar(8), @p_EndDate, 112) ';
	IF(@ShopId != 0)
		SET @whereClause += ' AND a.ShopId = @p_ShopId ';

	-- 定义通用参数
	SET @params = N'@p_BeginDate date, @p_EndDate date, @p_ShopId int';

	-- 模式0：查询详细信息
	IF(@t=0)
	BEGIN
		IF(@IsMerge=1) -- 合并查询
		BEGIN
			SET @sql = N'
			SELECT
				b.ShopName,
				-- 【修正】使用 ISNULL 处理所有可能为 NULL 的字段
				SUM(ISNULL(a.Turnover, 0)) AS Turnover,
				SUM(ISNULL(a.Tot, 0)) AS Tot,
				SUM(ISNULL(a.Tot, 0) - ISNULL(a.WechatOfficialPay, 0) - ISNULL(a.MTPay, 0) - ISNULL(a.DZPay, 0) - ISNULL(a.NMPay, 0)) AS ActualReceivedAmount,
				SUM(ISNULL(a.Cash, 0)) AS Cash,
				SUM(ISNULL(a.Cash_Targ, 0)) AS Cash_Targ,
        SUM(ISNULL(a.Vesa, 0)) AS Vesa,
				SUM(ISNULL(a.GZ, 0)) AS GZ,
				SUM(ISNULL(a.AccOkZD, 0)) AS AccOkZD,
				SUM(ISNULL(a.RechargeAccount, 0)) AS RechargeAccount,
				SUM(ISNULL(a.ReturnAccount, 0)) AS ReturnAccount,
				SUM(ISNULL(a.NoPayed, 0)) AS NoPayed,
				SUM(ISNULL(a.WXPay, 0)) AS WXPay,
				SUM(ISNULL(a.AliPay, 0)) AS AliPay,
				SUM(ISNULL(a.MTPay, 0)) AS MTPay,
				SUM(ISNULL(a.DZPay, 0)) AS DZPay,
				SUM(ISNULL(a.NMPay, 0)) AS NMPay,
				SUM(ISNULL(a.[Check], 0)) AS [Check],
				SUM(ISNULL(a.WechatDeposit, 0)) AS WechatDeposit,
				SUM(ISNULL(a.WechatShopping, 0)) AS WechatShopping,
				SUM(ISNULL(a.WechatOfficialPay, 0)) AS WechatOfficialPay, 
				CONVERT(varchar(10), @p_BeginDate, 120) + '' ~ '' + CONVERT(varchar(10), @p_EndDate, 120) AS WorkDate
			FROM RmCloseInfo_Day a
			JOIN MIMS.dbo.ShopInfo b ON a.Shopid = b.Shopid'
			+ @whereClause +
			' GROUP BY b.ShopName
			  ORDER BY ' + @OrderByClause + ';';
		END
		ELSE -- 不合并，带分页的详细查询
		BEGIN
			SET @start = (@PageIndex-1) * @PageSize + 1;
			SET @end = @start + @PageSize - 1;

			SET @sql = N'
			SELECT * FROM (
				SELECT
					COUNT(*) OVER() AS Total,
					ROW_NUMBER() OVER(ORDER BY ' + @SafeSort + ') AS rownum,
					b.ShopName,
					-- 【修正】使用 ISNULL 处理所有可能为 NULL 的字段
					ISNULL(a.Turnover, 0) AS Turnover,
					ISNULL(a.Tot, 0) AS Tot,
					ISNULL(a.Tot, 0) - ISNULL(a.WechatOfficialPay, 0) - ISNULL(a.MTPay, 0) - ISNULL(a.DZPay, 0) - ISNULL(a.NMPay, 0) AS ActualReceivedAmount,
					ISNULL(a.Cash, 0) AS Cash, ISNULL(a.Cash_Targ, 0) AS Cash_Targ, ISNULL(a.Vesa, 0) AS Vesa, ISNULL(a.GZ, 0) AS GZ, 
					ISNULL(a.AccOkZD, 0) AS AccOkZD, ISNULL(a.RechargeAccount, 0) AS RechargeAccount, ISNULL(a.ReturnAccount, 0) AS ReturnAccount,
					ISNULL(a.NoPayed, 0) AS NoPayed, ISNULL(a.WXPay, 0) AS WXPay, ISNULL(a.AliPay, 0) AS AliPay, 
					ISNULL(a.MTPay, 0) AS MTPay, ISNULL(a.DZPay, 0) AS DZPay, ISNULL(a.NMPay, 0) AS NMPay, 
					ISNULL(a.[Check], 0) AS [Check], ISNULL(a.WechatDeposit, 0) AS WechatDeposit, ISNULL(a.WechatShopping, 0) AS WechatShopping,
					ISNULL(a.WechatOfficialPay, 0) AS WechatOfficialPay,
					a.WorkDate
				FROM RmCloseInfo_Day a
				JOIN Mims.dbo.ShopInfo b ON a.shopid = b.shopid'
				+ @whereClause +
			') AS t
			WHERE rownum BETWEEN @p_start AND @p_end;';

			SET @params += ', @p_start int, @p_end int';
		END

		IF (@IsMerge = 1)
			EXEC sp_executesql @sql, @params, @p_BeginDate = @BeginDate, @p_EndDate = @EndDate, @p_ShopId = @ShopId;
		ELSE
			EXEC sp_executesql @sql, @params, @p_BeginDate = @BeginDate, @p_EndDate = @EndDate, @p_ShopId = @ShopId, @p_start = @start, @p_end = @end;
	END

	-- 模式1：数据导出成CSV报表
	IF(@t=1)
	BEGIN
		IF(@IsMerge=1) -- 合并报表
		BEGIN
			SET @sql = N'
			SELECT
				b.ShopName AS 门店,
				-- 【修正】使用 ISNULL 处理所有可能为 NULL 的字段
				SUM(ISNULL(a.Turnover, 0)) AS 营业额,
				SUM(ISNULL(a.Tot, 0)) AS 应收金额,
				SUM(ISNULL(a.Tot, 0) - ISNULL(a.WechatOfficialPay, 0) - ISNULL(a.MTPay, 0) - ISNULL(a.DZPay, 0) - ISNULL(a.NMPay, 0)) AS 实收金额,
				SUM(ISNULL(a.Cash, 0)) AS 人民币, SUM(ISNULL(a.Cash_Targ, 0)) AS 港币, SUM(ISNULL(a.Vesa, 0)) AS 人民币卡,
				SUM(ISNULL(a.GZ, 0)) AS 挂账, SUM(ISNULL(a.AccOkZD, 0)) AS 招待, SUM(ISNULL(a.RechargeAccount, 0)) AS 会员充值,
				SUM(ISNULL(a.ReturnAccount, 0)) AS 会员返还, SUM(ISNULL(a.NoPayed, 0)) AS 免单, SUM(ISNULL(a.WXPay, 0)) AS 微信,
				SUM(ISNULL(a.AliPay, 0)) AS 支付宝, SUM(ISNULL(a.MTPay, 0)) AS 美团, SUM(ISNULL(a.DZPay, 0)) AS 抖音,
				SUM(ISNULL(a.NMPay, 0)) AS 银行渠道, SUM(ISNULL(a.[Check], 0)) AS 龙支付, SUM(ISNULL(a.WechatShopping, 0)) AS 微信商城,
				SUM(ISNULL(a.WechatOfficialPay, 0)) AS 公众号,
				CONVERT(varchar(10), @p_BeginDate, 120) + '' ~ '' + CONVERT(varchar(10), @p_EndDate, 120) AS 时间
			FROM RmCloseInfo_Day a
			JOIN MIMS.dbo.ShopInfo b ON a.Shopid = b.Shopid'
			+ @whereClause +
			' GROUP BY b.ShopName;';
		END
		ELSE -- 不合并报表
		BEGIN
			SET @sql = N'
			SELECT
				b.ShopName AS 门店,
				-- 【修正】使用 ISNULL 处理所有可能为 NULL 的字段
				ISNULL(a.Turnover, 0) AS 营业额,
				ISNULL(a.Tot, 0) AS 应收金额,
				ISNULL(a.Tot, 0) - ISNULL(a.WechatOfficialPay, 0) - ISNULL(a.MTPay, 0) - ISNULL(a.DZPay, 0) - ISNULL(a.NMPay, 0) AS 实收金额,
				ISNULL(a.Cash, 0) AS 人民币, ISNULL(a.Vesa, 0) AS 人民币卡, ISNULL(a.GZ, 0) AS 挂账, ISNULL(a.AccOkZD, 0) AS 招待,
				ISNULL(a.RechargeAccount, 0) AS 会员充值, ISNULL(a.ReturnAccount, 0) AS 会员返还, ISNULL(a.NoPayed, 0) AS 免单,
				ISNULL(a.WXPay, 0) AS 微信, ISNULL(a.AliPay, 0) AS 支付宝, ISNULL(a.MTPay, 0) AS 美团, ISNULL(a.DZPay, 0) AS 抖音,
				ISNULL(a.NMPay, 0) AS 银行渠道, ISNULL(a.[Check], 0) AS 龙支付, ISNULL(a.WechatShopping, 0) AS 微信商城,
				ISNULL(a.WechatOfficialPay, 0) AS 公众号,
				a.WorkDate AS 时间
			FROM RmCloseInfo_Day a
			JOIN Mims.dbo.ShopInfo b ON a.ShopId = b.ShopId'
			+ @whereClause +
			' ORDER BY ' + CASE 
				WHEN @Sort = 'ShopName' THEN 'b.ShopName'
				WHEN @Sort = 'Turnover' THEN 'a.Turnover DESC'
				WHEN @Sort = 'Tot' THEN 'a.Tot DESC'
				WHEN @Sort = 'WorkDate' THEN 'a.WorkDate DESC'
				ELSE 'a.WorkDate DESC'
			END + ';';
		END

		EXEC sp_executesql @sql, @params, @p_BeginDate = @BeginDate, @p_EndDate = @EndDate, @p_ShopId = @ShopId;
	END
END
