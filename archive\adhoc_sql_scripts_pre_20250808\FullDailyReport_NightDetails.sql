/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : OperateData
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 29/07/2025 15:02:25
*/


-- ----------------------------
-- Table structure for FullDailyReport_NightDetails
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[FullDailyReport_NightDetails]') AND type IN ('U'))
	DROP TABLE [dbo].[FullDailyReport_NightDetails]
GO

CREATE TABLE [dbo].[FullDailyReport_NightDetails] (
  [NightDetailID] int  IDENTITY(1,1) NOT NULL,
  [ReportID] int  NOT NULL,
  [FreeMeal_KPlus] int  NULL,
  [FreeMeal_Special] int  NULL,
  [FreeMeal_Meituan] int  NULL,
  [FreeMeal_Douyin] int  NULL,
  [FreeMeal_BatchCount] int  NULL,
  [FreeMeal_Revenue] decimal(18,2)  NULL,
  [Buyout_BatchCount] int  NULL,
  [Buyout_Revenue] decimal(18,2)  NULL,
  [Changyin_BatchCount] int  NULL,
  [Changyin_Revenue] decimal(18,2)  NULL,
  [FreeConsumption_BatchCount] int  NULL,
  [NonPackage_Special] int  NULL,
  [NonPackage_Meituan] int  NULL,
  [NonPackage_Douyin] int  NULL,
  [NonPackage_Others] int  NULL,
  [DiscountFree_BatchCount] int  NULL,
  [DiscountFree_Revenue] decimal(18,2)  NULL,
  [Night_Verify_BatchCount] int  NULL,
  [Night_Verify_Revenue] decimal(18)  NULL
)
GO

ALTER TABLE [dbo].[FullDailyReport_NightDetails] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'k+自由餐_k+',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'FreeMeal_KPlus'
GO

EXEC sp_addextendedproperty
'MS_Description', N'k+自由餐_特权预约',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'FreeMeal_Special'
GO

EXEC sp_addextendedproperty
'MS_Description', N'k+自由餐_美团',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'FreeMeal_Meituan'
GO

EXEC sp_addextendedproperty
'MS_Description', N'k+自由餐_抖音',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'FreeMeal_Douyin'
GO

EXEC sp_addextendedproperty
'MS_Description', N'k+自由餐_小计',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'FreeMeal_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'k+自由餐_营业额',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'FreeMeal_Revenue'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_买断',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'Buyout_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_畅饮',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'Changyin_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_自由消套餐',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'FreeConsumption_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_促销套餐_特权预约',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'NonPackage_Special'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_促销套餐_美团',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'NonPackage_Meituan'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_促销套餐_抖音',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'NonPackage_Douyin'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后其他批次',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'NonPackage_Others'
GO

EXEC sp_addextendedproperty
'MS_Description', N'招待批次',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'DiscountFree_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'招待金额',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'DiscountFree_Revenue'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_批次小计',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'Night_Verify_BatchCount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'20点后_营收金额',
'SCHEMA', N'dbo',
'TABLE', N'FullDailyReport_NightDetails',
'COLUMN', N'Night_Verify_Revenue'
GO


-- ----------------------------
-- Auto increment value for FullDailyReport_NightDetails
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[FullDailyReport_NightDetails]', RESEED, 10)
GO


-- ----------------------------
-- Uniques structure for table FullDailyReport_NightDetails
-- ----------------------------
ALTER TABLE [dbo].[FullDailyReport_NightDetails] ADD CONSTRAINT [UQ_NightDetails_ReportID] UNIQUE NONCLUSTERED ([ReportID] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table FullDailyReport_NightDetails
-- ----------------------------
ALTER TABLE [dbo].[FullDailyReport_NightDetails] ADD CONSTRAINT [PK__FullDail__50BB8608AF1796F2] PRIMARY KEY CLUSTERED ([NightDetailID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Foreign Keys structure for table FullDailyReport_NightDetails
-- ----------------------------
ALTER TABLE [dbo].[FullDailyReport_NightDetails] ADD CONSTRAINT [FK_NightDetails_ReportHeader] FOREIGN KEY ([ReportID]) REFERENCES [dbo].[FullDailyReport_Header] ([ReportID]) ON DELETE CASCADE ON UPDATE NO ACTION
GO

