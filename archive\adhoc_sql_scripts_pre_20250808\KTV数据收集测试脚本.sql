-- ====================================================================
-- KTV每日数据收集系统测试脚本
-- 功能：全面测试usp_CollectKTVDailyData存储过程的各种场景
-- 创建时间: 2025-01-24
-- 版本: 1.0
-- ====================================================================

USE OperateData;
GO

PRINT N'=== KTV每日数据收集系统测试开始 ===';
PRINT N'测试时间: ' + CONVERT(NVARCHAR(23), GETDATE(), 121);
PRINT N'';

-- ====================================================================
-- 测试1：参数验证测试
-- ====================================================================

PRINT N'【测试1】参数验证测试';
PRINT N'----------------------------------------';

-- 测试1.1：门店名称为空
PRINT N'测试1.1：门店名称为空（应该失败）';
BEGIN TRY
    EXEC dbo.usp_CollectKTVDailyData @ShopName = N'', @TargetDate = '20250720';
    PRINT N'❌ 测试失败：应该报错但没有报错';
END TRY
BEGIN CATCH
    PRINT N'✅ 测试通过：' + ERROR_MESSAGE();
END CATCH

-- 测试1.2：日期格式错误
PRINT N'测试1.2：日期格式错误（应该失败）';
BEGIN TRY
    EXEC dbo.usp_CollectKTVDailyData @ShopName = N'名堂店', @TargetDate = '2025-07-20';
    PRINT N'❌ 测试失败：应该报错但没有报错';
END TRY
BEGIN CATCH
    PRINT N'✅ 测试通过：' + ERROR_MESSAGE();
END CATCH

-- 测试1.3：未来日期
PRINT N'测试1.3：未来日期（应该失败）';
DECLARE @FutureDate VARCHAR(8) = CONVERT(VARCHAR(8), DATEADD(day, 1, GETDATE()), 112);
BEGIN TRY
    EXEC dbo.usp_CollectKTVDailyData @ShopName = N'名堂店', @TargetDate = @FutureDate;
    PRINT N'❌ 测试失败：应该报错但没有报错';
END TRY
BEGIN CATCH
    PRINT N'✅ 测试通过：' + ERROR_MESSAGE();
END CATCH

-- 测试1.4：不存在的门店
PRINT N'测试1.4：不存在的门店（应该失败）';
BEGIN TRY
    EXEC dbo.usp_CollectKTVDailyData @ShopName = N'不存在的门店', @TargetDate = '20250720';
    PRINT N'❌ 测试失败：应该报错但没有报错';
END TRY
BEGIN CATCH
    PRINT N'✅ 测试通过：' + ERROR_MESSAGE();
END CATCH

PRINT N'';

-- ====================================================================
-- 测试2：门店信息验证测试
-- ====================================================================

PRINT N'【测试2】门店信息验证测试';
PRINT N'----------------------------------------';

-- 查询可用的门店信息
PRINT N'可用门店列表：';
SELECT 
    Shopid AS '门店ID',
    ShopName AS '门店名称'
FROM MIMS.dbo.ShopInfo
WHERE Shopid IN (3, 11)  -- 已知的测试门店
ORDER BY Shopid;

PRINT N'';

-- ====================================================================
-- 测试3：数据存在性检查
-- ====================================================================

PRINT N'【测试3】数据存在性检查';
PRINT N'----------------------------------------';

-- 检查测试日期是否有业务数据
DECLARE @TestDate VARCHAR(8) = '20250720';  -- 可以修改为有数据的日期
DECLARE @TestShopId INT = 11;  -- 名堂店

PRINT N'检查测试数据存在性：';
PRINT N'门店ID: ' + CAST(@TestShopId AS NVARCHAR(10));
PRINT N'测试日期: ' + @TestDate;

DECLARE @DataCount INT;
SELECT @DataCount = COUNT(*)
FROM dbo.RmCloseInfo_Test
WHERE ShopId = @TestShopId AND WorkDate = @TestDate;

PRINT N'原始数据记录数: ' + CAST(@DataCount AS NVARCHAR(10));

IF @DataCount = 0
BEGIN
    PRINT N'⚠️  警告：测试日期无业务数据，请修改@TestDate为有数据的日期';
    PRINT N'建议查询：';
    PRINT N'SELECT DISTINCT WorkDate FROM dbo.RmCloseInfo_Test WHERE ShopId = ' + CAST(@TestShopId AS NVARCHAR(10)) + N' ORDER BY WorkDate DESC;';
END
ELSE
BEGIN
    PRINT N'✅ 测试数据存在，可以进行功能测试';
END

PRINT N'';

-- ====================================================================
-- 测试4：功能测试（仅在有数据时执行）
-- ====================================================================

IF @DataCount > 0
BEGIN
    PRINT N'【测试4】功能测试';
    PRINT N'----------------------------------------';
    
    -- 测试4.1：清理测试环境
    PRINT N'测试4.1：清理测试环境';
    DELETE FROM dbo.KTV_DailyReport_Comprehensive 
    WHERE 日期 = @TestDate AND 门店 = N'名堂店';
    PRINT N'✅ 测试环境清理完成';
    
    -- 测试4.2：正常数据收集（调试模式）
    PRINT N'测试4.2：正常数据收集（调试模式）';
    BEGIN TRY
        EXEC dbo.usp_CollectKTVDailyData 
            @ShopName = N'名堂店', 
            @TargetDate = @TestDate,
            @Debug = 1;
        PRINT N'✅ 数据收集成功';
    END TRY
    BEGIN CATCH
        PRINT N'❌ 数据收集失败：' + ERROR_MESSAGE();
    END CATCH
    
    -- 测试4.3：验证数据插入
    PRINT N'测试4.3：验证数据插入';
    DECLARE @InsertedCount INT;
    SELECT @InsertedCount = COUNT(*)
    FROM dbo.KTV_DailyReport_Comprehensive
    WHERE 日期 = @TestDate AND 门店 = N'名堂店';
    
    IF @InsertedCount > 0
    BEGIN
        PRINT N'✅ 数据插入成功，记录数: ' + CAST(@InsertedCount AS NVARCHAR(10));
        
        -- 显示插入的数据
        SELECT 
            日期, 门店, 星期, 营收_总收入, 营收_白天档, 营收_晚上档,
            带客_全天总批数, 带客_白天档_总批次, 带客_晚上档_总批次,
            用餐_总人数, 用餐_自助餐人数
        FROM dbo.KTV_DailyReport_Comprehensive
        WHERE 日期 = @TestDate AND 门店 = N'名堂店';
    END
    ELSE
    BEGIN
        PRINT N'❌ 数据插入失败，未找到插入的记录';
    END
    
    -- 测试4.4：重复执行测试
    PRINT N'测试4.4：重复执行测试（应该更新现有数据）';
    BEGIN TRY
        EXEC dbo.usp_CollectKTVDailyData 
            @ShopName = N'名堂店', 
            @TargetDate = @TestDate,
            @Debug = 0;
        PRINT N'✅ 重复执行成功';
        
        -- 验证仍然只有一条记录
        SELECT @InsertedCount = COUNT(*)
        FROM dbo.KTV_DailyReport_Comprehensive
        WHERE 日期 = @TestDate AND 门店 = N'名堂店';
        
        IF @InsertedCount = 1
        BEGIN
            PRINT N'✅ 重复执行验证通过，仍然只有1条记录';
        END
        ELSE
        BEGIN
            PRINT N'❌ 重复执行验证失败，记录数: ' + CAST(@InsertedCount AS NVARCHAR(10));
        END
    END TRY
    BEGIN CATCH
        PRINT N'❌ 重复执行失败：' + ERROR_MESSAGE();
    END CATCH
    
    PRINT N'';
END

-- ====================================================================
-- 测试5：数据准确性验证
-- ====================================================================

IF @DataCount > 0
BEGIN
    PRINT N'【测试5】数据准确性验证';
    PRINT N'----------------------------------------';
    
    -- 对比原始数据和收集的数据
    PRINT N'原始数据统计：';
    SELECT 
        COUNT(*) AS '总订单数',
        SUM(TotalAmount) AS '总营业额',
        SUM(Numbers) AS '总人数',
        COUNT(CASE WHEN DATEPART(hour, OpenDateTime) < 20 THEN 1 ELSE NULL END) AS '白天档订单数',
        COUNT(CASE WHEN DATEPART(hour, OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS '晚上档订单数'
    FROM dbo.RmCloseInfo_Test
    WHERE ShopId = @TestShopId AND WorkDate = @TestDate;
    
    PRINT N'收集的数据：';
    SELECT 
        营收_总收入 AS '总营业额',
        带客_全天总批数 AS '总订单数',
        用餐_总人数 AS '总人数',
        带客_白天档_总批次 AS '白天档订单数',
        带客_晚上档_总批次 AS '晚上档订单数'
    FROM dbo.KTV_DailyReport_Comprehensive
    WHERE 日期 = @TestDate AND 门店 = N'名堂店';
    
    PRINT N'✅ 请手动对比上述两个结果的准确性';
    PRINT N'';
END

-- ====================================================================
-- 测试6：性能测试
-- ====================================================================

IF @DataCount > 0
BEGIN
    PRINT N'【测试6】性能测试';
    PRINT N'----------------------------------------';
    
    DECLARE @StartTime DATETIME2 = GETDATE();
    
    -- 执行性能测试
    BEGIN TRY
        EXEC dbo.usp_CollectKTVDailyData 
            @ShopName = N'名堂店', 
            @TargetDate = @TestDate,
            @Debug = 0;
        
        DECLARE @EndTime DATETIME2 = GETDATE();
        DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, @EndTime);
        
        PRINT N'✅ 性能测试完成';
        PRINT N'执行时间: ' + CAST(@Duration AS NVARCHAR(10)) + N' 毫秒';
        
        IF @Duration < 5000
        BEGIN
            PRINT N'✅ 性能良好（< 5秒）';
        END
        ELSE IF @Duration < 10000
        BEGIN
            PRINT N'⚠️  性能一般（5-10秒）';
        END
        ELSE
        BEGIN
            PRINT N'❌ 性能较差（> 10秒）';
        END
    END TRY
    BEGIN CATCH
        PRINT N'❌ 性能测试失败：' + ERROR_MESSAGE();
    END CATCH
    
    PRINT N'';
END

-- ====================================================================
-- 测试总结
-- ====================================================================

PRINT N'【测试总结】';
PRINT N'========================================';
PRINT N'测试完成时间: ' + CONVERT(NVARCHAR(23), GETDATE(), 121);

IF @DataCount = 0
BEGIN
    PRINT N'⚠️  部分测试跳过：无测试数据';
    PRINT N'建议：修改测试脚本中的@TestDate为有业务数据的日期';
END
ELSE
BEGIN
    PRINT N'✅ 所有测试项目已执行完成';
    PRINT N'请检查上述测试结果，确保所有✅标记的测试都通过';
END

PRINT N'';
PRINT N'=== KTV每日数据收集系统测试结束 ===';

-- 清理测试数据（可选）
-- DELETE FROM dbo.KTV_DailyReport_Comprehensive WHERE 日期 = @TestDate AND 门店 = N'名堂店';
-- PRINT N'测试数据已清理';

GO
