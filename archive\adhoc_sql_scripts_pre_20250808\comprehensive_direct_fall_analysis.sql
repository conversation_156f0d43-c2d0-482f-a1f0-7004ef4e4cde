-- ================================================================================
-- KTV白天档直落分析SQL查询
-- 业务规则：只有20点前进场的白天档自助餐才有直落概念
-- ================================================================================

-- 主查询：分析白天档直落订单
WITH DaytimeOrders AS (
    -- 获取白天档开台数据（20点前进场）
    SELECT 
        o.Ikey,
        o.BookNo,
        o.ShopId,
        o.CustName,
        o.CustTel,
        o.ComeDate,
        o.ComeTime,
        o.Beg_Key,
        o.Beg_Name,
        o.End_Key, 
        o.End_Name,
        o.Numbers,
        o.<PERSON>t<PERSON><PERSON>,
        o.<PERSON>t<PERSON><PERSON>,
        o.RmNo,
        o.Invno,
        o.Remark,
        -- 判断是否直落预约
        CASE 
            WHEN o.Beg_Name != o.End_Name AND o.Beg_Name IS NOT NULL AND o.End_Name IS NOT NULL 
            THEN '直落预约'
            ELSE '单时间段预约'
        END AS 预约类型,
        -- 预约时间段组合
        CASE 
            WHEN o.Beg_Name != o.End_Name 
            THEN o.Beg_Name + ' → ' + o.End_Name
            ELSE o.Beg_Name
        END AS 预约时间段
    FROM [193.112.2.229].[rms2019].[dbo].[opencacheinfo] o
    WHERE o.shopid = 11 
        AND o.ComeDate = '20250717'
        AND o.ComeTime < '20:00:00'  -- 只要白天档进场
),

-- 获取时间段配置信息
TimeSlotConfig AS (
    SELECT 
        st.Ikey,
        st.ShopId,
        st.TimeNo,
        st.TimeName,
        st.TimeType,
        st.TimeMode,
        st.DayType,
        st.BegTime,
        st.EndTime
    FROM [193.112.2.229].[rms2019].[dbo].[shoptimeinfo] st
    WHERE st.shopid = 11
),

-- 获取结账数据（尝试多个可能的数据源）
CheckoutData AS (
    SELECT 
        InvNo,
        CloseDatetime,
        Tot,
        VesaName,
        -- 尝试获取workdate（如果存在）
        CASE 
            WHEN COLUMNPROPERTY(OBJECT_ID('rmcloseinfo'), 'workdate', 'ColumnId') IS NOT NULL 
            THEN workdate 
            ELSE NULL 
        END AS WorkDate
    FROM [193.112.2.229].[dbfood].[dbo].[rmcloseinfo]
    
    UNION ALL
    
    -- 如果192.168.2.5的数据更完整，也包含进来
    SELECT 
        InvNo,
        CloseDatetime,
        Tot,
        VesaName,
        workdate AS WorkDate
    FROM [192.168.2.5].[dbfood].[dbo].[rmcloseinfo]
    WHERE InvNo IS NOT NULL
),

-- 主要分析结果
MainAnalysis AS (
    SELECT 
        d.*,
        c.CloseDatetime,
        c.Tot,
        c.VesaName,
        c.WorkDate,
        
        -- 计算实际消费时长
        CASE 
            WHEN c.CloseDatetime IS NOT NULL 
            THEN DATEDIFF(MINUTE, 
                CAST(d.ComeDate AS DATETIME) + CAST(d.ComeTime AS DATETIME), 
                c.CloseDatetime) / 60.0
            ELSE NULL
        END AS 实际消费时长_小时,
        
        -- 获取开始时间段的TimeType
        ts1.TimeType AS 开始TimeType,
        ts1.TimeMode AS 开始TimeMode,
        ts1.DayType AS 开始DayType,
        
        -- 获取结束时间段的TimeType
        ts2.TimeType AS 结束TimeType,
        ts2.TimeMode AS 结束TimeMode,
        ts2.DayType AS 结束DayType,
        
        -- 判断TimeType是否支持直落
        CASE 
            WHEN d.Beg_Name = d.End_Name THEN '单时间段'
            WHEN ts1.TimeType IS NULL OR ts2.TimeType IS NULL THEN '时间段配置缺失'
            WHEN (ts1.TimeType & ts2.TimeType) > 0 OR ts1.TimeType = ts2.TimeType THEN '支持直落'
            ELSE '不支持直落'
        END AS TimeType直落支持,
        
        -- 结账状态
        CASE 
            WHEN c.CloseDatetime IS NULL THEN '未结账'
            ELSE '已结账'
        END AS 结账状态
        
    FROM DaytimeOrders d
    LEFT JOIN CheckoutData c ON d.Invno = c.InvNo
    LEFT JOIN TimeSlotConfig ts1 ON ts1.TimeName = d.Beg_Name AND ts1.shopid = 11
    LEFT JOIN TimeSlotConfig ts2 ON ts2.TimeName = d.End_Name AND ts2.shopid = 11
)

-- 最终结果查询
SELECT 
    Invno AS 订单号,
    CustName AS 客户姓名,
    ComeTime AS 开台时间,
    预约类型,
    预约时间段,
    Numbers AS 人数,
    RmNo AS 房间号,
    结账状态,
    实际消费时长_小时,
    Tot AS 金额,
    
    -- 直落判断逻辑
    CASE 
        WHEN 预约类型 = '单时间段预约' THEN '非直落预约'
        WHEN TimeType直落支持 = '不支持直落' THEN '❌ 时间段不支持直落'
        WHEN TimeType直落支持 = '时间段配置缺失' THEN '❓ 时间段配置缺失'
        WHEN 结账状态 = '未结账' THEN '⏳ 未结账，无法判断'
        WHEN 实际消费时长_小时 IS NULL THEN '❓ 无法计算时长'
        WHEN 实际消费时长_小时 < 3.0 THEN '❌ 消费时长不足(<3h)'
        WHEN 实际消费时长_小时 >= 3.0 AND TimeType直落支持 = '支持直落' THEN '✅ 真正直落'
        ELSE '❓ 需要进一步分析'
    END AS 直落判断结果,
    
    -- 详细信息
    开始TimeType,
    结束TimeType,
    TimeType直落支持,
    CloseDatetime AS 结账时间,
    Remark AS 备注
    
FROM MainAnalysis
ORDER BY 
    CASE WHEN 直落判断结果 = '✅ 真正直落' THEN 1 ELSE 2 END,
    ComeTime;

-- ================================================================================
-- 统计汇总查询
-- ================================================================================
WITH DaytimeOrders AS (
    SELECT 
        o.Invno,
        o.CustName,
        o.ComeTime,
        o.Beg_Name,
        o.End_Name,
        CASE 
            WHEN o.Beg_Name != o.End_Name AND o.Beg_Name IS NOT NULL AND o.End_Name IS NOT NULL 
            THEN '直落预约'
            ELSE '单时间段预约'
        END AS 预约类型
    FROM [193.112.2.229].[rms2019].[dbo].[opencacheinfo] o
    WHERE o.shopid = 11 
        AND o.ComeDate = '20250717'
        AND o.ComeTime < '20:00:00'
),
CheckoutData AS (
    SELECT InvNo, CloseDatetime, Tot
    FROM [193.112.2.229].[dbfood].[dbo].[rmcloseinfo]
    UNION ALL
    SELECT InvNo, CloseDatetime, Tot
    FROM [192.168.2.5].[dbfood].[dbo].[rmcloseinfo]
    WHERE InvNo IS NOT NULL
)

SELECT 
    '20250717白天档统计' AS 统计项目,
    COUNT(*) AS 总订单数,
    SUM(CASE WHEN 预约类型 = '直落预约' THEN 1 ELSE 0 END) AS 直落预约数,
    SUM(CASE WHEN c.CloseDatetime IS NOT NULL THEN 1 ELSE 0 END) AS 已结账数,
    SUM(CASE WHEN c.Tot IS NOT NULL THEN c.Tot ELSE 0 END) AS 总金额,
    CAST(SUM(CASE WHEN 预约类型 = '直落预约' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,1)) AS 直落预约率_百分比
FROM DaytimeOrders d
LEFT JOIN CheckoutData c ON d.Invno = c.InvNo;

-- ================================================================================
-- 时间段配置查询（用于验证TimeType）
-- ================================================================================
SELECT 
    TimeNo AS 时间段编号,
    TimeName AS 时间段名称,
    TimeType,
    TimeMode,
    DayType,
    BegTime AS 开始时间,
    EndTime AS 结束时间,
    -- 解析TimeType的二进制含义
    CASE 
        WHEN TimeType & 1 > 0 THEN '支持类型1 ' ELSE ''
    END +
    CASE 
        WHEN TimeType & 2 > 0 THEN '支持类型2 ' ELSE ''
    END +
    CASE 
        WHEN TimeType & 4 > 0 THEN '支持类型4 ' ELSE ''
    END +
    CASE 
        WHEN TimeType & 8 > 0 THEN '支持类型8 ' ELSE ''
    END +
    CASE 
        WHEN TimeType & 16 > 0 THEN '支持类型16 ' ELSE ''
    END AS TimeType解析
FROM [193.112.2.229].[rms2019].[dbo].[shoptimeinfo]
WHERE shopid = 11
ORDER BY TimeNo;
