
-- 确保在正确的数据库上下文中执行
USE dbfood;
GO

-- 1. 创建状态维度表
PRINT 'Step 1: Creating table Dim_RoomStatus...';
IF OBJECT_ID('Dim_RoomStatus', 'U') IS NOT NULL
BEGIN
    PRINT 'Table Dim_RoomStatus already exists. Truncating it before re-filling.';
    TRUNCATE TABLE Dim_RoomStatus;
END
ELSE
BEGIN
    CREATE TABLE Dim_RoomStatus (
        StatusID VARCHAR(10) PRIMARY KEY,
        StatusName NVARCHAR(50) NOT NULL
    );
    PRINT 'Table Dim_RoomStatus created successfully.';
END
GO

-- 2. 填充状态数据
PRINT 'Step 2: Populating table Dim_RoomStatus with data...';

-- 使用 MERGE 语句，如果存在则更新，不存在则插入，确保数据同步且脚本可重复执行
MERGE INTO Dim_RoomStatus AS Target
USING (VALUES
    ('A', N'结帐'),
    ('B', N'坏房'),
    ('C', N'续单'),
    ('D', N'清洁'),
    ('E', N'空房'),
    ('H', N'预转'),
    ('L', N'慢带'),
    ('U', N'占用'),
    ('R', N'留房'),
    ('W', N'派房'),
    ('V', N'微信'),
    ('F', N'预结')
) AS Source (StatusID, StatusName)
ON Target.StatusID = Source.StatusID
WHEN MATCHED THEN
    UPDATE SET StatusName = Source.StatusName
WHEN NOT MATCHED BY TARGET THEN
    INSERT (StatusID, StatusName) VALUES (Source.StatusID, Source.StatusName);

PRINT 'Data population complete.';
GO
