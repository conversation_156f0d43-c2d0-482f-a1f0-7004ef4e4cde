
IF OBJECT_ID('BillAnalysisResults', 'U') IS NOT NULL
    DROP TABLE BillAnalysisResults;

CREATE TABLE BillAnalysisResults (
    AnalysisID INT IDENTITY(1,1) PRIMARY KEY,
    InvNo VARCHAR(50),
    RmNo VARCHAR(50),
    RoomSystemAmount DECIMAL(18, 2),
    WxPayTotalAmount DECIMAL(18, 2),
    Difference DECIMAL(18, 2),
    TransactionIDs NVARCHAR(MAX),
    CheckoutTime DATETIME,
    IsDifferenceNormal BIT, -- 1 表示差额<=0 (正常), 0 表示差额>0 (异常)
    LogTime DATETIME DEFAULT GETDATE()
);
GO

IF OBJECT_ID('usp_CalculateAndStoreBillDifferences', 'P') IS NOT NULL
    DROP PROCEDURE usp_CalculateAndStoreBillDifferences;
GO

CREATE PROCEDURE usp_CalculateAndStoreBillDifferences
AS
BEGIN
    SET NOCOUNT ON;

    -- 使用嵌套的 WITH 子句来分步处理逻辑
    -- 第一层: 聚合微信支付信息
    WITH WxPayAggregated AS (
        SELECT
            p.InvNo,
            SUM(p.Tot) AS WxPayTotalForInv,
            STUFF(
                (
                    SELECT ', ' + w.transaction_id
                    FROM wxpayinfo w
                    WHERE w.InvNo = p.InvNo
                    FOR XML PATH('')
                ), 1, 2, ''
            ) AS TransactionIDs
        FROM
            wxpayinfo p
        GROUP BY
            p.InvNo
    ),
    -- 第二层: 计算出包含差额在内的所有账单明细
    AllBillDetails AS (
        SELECT
            r.InvNo,
            r.RmNo,
            r.Tot AS RoomSystemAmount,
            w.WxPayTotalForInv AS WxPayTotalAmount,
            (w.WxPayTotalForInv - r.Tot) AS Difference,
            w.TransactionIDs,
            CONVERT(datetime, r.AccDate + ' ' + r.AccTime) AS CheckoutTime
        FROM
            ROOM r
        INNER JOIN
            WxPayAggregated w ON r.InvNo = w.InvNo
        WHERE
            r.RmStatus = 'A'
            AND DATEDIFF(minute, CONVERT(datetime, r.AccDate + ' ' + r.AccTime), GETDATE()) BETWEEN 0 AND 10
    )
    -- 将所有符合条件的账单插入测试表，并进行标记
    INSERT INTO BillAnalysisResults (
        InvNo,
        RmNo,
        RoomSystemAmount,
        WxPayTotalAmount,
        Difference,
        TransactionIDs,
        CheckoutTime,
        IsDifferenceNormal
    )
    SELECT
        InvNo,
        RmNo,
        RoomSystemAmount,
        WxPayTotalAmount,
        Difference,
        TransactionIDs,
        CheckoutTime,
        CASE
            WHEN Difference <= 0 THEN 1 -- 差额小于等于0，标记为1 (正常)
            ELSE 0                    -- 差额大于0，标记为0 (异常)
        END AS IsDifferenceNormal
    FROM
        AllBillDetails;

    -- (可选) 可以返回插入的行数
    SELECT @@ROWCOUNT AS InsertedRows;

END;
GO
