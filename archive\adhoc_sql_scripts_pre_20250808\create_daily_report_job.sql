
-- ==================================================================
-- 脚本: 创建每日绩效报表定时任务
-- 描述: 此脚本创建一个新的 SQL Server Agent 任务，用于
--       每日自动执行 usp_Job_GenerateDailyPerformanceReport 存储过程，
--       为所有已配置的门店生成报表。
-- ==================================================================

USE msdb;
GO

BEGIN TRANSACTION;

DECLARE @ReturnCode INT = 0;
DECLARE @jobId BINARY(16);
DECLARE @job_name NVARCHAR(128) = N'Daily_Performance_Report_ForAllShops';

-- 检查任务是否已存在，如果存在则先删除
IF EXISTS (SELECT job_id FROM dbo.sysjobs WHERE name = @job_name)
BEGIN
    EXEC msdb.dbo.sp_delete_job @job_name = @job_name, @delete_unused_schedule = 1;
    PRINT '旧的同名任务已被删除。';
END

-- 1. 添加一个新的任务 (Job)
EXEC @ReturnCode = dbo.sp_add_job
    @job_name = @job_name,
    @enabled = 1,
    @notify_level_eventlog = 0,
    @notify_level_email = 0,
    @notify_level_netsend = 0,
    @notify_level_page = 0,
    @delete_level = 0,
    @description = N'每日定时执行，为所有门店生成绩效报表。它通过调用 usp_Job_GenerateDailyPerformanceReport 来完成工作。',
    @category_name = N'[Uncategorized (Local)]',
    @owner_login_name = N'sa',
    @job_id = @jobId OUTPUT;

IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

-- 2. 添加任务步骤 (Job Step)
EXEC @ReturnCode = dbo.sp_add_jobstep
    @job_id = @jobId,
    @step_name = N'Execute Master Stored Procedure',
    @step_id = 1,
    @cmdexec_success_code = 0,
    @on_success_action = 1, -- 成功后退出任务
    @on_fail_action = 2,    -- 失败后退出任务
    @retry_attempts = 0,
    @retry_interval = 0,
    @os_run_priority = 0,
    @subsystem = N'TSQL',
    @command = N'EXEC operatedata.dbo.usp_Job_GenerateDailyPerformanceReport;',
    @database_name = N'operatedata';

IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

-- 3. 更新任务的起始步骤
EXEC @ReturnCode = dbo.sp_update_job
    @job_id = @jobId,
    @start_step_id = 1;

IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

-- 4. 添加执行计划 (Schedule)
EXEC @ReturnCode = dbo.sp_add_jobschedule
    @job_id = @jobId,
    @name = N'Daily_At_0200_AM',
    @enabled = 1,
    @freq_type = 4, -- 每天
    @freq_interval = 1, -- 每 1 天
    @freq_subday_type = 1, -- 在指定时间
    @freq_subday_interval = 0,
    @freq_relative_interval = 0,
    @freq_recurrence_factor = 0,
    @active_start_date = 20250101, -- 一个过去的开始日期
    @active_end_date = 99991231,
    @active_start_time = 80000, -- 早上 08:00:00
    @active_end_time = 235959;

IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

-- 5. 将任务附加到服务器
EXEC dbo.sp_add_jobserver @job_id = @jobId, @server_name = N'(local)';

IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback;

COMMIT TRANSACTION;
PRINT '定时任务 ''Daily_Performance_Report_ForAllShops'' 已成功创建。';
GOTO EndSave;

QuitWithRollback:
    IF (@@TRANCOUNT > 0) ROLLBACK TRANSACTION;
    PRINT '创建任务失败，事务已回滚。';

EndSave:
GO
