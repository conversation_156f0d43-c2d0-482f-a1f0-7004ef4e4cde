
-- 确保在正确的数据库上下文中执行
USE dbfood;
GO

-- 创建新的从表 RoomStatusHourlyDetail
IF OBJECT_ID('RoomStatusHourlyDetail', 'U') IS NOT NULL
BEGIN
    PRINT 'Table RoomStatusHourlyDetail already exists.';
END
ELSE
BEGIN
    CREATE TABLE RoomStatusHourlyDetail (
        DetailID INT IDENTITY(1,1) PRIMARY KEY,
        LogID INT NOT NULL,
        RmStatus VARCHAR(10) NOT NULL,
        StatusCount INT NOT NULL,
        CONSTRAINT FK_RoomStatusHourlyDetail_LogID FOREIGN KEY (LogID) REFERENCES RoomStatisticsHourly(LogID) ON DELETE CASCADE
    );
    PRINT 'Table RoomStatusHourlyDetail created successfully.';
END
GO
