
-- ==================================================================
-- 脚本: 创建动态报表的总调度存储过程
-- 名称: usp_Job_RunDynamicReportForAllShops
-- 描述: 此存储过程用于循环调用 usp_GenerateDynamicDailyReport，
--       为所有已配置的门店生成动态日报表。
-- ==================================================================

USE operatedata;
GO

IF OBJECT_ID('dbo.usp_Job_RunDynamicReportForAllShops', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_Job_RunDynamicReportForAllShops;
    PRINT '旧的同名存储过程已被删除。';
END
GO

CREATE PROCEDURE dbo.usp_Job_RunDynamicReportForAllShops
AS
BEGIN
    SET NOCOUNT ON;

    -- 硬编码的门店列表，与之前的报表逻辑保持一致
    DECLARE @ShopList TABLE (ShopId INT PRIMARY KEY);
    INSERT INTO @ShopList (ShopId) VALUES (2), (3), (4), (5), (6), (8), (9), (10), (11);

    -- 设置报表的目标日期为昨天
    DECLARE @TargetDate DATE = DATEADD(day, -1, GETDATE());
    
    DECLARE @CurrentShopId INT;
    DECLARE @LogMessage NVARCHAR(1000);

    PRINT N'--- 开始执行动态日报表生成任务，日期: ' + CONVERT(NVARCHAR, @TargetDate, 120) + N' ---';

    -- 使用游标遍历所有门店
    DECLARE ShopCursor CURSOR FOR SELECT ShopId FROM @ShopList ORDER BY ShopId;
    OPEN ShopCursor;
    FETCH NEXT FROM ShopCursor INTO @CurrentShopId;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        BEGIN TRY
            PRINT N'正在为门店ID处理: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            
            -- 为当前门店执行核心报表生成存储过程
            EXEC dbo.usp_GenerateDynamicDailyReport @TargetDate = @TargetDate, @ShopID = @CurrentShopId;
            
            SET @LogMessage = N'成功处理门店ID: ' + CAST(@CurrentShopId AS NVARCHAR(10));
            PRINT @LogMessage;
        END TRY
        BEGIN CATCH
            DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
            SET @LogMessage = N'处理门店ID失败: ' + CAST(@CurrentShopId AS NVARCHAR(10)) + N'. 错误: ' + @ErrorMessage;
            -- 使用 RAISERROR WITH NOWAIT 将错误消息立即打印到客户端
            RAISERROR(@LogMessage, 10, 1) WITH NOWAIT;
        END CATCH

        FETCH NEXT FROM ShopCursor INTO @CurrentShopId;
    END

    CLOSE ShopCursor;
    DEALLOCATE ShopCursor;

    PRINT N'--- 动态日报表生成任务已全部完成。 ---';
END
GO

PRINT '存储过程 [usp_Job_RunDynamicReportForAllShops] 已成功创建。';
GO
