
-- =============================================
-- Author:      Gemini AI
-- Create date: 2025-08-01
-- Description: 生成KTV动态日报表 (V17 - 最终版，人数来源FdCashBak)
-- =============================================

IF OBJECT_ID('dbo.usp_GenerateDynamicDailyReport', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateDynamicDailyReport;
GO

CREATE PROCEDURE usp_GenerateDynamicDailyReport
    @TargetDate DATE,
    @ShopID INT
AS
BEGIN
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- 1. 数据清理
        DELETE FROM dbo.DynamicReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopID;

        -- 2. 插入总览数据
        DECLARE @Weekday NVARCHAR(10) = FORMAT(@TargetDate, 'dddd', 'zh-CN');
        DECLARE @TotalRevenue DECIMAL(18, 2);
        SELECT @TotalRevenue = ISNULL(SUM(Tot), 0) FROM dbo.RmCloseInfo WHERE WorkDate = FORMAT(@TargetDate, 'yyyyMMdd') AND ShopId = @ShopID;
        INSERT INTO dbo.DynamicReport_Header (ReportDate, ShopID, Weekday, TotalRevenue)
        VALUES (@TargetDate, @ShopID, @Weekday, @TotalRevenue);
        DECLARE @NewHeaderID INT = SCOPE_IDENTITY();

        -- 3. 准备临时表
        CREATE TABLE #AllBookings_Raw (Beg_Key VARCHAR(20), End_Key VARCHAR(20), Numbers INT, RmNo VARCHAR(20), isdelete BIT);
        CREATE TABLE #AllOccupancies_Raw (Beg_Key VARCHAR(20), End_Key VARCHAR(20), RmNo VARCHAR(20), Invno VARCHAR(30));
        CREATE TABLE #GuestCounts_Raw (Invno VARCHAR(30), FdQty INT, CashType VARCHAR(1));

        DECLARE @Sql NVARCHAR(MAX);
        DECLARE @TargetDateStr VARCHAR(8) = FORMAT(@TargetDate, 'yyyyMMdd');
        DECLARE @ShopIDStr VARCHAR(10) = CAST(@ShopID AS VARCHAR);

        -- a. 获取预订数据
        SET @Sql = N'SELECT Beg_Key, End_Key, Numbers, RmNo, isdelete FROM OPENQUERY([cloudRms2019], ''SELECT Beg_Key, End_Key, Numbers, RmNo, isdelete FROM rms2019.dbo.bookcacheinfo WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''')';
        INSERT INTO #AllBookings_Raw EXEC sp_executesql @Sql;
        SET @Sql = N'SELECT Beg_Key, End_Key, Numbers, RmNo, isdelete FROM OPENQUERY([cloudRms2019], ''SELECT Beg_Key, End_Key, Numbers, RmNo, isdelete FROM rms2019.dbo.bookhistory WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''')';
        INSERT INTO #AllBookings_Raw EXEC sp_executesql @Sql;

        -- b. 获取待客数据
        SET @Sql = N'SELECT Beg_Key, End_Key, RmNo, Invno FROM OPENQUERY([cloudRms2019], ''SELECT Beg_Key, End_Key, RmNo, Invno FROM rms2019.dbo.opencacheinfo WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''')';
        INSERT INTO #AllOccupancies_Raw EXEC sp_executesql @Sql;
        SET @Sql = N'SELECT Beg_Key, End_Key, RmNo, Invno FROM OPENQUERY([cloudRms2019], ''SELECT Beg_Key, End_Key, RmNo, Invno FROM rms2019.dbo.openhistory WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''')';
        INSERT INTO #AllOccupancies_Raw EXEC sp_executesql @Sql;

        -- c. 从本地 FdCashBak 获取人数相关消费项
        INSERT INTO #GuestCounts_Raw (Invno, FdQty, CashType)
        SELECT f.Invno, f.FdQty, f.CashType
        FROM dbo.FdCashBak f
        JOIN #AllOccupancies_Raw o ON f.Invno = o.Invno COLLATE Chinese_PRC_CI_AS
        WHERE f.FdCName LIKE N'%消费人数%';

        -- 4. 核心计算逻辑
        ;WITH TimeSlots AS (
            SELECT t.timeno, t.TimeName FROM dbo.timeinfo t
            JOIN dbo.shoptimeinfo st ON t.timeno = st.timeno
            WHERE st.shopid = @ShopID
        ),
        GuestCounts_From_Bills AS (
            SELECT Invno, SUM(FdQty) as GuestCount FROM #GuestCounts_Raw WHERE CashType = 'N' GROUP BY Invno
        ),
        ValidBookings AS (
            SELECT Beg_Key, Numbers, RmNo FROM #AllBookings_Raw WHERE (isdelete = 0 OR isdelete IS NULL) AND Beg_Key = End_Key
        ),
        ValidOccupancies AS (
            SELECT o.Beg_Key, o.RmNo, ISNULL(g.GuestCount, 0) AS Numbers
            FROM #AllOccupancies_Raw o
            LEFT JOIN GuestCounts_From_Bills g ON o.Invno = g.Invno COLLATE Chinese_PRC_CI_AS
            WHERE o.Beg_Key = o.End_Key
        ),
        BookingsBySlot AS (
            SELECT b.Beg_Key, COUNT(DISTINCT b.RmNo) as BookedRooms, ISNULL(SUM(b.Numbers), 0) as BookedGuests
            FROM ValidBookings b GROUP BY b.Beg_Key
        ),
        OccupanciesBySlot AS (
            SELECT o.Beg_Key, COUNT(DISTINCT o.RmNo) as OccupiedRooms, ISNULL(SUM(o.Numbers), 0) as OccupiedGuests
            FROM ValidOccupancies o GROUP BY o.Beg_Key
        )
        INSERT INTO dbo.DynamicReport_TimeSlotDetails (
            HeaderReportID, TimeSlotName, BookedRooms, BookedGuests, 
            OccupiedRooms, OccupiedGuests, OccupancyRate
        )
        SELECT 
            @NewHeaderID, ts.TimeName, ISNULL(b.BookedRooms, 0), ISNULL(b.BookedGuests, 0),
            ISNULL(o.OccupiedRooms, 0), ISNULL(o.OccupiedGuests, 0),
            CASE WHEN ISNULL(b.BookedRooms, 0) > 0 THEN CAST(ISNULL(o.OccupiedRooms, 0) AS DECIMAL(10,2)) / b.BookedRooms ELSE 0 END
        FROM TimeSlots ts
        LEFT JOIN BookingsBySlot b ON ts.timeno = b.Beg_Key
        LEFT JOIN OccupanciesBySlot o ON ts.timeno = o.Beg_Key
        ORDER BY ts.timeno;

        -- 清理
        DROP TABLE #AllBookings_Raw; DROP TABLE #AllOccupancies_Raw; DROP TABLE #GuestCounts_Raw;

        COMMIT TRANSACTION;
        PRINT 'Dynamic report (V17 - Final Business Logic) generated successfully for ' + CONVERT(VARCHAR, @TargetDate, 23) + ' and ShopID ' + CAST(@ShopID AS VARCHAR);

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
        IF OBJECT_ID('tempdb..#AllBookings_Raw') IS NOT NULL DROP TABLE #AllBookings_Raw;
        IF OBJECT_ID('tempdb..#AllOccupancies_Raw') IS NOT NULL DROP TABLE #AllOccupancies_Raw;
        IF OBJECT_ID('tempdb..#GuestCounts_Raw') IS NOT NULL DROP TABLE #GuestCounts_Raw;
        PRINT 'An error occurred: ' + ERROR_MESSAGE();
        THROW;
    END CATCH

END
GO
