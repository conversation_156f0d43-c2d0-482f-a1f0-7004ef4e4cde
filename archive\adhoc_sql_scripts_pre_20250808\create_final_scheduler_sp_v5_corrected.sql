USE operatedata;
GO

-- ===================================================================
-- 脚本: 创建最终的、模块化的“调度”存储过程 (V6 - 修正嵌套错误)
-- 修正了`INSERT EXEC`嵌套调用的问题
-- ===================================================================

IF OBJECT_ID('dbo.usp_RunNormalizedDailyReportJob_V2', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2;
GO

CREATE PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2
    @TargetDate DATE = NULL,
    @ShopId INT = 3
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Normalized_Full_Report';

    IF EXISTS (SELECT 1 FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId)
    BEGIN
        DECLARE @SkipMessage NVARCHAR(500) = N'Skipped: Data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' and ShopID ' + CAST(@ShopId AS NVARCHAR) + N' already exists.';
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Skipped', @SkipMessage);
        PRINT @SkipMessage;
        RETURN;
    END

    BEGIN TRANSACTION;

    BEGIN TRY
        -- 步骤 1: 调用V3版总览SP，获取Header数据
        PRINT N'Step 1: Getting header data from usp_GenerateDayTimeReport_Simple_V3...';
        CREATE TABLE #TempHeader (
            WorkDate varchar(8), ShopName nvarchar(100), WeekdayName nvarchar(20),
            TotalRevenue decimal(18,2), DayTimeRevenue decimal(18,2), NightTimeRevenue decimal(18,2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            DayTimeDropInBatch int, NightTimeDropInBatch int, TotalGuestCount int,
            BuffetGuestCount int, TotalDropInGuests int, 
            MealBatchCount INT, MealDirectFallBatchCount INT, MealDirectFallRevenue DECIMAL(18,2),
            Night_FreeMeal_Subtotal int, Night_FreeMeal_Amount decimal(18,2), Night_After20_Revenue decimal(18,2)
        );
        INSERT INTO #TempHeader
        EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;

        -- 步骤 2: 将Header数据插入主表
        PRINT N'Step 2: Inserting header data...';
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, 
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDirectFall, NightTimeDropInBatch, 
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue
        )
        SELECT
            @TargetDate, @ShopId, ShopName, WeekdayName,
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDropInBatch, NightTimeDropInBatch,
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue
        FROM #TempHeader;

        DECLARE @ReportID INT = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- 步骤 3: 调用白天时段详情SP
        PRINT N'Step 3: Getting time slot details...';
        CREATE TABLE #TempDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        INSERT INTO #TempDetails
        EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;

        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        )
        SELECT 
            @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        FROM #TempDetails;
        PRINT N'Time slot details inserted.';

        -- 【修正】步骤 4: 调用夜间详情SP，先存入临时表
        PRINT N'Step 4: Getting and inserting night details...';
        CREATE TABLE #TempNightDetails (
            FreeMeal_KPlus INT, FreeMeal_Special INT, FreeMeal_Meituan INT, FreeMeal_Douyin INT, 
            FreeMeal_BatchCount INT, FreeMeal_Revenue DECIMAL(18,2),
            Buyout_BatchCount INT, Buyout_Revenue DECIMAL(18,2), 
            Changyin_BatchCount INT, Changyin_Revenue DECIMAL(18,2), 
            FreeConsumption_BatchCount INT,
            NonPackage_Special INT, NonPackage_Meituan INT, NonPackage_Douyin INT, NonPackage_Others INT
        );
        INSERT INTO #TempNightDetails
        EXEC dbo.usp_GetNightTimeDetails @TargetDate = @TargetDate, @ShopId = @ShopId;

        -- 从临时表插入最终表
        INSERT INTO dbo.FullDailyReport_NightDetails (
            ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, FreeMeal_BatchCount, FreeMeal_Revenue,
            Buyout_BatchCount, Buyout_Revenue, Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount,
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others
        )
        SELECT
            @ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, FreeMeal_BatchCount, FreeMeal_Revenue,
            Buyout_BatchCount, Buyout_Revenue, Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount,
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others
        FROM #TempNightDetails;
        PRINT N'Night details inserted.';

        DROP TABLE #TempHeader;
        DROP TABLE #TempDetails;
        DROP TABLE #TempNightDetails;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Final modular report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;

        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempDetails') IS NOT NULL DROP TABLE #TempDetails;
        IF OBJECT_ID('tempdb..#TempNightDetails') IS NOT NULL DROP TABLE #TempNightDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing final modular report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT 'Stored procedure [usp_RunNormalizedDailyReportJob_V2] (V6 - nested exec fixed) created successfully.';
GO