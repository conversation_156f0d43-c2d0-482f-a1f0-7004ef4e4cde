USE operatedata;
GO

-- ====================================================================
-- 脚本: 创建用于每日自动化执行“完整日报”的包装存储过程 (V3 - 列数修正版)
-- 修正了 #TempFullReport 的列数定义，从87列更正为83列。
-- ====================================================================

IF OBJECT_ID('dbo.usp_RunFullDailyReportJob', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_RunFullDailyReportJob;
GO

CREATE PROCEDURE dbo.usp_RunFullDailyReportJob
    @TargetDate DATE = NULL,
    @ShopId INT = 3
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Full_Daily_Report';

    IF EXISTS (SELECT 1 FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId)
    BEGIN
        DECLARE @SkipMessage NVARCHAR(500) = N'Skipped: Data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' and ShopID ' + CAST(@ShopId AS NVARCHAR) + N' already exists.';
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Skipped', @SkipMessage);
        PRINT @SkipMessage;
        RETURN;
    END

    BEGIN TRANSACTION;

    BEGIN TRY
        -- 步骤 1: 获取并插入 Header 数据 (使用正确的83列定义)
        CREATE TABLE #TempFullReport (
            Col1 NVARCHAR(MAX), Col2 NVARCHAR(MAX), Col3 NVARCHAR(MAX), Col4 DECIMAL(18,2), Col5 DECIMAL(18,2), Col6 INT, Col7 INT, Col8 INT, Col9 INT, Col10 INT, Col11 DECIMAL(18,2), Col12 INT, Col13 INT, Col14 INT, Col15 INT, Col16 INT, Col17 INT, Col18 INT, Col19 INT, Col20 INT, Col21 INT, Col22 INT, Col23 INT, Col24 INT, Col25 INT, Col26 INT, Col27 INT, Col28 INT, Col29 INT, Col30 INT, Col31 INT, Col32 INT, Col33 INT, Col34 INT, Col35 INT, Col36 INT, Col37 INT, Col38 INT, Col39 INT, Col40 INT, Col41 INT, Col42 INT, Col43 INT, Col44 INT, Col45 INT, Col46 INT, Col47 INT, Col48 INT, Col49 INT, Col50 INT, Col51 INT, Col52 INT, Col53 INT, Col54 INT, Col55 INT, Col56 INT, Col57 INT, Col58 INT, Col59 INT, Col60 INT, Col61 INT, Col62 INT, Col63 INT, Col64 INT, Col65 INT, Col66 INT, Col67 INT, Col68 INT, Col69 INT, Col70 INT, Col71 INT, Col72 INT, Col73 INT, Col74 INT, Col75 INT, Col76 INT, Col77 INT, Col78 INT, Col79 INT, Col80 INT, Col81 INT, Col82 INT, Col83 INT
        );
        INSERT INTO #TempFullReport
        EXEC dbo.usp_GenerateFullDailyReport @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;

        DECLARE @ShopName NVARCHAR(50) = (SELECT Col2 FROM #TempFullReport);

        INSERT INTO dbo.FullDailyReport_Header (ReportDate, ShopID, ShopName, Weekday, TotalRevenue, DayTimeRevenue, TotalBatchCount, DayTimeBatchCount, DayTimeDirectFall, MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue)
        SELECT @TargetDate, @ShopId, @ShopName, Col3, Col4, Col5, Col6, Col7, Col8, Col9, Col10, Col11 FROM #TempFullReport;

        DECLARE @ReportID INT = SCOPE_IDENTITY();

        -- 步骤 2: 使用 sp_executesql 安全地执行动态SQL
        DECLARE @SqlStatement NVARCHAR(MAX);
        DECLARE @ParamDefinition NVARCHAR(500);

        SET @ParamDefinition = N'@pReportID INT, @pShopId INT, @pTargetDate DATE';

        SET @SqlStatement = N'
            WITH TimeSlots AS (
                SELECT ti.TimeNo, ti.TimeName, ti.BegTime,
                       DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@pTargetDate AS datetime))) AS SlotStartDateTime,
                       LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@pTargetDate AS datetime))), 1, ''2999-12-31'') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
                FROM dbo.shoptimeinfo AS sti JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo WHERE sti.ShopId = @pShopId
            ),
            TrueDropInData AS (
                SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
                FROM dbo.RmCloseInfo_Test AS rt
                JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
                JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
                WHERE rt.ShopId = @pShopId AND rt.WorkDate = @pTargetDate AND rt.OpenDateTime IS NOT NULL
                  AND rt.Beg_Key <> rt.End_Key
                  AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
                  AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
            )
            INSERT INTO dbo.FullDailyReport_TimeSlotDetails (ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall)
            SELECT @pReportID, ti_main.TimeName, ROW_NUMBER() OVER (ORDER BY ti_main.BegTime), COUNT(CASE WHEN rt.CtNo = 2 THEN 1 ELSE NULL END), COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END), COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END), COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END), COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END), COUNT(rt.InvNo), ISNULL((SELECT COUNT(tdi.InvNo) FROM TrueDropInData AS tdi JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo WHERE ts_beg.BegTime < ti_main.BegTime AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime), 0)
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
            JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
            WHERE rt.ShopId = @pShopId AND rt.WorkDate = @pTargetDate AND rt.OpenDateTime IS NOT NULL
            GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
            ORDER BY ti_main.BegTime;';

        EXEC sp_executesql @SqlStatement, @ParamDefinition, @pReportID = @ReportID, @pShopId = @ShopId, @pTargetDate = @TargetDate;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Full report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

        DROP TABLE #TempFullReport;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing full report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        PRINT @ErrorLogMessage;
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT 'Stored procedure [usp_RunFullDailyReportJob] (V3 - Column Count Corrected) created successfully.';
GO
