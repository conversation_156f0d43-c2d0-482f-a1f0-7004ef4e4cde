USE msdb;
GO

-- ====================================================================
-- 脚本: 创建 SQL Server 代理作业以每日运行“完整版”KTV报告
-- ====================================================================

DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'KTV Daily Full Report Generation';

-- --------------------------------------------------------------------
-- 步骤 1: 如果作业已存在，则先删除，确保脚本可重复执行
-- --------------------------------------------------------------------
SELECT @jobId = job_id FROM msdb.dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    EXEC msdb.dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
    PRINT N'Existing job ''KTV Daily Full Report Generation'' has been deleted.';
END

-- --------------------------------------------------------------------
-- 步骤 2: 创建一个新的作业
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @notify_level_eventlog = 0,
    @description = N'每日自动执行usp_RunFullDailyReportJob存储过程，生成KTV完整版日报表（包含分时段详情），并记录日志。',
    @category_name = N'[Uncategorized (Local)]',
    @owner_login_name = N'sa', -- 确保 'sa' 账户是启用且密码正确的
    @job_id = @jobId OUTPUT;

PRINT N'Job ''KTV Daily Full Report Generation'' created successfully.';

-- --------------------------------------------------------------------
-- 步骤 3: 为作业创建一个执行步骤
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_add_jobstep
    @job_id = @jobId,
    @step_name = N'Run Full Daily Report SP',
    @step_id = 1,
    @cmdexec_success_code = 0,
    @on_success_action = 1, -- 成功后退出并报告成功
    @on_fail_action = 2,    -- 失败后退出并报告失败
    @retry_attempts = 1,    -- 失败后重试1次
    @retry_interval = 5,    -- 失败后等待5分钟再重试
    @subsystem = N'TSQL',
    @command = N'-- 执行包装存储过程，它会自动处理昨天的日期
EXEC operatedata.dbo.usp_RunFullDailyReportJob;',
    @database_name = N'operatedata',
    @flags = 0;

PRINT N'Job step ''Run Full Daily Report SP'' created successfully.';

-- --------------------------------------------------------------------
-- 步骤 4: 为作业创建一个每日执行的计划 (02:30 AM)
-- --------------------------------------------------------------------
DECLARE @schedule_name_full NVARCHAR(128) = N'Daily 02:30 AM (Full Report)';

EXEC msdb.dbo.sp_add_schedule
    @schedule_name = @schedule_name_full,
    @enabled = 1,
    @freq_type = 4, -- 每日
    @freq_interval = 1,
    @freq_subday_type = 1, -- 在指定时间
    @active_start_date = 20250701,
    @active_end_date = 99991231,
    @active_start_time = 23000, -- 02:30:00
    @active_end_time = 235959;

PRINT N'Schedule ''Daily 02:30 AM (Full Report)'' created successfully.';

-- --------------------------------------------------------------------
-- 步骤 5: 将计划附加到作业上
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_attach_schedule
    @job_id = @jobId,
    @schedule_name = @schedule_name_full;

PRINT N'Schedule attached to job successfully.';

-- --------------------------------------------------------------------
-- 步骤 6: 将作业分配给当前服务器
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_add_jobserver
    @job_id = @jobId,
    @server_name = N'(local)';

PRINT N'Job assigned to local server.';
GO
