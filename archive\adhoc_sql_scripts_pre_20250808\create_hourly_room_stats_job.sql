
-- 确保在正确的数据库上下文中执行
USE dbfood;
GO

-- 1. 创建用于存储每小时统计结果的表
PRINT 'Step 1: Creating table RoomStatisticsHourly...';
IF OBJECT_ID('RoomStatisticsHourly', 'U') IS NOT NULL
BEGIN
    PRINT 'Table RoomStatisticsHourly already exists.';
END
ELSE
BEGIN
    CREATE TABLE RoomStatisticsHourly (
        LogID INT IDENTITY(1,1) PRIMARY KEY,
        LogTime DATETIME NOT NULL DEFAULT GETDATE(),
        TotalRoomsBeforeFilter INT, -- 过滤前的总房数（用于参考）
        ValidRoomsCount INT,      -- 过滤掉带B的房间后的有效总房数
        BadRoomsCount INT,        -- 坏房数 (状态为B)
        AvailableRoomsCount INT   -- 可用房数 (有效总房数 - 坏房数)
    );
    PRINT 'Table RoomStatisticsHourly created successfully.';
END
GO

-- 2. 创建核心统计逻辑的存储过程
PRINT 'Step 2: Creating stored procedure usp_LogHourlyRoomStatistics...';
IF OBJECT_ID('usp_LogHourlyRoomStatistics', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE usp_LogHourlyRoomStatistics;
END
GO

CREATE PROCEDURE usp_LogHourlyRoomStatistics
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @TotalRoomsBeforeFilter INT;
    DECLARE @ValidRoomsCount INT;
    DECLARE @BadRoomsCount INT;

    -- 计算过滤前的总房数
    SELECT @TotalRoomsBeforeFilter = COUNT(*) FROM dbo.ROOM;

    -- 在排除了房间名带B的房间后，进行统计
    SELECT 
        @ValidRoomsCount = COUNT(*),
        @BadRoomsCount = SUM(CASE WHEN RmStatus = 'B' THEN 1 ELSE 0 END)
    FROM 
        dbo.ROOM
    WHERE 
        RmNo NOT LIKE '%B';

    -- 插入统计结果到新表
    INSERT INTO dbo.RoomStatisticsHourly (
        TotalRoomsBeforeFilter,
        ValidRoomsCount,
        BadRoomsCount,
        AvailableRoomsCount
    ) 
    VALUES (
        @TotalRoomsBeforeFilter,
        ISNULL(@ValidRoomsCount, 0),
        ISNULL(@BadRoomsCount, 0),
        ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0)
    );

    PRINT 'Hourly room statistics logged successfully.';
END
GO

PRINT 'Stored procedure usp_LogHourlyRoomStatistics created successfully.';
GO

-- 3. 创建每小时执行的 SQL Server Agent 作业
-- 注意: 作业相关的操作需要在 msdb 数据库中执行
USE msdb;
GO

PRINT 'Step 3: Creating SQL Server Agent job LogRoomStats_EveryHour...';

DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'LogRoomStats_EveryHour';

-- 如果作业已存在，则先删除，确保脚本可重复执行
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    EXEC dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
    PRINT 'Existing job LogRoomStats_EveryHour has been deleted.';
END

-- 添加作业
EXEC dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @description = N'每小时统计一次KTV房间状态（总数、坏房、可用数）并记录到RoomStatisticsHourly表中。';

-- 添加作业步骤，执行我们的存储过程
EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'Execute Room Statistics Logging',
    @subsystem = N'TSQL',
    @command = N'EXEC dbfood.dbo.usp_LogHourlyRoomStatistics;',
    @database_name = N'dbfood';

-- 创建执行计划（每小时）
EXEC dbo.sp_add_schedule
    @schedule_name = N'Run Every Hour',
    @freq_type = 4, -- 每天
    @freq_interval = 1,
    @freq_subday_type = 8, -- 小时
    @freq_subday_interval = 1; -- 每1小时

-- 将计划附加到作业
EXEC dbo.sp_attach_schedule
    @job_name = @jobName,
    @schedule_name = N'Run Every Hour';

-- 将作业分配给当前服务器
EXEC dbo.sp_add_jobserver
    @job_name = @jobName,
    @server_name = N'(local)';

PRINT 'Job LogRoomStats_EveryHour created and scheduled successfully.';
GO
