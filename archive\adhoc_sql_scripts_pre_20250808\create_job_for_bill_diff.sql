
USE msdb;
GO

-- 变量定义
DECLARE @job_name NVARCHAR(128) = N'CalculateBillDifferences_EveryMinute';
DECLARE @job_id UNIQUEIDENTIFIER;

-- 1. 如果存在同名作业，则先删除
IF EXISTS (SELECT 1 FROM dbo.sysjobs WHERE name = @job_name)
BEGIN
    SELECT @job_id = job_id FROM dbo.sysjobs WHERE name = @job_name;
    EXEC dbo.sp_delete_job @job_id = @job_id, @delete_unused_schedule = 1;
    PRINT 'Existing job "' + @job_name + '" has been deleted.';
END

-- 2. 创建作业
EXEC dbo.sp_add_job
    @job_name = @job_name,
    @enabled = 1,
    @description = N'每分钟执行一次，计算并存储微信支付与系统账单的差额。Executes every minute to calculate and store the difference between WeChat Pay and system bills.';

-- 3. 添加作业步骤
EXEC dbo.sp_add_jobstep
    @job_name = @job_name,
    @step_name = N'Execute_usp_CalculateAndStoreBillDifferences',
    @subsystem = N'TSQL',
    @command = N'EXEC dbo.usp_CalculateAndStoreBillDifferences;',
    @database_name = N'dbfood';

-- 4. 创建执行计划
EXEC dbo.sp_add_schedule
    @schedule_name = N'Run_Every_1_Minute',
    @freq_type = 4, -- 每天
    @freq_interval = 1, -- 每1天
    @freq_subday_type = 4, -- 分钟
    @freq_subday_interval = 1; -- 每1分钟

-- 5. 将计划附加到作业
EXEC dbo.sp_attach_schedule
    @job_name = @job_name,
    @schedule_name = N'Run_Every_1_Minute';

-- 6. 将作业分配给当前服务器
EXEC dbo.sp_add_jobserver
    @job_name = @job_name,
    @server_name = N'(local)'; -- (local) 表示当前服务器

PRINT 'Job "' + @job_name + '" has been created successfully and is scheduled to run every minute.';
GO
