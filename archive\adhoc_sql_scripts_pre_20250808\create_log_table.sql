USE operatedata;
GO

-- ====================================================================
-- 脚本: 创建用于所有自动化任务的通用日志表
-- ====================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='JobExecutionLog' and xtype='U')
BEGIN
    CREATE TABLE dbo.JobExecutionLog (
        LogID INT IDENTITY(1,1) PRIMARY KEY,
        JobName NVARCHAR(255) NOT NULL, -- 任务名称, e.g., 'KTV_Simplified_Report', 'KTV_Full_Report'
        ExecutionTime DATETIME DEFAULT GETDATE(),
        ReportDate DATE NOT NULL,
        [Status] NVARCHAR(50) NOT NULL, -- 'Success', 'Failure', 'Skipped'
        [Message] NVARCHAR(MAX) NULL -- 记录详细信息或错误消息
    );
    PRINT 'Table [JobExecutionLog] created successfully.';
END
ELSE
BEGIN
    PRINT 'Table [JobExecutionLog] already exists.';
END
GO
