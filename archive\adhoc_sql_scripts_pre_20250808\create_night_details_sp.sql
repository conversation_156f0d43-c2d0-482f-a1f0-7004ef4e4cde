USE operatedata;
GO

-- ====================================================================
-- 脚本: 创建独立的、获取夜间详情的存储过程 (三表联动方案第二步)
-- ====================================================================

IF OBJECT_ID('dbo.usp_GetNightTimeDetails', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GetNightTimeDetails;
GO

CREATE PROCEDURE dbo.usp_GetNightTimeDetails
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;

    -- 创建一个临时表来接收 usp_GenerateSimplifiedDailyReport 的完整输出
    CREATE TABLE #TempSimplifiedReport (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2)
    );

    -- 执行现有的简化版报表存储过程
    INSERT INTO #TempSimplifiedReport
    EXEC dbo.usp_GenerateSimplifiedDailyReport @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;

    -- 从临时表中只选择我们夜间详情表需要的字段并输出
    SELECT
        FreeMeal_KPlus,
        FreeMeal_Special,
        FreeMeal_Meituan,
        FreeMeal_Douyin,
        FreeMeal_BatchCount,
        FreeMeal_Revenue,
        Buyout_BatchCount,
        Buyout_Revenue,
        Changyin_BatchCount,
        Changyin_Revenue,
        FreeConsumption_BatchCount,
        NonPackage_Special,
        NonPackage_Meituan,
        NonPackage_Douyin,
        NonPackage_Others
    FROM #TempSimplifiedReport;

    -- 清理临时表
    DROP TABLE #TempSimplifiedReport;

END
GO

PRINT 'Stored procedure [usp_GetNightTimeDetails] created successfully.';
GO
