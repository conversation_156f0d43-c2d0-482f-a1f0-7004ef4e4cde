USE operatedata;
GO

-- ====================================================================
-- 脚本: 创建夜间消费详情表 (三表联动方案第一步)
-- ====================================================================

IF OBJECT_ID('dbo.FullDailyReport_NightDetails', 'U') IS NOT NULL
    DROP TABLE dbo.FullDailyReport_NightDetails;
GO

CREATE TABLE dbo.FullDailyReport_NightDetails (
    NightDetailID INT IDENTITY(1,1) PRIMARY KEY,
    ReportID INT NOT NULL, -- 外键，关联到主表
    
    -- K+自由餐分类 (来自 usp_GenerateSimplifiedDailyReport)
    FreeMeal_KPlus INT,
    FreeMeal_Special INT,
    FreeMeal_Meituan INT,
    FreeMeal_Douyin INT,
    FreeMeal_BatchCount INT,
    FreeMeal_Revenue DECIMAL(18, 2),

    -- 20点后进场套餐 - 买断
    Buyout_BatchCount INT,
    Buyout_Revenue DECIMAL(18, 2),

    -- 20点后进场套餐 - 畅饮
    Changyin_BatchCount INT,
    Changyin_Revenue DECIMAL(18, 2),

    -- 20点后进场套餐 - 自由消
    FreeConsumption_BatchCount INT,

    -- 20点后非套餐分类
    NonPackage_Special INT,
    NonPackage_Meituan INT,
    NonPackage_Douyin INT,
    NonPackage_Others INT,

    -- 添加唯一约束，确保一个日报只有一个夜间详情
    CONSTRAINT UQ_NightDetails_ReportID UNIQUE (ReportID),

    -- 添加外键约束
    CONSTRAINT FK_NightDetails_ReportHeader FOREIGN KEY (ReportID)
        REFERENCES dbo.FullDailyReport_Header(ReportID)
        ON DELETE CASCADE
);
GO

PRINT 'Table [FullDailyReport_NightDetails] created successfully.';
GO
