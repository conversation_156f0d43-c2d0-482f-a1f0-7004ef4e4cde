USE operatedata;
GO

-- ====================================================================
-- 脚本: 创建范式化的KTV完整日报表结构
-- ====================================================================

-- --------------------------------------------------------------------
-- 表 1: FullDailyReport_Header (报表主表)
-- 存放每日的总体数据和非时段相关的汇总信息。
-- --------------------------------------------------------------------
IF OBJECT_ID('dbo.FullDailyReport_Header', 'U') IS NOT NULL
    DROP TABLE dbo.FullDailyReport_Header;
GO

CREATE TABLE dbo.FullDailyReport_Header (
    ReportID INT IDENTITY(1,1) PRIMARY KEY,
    ReportDate DATE NOT NULL,
    ShopID INT NOT NULL,
    ShopName NVARCHAR(50),
    Weekday NVARCHAR(10),
    
    -- 从 usp_GenerateFullDailyReport 获取的汇总字段
    TotalRevenue DECIMAL(18, 2),
    DayTimeRevenue DECIMAL(18, 2),
    TotalBatchCount INT,
    DayTimeBatchCount INT,
    DayTimeDirectFall INT,
    MealBatchCount INT, -- 餐别总批次
    MealDirectFallBatchCount INT, -- 餐别直落总批次
    MealDirectFallRevenue DECIMAL(18, 2), -- 餐别直落总消费

    -- 添加一个唯一约束，防止同一门店同一天的数据重复
    CONSTRAINT UQ_Report_Shop_Date UNIQUE (ReportDate, ShopID)
);
GO

PRINT 'Table [FullDailyReport_Header] created successfully.';
GO

-- --------------------------------------------------------------------
-- 表 2: FullDailyReport_TimeSlotDetails (时间段详情表)
-- 存放每个时间段的详细数据。
-- --------------------------------------------------------------------
IF OBJECT_ID('dbo.FullDailyReport_TimeSlotDetails', 'U') IS NOT NULL
    DROP TABLE dbo.FullDailyReport_TimeSlotDetails;
GO

CREATE TABLE dbo.FullDailyReport_TimeSlotDetails (
    SlotDetailID INT IDENTITY(1,1) PRIMARY KEY,
    ReportID INT NOT NULL, -- 外键，关联到主表
    
    -- 从 zhiluo.sql 的逻辑获取的字段
    TimeSlotName NVARCHAR(50) NOT NULL, -- 时间段名称, e.g., '10:50-13:50'
    TimeSlotOrder INT, -- 时间段的顺序，用于排序
    KPlus_Count INT,
    Special_Count INT,
    Meituan_Count INT,
    Douyin_Count INT,
    RoomFee_Count INT, -- 房费批数
    Subtotal_Count INT,
    PreviousSlot_DirectFall INT, -- 上一档直落批次

    -- 添加外键约束
    CONSTRAINT FK_TimeSlotDetails_ReportHeader FOREIGN KEY (ReportID)
        REFERENCES dbo.FullDailyReport_Header(ReportID)
        ON DELETE CASCADE -- 如果主报告被删除，相关的时段详情也一并删除
);
GO

PRINT 'Table [FullDailyReport_TimeSlotDetails] created successfully.';
GO
