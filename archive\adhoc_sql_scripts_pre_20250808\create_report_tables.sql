
-- SQL Script to Create Dynamic Report Tables
-- Database: operatedata

-- 检查并删除旧表，确保脚本可以重复执行
IF OBJECT_ID('dbo.DynamicReport_TimeSlotDetails', 'U') IS NOT NULL
    DROP TABLE dbo.DynamicReport_TimeSlotDetails;
GO

IF OBJECT_ID('dbo.DynamicReport_Header', 'U') IS NOT NULL
    DROP TABLE dbo.DynamicReport_Header;
GO

-- 表1：总览表头 (DynamicReport_Header)
PRINT 'Creating table: DynamicReport_Header';
CREATE TABLE DynamicReport_Header (
    ReportID INT IDENTITY(1,1) PRIMARY KEY,
    ReportDate DATE NOT NULL,
    ShopID INT NOT NULL,
    Weekday NVARCHAR(10),
    TotalRevenue DECIMAL(18, 2),
    CONSTRAINT UQ_DynamicReport_Header UNIQUE (ReportDate, ShopID) -- 确保每个店铺每天只有一条总览记录
);
GO

-- 表2：时段详情 (DynamicReport_TimeSlotDetails)
PRINT 'Creating table: DynamicReport_TimeSlotDetails';
CREATE TABLE DynamicReport_TimeSlotDetails (
    DetailID INT IDENTITY(1,1) PRIMARY KEY,
    HeaderReportID INT NOT NULL,
    TimeSlotName NVARCHAR(50),
    BookedRooms INT,
    BookedGuests INT,
    OccupiedRooms INT,
    OccupiedGuests INT,
    OccupancyRate DECIMAL(5, 2), -- 存储为小数, 如 0.85 代表 85%
    CONSTRAINT FK_DynamicReport_TimeSlotDetails_Header FOREIGN KEY (HeaderReportID)
        REFERENCES DynamicReport_Header(ReportID)
        ON DELETE CASCADE -- 级联删除，当删除主表头记录时，自动删除所有关联的详情记录
);
GO

PRINT 'Tables created successfully.';

