USE operatedata;
GO

-- ====================================================================
-- 脚本: 创建 KTV_Simplified_Daily_Report 表
-- 这是根据 usp_GenerateSimplifiedDailyReport 存储过程的输出推断出的结构
-- ====================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='KTV_Simplified_Daily_Report' and xtype='U')
BEGIN
    CREATE TABLE dbo.KTV_Simplified_Daily_Report (
        ReportID INT IDENTITY(1,1) PRIMARY KEY,
        ReportDate DATE NOT NULL,
        ShopName NVARCHAR(100) NOT NULL,
        Weekday NVARCHAR(20),
        
        -- 营收总览
        TotalRevenue DECIMAL(18, 2),
        DayTimeRevenue DECIMAL(18, 2),
        NightTimeRevenue DECIMAL(18, 2),
        
        -- 带客总览
        TotalBatchCount INT,
        DayTimeBatchCount INT,
        NightTimeBatchCount INT,
        
        -- 夜间档 - K+自由餐分类
        FreeMeal_KPlus INT,
        FreeMeal_Special INT,
        FreeMeal_Meituan INT,
        FreeMeal_Douyin INT,
        FreeMeal_BatchCount INT,
        FreeMeal_Revenue DECIMAL(18, 2),
        
        -- 夜间档 - 20点后进场套餐
        Buyout_BatchCount INT,
        Buyout_Revenue DECIMAL(18, 2),
        Changyin_BatchCount INT,
        Changyin_Revenue DECIMAL(18, 2),
        FreeConsumption_BatchCount INT,
        
        -- 夜间档 - 20点后非套餐分类
        NonPackage_Special INT,
        NonPackage_Meituan INT,
        NonPackage_Douyin INT,
        NonPackage_Others INT,
        
        -- 验证字段
        Night_Verify_BatchCount INT,
        Night_Verify_Revenue DECIMAL(18, 2),

        -- 添加一个时间戳，记录数据插入的时间
        CreatedAt DATETIME DEFAULT GETDATE()
    );

    -- 为常用查询字段创建索引
    CREATE INDEX IX_KTV_Simplified_Daily_Report_DateShop ON dbo.KTV_Simplified_Daily_Report (ReportDate, ShopName);

    PRINT 'Table [KTV_Simplified_Daily_Report] created successfully.';
END
ELSE
BEGIN
    PRINT 'Table [KTV_Simplified_Daily_Report] already exists.';
END
GO
