USE msdb;
GO

-- ====================================================================
-- 脚本: 创建 SQL Server 代理作业以每日运行KTV报告
-- ====================================================================

DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'KTV Daily Simplified Report Generation';

-- --------------------------------------------------------------------
-- 步骤 1: 如果作业已存在，则先删除，确保脚本可重复执行
-- --------------------------------------------------------------------
SELECT @jobId = job_id FROM msdb.dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    EXEC msdb.dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
    PRINT N'Existing job ''KTV Daily Simplified Report Generation'' has been deleted.';
END

-- --------------------------------------------------------------------
-- 步骤 2: 创建一个新的作业
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @notify_level_eventlog = 0,
    @notify_level_email = 0,
    @notify_level_netsend = 0,
    @notify_level_page = 0,
    @delete_level = 0,
    @description = N'每日自动执行usp_RunDailyReportJob存储过程，生成KTV简化版日报表，并记录日志。',
    @category_name = N'[Uncategorized (Local)]',
    @owner_login_name = N'sa', -- 确保 'sa' 账户是启用且密码正确的
    @job_id = @jobId OUTPUT;

PRINT N'Job ''KTV Daily Simplified Report Generation'' created successfully.';

-- --------------------------------------------------------------------
-- 步骤 3: 为作业创建一个执行步骤
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_add_jobstep
    @job_id = @jobId,
    @step_name = N'Run Daily Report SP',
    @step_id = 1,
    @cmdexec_success_code = 0,
    @on_success_action = 1, -- 成功后退出并报告成功
    @on_success_step_id = 0,
    @on_fail_action = 2,    -- 失败后退出并报告失败
    @on_fail_step_id = 0,
    @retry_attempts = 1,    -- 失败后重试1次
    @retry_interval = 5,    -- 失败后等待5分钟再重试
    @os_run_priority = 0,
    @subsystem = N'TSQL',
    @command = N'-- 执行包装存储过程，不带参数，它会自动处理日期
EXEC operatedata.dbo.usp_RunDailyReportJob;',
    @database_name = N'operatedata', -- 在operatedata数据库的上下文中运行
    @flags = 0;

PRINT N'Job step ''Run Daily Report SP'' created successfully.';

-- --------------------------------------------------------------------
-- 步骤 4: 为作业创建一个每日执行的计划
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_add_schedule
    @schedule_name = N'Daily 02:00 AM',
    @enabled = 1,
    @freq_type = 4, -- 4 = 每日
    @freq_interval = 1, -- 每 1 天
    @freq_subday_type = 1, -- 1 = 在指定时间
    @freq_subday_interval = 0,
    @freq_relative_interval = 0,
    @freq_recurrence_factor = 1,
    @active_start_date = 20250701, -- 您可以根据需要修改开始日期
    @active_end_date = 99991231,
    @active_start_time = 20000, -- 02:00:00
    @active_end_time = 235959;

PRINT N'Schedule ''Daily 02:00 AM'' created successfully.';

-- --------------------------------------------------------------------
-- 步骤 5: 将计划附加到作业上
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_attach_schedule
    @job_id = @jobId,
    @schedule_name = N'Daily 02:00 AM';

PRINT N'Schedule attached to job successfully.';

-- --------------------------------------------------------------------
-- 步骤 6: 将作业分配给当前服务器
-- --------------------------------------------------------------------
EXEC msdb.dbo.sp_add_jobserver
    @job_id = @jobId,
    @server_name = N'(local)';

PRINT N'Job assigned to local server.';
GO
