
-- ===================================================================
-- 脚本: create_sync_book_data_proc.sql
-- 功能: 创建同步预订数据(bookcacheinfo, bookhistory)的存储过程
-- ===================================================================

-- 切换到 operatedata 数据库
USE operatedata;
GO

IF OBJECT_ID('usp_Sync_RMS_DailyBookData', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE usp_Sync_RMS_DailyBookData;
END
GO

CREATE PROCEDURE usp_Sync_RMS_DailyBookData
AS
BEGIN
    SET NOCOUNT ON;
    PRINT 'Starting synchronization for Book Data...';

    -- 定义要同步的自然日 (昨天)
    DECLARE @TargetNaturalDate DATE = GETDATE() - 1;
    PRINT 'Target Natural Date: ' + CONVERT(VARCHAR, @TargetNaturalDate);

    BEGIN TRY
        -- 3. 同步 bookcacheinfo 表
        PRINT 'Syncing bookcacheinfo...';
        MERGE INTO rms2019.dbo.bookcacheinfo AS Target
        USING (
            SELECT * FROM cloudRms2019.rms2019.dbo.bookcacheinfo
            WHERE CAST(BookDateTime AS DATE) = @TargetNaturalDate
        ) AS Source
        ON Target.Ikey = Source.Ikey
        WHEN MATCHED THEN
            UPDATE SET
                Target.BookStatus = Source.BookStatus,
                Target.CheckinStatus = Source.CheckinStatus,
                Target.Invno = Source.Invno,
                Target.RmNo = Source.RmNo
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, BookUserName, BookDateTime, Invno, Openmemory, OrderUserID, OrderUserName, RmNo, Val1, FromRmNo, IsBirthday, Remark)
            VALUES (Source.Ikey, Source.BookNo, Source.ShopId, Source.CustKey, Source.CustName, Source.CustTel, Source.ComeDate, Source.ComeTime, Source.Beg_Key, Source.Beg_Name, Source.End_Key, Source.End_Name, Source.Numbers, Source.RtNo, Source.RtName, Source.CtNo, Source.CtName, Source.PtNo, Source.PtName, Source.BookMemory, Source.BookStatus, Source.CheckinStatus, Source.BookShopId, Source.BookUserId, Source.BookUserName, Source.BookDateTime, Source.Invno, Source.Openmemory, Source.OrderUserID, Source.OrderUserName, Source.RmNo, Source.Val1, Source.FromRmNo, Source.IsBirthday, Source.Remark);
        PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows affected in bookcacheinfo.';

        -- 4. 同步 bookhistory 表
        PRINT 'Syncing bookhistory...';
        MERGE INTO rms2019.dbo.bookhistory AS Target
        USING (
            SELECT * FROM cloudRms2019.rms2019.dbo.bookhistory
            WHERE CAST(BookDateTime AS DATE) = @TargetNaturalDate
        ) AS Source
        ON Target.Ikey = Source.Ikey
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, BookUserName, BookDateTime, Invno, Openmemory, OrderUserID, OrderUserName, RmNo, Val1, FromRmNo, IsBirthday, Remark)
            VALUES (Source.Ikey, Source.BookNo, Source.ShopId, Source.CustKey, Source.CustName, Source.CustTel, Source.ComeDate, Source.ComeTime, Source.Beg_Key, Source.Beg_Name, Source.End_Key, Source.End_Name, Source.Numbers, Source.RtNo, Source.RtName, Source.CtNo, Source.CtName, Source.PtNo, Source.PtName, Source.BookMemory, Source.BookStatus, Source.CheckinStatus, Source.BookShopId, Source.BookUserId, Source.BookUserName, Source.BookDateTime, Source.Invno, Source.Openmemory, Source.OrderUserID, Source.OrderUserName, Source.RmNo, Source.Val1, Source.FromRmNo, Source.IsBirthday, Source.Remark);
        PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows affected in bookhistory.';

        PRINT 'Synchronization for Book Data completed successfully.';
    END TRY
    BEGIN CATCH
        PRINT 'An error occurred during synchronization:' + ERROR_MESSAGE();
    END CATCH
END
GO
