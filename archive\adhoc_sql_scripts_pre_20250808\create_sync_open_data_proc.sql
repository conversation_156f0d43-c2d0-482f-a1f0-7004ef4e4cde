
-- ===================================================================
-- 脚本: create_sync_open_data_proc.sql
-- 功能: 创建同步开台数据(opencacheinfo, openhistory)的存储过程
-- ===================================================================

-- 切换到 operatedata 数据库，因为同步过程属于数据运营的一部分
USE operatedata;
GO

IF OBJECT_ID('usp_Sync_RMS_DailyOpenData', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE usp_Sync_RMS_DailyOpenData;
END
GO

CREATE PROCEDURE usp_Sync_RMS_DailyOpenData
AS
BEGIN
    SET NOCOUNT ON;
    PRINT 'Starting synchronization for Open Data...';

    -- 1. 定义要同步的“营业日” (昨天)
    -- 以早上9点为日切点
    DECLARE @TargetBusinessDate DATE = CAST(DATEADD(hour, -9, GETDATE() - 1) AS DATE);
    PRINT 'Target Business Date: ' + CONVERT(VARCHAR, @TargetBusinessDate);

    -- 2. 定义源数据的时间范围
    DECLARE @StartDate DATETIME = DATEADD(hour, 9, CAST(@TargetBusinessDate AS DATETIME));
    DECLARE @EndDate DATETIME = DATEADD(hour, 9, CAST(DATEADD(day, 1, @TargetBusinessDate) AS DATETIME));
    PRINT 'Syncing data from ' + CONVERT(VARCHAR, @StartDate, 20) + ' to ' + CONVERT(VARCHAR, @EndDate, 20);

    BEGIN TRY
        -- 3. 同步 opencacheinfo 表
        PRINT 'Syncing opencacheinfo...';
        MERGE INTO rms2019.dbo.opencacheinfo AS Target
        USING (
            SELECT * FROM cloudRms2019.rms2019.dbo.opencacheinfo
            WHERE BookDateTime >= @StartDate AND BookDateTime < @EndDate
        ) AS Source
        ON Target.Ikey = Source.Ikey
        WHEN MATCHED THEN
            UPDATE SET
                Target.CheckinStatus = Source.CheckinStatus,
                Target.Invno = Source.Invno,
                Target.RmNo = Source.RmNo,
                Target.WorkDate = @TargetBusinessDate -- 更新营业日
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, BookUserName, BookDateTime, Invno, Openmemory, OrderUserID, OrderUserName, RmNo, Val1, FromRmNo, IsBirthday, Remark, WorkDate)
            VALUES (Source.Ikey, Source.BookNo, Source.ShopId, Source.CustKey, Source.CustName, Source.CustTel, Source.ComeDate, Source.ComeTime, Source.Beg_Key, Source.Beg_Name, Source.End_Key, Source.End_Name, Source.Numbers, Source.RtNo, Source.RtName, Source.CtNo, Source.CtName, Source.PtNo, Source.PtName, Source.BookMemory, Source.BookStatus, Source.CheckinStatus, Source.BookShopId, Source.BookUserId, Source.BookUserName, Source.BookDateTime, Source.Invno, Source.Openmemory, Source.OrderUserID, Source.OrderUserName, Source.RmNo, Source.Val1, Source.FromRmNo, Source.IsBirthday, Source.Remark, @TargetBusinessDate);
        PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows affected in opencacheinfo.';

        -- 4. 同步 openhistory 表
        PRINT 'Syncing openhistory...';
        MERGE INTO rms2019.dbo.openhistory AS Target
        USING (
            SELECT * FROM cloudRms2019.rms2019.dbo.openhistory
            WHERE BookDateTime >= @StartDate AND BookDateTime < @EndDate
        ) AS Source
        ON Target.Ikey = Source.Ikey
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, BookUserName, BookDateTime, Invno, Openmemory, OrderUserID, OrderUserName, RmNo, Val1, FromRmNo, IsBirthday, Remark, WorkDate)
            VALUES (Source.Ikey, Source.BookNo, Source.ShopId, Source.CustKey, Source.CustName, Source.CustTel, Source.ComeDate, Source.ComeTime, Source.Beg_Key, Source.Beg_Name, Source.End_Key, Source.End_Name, Source.Numbers, Source.RtNo, Source.RtName, Source.CtNo, Source.CtName, Source.PtNo, Source.PtName, Source.BookMemory, Source.BookStatus, Source.CheckinStatus, Source.BookShopId, Source.BookUserId, Source.BookUserName, Source.BookDateTime, Source.Invno, Source.Openmemory, Source.OrderUserID, Source.OrderUserName, Source.RmNo, Source.Val1, Source.FromRmNo, Source.IsBirthday, Source.Remark, @TargetBusinessDate);
        PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows affected in openhistory.';

        PRINT 'Synchronization for Open Data completed successfully.';
    END TRY
    BEGIN CATCH
        PRINT 'An error occurred during synchronization:' + ERROR_MESSAGE();
        -- 可以选择在这里记录错误日志
    END CATCH
END
GO
