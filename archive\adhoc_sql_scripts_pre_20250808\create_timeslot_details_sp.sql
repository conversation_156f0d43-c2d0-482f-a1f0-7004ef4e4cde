USE operatedata;
GO

-- ====================================================================
-- 脚本: 创建一个独立的、获取分时段详情的存储过程
-- 模块化重构第一步
-- ====================================================================

IF OBJECT_ID('dbo.usp_GetTimeSlotDetails_WithDirectFall', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GetTimeSlotDetails_WithDirectFall;
GO

CREATE PROCEDURE dbo.usp_GetTimeSlotDetails_WithDirectFall
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;

    -- 使用 sp_executesql 来安全地执行动态SQL，确保兼容性
    DECLARE @SqlStatement NVARCHAR(MAX);
    DECLARE @ParamDefinition NVARCHAR(500);

    SET @ParamDefinition = N'@pShopId INT, @pTargetDate DATE';

    -- 核心逻辑完全基于我们之前验证过的 zhiluo.sql
    SET @SqlStatement = N'
        WITH TimeSlots AS (
            SELECT ti.TimeNo, ti.TimeName, ti.BegTime,
                   DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@pTargetDate AS datetime))) AS SlotStartDateTime,
                   LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@pTargetDate AS datetime))), 1, ''2999-12-31'') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
            FROM dbo.shoptimeinfo AS sti JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo WHERE sti.ShopId = @pShopId
        ),
        TrueDropInData AS (
            SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
            JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
            WHERE rt.ShopId = @pShopId AND rt.WorkDate = @pTargetDate AND rt.OpenDateTime IS NOT NULL
              AND rt.Beg_Key <> rt.End_Key
              AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
              AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
        )
        SELECT
            ti_main.TimeName AS TimeSlotName,
            ROW_NUMBER() OVER (ORDER BY ti_main.BegTime) AS TimeSlotOrder,
            COUNT(CASE WHEN rt.CtNo = 2 THEN 1 ELSE NULL END) AS KPlus_Count,
            COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS Special_Count,
            COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS Meituan_Count,
            COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS Douyin_Count,
            COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS RoomFee_Count,
            COUNT(rt.InvNo) AS Subtotal_Count,
            ISNULL((SELECT COUNT(tdi.InvNo) FROM TrueDropInData AS tdi JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo WHERE ts_beg.BegTime < ti_main.BegTime AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime), 0) AS PreviousSlot_DirectFall
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
        JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
        WHERE rt.ShopId = @pShopId AND rt.WorkDate = @pTargetDate AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
        ORDER BY ti_main.BegTime;';

    -- 执行查询
    EXEC sp_executesql @SqlStatement, @ParamDefinition, @pShopId = @ShopId, @pTargetDate = @TargetDate;

END
GO

PRINT 'Stored procedure [usp_GetTimeSlotDetails_WithDirectFall] created successfully.';
GO
