
-- 调试脚本：直接验证远程数据源
PRINT N'--- 步骤 1: 验证数据是否存在 ---';

-- 查询1：验证 bookcacheinfo
PRINT N'查询 [cloudRms2019].rms2019.dbo.bookcacheinfo...';
SELECT 
    COUNT(*) AS RecordCount,
    'bookcacheinfo' AS TableName
FROM 
    OPENQUERY([cloudRms2019], 'SELECT * FROM rms2019.dbo.bookcacheinfo WHERE ShopID = 11 AND ComeDate = ''20250731''');

-- 查询2：验证 bookhistory
PRINT N'查询 [cloudRms2019].rms2019.dbo.bookhistory...';
SELECT 
    COUNT(*) AS RecordCount,
    'bookhistory' AS TableName
FROM 
    OPENQUERY([cloudRms2019], 'SELECT * FROM rms2019.dbo.bookhistory WHERE ShopID = 11 AND ComeDate = ''20250731''');

-- 查询3：验证 opencacheinfo
PRINT N'查询 [cloudRms2019].rms2019.dbo.opencacheinfo...';
SELECT 
    COUNT(*) AS RecordCount,
    'opencacheinfo' AS TableName
FROM 
    OPENQUERY([cloudRms2019], 'SELECT * FROM rms2019.dbo.opencacheinfo WHERE ShopID = 11 AND ComeDate = ''20250731''');

-- 查询4：验证 openhistory
PRINT N'查询 [cloudRms2019].rms2019.dbo.openhistory...';
SELECT 
    COUNT(*) AS RecordCount,
    'openhistory' AS TableName
FROM 
    OPENQUERY([cloudRms2019], 'SELECT * FROM rms2019.dbo.openhistory WHERE ShopID = 11 AND ComeDate = ''20250731''');


PRINT N'
--- 步骤 2: 查看实际数据格式 ---';

-- 查询5：查看 opencacheinfo 的 ComeDate 格式
PRINT N'查看 opencacheinfo 的 ComeDate 实际格式 (TOP 5)...';
SELECT TOP 5 
    ComeDate,
    ComeTime
FROM 
    OPENQUERY([cloudRms2019], 'SELECT TOP 5 ComeDate, ComeTime FROM rms2019.dbo.opencacheinfo WHERE ShopID = 11 ORDER BY BookDateTime DESC');

-- 查询6：查看 bookcacheinfo 的 ComeDate 格式
PRINT N'查看 bookcacheinfo 的 ComeDate 实际格式 (TOP 5)...';
SELECT TOP 5 
    ComeDate,
    ComeTime
FROM 
    OPENQUERY([cloudRms2019], 'SELECT TOP 5 ComeDate, ComeTime FROM rms2019.dbo.bookcacheinfo WHERE ShopID = 11 ORDER BY BookDateTime DESC');
GO
