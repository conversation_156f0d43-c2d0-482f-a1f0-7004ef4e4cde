-- ====================================================================
-- 部署可重复执行的存储过程
-- 描述：更新usp_RunDailyReportJob和usp_RunNormalizedDailyReportJob_V2
--         支持重复执行（先删除再插入）
-- 执行环境：SQL Server 192.168.2.5, operatedata数据库
-- 创建时间: 2025-07-25
-- ====================================================================

-- 连接到目标数据库
USE OperateData;
GO

-- 设置执行环境
SET NOCOUNT ON;
SET QUOTED_IDENTIFIER ON;
GO

PRINT N'==========================================';
PRINT N'开始部署可重复执行的存储过程';
PRINT N'==========================================';

-- ====================================================================
-- 第一部分：更新 usp_RunDailyReportJob
-- ====================================================================

PRINT N'正在更新 usp_RunDailyReportJob...';

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_RunDailyReportJob', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_RunDailyReportJob;
    PRINT N'已删除旧的 usp_RunDailyReportJob';
END
GO

-- 创建新的可重复执行版本
CREATE PROCEDURE dbo.usp_RunDailyReportJob
    @TargetDate DATE = NULL, -- 允许手动指定日期进行测试
    @ShopId INT = 3, -- 将ShopId作为参数
    @ForceReRun BIT = 0 -- 新增参数：强制重新执行（删除后重新插入）
AS
BEGIN
    SET NOCOUNT ON;

    -- 如果未指定日期，则默认为昨天
    IF @TargetDate IS NULL
    BEGIN
        SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);
    END

    -- 获取门店名称
    DECLARE @ShopName NVARCHAR(100);
    SELECT @ShopName = ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId;
    
    IF @ShopName IS NULL
    BEGIN
        RAISERROR(N'无效的门店ID: %d', 16, 1, @ShopId);
        RETURN;
    END

    -- 检查当天的数据是否已经存在
    IF EXISTS (SELECT 1 FROM dbo.KTV_Simplified_Daily_Report WHERE ReportDate = @TargetDate AND ShopName = @ShopName)
    BEGIN
        IF @ForceReRun = 1
        BEGIN
            -- 强制重新执行：删除现有数据
            DELETE FROM dbo.KTV_Simplified_Daily_Report 
            WHERE ReportDate = @TargetDate AND ShopName = @ShopName;
            
            DECLARE @DeleteMessage NVARCHAR(500) = N'Deleted: Existing data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' has been deleted for re-run.';
            INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])
            VALUES (N'KTV_Daily_Report', @TargetDate, N'Deleted', @DeleteMessage);
            PRINT @DeleteMessage;
        END
        ELSE
        BEGIN
            -- 不强制重新执行：跳过
            DECLARE @SkipMessage NVARCHAR(500) = N'Skipped: Data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' already exists. Use @ForceReRun=1 to re-run.';
            INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])
            VALUES (N'KTV_Daily_Report', @TargetDate, N'Skipped', @SkipMessage);
            PRINT @SkipMessage;
            RETURN;
        END
    END

    BEGIN TRANSACTION;

    BEGIN TRY
        -- 准备一个临时表来接收存储过程的输出
        CREATE TABLE #TempReportData (
            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
            FreeConsumption_BatchCount int,
            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2)
        );

        -- 执行核心存储过程，并将结果存入临时表
        INSERT INTO #TempReportData
        EXEC dbo.usp_GenerateSimplifiedDailyReport @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;

        -- 将临时表的数据正式插入目标表
        INSERT INTO dbo.KTV_Simplified_Daily_Report (
            ReportDate, ShopName, Weekday, TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount, FreeMeal_KPlus, FreeMeal_Special,
            FreeMeal_Meituan, FreeMeal_Douyin, FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount,
            Buyout_Revenue, Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount,
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others,
            Night_Verify_BatchCount, Night_Verify_Revenue
        )
        SELECT * FROM #TempReportData;

        -- 提交事务
        COMMIT TRANSACTION;

        -- 记录成功日志
        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully.';
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])
        VALUES (N'KTV_Daily_Report', @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

        -- 删除临时表
        DROP TABLE #TempReportData;

    END TRY
    BEGIN CATCH
        -- 如果发生错误，回滚事务
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;

        -- 清理临时表
        IF OBJECT_ID('tempdb..#TempReportData') IS NOT NULL
            DROP TABLE #TempReportData;

        -- 记录失败日志
        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])
        VALUES (N'KTV_Daily_Report', @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;

        -- 重新引发错误
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT N'usp_RunDailyReportJob 更新完成';
GO

-- ====================================================================
-- 第二部分：更新 usp_RunNormalizedDailyReportJob_V2
-- ====================================================================

PRINT N'正在更新 usp_RunNormalizedDailyReportJob_V2...';

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_RunNormalizedDailyReportJob_V2', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2;
    PRINT N'已删除旧的 usp_RunNormalizedDailyReportJob_V2';
END
GO

-- 创建新的可重复执行版本
CREATE PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2
    @TargetDate DATE = NULL,
    @ShopId INT = 3,
    @ForceReRun BIT = 0 -- 新增参数：强制重新执行（删除后重新插入）
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Normalized_Full_Report';

    -- 检查当天的数据是否已经存在
    IF EXISTS (SELECT 1 FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId)
    BEGIN
        IF @ForceReRun = 1
        BEGIN
            -- 强制重新执行：删除现有数据（包括主表和详情表）
            DECLARE @ReportIDs TABLE (ReportID INT);
            
            -- 获取要删除的ReportID
            INSERT INTO @ReportIDs 
            SELECT ReportID 
            FROM dbo.FullDailyReport_Header 
            WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
            
            -- 先删除详情表数据
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails 
            WHERE ReportID IN (SELECT ReportID FROM @ReportIDs);
            
            -- 再删除主表数据
            DELETE FROM dbo.FullDailyReport_Header 
            WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
            
            DECLARE @DeleteMessage NVARCHAR(500) = N'Deleted: Existing data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' and ShopID ' + CAST(@ShopId AS NVARCHAR) + N' has been deleted for re-run.';
            INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Deleted', @DeleteMessage);
            PRINT @DeleteMessage;
        END
        ELSE
        BEGIN
            -- 不强制重新执行：跳过
            DECLARE @SkipMessage NVARCHAR(500) = N'Skipped: Data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' and ShopID ' + CAST(@ShopId AS NVARCHAR) + N' already exists. Use @ForceReRun=1 to re-run.';
            INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Skipped', @SkipMessage);
            PRINT @SkipMessage;
            RETURN;
        END
    END

    BEGIN TRANSACTION;

    BEGIN TRY
        -- 步骤 1: 调用V3版总览SP，获取所有Header数据
        PRINT N'Step 1: Getting header data from usp_GenerateDayTimeReport_Simple_V3...';
        CREATE TABLE #TempHeader (
            WorkDate varchar(8), ShopName nvarchar(100), WeekdayName nvarchar(20),
            TotalRevenue decimal(18,2), DayTimeRevenue decimal(18,2), NightTimeRevenue decimal(18,2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            DayTimeDropInBatch int, NightTimeDropInBatch int, TotalGuestCount int,
            BuffetGuestCount int, TotalDropInGuests int, 
            MealBatchCount INT, MealDirectFallBatchCount INT, MealDirectFallRevenue DECIMAL(18,2),
            Night_FreeMeal_Subtotal int, Night_FreeMeal_Amount decimal(18,2), Night_After20_Revenue decimal(18,2)
        );
        INSERT INTO #TempHeader
        EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;

        -- 步骤 2: 将所有新指标插入Header表
        PRINT N'Step 2: Inserting all new metrics into FullDailyReport_Header...';
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, 
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDirectFall, NightTimeDropInBatch, 
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue
        )
        SELECT
            @TargetDate, @ShopId, ShopName, WeekdayName,
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDropInBatch, NightTimeDropInBatch,
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue
        FROM #TempHeader;

        DECLARE @ReportID INT = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- 步骤 3: 调用时段详情SP，获取Details数据
        PRINT N'Step 3: Getting time slot details from usp_GetTimeSlotDetails_WithDirectFall...';
        CREATE TABLE #TempDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        INSERT INTO #TempDetails
        EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;

        -- 步骤 4: 将Details数据插入详情表
        PRINT N'Step 4: Inserting data into FullDailyReport_TimeSlotDetails...';
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        )
        SELECT 
            @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        FROM #TempDetails;
        
        PRINT N'Time slot details inserted.';

        -- 清理临时表
        DROP TABLE #TempHeader;
        DROP TABLE #TempDetails;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Final modular report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

    END TRY
    BEGIN CATCH
        -- 如果发生错误，回滚事务
        IF @@TRANCOUNT > 0 
            ROLLBACK TRANSACTION;

        -- 清理临时表
        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempDetails') IS NOT NULL DROP TABLE #TempDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;

        -- 重新引发错误
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT N'usp_RunNormalizedDailyReportJob_V2 更新完成';
GO

-- ====================================================================
-- 第三部分：验证部署
-- ====================================================================

PRINT N'==========================================';
PRINT N'验证存储过程部署';
PRINT N'==========================================';

-- 验证存储过程是否存在
IF OBJECT_ID('dbo.usp_RunDailyReportJob', 'P') IS NOT NULL
    PRINT N'✓ usp_RunDailyReportJob 部署成功';
ELSE
    PRINT N'✗ usp_RunDailyReportJob 部署失败';

IF OBJECT_ID('dbo.usp_RunNormalizedDailyReportJob_V2', 'P') IS NOT NULL
    PRINT N'✓ usp_RunNormalizedDailyReportJob_V2 部署成功';
ELSE
    PRINT N'✗ usp_RunNormalizedDailyReportJob_V2 部署失败';

-- 显示存储过程参数信息
PRINT N'';
PRINT N'存储过程参数信息：';
PRINT N'usp_RunDailyReportJob: @TargetDate DATE, @ShopId INT, @ForceReRun BIT = 0';
PRINT N'usp_RunNormalizedDailyReportJob_V2: @TargetDate DATE, @ShopId INT, @ForceReRun BIT = 0';
PRINT N'';
PRINT N'使用说明：';
PRINT N'- 正常执行：EXEC dbo.usp_RunDailyReportJob @ShopId = 3';
PRINT N'- 强制重新执行：EXEC dbo.usp_RunDailyReportJob @ShopId = 3, @ForceReRun = 1';
PRINT N'- 指定日期：EXEC dbo.usp_RunDailyReportJob @TargetDate = ''2025-07-24'', @ShopId = 3';

PRINT N'==========================================';
PRINT N'部署完成！';
PRINT N'==========================================';
