-- 名堂店20250717直落订单验证SQL
-- 我的算法识别出以下订单为直落订单，请您验证是否准确

-- 第一步：查看我识别为直落的订单详情（前20个样例）
SELECT 
    c.InvNo as '订单号',
    c.CloseDatetime as '结账时间',
    DATEADD(HOUR, -2.5, c.CloseDatetime) as '估算开台时间',
    CONVERT(varchar, DATEADD(HOUR, -2.5, c.CloseDatetime), 108) as '估算开台时分',
    CONVERT(varchar, c.CloseDatetime, 108) as '结账时分',
    c.<PERSON> as '金额',
    c.<PERSON> as '支付方式'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250717'
    AND c.InvNo IN (
        'A02426776', 'A02426780', 'A02426777', 'A02426781', 'A02426779', 'A02426778', 'A02426782',
        'A02426784', 'A02426785', 'A02426783', 'A02426786', 'A02426787', 'A02426789', 'A02426788',
        'A02426790', 'A02426791', 'A02426792', 'A02426793', 'A02426794', 'A02426795'
    )
ORDER BY c.CloseDatetime;

-- 第二步：查看时间段配置
SELECT 
    t.TimeName as '时间段名称',
    FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00') as '开始时间',
    FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00') as '结束时间',
    CASE 
        WHEN t.EndTime < t.BegTime THEN '跨天时间段'
        ELSE '当天时间段'
    END as '时间段类型'
FROM shoptimeinfo st
LEFT JOIN timeinfo t ON st.timeno = t.timeno
WHERE st.shopid = 11
ORDER BY t.BegTime;

-- 第三步：验证11:50-14:50时间段的直落订单
SELECT 
    '11:50-14:50时间段' as '分析时间段',
    c.InvNo as '订单号',
    CONVERT(varchar, DATEADD(HOUR, -2.5, c.CloseDatetime), 108) as '估算开台时间',
    CONVERT(varchar, c.CloseDatetime, 108) as '结账时间',
    CASE 
        WHEN DATEADD(HOUR, -2.5, c.CloseDatetime) < CONVERT(datetime, '20250717 11:50:00') 
             AND c.CloseDatetime >= CONVERT(datetime, '20250717 11:50:00')
        THEN '是直落'
        ELSE '不是直落'
    END as '是否直落',
    c.Tot as '金额'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250717'
    AND c.InvNo IN (
        'A02426776', 'A02426780', 'A02426777', 'A02426781', 'A02426779', 'A02426778', 'A02426782'
    )
ORDER BY c.CloseDatetime;

-- 第四步：验证13:30-16:30时间段的直落订单
SELECT 
    '13:30-16:30时间段' as '分析时间段',
    c.InvNo as '订单号',
    CONVERT(varchar, DATEADD(HOUR, -2.5, c.CloseDatetime), 108) as '估算开台时间',
    CONVERT(varchar, c.CloseDatetime, 108) as '结账时间',
    CASE 
        WHEN DATEADD(HOUR, -2.5, c.CloseDatetime) < CONVERT(datetime, '20250717 13:30:00') 
             AND c.CloseDatetime >= CONVERT(datetime, '20250717 13:30:00')
        THEN '是直落'
        ELSE '不是直落'
    END as '是否直落',
    c.Tot as '金额'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250717'
    AND c.InvNo IN (
        'A02426784', 'A02426785', 'A02426783', 'A02426786', 'A02426787', 'A02426789', 'A02426788',
        'A02426790', 'A02426791', 'A02426792'
    )
ORDER BY c.CloseDatetime;

-- 第五步：查看实际开台数据进行对比
SELECT 
    o.BookNo as '开台号',
    o.CustName as '客户名称',
    o.ComeTime as '实际开台时间',
    o.RmNo as '房间号',
    CONVERT(datetime, o.ComeDate + ' ' + o.ComeTime) as '开台时间戳'
FROM opencacheinfo o
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.ComeTime BETWEEN '09:00:00' AND '16:00:00'  -- 查看上午到下午的开台记录
ORDER BY o.ComeTime;

-- 第六步：完整的直落验证逻辑
WITH DirectFallAnalysis AS (
    SELECT 
        c.InvNo,
        c.CloseDatetime,
        DATEADD(HOUR, -2.5, c.CloseDatetime) as EstimatedOpen,
        c.Tot,
        -- 检查各个时间段
        CASE WHEN DATEADD(HOUR, -2.5, c.CloseDatetime) < CONVERT(datetime, '20250717 11:50:00') 
                  AND c.CloseDatetime >= CONVERT(datetime, '20250717 11:50:00')
             THEN '11:50-14:50直落' ELSE '' END as Slot1150,
        CASE WHEN DATEADD(HOUR, -2.5, c.CloseDatetime) < CONVERT(datetime, '20250717 13:30:00') 
                  AND c.CloseDatetime >= CONVERT(datetime, '20250717 13:30:00')
             THEN '13:30-16:30直落' ELSE '' END as Slot1330,
        CASE WHEN DATEADD(HOUR, -2.5, c.CloseDatetime) < CONVERT(datetime, '20250717 15:00:00') 
                  AND c.CloseDatetime >= CONVERT(datetime, '20250717 15:00:00')
             THEN '15:00-18:00直落' ELSE '' END as Slot1500
    FROM rmcloseinfo c
    WHERE c.shopid = 11 AND c.WorkDate = '20250717'
)
SELECT 
    InvNo as '订单号',
    CONVERT(varchar, EstimatedOpen, 108) as '估算开台时间',
    CONVERT(varchar, CloseDatetime, 108) as '结账时间',
    Tot as '金额',
    CONCAT(Slot1150, ' ', Slot1330, ' ', Slot1500) as '直落时间段'
FROM DirectFallAnalysis
WHERE Slot1150 != '' OR Slot1330 != '' OR Slot1500 != ''
ORDER BY CloseDatetime;
