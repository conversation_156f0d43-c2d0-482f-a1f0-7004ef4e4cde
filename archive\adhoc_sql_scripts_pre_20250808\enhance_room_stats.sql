
-- 确保在正确的数据库上下文中执行
USE dbfood;
GO

-- 1. 为 RoomStatisticsHourly 表添加 OccupancyRate 字段
PRINT 'Step 1: Enhancing table RoomStatisticsHourly with OccupancyRate column...';
IF COL_LENGTH('RoomStatisticsHourly', 'OccupancyRate') IS NULL
BEGIN
    ALTER TABLE RoomStatisticsHourly ADD OccupancyRate DECIMAL(5, 2) NULL;
    PRINT 'Column OccupancyRate added to RoomStatisticsHourly table.';
END
ELSE
BEGIN
    PRINT 'Column OccupancyRate already exists in RoomStatisticsHourly table.';
END
GO

-- 2. 更新存储过程以计算并插入开房率
PRINT 'Step 2: Updating stored procedure usp_LogHourlyRoomStatistics...';
IF OBJECT_ID('usp_LogHourlyRoomStatistics', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE usp_LogHourlyRoomStatistics;
END
GO

CREATE PROCEDURE usp_LogHourlyRoomStatistics
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @TotalRoomsBeforeFilter INT;
    DECLARE @ValidRoomsCount INT;
    DECLARE @BadRoomsCount INT;
    DECLARE @OccupiedRoomsCount INT;
    DECLARE @OccupancyRate DECIMAL(5, 2);

    -- 计算过滤前的总房数
    SELECT @TotalRoomsBeforeFilter = COUNT(*) FROM dbo.ROOM;

    -- 在排除了房间名带B的房间后，进行统计
    -- 同时统计被占用的房间数 (状态不为 'N' - 空闲)
    SELECT 
        @ValidRoomsCount = COUNT(*),
        @BadRoomsCount = SUM(CASE WHEN RmStatus = 'B' THEN 1 ELSE 0 END),
        @OccupiedRoomsCount = SUM(CASE WHEN RmStatus <> 'N' AND RmStatus <> 'B' THEN 1 ELSE 0 END) -- 开房数 = 非空闲且非坏房
    FROM 
        dbo.ROOM
    WHERE 
        RmNo NOT LIKE '%B';

    -- 计算开房率 (开房数 / (有效总房数 - 坏房数))
    -- 避免除以零的错误
    IF (ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0)) > 0
    BEGIN
        SET @OccupancyRate = (CAST(ISNULL(@OccupiedRoomsCount, 0) AS DECIMAL(10, 2)) / (ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0))) * 100;
    END
    ELSE
    BEGIN
        SET @OccupancyRate = 0;
    END

    -- 插入统计结果到新表
    INSERT INTO dbo.RoomStatisticsHourly (
        TotalRoomsBeforeFilter,
        ValidRoomsCount,
        BadRoomsCount,
        AvailableRoomsCount,
        OccupancyRate
    ) 
    VALUES (
        @TotalRoomsBeforeFilter,
        ISNULL(@ValidRoomsCount, 0),
        ISNULL(@BadRoomsCount, 0),
        ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0),
        @OccupancyRate
    );

    PRINT 'Hourly room statistics with Occupancy Rate logged successfully.';
END
GO

PRINT 'Stored procedure usp_LogHourlyRoomStatistics updated successfully.';
GO
