
-- =============================================
-- 预订报表导出脚本
-- =============================================

-- 1. 定义参数
DECLARE @TargetDate DATE = '20250731';
DECLARE @ShopID INT = 11;

-- 2. 查询并导出总览
PRINT N'--- 预订报表总览 ---';
SELECT 
    h.ReportDate AS '报表日期',
    h.ShopID AS '店铺ID',
    h.Weekday AS '星期'
FROM 
    dbo.DynamicReport_Header h
WHERE 
    h.ReportDate = @TargetDate AND h.ShopID = @ShopID;

-- 3. 查询并导出时段详情
PRINT N'
--- 预订报表时段详情 ---';
SELECT 
    d.TimeSlotName AS '时间段',
    d.BookedRooms AS '预订房间数',
    d.BookedGuests AS '预订人数'
FROM 
    dbo.DynamicReport_TimeSlotDetails d
JOIN 
    dbo.DynamicReport_Header h ON d.HeaderReportID = h.ReportID
WHERE 
    h.ReportDate = @TargetDate AND h.ShopID = @ShopID
ORDER BY
    -- 确保时间段正确排序
    CASE 
        WHEN ISNUMERIC(LEFT(d.TimeSlotName, 2)) = 1 THEN CAST(LEFT(d.TimeSlotName, 2) AS INT)
        ELSE 99
    END;
GO
