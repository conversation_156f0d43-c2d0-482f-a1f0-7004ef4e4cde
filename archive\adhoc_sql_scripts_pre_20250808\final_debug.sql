
-- 终极调试脚本：深入分析 bookcacheinfo 的源数据

PRINT N'--- 深入分析 [cloudRms2019].rms2019.dbo.bookcacheinfo ---';

-- 查询并分析 ShopID=11, ComeDate='20250731' 的所有预订记录
SELECT 
    TOP 20 -- 只显示前20条作为样本
    ComeDate,
    ComeTime,
    BookStatus,
    CheckinStatus,
    Numbers,
    RmNo,
    -- 在这里直接进行转换，看看结果是否符合预期
    CAST(LEFT(REPLACE(ComeTime, ':', ''), 4) AS INT) AS ConvertedComeTime
FROM 
    OPENQUERY([cloudRms2019], 
        'SELECT ComeDate, ComeTime, BookStatus, CheckinStatus, Numbers, RmNo 
         FROM rms2019.dbo.bookcacheinfo 
         WHERE ShopID = 11 AND ComeDate = ''20250731''');

-- 同时，统计不同状态的记录数
PRINT N'
--- 按状态统计记录数 ---';
SELECT
    BookStatus,
    CheckinStatus,
    COUNT(*) as RecordCount
FROM
    OPENQUERY([cloudRms2019], 
        'SELECT BookStatus, CheckinStatus 
         FROM rms2019.dbo.bookcacheinfo 
         WHERE ShopID = 11 AND ComeDate = ''20250731''')
GROUP BY
    BookStatus, CheckinStatus;
GO
