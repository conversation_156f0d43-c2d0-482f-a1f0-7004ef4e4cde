
-- =============================================
-- 最终验证：不限日期，在 operatedata.dbo.FdCashBak 中查找“消费人数”
-- =============================================

-- 1. 定义参数
DECLARE @ShopID INT = 11;
DECLARE @SearchTerm NVARCHAR(100) = N'%消费人数%';

PRINT N'--- 正在 ShopID=' + CAST(@ShopID AS VARCHAR) + ' 的所有账单中，查找 FdCName LIKE ''' + @SearchTerm + ''' 的记录 ---';

-- 2. 执行查询
SELECT 
    TOP 100 -- 最多显示100条样本数据
    Invno AS '账单号',
    FdCName AS '下单项名称',
    FdQty AS '数量',
    CashType AS '支付类型'
FROM 
    dbo.FdCashBak
WHERE 
    ShopId = @ShopID AND FdCName LIKE @SearchTerm;

-- 3. 同时，统计总共有多少条这样的记录
SELECT 
    COUNT(*) AS TotalMatchingRecords
FROM 
    dbo.FdCashBak
WHERE 
    ShopId = @ShopID AND FdCName LIKE @SearchTerm;
GO
