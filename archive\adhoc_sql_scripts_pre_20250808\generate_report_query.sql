
-- =============================================
-- KTV动态日报表 - 纯查询脚本 (V27 - 最终绝对正确版)
-- =============================================

-- 1. 定义参数
DECLARE @TargetDate DATE = '20250731';
DECLARE @ShopID INT = 11;

-- 2. 准备临时表
IF OBJECT_ID('tempdb..#AllBookings_Raw') IS NOT NULL DROP TABLE #AllBookings_Raw;
IF OBJECT_ID('tempdb..#AllOccupancies_Raw') IS NOT NULL DROP TABLE #AllOccupancies_Raw;
IF OBJECT_ID('tempdb..#GuestCounts_Raw') IS NOT NULL DROP TABLE #GuestCounts_Raw;

CREATE TABLE #AllBookings_Raw (Beg_Key VARCHAR(20), End_Key VARCHAR(20), Numbers INT, RmNo VARCHAR(20), isdelete BIT);
CREATE TABLE #AllOccupancies_Raw (Beg_Key VARCHAR(20), End_Key VARCHAR(20), RmNo VARCHAR(20), Invno VARCHAR(30));
CREATE TABLE #GuestCounts_Raw (Invno VARCHAR(30), FdCName NVARCHAR(100), FdQty INT, CashType VARCHAR(1));

-- 3. 从远程服务器拉取数据
-- a. 获取预订数据
INSERT INTO #AllBookings_Raw (Beg_Key, End_Key, Numbers, RmNo, isdelete)
SELECT Beg_Key, End_Key, Numbers, RmNo, isdelete FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, End_Key, Numbers, RmNo, isdelete FROM rms2019.dbo.bookcacheinfo WHERE ComeDate = ''20250731'' AND ShopID = 11');
INSERT INTO #AllBookings_Raw (Beg_Key, End_Key, Numbers, RmNo, isdelete)
SELECT Beg_Key, End_Key, Numbers, RmNo, isdelete FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, End_Key, Numbers, RmNo, isdelete FROM rms2019.dbo.bookhistory WHERE ComeDate = ''20250731'' AND ShopID = 11');

-- b. 获取待客数据
INSERT INTO #AllOccupancies_Raw (Beg_Key, End_Key, RmNo, Invno)
SELECT Beg_Key, End_Key, RmNo, Invno FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, End_Key, RmNo, Invno FROM rms2019.dbo.opencacheinfo WHERE ComeDate = ''20250731'' AND ShopID = 11');
INSERT INTO #AllOccupancies_Raw (Beg_Key, End_Key, RmNo, Invno)
SELECT Beg_Key, End_Key, RmNo, Invno FROM OPENQUERY([cloudRms2019], 'SELECT Beg_Key, End_Key, RmNo, Invno FROM rms2019.dbo.openhistory WHERE ComeDate = ''20250731'' AND ShopID = 11');

-- c. 获取人数数据 (不进行预筛选)
INSERT INTO #GuestCounts_Raw (Invno, FdCName, FdQty, CashType)
SELECT f.Invno, f.FdCName, f.FdQty, f.CashType
FROM dbo.FdCashBak f
JOIN #AllOccupancies_Raw o ON f.Invno = o.Invno COLLATE Chinese_PRC_CI_AS;

-- 4. 核心计算逻辑
-- a. 准备总览数据
SELECT 
    @TargetDate AS '报表日期',
    @ShopID AS '店铺ID',
    FORMAT(@TargetDate, 'dddd', 'zh-CN') AS '星期',
    (SELECT SUM(Tot) FROM dbo.RmCloseInfo WHERE WorkDate = FORMAT(@TargetDate, 'yyyyMMdd') AND ShopId = @ShopID) AS '总营业额';

-- b. 准备详细数据
;WITH TimeSlots AS (
    SELECT t.timeno, t.TimeName FROM dbo.timeinfo t
    JOIN dbo.shoptimeinfo st ON t.timeno = st.timeno
    WHERE st.shopid = @ShopID
),
GuestCounts_From_Bills AS (
    -- 在这里进行最终的筛选和聚合
    SELECT Invno, SUM(FdQty) as GuestCount 
    FROM #GuestCounts_Raw 
    WHERE FdCName LIKE N'%消费人数%' AND CashType = 'N' 
    GROUP BY Invno
),
ValidBookings AS (
    SELECT Beg_Key, Numbers, RmNo FROM #AllBookings_Raw WHERE (isdelete = 0 OR isdelete IS NULL) AND Beg_Key = End_Key
),
ValidOccupancies AS (
    SELECT o.Beg_Key, o.RmNo, ISNULL(g.GuestCount, 0) AS Numbers
    FROM #AllOccupancies_Raw o
    LEFT JOIN GuestCounts_From_Bills g ON o.Invno = g.Invno COLLATE Chinese_PRC_CI_AS
    WHERE o.Beg_Key = o.End_Key
),
BookingsBySlot AS (
    SELECT b.Beg_Key, COUNT(DISTINCT b.RmNo) as BookedRooms, ISNULL(SUM(b.Numbers), 0) as BookedGuests
    FROM ValidBookings b GROUP BY b.Beg_Key
),
OccupanciesBySlot AS (
    SELECT o.Beg_Key, COUNT(DISTINCT o.RmNo) as OccupiedRooms, ISNULL(SUM(o.Numbers), 0) as OccupiedGuests
    FROM ValidOccupancies o GROUP BY o.Beg_Key
)
SELECT 
    ts.TimeName AS '时间段',
    ISNULL(b.BookedRooms, 0) AS '预订房间数',
    ISNULL(b.BookedGuests, 0) AS '预订人数',
    ISNULL(o.OccupiedRooms, 0) AS '待客房间数',
    ISNULL(o.OccupiedGuests, 0) AS '待客人数',
    CASE WHEN ISNULL(b.BookedRooms, 0) > 0 THEN FORMAT(CAST(ISNULL(o.OccupiedRooms, 0) AS DECIMAL(10,2)) / b.BookedRooms, 'P') ELSE '0.00%' END AS '开房率'
FROM TimeSlots ts
LEFT JOIN BookingsBySlot b ON ts.timeno = b.Beg_Key
LEFT JOIN OccupanciesBySlot o ON ts.timeno = o.Beg_Key
ORDER BY ts.timeno;

-- 5. 清理
DROP TABLE #AllBookings_Raw;
DROP TABLE #AllOccupancies_Raw;
DROP TABLE #GuestCounts_Raw;
GO
