-- 改进的直落订单识别SQL - 基于真实开台数据
-- 使用Beg_Name和End_Name字段准确识别直落

-- 1. 真正的直落订单识别（Beg_Name ≠ End_Name）
SELECT 
    '真正直落订单' as '订单类型',
    o.<PERSON> as '开台号',
    o.<PERSON><PERSON> as '客户名称',
    o.ComeTime as '实际开台时间',
    o.Beg_Name as '开始时间段',
    o.End_Name as '结束时间段',
    o.RmNo as '房间号',
    o.Remark as '备注',
    c.CloseDatetime as '结账时间',
    c.Tot as '金额'
FROM opencacheinfo o
LEFT JOIN rmcloseinfo c ON o.Invno = c.InvNo
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.Beg_Name != o.End_Name  -- 关键条件：开始和结束时间段不同
ORDER BY o.ComeTime;

-- 2. 单时间段订单（Beg_Name = End_Name）
SELECT 
    '单时间段订单' as '订单类型',
    o.<PERSON><PERSON><PERSON> as '开台号',
    o.<PERSON> as '客户名称',
    o.ComeTime as '实际开台时间',
    o.<PERSON>g_Name as '时间段',
    o.RmNo as '房间号',
    c.CloseDatetime as '结账时间',
    c.Tot as '金额'
FROM opencacheinfo o
LEFT JOIN rmcloseinfo c ON o.Invno = c.InvNo
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.Beg_Name = o.End_Name  -- 开始和结束时间段相同
ORDER BY o.ComeTime;

-- 3. 验证我之前识别的订单
SELECT 
    '我的算法识别结果验证' as '分析类型',
    o.InvNo as '订单号',
    o.CustName as '客户名称',
    o.ComeTime as '实际开台时间',
    CONVERT(varchar, DATEADD(HOUR, -2.5, c.CloseDatetime), 108) as '我估算的开台时间',
    o.Beg_Name as '开始时间段',
    o.End_Name as '结束时间段',
    CASE 
        WHEN o.Beg_Name != o.End_Name THEN '真直落'
        ELSE '非直落'
    END as '实际情况',
    CASE 
        WHEN o.InvNo IN ('A02426811', 'A02426810', 'A02426807', 'A02426809', 'A02426808', 
                        'A02426814', 'A02426813', 'A02426812', 'A02426806') THEN '我识别为直落'
        ELSE '我识别为非直落'
    END as '我的识别结果',
    c.Tot as '金额',
    o.Remark as '备注'
FROM opencacheinfo o
LEFT JOIN rmcloseinfo c ON o.Invno = c.InvNo
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.InvNo IN (
        'A02426811', 'A02426810', 'A02426807', 'A02426809', 'A02426808', 
        'A02426814', 'A02426813', 'A02426812', 'A02426806'
    )
ORDER BY o.ComeTime;

-- 4. 统计分析
SELECT 
    '直落订单统计' as '统计类型',
    COUNT(*) as '订单数量',
    SUM(CASE WHEN c.Tot IS NOT NULL THEN c.Tot ELSE 0 END) as '总金额'
FROM opencacheinfo o
LEFT JOIN rmcloseinfo c ON o.Invno = c.InvNo
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.Beg_Name != o.End_Name

UNION ALL

SELECT 
    '单时间段订单统计' as '统计类型',
    COUNT(*) as '订单数量',
    SUM(CASE WHEN c.Tot IS NOT NULL THEN c.Tot ELSE 0 END) as '总金额'
FROM opencacheinfo o
LEFT JOIN rmcloseinfo c ON o.Invno = c.InvNo
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.Beg_Name = o.End_Name;

-- 5. 按时间段分析直落情况
SELECT 
    o.Beg_Name as '起始时间段',
    o.End_Name as '结束时间段',
    COUNT(*) as '直落订单数',
    STRING_AGG(o.InvNo, ', ') as '订单号列表'
FROM opencacheinfo o
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.Beg_Name != o.End_Name
GROUP BY o.Beg_Name, o.End_Name
ORDER BY o.Beg_Name, o.End_Name;
