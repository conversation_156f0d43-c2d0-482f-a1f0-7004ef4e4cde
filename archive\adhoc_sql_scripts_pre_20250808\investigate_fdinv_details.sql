-- 首先，请确保您连接的是名堂本地数据库: 193.112.2.229
-- 然后，选择正确的数据库上下文
USE Dbfood;
GO

-- 查询这13个缺失开台记录的账单，在 fdinv 表中的所有消费明细
SELECT 
    * 
FROM 
    dbo.fdinv
WHERE 
    InvNo IN (
        'A02427558', 
        'A02427575', 
        'A02427560', 
        'A02427582', 
        'A02427563', 
        'A02427538', 
        'A02427523', 
        'A02427499', 
        'A02427564', 
        'A02427509', 
        'A02427581', 
        'A02427510', 
        'A02427566'
    )
ORDER BY
    InvNo, -- 按单号排序
    InTime;  -- 按消费时间排序
GO
