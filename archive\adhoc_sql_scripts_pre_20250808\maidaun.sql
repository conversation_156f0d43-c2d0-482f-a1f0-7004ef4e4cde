WITH TargetInvoices AS (
    SELECT invno
    FROM rmcloseinfo
    WHERE WorkDate = '20250724'
    AND ShopId = 11
)
SELECT  fi.*, -- 只选择唯一的 invno
fdc.*
FROM fdinv fi
JOIN FdCashBak fdc ON fi.invno = fdc.InvNo COLLATE Chinese_PRC_CI_AS
JOIN TargetInvoices ti ON fi.invno = ti.invno COLLATE Chinese_PRC_CI_AS
WHERE fdc.FdCName LIKE '%买断%'
AND fi.ShopId = 11
AND fdc.ShopId = 11;


WITH TargetInvoices AS (
    SELECT invno
    FROM rmcloseinfo
    WHERE WorkDate = '20250724'
    AND ShopId = 11
)
SELECT  fi.*, -- 只选择唯一的 invno
fdc.*
FROM fdinv fi
JOIN FdCashBak fdc ON fi.invno = fdc.InvNo COLLATE Chinese_PRC_CI_AS
JOIN TargetInvoices ti ON fi.invno = ti.invno COLLATE Chinese_PRC_CI_AS
WHERE fdc.FdCName LIKE '%畅饮%'
AND fi.ShopId = 11
AND fdc.ShopId = 11;


WITH TargetInvoices AS (
    SELECT invno
    FROM rmcloseinfo
    WHERE WorkDate = '20250724'
    AND ShopId = 11
)
SELECT  fi.*, -- 只选择唯一的 invno
fdc.*
FROM fdinv fi
JOIN FdCashBak fdc ON fi.invno = fdc.InvNo COLLATE Chinese_PRC_CI_AS
JOIN TargetInvoices ti ON fi.invno = ti.invno COLLATE Chinese_PRC_CI_AS
WHERE fdc.FdCName LIKE '%自由畅饮%'
AND fi.ShopId = 11
AND fdc.ShopId = 11;