-- 优化的插入语句集合


-- 优化版本1: 只插入有意义的数据
INSERT INTO dbo.FullDailyReport_NightDetails (
    ReportID, 
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NonPackage_Others,
    DiscountFree_BatchCount, DiscountFree_Revenue
) SELECT 
    @ReportID,
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NonPackage_Others,
    DiscountFree_BatchCount, DiscountFree_Revenue
FROM #TempNightDetails;



-- 优化版本2: 完整字段插入（未使用字段设为NULL或0）
INSERT INTO dbo.FullDailyReport_NightDetails (
    ReportID,
    FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin,
    FreeMeal_BatchCount, FreeMeal_Revenue,
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others,
    DiscountFree_BatchCount, DiscountFree_Revenue
) SELECT 
    @ReportID,
    NULL, NULL, NULL, NULL,  -- FreeMeal字段设为NULL
    NULL, NULL,              -- FreeMeal批次和收入设为NULL
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NULL, NULL, NULL, NonPackage_Others,  -- 大部分NonPackage字段设为NULL
    DiscountFree_BatchCount, DiscountFree_Revenue
FROM #TempNightDetails;
