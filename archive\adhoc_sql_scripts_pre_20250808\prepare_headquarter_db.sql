
-- ===================================================================
-- 脚本: prepare_headquarter_db.sql
-- 功能: 准备总部(192.168.2.5)的rms2019数据库环境
-- ===================================================================

-- 确保在正确的数据库上下文中执行
USE rms2019;
GO

-- 1. 为 opencacheinfo 表添加 WorkDate 字段
PRINT 'Step 1.1: Modifying table opencacheinfo...';
IF COL_LENGTH('opencacheinfo', 'WorkDate') IS NULL
BEGIN
    ALTER TABLE opencacheinfo ADD WorkDate DATE NULL;
    PRINT 'Column WorkDate added to opencacheinfo table.';
END
ELSE
BEGIN
    PRINT 'Column WorkDate already exists in opencacheinfo table.';
END
GO

-- 2. 为 openhistory 表添加 WorkDate 字段
PRINT 'Step 1.2: Modifying table openhistory...';
IF COL_LENGTH('openhistory', 'WorkDate') IS NULL
BEGIN
    ALTER TABLE openhistory ADD WorkDate DATE NULL;
    PRINT 'Column WorkDate added to openhistory table.';
END
ELSE
BEGIN
    PRINT 'Column WorkDate already exists in openhistory table.';
END
GO

-- 3. 创建 bookcacheinfo 表 (如果不存在)
PRINT 'Step 2.1: Creating table bookcacheinfo...';
IF OBJECT_ID('bookcacheinfo', 'U') IS NOT NULL
BEGIN
    PRINT 'Table bookcacheinfo already exists.';
END
ELSE
BEGIN
    CREATE TABLE bookcacheinfo (
        Ikey varchar(36) NOT NULL PRIMARY KEY,
        BookNo varchar(4) NULL,
        ShopId int NULL,
        CustKey varchar(36) NULL,
        CustName nvarchar(10) NULL,
        CustTel nvarchar(20) NULL,
        ComeDate nvarchar(10) NULL,
        ComeTime nvarchar(20) NULL,
        Beg_Key nvarchar(10) NULL,
        Beg_Name nvarchar(20) NULL,
        End_Key nvarchar(10) NULL,
        End_Name nvarchar(20) NULL,
        Numbers int NULL,
        RtNo nvarchar(10) NULL,
        RtName nvarchar(10) NULL,
        CtNo int NULL,
        CtName nvarchar(20) NULL,
        PtNo int NULL,
        PtName nvarchar(10) NULL,
        BookMemory nvarchar(255) NULL,
        BookStatus int NULL,
        CheckinStatus int NULL,
        BookShopId int NULL,
        BookUserId nvarchar(10) NULL,
        BookUserName nvarchar(10) NULL,
        BookDateTime datetime NULL,
        Invno nvarchar(15) NULL,
        Openmemory nvarchar(100) NULL,
        OrderUserID nvarchar(20) NULL,
        OrderUserName nvarchar(20) NULL,
        RmNo nvarchar(10) NULL,
        Val1 int NULL,
        FromRmNo nvarchar(10) NULL,
        IsBirthday bit NULL,
        Remark nvarchar(500) NULL
    );
    PRINT 'Table bookcacheinfo created successfully.';
END
GO

-- 4. 创建 bookhistory 表 (如果不存在)
PRINT 'Step 2.2: Creating table bookhistory...';
IF OBJECT_ID('bookhistory', 'U') IS NOT NULL
BEGIN
    PRINT 'Table bookhistory already exists.';
END
ELSE
BEGIN
    CREATE TABLE bookhistory (
        Ikey varchar(36) NOT NULL PRIMARY KEY,
        BookNo varchar(4) NULL,
        ShopId int NULL,
        CustKey varchar(36) NULL,
        CustName nvarchar(10) NULL,
        CustTel nvarchar(20) NULL,
        ComeDate nvarchar(10) NULL,
        ComeTime nvarchar(20) NULL,
        Beg_Key nvarchar(10) NULL,
        Beg_Name nvarchar(20) NULL,
        End_Key nvarchar(10) NULL,
        End_Name nvarchar(20) NULL,
        Numbers int NULL,
        RtNo nvarchar(10) NULL,
        RtName nvarchar(10) NULL,
        CtNo int NULL,
        CtName nvarchar(20) NULL,
        PtNo int NULL,
        PtName nvarchar(10) NULL,
        BookMemory nvarchar(255) NULL,
        BookStatus int NULL,
        CheckinStatus int NULL,
        BookShopId int NULL,
        BookUserId nvarchar(10) NULL,
        BookUserName nvarchar(10) NULL,
        BookDateTime datetime NULL,
        Invno nvarchar(15) NULL,
        Openmemory nvarchar(100) NULL,
        OrderUserID nvarchar(20) NULL,
        OrderUserName nvarchar(20) NULL,
        RmNo nvarchar(10) NULL,
        Val1 int NULL,
        FromRmNo nvarchar(10) NULL,
        IsBirthday bit NULL,
        Remark nvarchar(500) NULL
    );
    PRINT 'Table bookhistory created successfully.';
END
GO

PRINT 'Database preparation script finished.';
GO
