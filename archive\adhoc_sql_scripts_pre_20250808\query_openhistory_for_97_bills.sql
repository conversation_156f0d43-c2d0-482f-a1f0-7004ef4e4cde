-- 首先，请确保您连接的是名堂本地数据库: 193.112.2.229
-- 然后，选择正确的数据库上下文
USE rms2019;
GO

-- 查询这97个“直落”账单在 openhistory 表中的所有记录
SELECT 
    * 
FROM 
    dbo.openhistory
WHERE 
    Invno IN (
        'A02426894', 'A02426900', 'A02426907', 'A02426948', 'A02426956', 
        'A02426958', 'A02426966', 'A02426976', 'A02426977', 'A02426978', 
        'A02426982', 'A02426992', 'A02426995', 'A02426996', 'A02427000', 
        'A02427003', 'A02427004', 'A02427005', 'A02427010', 'A02427011', 
        'A02427012', 'A02427014', 'A02427015', 'A02427018', 'A02427019', 
        'A02427021', 'A02427023', 'A02427024', 'A02427026', 'A02427029', 
        'A02427033', 'A02427035', 'A02427040', 'A02427041', 'A02427046', 
        'A02427056', 'A02427057', 'A02427060', 'A02427061', 'A02427062', 
        'A02427063', 'A02427064', 'A02427068', 'A02427085', 'A02427088', 
        'A02427092', 'A02427099', 'A02427102', 'A02427110', 'A02427111', 
        'A02427112', 'A02427114', 'A02427118', 'A02427122', 'A02427130', 
        'A02427139', 'A02427146', 'A02427147', 'A02427165', 'A02427166', 
        'A02427173', 'A02427174', 'A02427178', 'A02427183', 'A02427184', 
        'A02427205', 'A02427208', 'A02427212', 'A02427213', 'A02427215', 
        'A02427218', 'A02427222', 'A02427234', 'A02427241', 'A02427242', 
        'A02427243', 'A02427246', 'A02427247', 'A02427251', 'A02427257', 
        'A02427258', 'A02427263', 'A02427264', 'A02427275', 'A02427278', 
        'A02427289', 'A02427290', 'A02427291', 'A02427297', 'A02427298', 
        'A02427305', 'A02427306', 'A02427307', 'A02427309', 'A02427310', 
        'A02427314', 'A02427315'
    )
ORDER BY
    Invno;
GO