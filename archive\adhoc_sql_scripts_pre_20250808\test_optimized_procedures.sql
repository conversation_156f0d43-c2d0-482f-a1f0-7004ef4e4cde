-- 测试优化后的存储过程
USE OperateData;
GO

-- 测试1: 创建并测试白天分时段报告（包含上一档直落）
PRINT '=== 创建白天分时段报告存储过程 ===';
EXEC sp_executesql N'
IF OBJECT_ID(''dbo.usp_GenerateDayTimeSlotReport'', ''P'') IS NOT NULL
    PRINT ''存储过程已存在''
ELSE
    PRINT ''存储过程不存在，需要先创建''
';

-- 测试2: 执行白天分时段报告
PRINT '=== 测试白天分时段报告 ===';
-- EXEC dbo.usp_GenerateDayTimeSlotReport @ShopId = 3, @BeginDate = '2025-05-08', @EndDate = '2025-05-09';

-- 测试3: 验证夜间档优化分类逻辑
PRINT '=== 验证夜间档优化分类逻辑 ===';
DECLARE @TestShopId int = 3;
DECLARE @TestWorkDate varchar(8) = '20250508';

-- 使用优化的CTE方式验证分类
WITH NightOrderClassifications AS (
    SELECT
        rt.InvNo,
        rt.TotalAmount,
        rt.CtNo,
        rt.MTPay,
        rt.DZPay,
        rt.AliPay,
        MAX(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN 1 ELSE 0 END) AS HasBuyout,
        MAX(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN 1 ELSE 0 END) AS HasChangyin
    FROM dbo.RmCloseInfo_Test AS rt
    LEFT JOIN dbo.FdCashBak AS fcb ON rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
    WHERE rt.ShopId = @TestShopId 
        AND rt.WorkDate = @TestWorkDate
        AND rt.OpenDateTime >= DATEADD(hour, 20, CAST(@TestWorkDate AS datetime))
    GROUP BY rt.InvNo, rt.TotalAmount, rt.CtNo, rt.MTPay, rt.DZPay, rt.AliPay
)
SELECT 
    '夜间档分类验证' AS 测试类型,
    CASE 
        WHEN HasBuyout = 1 THEN '啤酒买断'
        WHEN HasChangyin = 1 THEN '畅饮套餐'
        WHEN CtNo = 19 THEN '自由餐'
        ELSE '其他非自由餐'
    END AS 消费类型,
    CASE 
        WHEN MTPay > 0 THEN '美团'
        WHEN DZPay > 0 THEN '抖音'
        WHEN AliPay > 0 THEN '特权预约'
        WHEN CtNo = 2 THEN 'K+'
        WHEN CtNo = 1 THEN '房费'
        ELSE '其他'
    END AS 渠道,
    COUNT(*) AS 订单数量,
    SUM(TotalAmount) AS 总金额
FROM NightOrderClassifications
GROUP BY 
    CASE 
        WHEN HasBuyout = 1 THEN '啤酒买断'
        WHEN HasChangyin = 1 THEN '畅饮套餐'
        WHEN CtNo = 19 THEN '自由餐'
        ELSE '其他非自由餐'
    END,
    CASE 
        WHEN MTPay > 0 THEN '美团'
        WHEN DZPay > 0 THEN '抖音'
        WHEN AliPay > 0 THEN '特权预约'
        WHEN CtNo = 2 THEN 'K+'
        WHEN CtNo = 1 THEN '房费'
        ELSE '其他'
    END
ORDER BY 消费类型, 渠道;

-- 测试4: 验证上一档直落逻辑
PRINT '=== 验证上一档直落逻辑 ===';
WITH TimeSlots AS (
    SELECT
        ti.TimeNo, ti.TimeName, ti.BegTime,
        DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@TestWorkDate AS date))) AS SlotStartDateTime,
        LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@TestWorkDate AS date))), 1, '2999-12-31') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
    FROM dbo.shoptimeinfo AS sti
    JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
    WHERE sti.ShopId = @TestShopId
        AND ti.BegTime < 2000
),
TrueDropInData AS (
    SELECT 
        rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key,
        ti_beg.BegTime AS BegSlotTime
    FROM dbo.RmCloseInfo_Test AS rt
    JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
    JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
    JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
    WHERE rt.ShopId = @TestShopId 
        AND rt.WorkDate = @TestWorkDate 
        AND rt.OpenDateTime IS NOT NULL
        AND rt.Beg_Key <> rt.End_Key
        AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
        AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
)
SELECT TOP 5
    '上一档直落验证' AS 测试类型,
    ts.TimeName AS 时间段,
    ts.BegTime AS 开始时间,
    (
        SELECT COUNT(tdi.InvNo)
        FROM TrueDropInData AS tdi
        WHERE tdi.BegSlotTime < ts.BegTime
            AND tdi.CloseDatetime > ts.NextSlotStartDateTime
    ) AS 上一档直落数量
FROM TimeSlots AS ts
ORDER BY ts.BegTime;

-- 测试5: 性能对比测试
PRINT '=== 性能对比测试 ===';
DECLARE @StartTime datetime2 = SYSDATETIME();

-- 执行优化查询
WITH NightOrderClassifications AS (
    SELECT
        rt.InvNo,
        MAX(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN 1 ELSE 0 END) AS HasBuyout,
        MAX(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN 1 ELSE 0 END) AS HasChangyin
    FROM dbo.RmCloseInfo_Test AS rt
    LEFT JOIN dbo.FdCashBak AS fcb ON rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
    WHERE rt.ShopId = @TestShopId 
        AND rt.WorkDate = @TestWorkDate
        AND rt.OpenDateTime >= DATEADD(hour, 20, CAST(@TestWorkDate AS datetime))
    GROUP BY rt.InvNo
)
SELECT COUNT(*) AS 优化查询结果数量
FROM NightOrderClassifications;

DECLARE @EndTime datetime2 = SYSDATETIME();
SELECT DATEDIFF(millisecond, @StartTime, @EndTime) AS 优化查询耗时毫秒;

GO
