-- ====================================================================
-- 测试可重复执行的存储过程
-- 描述：测试usp_RunDailyReportJob和usp_RunNormalizedDailyReportJob_V2
--         的重复执行功能
-- 创建时间: 2025-07-25
-- ====================================================================

USE OperateData;
GO

-- 设置测试参数
DECLARE @TestDate DATE = '2025-07-24';
DECLARE @TestShopId INT = 3;

PRINT N'==========================================';
PRINT N'开始测试可重复执行的存储过程';
PRINT N'测试日期: ' + CONVERT(NVARCHAR, @TestDate);
PRINT N'测试门店: ' + CAST(@TestShopId AS NVARCHAR);
PRINT N'==========================================';

-- ====================================================================
-- 测试1：首次执行 usp_RunDailyReportJob
-- ====================================================================

PRINT N'';
PRINT N'测试1：首次执行 usp_RunDailyReportJob';
PRINT N'------------------------------------------';

BEGIN TRY
    EXEC dbo.usp_RunDailyReportJob @TargetDate = @TestDate, @ShopId = @TestShopId;
    PRINT N'✓ 首次执行成功';
END TRY
BEGIN CATCH
    PRINT N'✗ 首次执行失败: ' + ERROR_MESSAGE();
END CATCH

-- ====================================================================
-- 测试2：重复执行 usp_RunDailyReportJob（不强制）
-- ====================================================================

PRINT N'';
PRINT N'测试2：重复执行 usp_RunDailyReportJob（不强制）';
PRINT N'------------------------------------------';

BEGIN TRY
    EXEC dbo.usp_RunDailyReportJob @TargetDate = @TestDate, @ShopId = @TestShopId;
    PRINT N'✓ 重复执行（不强制）- 应该跳过';
END TRY
BEGIN CATCH
    PRINT N'✗ 重复执行失败: ' + ERROR_MESSAGE();
END CATCH

-- ====================================================================
-- 测试3：强制重新执行 usp_RunDailyReportJob
-- ====================================================================

PRINT N'';
PRINT N'测试3：强制重新执行 usp_RunDailyReportJob';
PRINT N'------------------------------------------';

BEGIN TRY
    EXEC dbo.usp_RunDailyReportJob @TargetDate = @TestDate, @ShopId = @TestShopId, @ForceReRun = 1;
    PRINT N'✓ 强制重新执行成功';
END TRY
BEGIN CATCH
    PRINT N'✗ 强制重新执行失败: ' + ERROR_MESSAGE();
END CATCH

-- ====================================================================
-- 测试4：首次执行 usp_RunNormalizedDailyReportJob_V2
-- ====================================================================

PRINT N'';
PRINT N'测试4：首次执行 usp_RunNormalizedDailyReportJob_V2';
PRINT N'------------------------------------------';

BEGIN TRY
    EXEC dbo.usp_RunNormalizedDailyReportJob_V2 @TargetDate = @TestDate, @ShopId = @TestShopId;
    PRINT N'✓ 首次执行成功';
END TRY
BEGIN CATCH
    PRINT N'✗ 首次执行失败: ' + ERROR_MESSAGE();
END CATCH

-- ====================================================================
-- 测试5：重复执行 usp_RunNormalizedDailyReportJob_V2（不强制）
-- ====================================================================

PRINT N'';
PRINT N'测试5：重复执行 usp_RunNormalizedDailyReportJob_V2（不强制）';
PRINT N'------------------------------------------';

BEGIN TRY
    EXEC dbo.usp_RunNormalizedDailyReportJob_V2 @TargetDate = @TestDate, @ShopId = @TestShopId;
    PRINT N'✓ 重复执行（不强制）- 应该跳过';
END TRY
BEGIN CATCH
    PRINT N'✗ 重复执行失败: ' + ERROR_MESSAGE();
END CATCH

-- ====================================================================
-- 测试6：强制重新执行 usp_RunNormalizedDailyReportJob_V2
-- ====================================================================

PRINT N'';
PRINT N'测试6：强制重新执行 usp_RunNormalizedDailyReportJob_V2';
PRINT N'------------------------------------------';

BEGIN TRY
    EXEC dbo.usp_RunNormalizedDailyReportJob_V2 @TargetDate = @TestDate, @ShopId = @TestShopId, @ForceReRun = 1;
    PRINT N'✓ 强制重新执行成功';
END TRY
BEGIN CATCH
    PRINT N'✗ 强制重新执行失败: ' + ERROR_MESSAGE();
END CATCH

-- ====================================================================
-- 验证数据
-- ====================================================================

PRINT N'';
PRINT N'验证数据';
PRINT N'------------------------------------------';

-- 检查 usp_RunDailyReportJob 生成的数据
SELECT 
    ReportDate,
    ShopName,
    TotalRevenue,
    TotalBatchCount,
    FreeMeal_BatchCount,
    Buyout_BatchCount,
    Changyin_BatchCount
FROM dbo.KTV_Simplified_Daily_Report 
WHERE ReportDate = @TestDate
ORDER BY ReportDate DESC;

-- 检查 usp_RunNormalizedDailyReportJob_V2 生成的数据
SELECT 
    ReportDate,
    ShopName,
    TotalRevenue,
    TotalBatchCount,
    DayTimeDirectFall,
    MealDirectFallBatchCount
FROM dbo.FullDailyReport_Header 
WHERE ReportDate = @TestDate
ORDER BY ReportDate DESC;

-- 检查执行日志
SELECT TOP 10
    JobName,
    ReportDate,
    [Status],
    [Message],
    ExecutionTime
FROM dbo.JobExecutionLog 
WHERE ReportDate = @TestDate
ORDER BY ExecutionTime DESC;

PRINT N'';
PRINT N'==========================================';
PRINT N'测试完成！';
PRINT N'==========================================';
