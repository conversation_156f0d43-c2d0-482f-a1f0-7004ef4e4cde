
-- 确保在正确的数据库上下文中执行
USE dbfood;
GO

-- 1. 创建新的从表 RoomStatusHourlyDetail
PRINT 'Step 1: Creating table RoomStatusHourlyDetail...';
IF OBJECT_ID('RoomStatusHourlyDetail', 'U') IS NOT NULL
BEGIN
    PRINT 'Table RoomStatusHourlyDetail already exists.';
END
ELSE
BEGIN
    CREATE TABLE RoomStatusHourlyDetail (
        DetailID INT IDENTITY(1,1) PRIMARY KEY,
        LogID INT NOT NULL,
        RmStatus VARCHAR(10) NOT NULL,
        StatusCount INT NOT NULL,
        CONSTRAINT FK_RoomStatusHourlyDetail_LogID FOREIGN KEY (LogID) REFERENCES RoomStatisticsHourly(LogID) ON DELETE CASCADE
    );
    PRINT 'Table RoomStatusHourlyDetail created successfully.';
END
GO

-- 2. 重写核心存储过程以支持主从表结构
PRINT 'Step 2: Rewriting stored procedure usp_LogHourlyRoomStatistics...';
IF OBJECT_ID('usp_LogHourlyRoomStatistics', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE usp_LogHourlyRoomStatistics;
END
GO

CREATE PROCEDURE usp_LogHourlyRoomStatistics
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION; -- 将所有操作包含在一个事务中，确保数据一致性

    DECLARE @NewLogID INT;
    DECLARE @TotalRoomsBeforeFilter INT, @ValidRoomsCount INT, @BadRoomsCount INT, @OccupiedRoomsCount INT;
    DECLARE @OccupancyRate DECIMAL(5, 2);

    -- 计算总览数据 (和之前一样)
    SELECT @TotalRoomsBeforeFilter = COUNT(*) FROM dbo.ROOM;

    SELECT 
        @ValidRoomsCount = COUNT(*),
        @BadRoomsCount = SUM(CASE WHEN RmStatus = 'B' THEN 1 ELSE 0 END),
        @OccupiedRoomsCount = SUM(CASE WHEN RmStatus <> 'N' AND RmStatus <> 'B' THEN 1 ELSE 0 END)
    FROM 
        dbo.ROOM
    WHERE 
        RmNo NOT LIKE '%B';

    IF (ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0)) > 0
        SET @OccupancyRate = (CAST(ISNULL(@OccupiedRoomsCount, 0) AS DECIMAL(10, 2)) / (ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0))) * 100;
    ELSE
        SET @OccupancyRate = 0;

    -- a. 向主表插入总览记录
    INSERT INTO dbo.RoomStatisticsHourly (
        TotalRoomsBeforeFilter, ValidRoomsCount, BadRoomsCount, AvailableRoomsCount, OccupancyRate
    ) 
    VALUES (
        @TotalRoomsBeforeFilter, ISNULL(@ValidRoomsCount, 0), ISNULL(@BadRoomsCount, 0), 
        ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0), @OccupancyRate
    );

    -- b. 获取刚刚插入的主表记录的ID
    SET @NewLogID = SCOPE_IDENTITY();

    -- c. 统计每种状态的数量，并插入到从表
    INSERT INTO dbo.RoomStatusHourlyDetail (LogID, RmStatus, StatusCount)
    SELECT 
        @NewLogID, 
        RmStatus, 
        COUNT(*)
    FROM 
        dbo.ROOM
    WHERE 
        RmNo NOT LIKE '%B' -- 应用和主表相同的过滤条件
    GROUP BY 
        RmStatus;

    COMMIT TRANSACTION;
    PRINT 'Hourly room statistics (Master and Detail) logged successfully.';
END
GO

PRINT 'Stored procedure usp_LogHourlyRoomStatistics rewritten successfully.';
GO
