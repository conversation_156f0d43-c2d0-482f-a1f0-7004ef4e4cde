
-- 确保在正确的数据库上下文中执行
USE dbfood;
GO

-- 1. 修改 RoomStatisticsHourly 表结构
PRINT 'Step 1: Modifying table RoomStatisticsHourly...';

-- 删除不再需要的 OccupancyRate 列
IF COL_LENGTH('RoomStatisticsHourly', 'OccupancyRate') IS NOT NULL
BEGIN
    ALTER TABLE RoomStatisticsHourly DROP COLUMN OccupancyRate;
    PRINT 'Column OccupancyRate dropped.';
END

-- 添加新的状态计数字段
IF COL_LENGTH('RoomStatisticsHourly', 'Status_A_Count') IS NULL
BEGIN
    ALTER TABLE RoomStatisticsHourly ADD Status_A_Count INT NULL;
    PRINT 'Column Status_A_Count added.';
END

IF COL_LENGTH('RoomStatisticsHourly', 'Status_B_Count') IS NULL
BEGIN
    ALTER TABLE RoomStatisticsHourly ADD Status_B_Count INT NULL;
    PRINT 'Column Status_B_Count added.';
END

IF COL_LENGTH('RoomStatisticsHourly', 'Status_E_Count') IS NULL
BEGIN
    ALTER TABLE RoomStatisticsHourly ADD Status_E_Count INT NULL;
    PRINT 'Column Status_E_Count added.';
END

IF COL_LENGTH('RoomStatisticsHourly', 'Status_U_Count') IS NULL
BEGIN
    ALTER TABLE RoomStatisticsHourly ADD Status_U_Count INT NULL;
    PRINT 'Column Status_U_Count added.';
END

PRINT 'Table RoomStatisticsHourly modified successfully.';
GO

-- 2. 重写存储过程以填充新字段
PRINT 'Step 2: Rewriting stored procedure usp_LogHourlyRoomStatistics...';
IF OBJECT_ID('usp_LogHourlyRoomStatistics', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE usp_LogHourlyRoomStatistics;
END
GO

CREATE PROCEDURE usp_LogHourlyRoomStatistics
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @TotalRoomsBeforeFilter INT, @ValidRoomsCount INT, @BadRoomsCount INT;
    DECLARE @Status_A INT, @Status_B INT, @Status_E INT, @Status_U INT;

    -- 计算总览数据
    SELECT @TotalRoomsBeforeFilter = COUNT(*) FROM dbo.ROOM;

    -- 使用条件聚合一次性计算所有需要的状态数量
    -- 注意：这里的统计是基于 *有效房间* (RmNo NOT LIKE '%B')
    SELECT 
        @ValidRoomsCount = COUNT(*),
        @BadRoomsCount = SUM(CASE WHEN RmStatus = 'B' THEN 1 ELSE 0 END),
        @Status_A = SUM(CASE WHEN RmStatus = 'A' THEN 1 ELSE 0 END),
        @Status_E = SUM(CASE WHEN RmStatus = 'E' THEN 1 ELSE 0 END),
        @Status_U = SUM(CASE WHEN RmStatus = 'U' THEN 1 ELSE 0 END)
    FROM 
        dbo.ROOM
    WHERE 
        RmNo NOT LIKE '%B';

    -- 单独统计所有房间中的坏房数 (RmStatus = 'B')，无论其RmNo是否带'B'
    SELECT @Status_B = COUNT(*) FROM dbo.ROOM WHERE RmStatus = 'B';

    -- 插入统计结果
    INSERT INTO dbo.RoomStatisticsHourly (
        TotalRoomsBeforeFilter,
        ValidRoomsCount,
        BadRoomsCount,
        AvailableRoomsCount,
        Status_A_Count,
        Status_B_Count,
        Status_E_Count,
        Status_U_Count
    ) 
    VALUES (
        @TotalRoomsBeforeFilter,
        ISNULL(@ValidRoomsCount, 0),
        ISNULL(@BadRoomsCount, 0), -- 这是有效房间中的坏房数
        ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0),
        ISNULL(@Status_A, 0),
        ISNULL(@Status_B, 0), -- 这是总的坏房数
        ISNULL(@Status_E, 0),
        ISNULL(@Status_U, 0)
    );

    PRINT 'Hourly room statistics with specific status counts logged successfully.';
END
GO

PRINT 'Stored procedure usp_LogHourlyRoomStatistics rewritten successfully.';
GO
