-- ====================================================================
-- KTV每日报告数据收集存储过程
-- 功能：通过门店名称和日期自动收集并存储KTV每日报告数据
-- 创建时间: 2025-01-24
-- 版本: 1.0
-- ====================================================================

USE OperateData;
GO

-- 如果存储过程已存在，则删除
IF OBJECT_ID('dbo.usp_CollectKTVDailyData', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_CollectKTVDailyData;
GO

CREATE PROCEDURE dbo.usp_CollectKTVDailyData
    @ShopName NVARCHAR(100),        -- 门店名称，如"名堂店"
    @TargetDate VARCHAR(8),         -- 目标日期，格式：YYYYMMDD，如"20250720"
    @Debug BIT = 0                  -- 调试模式开关，默认关闭
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 声明变量
    DECLARE @ShopId INT = 0;
    DECLARE @BeginDate DATE;
    DECLARE @EndDate DATE;
    DECLARE @ErrorMessage NVARCHAR(4000);
    DECLARE @RowCount INT = 0;
    DECLARE @StartTime DATETIME2 = GETDATE();
    
    BEGIN TRY
        -- ====================================================================
        -- 第一步：参数验证和预处理
        -- ====================================================================
        
        -- 验证门店名称参数
        IF @ShopName IS NULL OR LTRIM(RTRIM(@ShopName)) = ''
        BEGIN
            RAISERROR('门店名称不能为空', 16, 1);
            RETURN;
        END
        
        -- 验证日期参数
        IF @TargetDate IS NULL OR LEN(@TargetDate) != 8 OR ISNUMERIC(@TargetDate) = 0
        BEGIN
            RAISERROR('日期格式错误，请使用YYYYMMDD格式，如：20250720', 16, 1);
            RETURN;
        END
        
        -- 转换日期格式
        BEGIN TRY
            SET @BeginDate = CAST(@TargetDate AS DATE);
            SET @EndDate = @BeginDate;
        END TRY
        BEGIN CATCH
            RAISERROR('日期转换失败，请检查日期格式是否正确', 16, 1);
            RETURN;
        END CATCH
        
        -- 验证日期不能是未来日期
        IF @BeginDate > CAST(GETDATE() AS DATE)
        BEGIN
            RAISERROR('不能收集未来日期的数据', 16, 1);
            RETURN;
        END
        
        -- ====================================================================
        -- 第二步：获取门店ID
        -- ====================================================================
        
        -- 通过门店名称获取ShopId
        SELECT @ShopId = Shopid 
        FROM MIMS.dbo.ShopInfo 
        WHERE ShopName = @ShopName;
        
        -- 验证门店是否存在
        IF @ShopId IS NULL OR @ShopId <= 0
        BEGIN
            SET @ErrorMessage = N'未找到门店：' + @ShopName + N'，请检查门店名称是否正确';
            RAISERROR(@ErrorMessage, 16, 1);
            RETURN;
        END
        
        -- 调试信息输出
        IF @Debug = 1
        BEGIN
            PRINT N'=== 参数验证完成 ===';
            PRINT N'门店名称: ' + @ShopName;
            PRINT N'门店ID: ' + CAST(@ShopId AS NVARCHAR(10));
            PRINT N'目标日期: ' + @TargetDate;
            PRINT N'开始时间: ' + CONVERT(NVARCHAR(23), @StartTime, 121);
        END
        
        -- ====================================================================
        -- 第三步：检查数据是否已存在
        -- ====================================================================
        
        SELECT @RowCount = COUNT(*)
        FROM dbo.KTV_DailyReport_Comprehensive
        WHERE 日期 = @TargetDate AND 门店 = @ShopName;
        
        IF @RowCount > 0
        BEGIN
            -- 如果数据已存在，先删除旧数据
            DELETE FROM dbo.KTV_DailyReport_Comprehensive
            WHERE 日期 = @TargetDate AND 门店 = @ShopName;
            
            IF @Debug = 1
            BEGIN
                PRINT N'已删除现有数据，记录数: ' + CAST(@RowCount AS NVARCHAR(10));
            END
        END
        
        -- ====================================================================
        -- 第四步：调用现有的数据生成存储过程
        -- ====================================================================
        
        -- 调用现有的优化存储过程生成数据
        IF @Debug = 1
        BEGIN
            PRINT N'=== 开始调用数据生成存储过程 ===';
            PRINT N'调用: usp_GenerateFullDailyReport_Enhanced_Simple';
            PRINT N'参数: @ShopId=' + CAST(@ShopId AS NVARCHAR(10)) + 
                  N', @BeginDate=' + CONVERT(NVARCHAR(10), @BeginDate, 120) + 
                  N', @EndDate=' + CONVERT(NVARCHAR(10), @EndDate, 120);
        END
        
        -- 由于现有存储过程返回完整的数据集，我们需要直接调用并让它处理数据插入
        -- 这里我们使用一个更简单的方法：直接调用现有的数据生成逻辑并手动插入基础数据

        -- 声明变量存储计算结果
        DECLARE @TotalRevenue DECIMAL(18,2) = 0;
        DECLARE @DayTimeRevenue DECIMAL(18,2) = 0;
        DECLARE @NightTimeRevenue DECIMAL(18,2) = 0;
        DECLARE @TotalBatchCount INT = 0;
        DECLARE @DayTimeBatchCount INT = 0;
        DECLARE @NightTimeBatchCount INT = 0;
        DECLARE @TotalGuestCount INT = 0;
        DECLARE @BuffetGuestCount INT = 0;
        DECLARE @WeekdayName NVARCHAR(20);

        -- 计算总览数据
        SELECT
            @TotalRevenue = ISNULL(SUM(rt.TotalAmount), 0),
            @DayTimeRevenue = ISNULL(SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END), 0),
            @NightTimeRevenue = ISNULL(SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END), 0),
            @TotalBatchCount = COUNT(rt.InvNo),
            @DayTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END),
            @NightTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END),
            @TotalGuestCount = ISNULL(SUM(rt.Numbers), 0),
            @BuffetGuestCount = ISNULL(SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END), 0),
            @WeekdayName = DATENAME(weekday, @BeginDate)
        FROM dbo.RmCloseInfo_Test AS rt WITH(NOLOCK)
        WHERE rt.ShopId = @ShopId
            AND CAST(rt.WorkDate AS date) = @BeginDate
            AND rt.OpenDateTime IS NOT NULL;

        -- 插入基础数据到综合表
        INSERT INTO dbo.KTV_DailyReport_Comprehensive (
            日期, 门店, 星期, 营收_总收入, 营收_白天档, 营收_晚上档,
            带客_全天总批数, 带客_白天档_总批次, 带客_晚上档_总批次,
            用餐_总人数, 用餐_自助餐人数
        )
        VALUES (
            @TargetDate,
            @ShopName,
            @WeekdayName,
            @TotalRevenue,
            @DayTimeRevenue,
            @NightTimeRevenue,
            @TotalBatchCount,
            @DayTimeBatchCount,
            @NightTimeBatchCount,
            @TotalGuestCount,
            @BuffetGuestCount
        );
        
        -- ====================================================================
        -- 第五步：验证数据生成结果
        -- ====================================================================
        
        -- 检查数据是否成功生成
        SELECT @RowCount = COUNT(*)
        FROM dbo.KTV_DailyReport_Comprehensive
        WHERE 日期 = @TargetDate AND 门店 = @ShopName;
        
        IF @RowCount = 0
        BEGIN
            RAISERROR('数据生成失败，请检查指定日期是否有业务数据', 16, 1);
            RETURN;
        END
        
        -- ====================================================================
        -- 第六步：输出执行结果
        -- ====================================================================
        
        DECLARE @EndTime DATETIME2 = GETDATE();
        DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, @EndTime);
        
        -- 成功消息
        PRINT N'=== 数据收集完成 ===';
        PRINT N'门店: ' + @ShopName + N' (ID: ' + CAST(@ShopId AS NVARCHAR(10)) + N')';
        PRINT N'日期: ' + @TargetDate;
        PRINT N'生成记录数: ' + CAST(@RowCount AS NVARCHAR(10));
        PRINT N'执行时间: ' + CAST(@Duration AS NVARCHAR(10)) + N' 毫秒';
        PRINT N'完成时间: ' + CONVERT(NVARCHAR(23), @EndTime, 121);
        
        -- 显示生成的数据摘要
        SELECT 
            日期,
            门店,
            星期,
            营收_总收入,
            营收_白天档,
            营收_晚上档,
            带客_全天总批数,
            带客_白天档_总批次,
            带客_晚上档_总批次,
            用餐_总人数,
            用餐_自助餐人数
        FROM dbo.KTV_DailyReport_Comprehensive
        WHERE 日期 = @TargetDate AND 门店 = @ShopName;
        
    END TRY
    BEGIN CATCH
        -- 错误处理
        SET @ErrorMessage = N'执行过程中发生错误: ' + ERROR_MESSAGE() + 
                           N' (错误号: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + 
                           N', 行号: ' + CAST(ERROR_LINE() AS NVARCHAR(10)) + N')';
        
        PRINT N'=== 执行失败 ===';
        PRINT @ErrorMessage;
        
        -- 抛出错误
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT N'存储过程 usp_CollectKTVDailyData 创建完成';
PRINT N'使用示例: EXEC usp_CollectKTVDailyData @ShopName = N''名堂店'', @TargetDate = ''20250720''';
GO
