USE OperateData;
GO

IF OBJECT_ID('dbo.usp_PopulateKTVReport', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_PopulateKTVReport;
GO

CREATE PROCEDURE dbo.usp_PopulateKTVReport
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    IF @ShopId <= 0
    BEGIN
        RAISERROR(N'门店ID必须大于0', 16, 1);
        RETURN;
    END

    BEGIN TRY
        DECLARE @CurrentDate date = @BeginDate;
        
        WHILE @CurrentDate <= @EndDate
        BEGIN
            DECLARE @CurrentWorkDate varchar(8) = CONVERT(varchar(8), @CurrentDate, 112);
            
            DELETE FROM dbo.KTV_DailyReport_Comprehensive 
            WHERE 日期 = @CurrentWorkDate;
            
            DECLARE @TotalRevenue decimal(18,2), @DayTimeRevenue decimal(18,2), @NightTimeRevenue decimal(18,2);
            DECLARE @TotalBatchCount int, @DayTimeBatchCount int, @NightTimeBatchCount int;
            DECLARE @DayTimeDropInBatch int, @NightTimeDropInBatch int;
            DECLARE @TotalGuestCount int, @BuffetGuestCount int, @TotalDropInGuests int;
            DECLARE @ShopName nvarchar(100), @WeekdayName nvarchar(20);

            SELECT 
                @TotalRevenue = SUM(rt.TotalAmount),
                @DayTimeRevenue = SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END),
                @NightTimeRevenue = SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END),
                @TotalBatchCount = COUNT(rt.InvNo),
                @DayTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END),
                @NightTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END),
                @TotalGuestCount = SUM(rt.Numbers),
                @BuffetGuestCount = SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END),
                @ShopName = MAX(b.ShopName),
                @WeekdayName = DATENAME(weekday, @CurrentDate),
                @DayTimeDropInBatch = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END),
                @NightTimeDropInBatch = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END),
                @TotalDropInGuests = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END)
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
            LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
            LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
            LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
            WHERE rt.ShopId = @ShopId 
              AND rt.WorkDate = @CurrentWorkDate 
              AND rt.OpenDateTime IS NOT NULL;

            INSERT INTO dbo.KTV_DailyReport_Comprehensive (
                日期, 门店, 星期, 营收_总收入, 营收_白天档, 营收_晚上档,
                带客_全天总批数, 带客_白天档_总批次, 带客_晚上档_总批次,
                带客_白天档_直落, 带客_晚上档_直落,
                用餐_总人数, 用餐_自助餐人数, 用餐_直落人数
            ) VALUES (
                @CurrentWorkDate, 
                ISNULL(@ShopName, ''), 
                ISNULL(@WeekdayName, ''),
                ISNULL(@TotalRevenue, 0), 
                ISNULL(@DayTimeRevenue, 0), 
                ISNULL(@NightTimeRevenue, 0),
                ISNULL(@TotalBatchCount, 0), 
                ISNULL(@DayTimeBatchCount, 0), 
                ISNULL(@NightTimeBatchCount, 0),
                ISNULL(@DayTimeDropInBatch, 0), 
                ISNULL(@NightTimeDropInBatch, 0),
                ISNULL(@TotalGuestCount, 0), 
                ISNULL(@BuffetGuestCount, 0), 
                ISNULL(@TotalDropInGuests, 0)
            );

            CREATE TABLE #TimeSlotData (
                TimeName nvarchar(50),
                KPlus int,
                Special int,
                Meituan int,
                Douyin int,
                RoomFee int,
                Subtotal int,
                DropInFromPrevious int
            );

            INSERT INTO #TimeSlotData
            EXEC sp_executesql N'
            WITH TimeSlots AS (
                SELECT
                    ti.TimeNo, ti.TimeName, ti.BegTime,
                    DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@TargetDate AS datetime))) AS SlotStartDateTime,
                    LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@TargetDate AS datetime))), 1, ''2999-12-31'') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
                FROM dbo.shoptimeinfo AS sti
                JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
                WHERE sti.ShopId = @ShopIdParam
            ),
            TrueDropInData AS (
                SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
                FROM dbo.RmCloseInfo_Test AS rt
                JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
                JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
                WHERE rt.ShopId = @ShopIdParam 
                  AND rt.WorkDate = @WorkDateParam 
                  AND rt.OpenDateTime IS NOT NULL
                  AND rt.Beg_Key <> rt.End_Key
                  AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
                  AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
            )
            SELECT 
                ti_main.TimeName,
                COUNT(CASE WHEN rt.CtNo = 2 THEN 1 ELSE NULL END) AS KPlus,
                COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS Special,
                COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS Meituan,
                COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS Douyin,
                COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS RoomFee,
                COUNT(rt.InvNo) AS Subtotal,
                ISNULL((
                    SELECT COUNT(tdi.InvNo)
                    FROM TrueDropInData AS tdi
                    JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo
                    WHERE ts_beg.BegTime < ti_main.BegTime
                      AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime
                ), 0) AS DropInFromPrevious
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
            JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
            WHERE rt.ShopId = @ShopIdParam
              AND rt.WorkDate = @WorkDateParam
              AND rt.OpenDateTime IS NOT NULL
              AND ti_main.BegTime < 2000
            GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
            ORDER BY ti_main.BegTime',
            N'@ShopIdParam int, @WorkDateParam varchar(8), @TargetDate date',
            @ShopIdParam = @ShopId,
            @WorkDateParam = @CurrentWorkDate,
            @TargetDate = @CurrentDate;

            DECLARE @TimeName nvarchar(50), @KPlus int, @Special int, @Meituan int, @Douyin int, @RoomFee int, @Subtotal int, @DropIn int;
            
            DECLARE time_cursor CURSOR FOR
            SELECT TimeName, KPlus, Special, Meituan, Douyin, RoomFee, Subtotal, DropInFromPrevious
            FROM #TimeSlotData;

            OPEN time_cursor;
            FETCH NEXT FROM time_cursor INTO @TimeName, @KPlus, @Special, @Meituan, @Douyin, @RoomFee, @Subtotal, @DropIn;

            WHILE @@FETCH_STATUS = 0
            BEGIN
                IF @TimeName = '10:50-13:50'
                BEGIN
                    UPDATE dbo.KTV_DailyReport_Comprehensive 
                    SET [10:50-13:50_K+] = @KPlus, [10:50-13:50_特权预约] = @Special, [10:50-13:50_美团] = @Meituan, [10:50-13:50_抖音] = @Douyin, [10:50-13:50_房费] = @RoomFee, [10:50-13:50_小计] = @Subtotal, [10:50-13:50_上一档直落] = @DropIn
                    WHERE 日期 = @CurrentWorkDate;
                END
                ELSE IF @TimeName = '11:50-14:50'
                BEGIN
                    UPDATE dbo.KTV_DailyReport_Comprehensive 
                    SET [11:50-14:50_K+] = @KPlus, [11:50-14:50_特权预约] = @Special, [11:50-14:50_美团] = @Meituan, [11:50-14:50_抖音] = @Douyin, [11:50-14:50_房费] = @RoomFee, [11:50-14:50_小计] = @Subtotal, [11:50-14:50_上一档直落] = @DropIn
                    WHERE 日期 = @CurrentWorkDate;
                END
                ELSE IF @TimeName = '13:30-16:30'
                BEGIN
                    UPDATE dbo.KTV_DailyReport_Comprehensive 
                    SET [13:30-16:30_K+] = @KPlus, [13:30-16:30_特权预约] = @Special, [13:30-16:30_美团] = @Meituan, [13:30-16:30_抖音] = @Douyin, [13:30-16:30_房费] = @RoomFee, [13:30-16:30_小计] = @Subtotal, [13:30-16:30_上一档直落] = @DropIn
                    WHERE 日期 = @CurrentWorkDate;
                END
                ELSE IF @TimeName = '14:00-17:00'
                BEGIN
                    UPDATE dbo.KTV_DailyReport_Comprehensive 
                    SET [14:00-17:00_K+] = @KPlus, [14:00-17:00_特权预约] = @Special, [14:00-17:00_美团] = @Meituan, [14:00-17:00_抖音] = @Douyin, [14:00-17:00_房费] = @RoomFee, [14:00-17:00_小计] = @Subtotal, [14:00-17:00_上一档直落] = @DropIn
                    WHERE 日期 = @CurrentWorkDate;
                END
                ELSE IF @TimeName = '15:00-18:00'
                BEGIN
                    UPDATE dbo.KTV_DailyReport_Comprehensive 
                    SET [15:00-18:00_K+] = @KPlus, [15:00-18:00_特权预约] = @Special, [15:00-18:00_美团] = @Meituan, [15:00-18:00_抖音] = @Douyin, [15:00-18:00_房费] = @RoomFee, [15:00-18:00_小计] = @Subtotal, [15:00-18:00_上一档直落] = @DropIn
                    WHERE 日期 = @CurrentWorkDate;
                END

                FETCH NEXT FROM time_cursor INTO @TimeName, @KPlus, @Special, @Meituan, @Douyin, @RoomFee, @Subtotal, @DropIn;
            END

            CLOSE time_cursor;
            DEALLOCATE time_cursor;

            DROP TABLE #TimeSlotData;

            SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
        END

        IF @Debug = 1
        BEGIN
            PRINT N'数据填充完成';
            PRINT N'查询日期范围: ' + CONVERT(nvarchar(10), @BeginDate, 120) + N' 到 ' + CONVERT(nvarchar(10), @EndDate, 120);
            PRINT N'门店ID: ' + CAST(@ShopId AS nvarchar(10));
        END

    END TRY
    BEGIN CATCH
        IF OBJECT_ID('tempdb..#TimeSlotData') IS NOT NULL
            DROP TABLE #TimeSlotData;
            
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
