CREATE PROCEDURE dbo.usp_RunDailyReportJob

    @TargetDate DATE = NULL, -- 允许手动指定日期进行测试

    @ShopId INT = 3 -- 将ShopId作为参数

AS

BEGIN

    SET NOCOUNT ON;



    -- 如果未指定日期，则默认为昨天

    IF @TargetDate IS NULL

    BEGIN

        SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    END



    -- 检查当天的数据是否已经存在，防止重复插入

    IF EXISTS (SELECT 1 FROM dbo.KTV_Simplified_Daily_Report WHERE ReportDate = @TargetDate AND ShopName = (SELECT ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId))

    BEGIN

        DECLARE @SkipMessage NVARCHAR(500) = N'Skipped: Data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' already exists.';

        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])

        VALUES (N'KTV_Daily_Report', @TargetDate, N'Skipped', @SkipMessage);

        PRINT @SkipMessage;

        RETURN;

    END



    BEGIN TRANSACTION;



    BEGIN TRY

        -- 准备一个临时表来接收存储过程的输出

        -- 这是将EXEC的输出插入表的标准方法

        CREATE TABLE #TempReportData (

            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),

            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),

            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,

            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,

            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),

            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),

            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),

            FreeConsumption_BatchCount int,

            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,

            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2)

        );



        -- 执行核心存储过程，并将结果存入临时表

        INSERT INTO #TempReportData

        EXEC dbo.usp_GenerateSimplifiedDailyReport @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;



        -- 将临时表的数据正式插入目标表

        INSERT INTO dbo.KTV_Simplified_Daily_Report (

            ReportDate, ShopName, Weekday, TotalRevenue, DayTimeRevenue, NightTimeRevenue,

            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount, FreeMeal_KPlus, FreeMeal_Special,

            FreeMeal_Meituan, FreeMeal_Douyin, FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount,

            Buyout_Revenue, Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount,

            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others,

            Night_Verify_BatchCount, Night_Verify_Revenue

        )

        SELECT * FROM #TempReportData;



        -- 如果没有错误，提交事务

        COMMIT TRANSACTION;



        -- 记录成功日志

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully.';

        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])

        VALUES (N'KTV_Daily_Report', @TargetDate, N'Success', @SuccessMessage);

        PRINT @SuccessMessage;



        -- 删除临时表

        DROP TABLE #TempReportData;



    END TRY

    BEGIN CATCH

        -- 如果发生错误，回滚事务

        IF @@TRANCOUNT > 0

            ROLLBACK TRANSACTION;



        -- 记录失败日志

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();

        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;

        

        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])

        VALUES (N'KTV_Daily_Report', @TargetDate, N'Failure', @ErrorLogMessage);

        

        PRINT @ErrorLogMessage;



        -- 重新引发错误，以便SQL Agent能捕获到失败状态

        RAISERROR (@ErrorMessage, 16, 1);

    END CATCH

END