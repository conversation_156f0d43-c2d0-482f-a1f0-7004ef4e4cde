
-- Final, Corrected Version of the Job Procedure
CREATE PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2
    @TargetDate DATE = NULL,
    @ShopId INT = 3
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Normalized_Full_Report';

    BEGIN TRANSACTION;

    BEGIN TRY
        -- Step 0: Delete existing data for the given ReportDate and ShopId
        PRINT N'Step 0: Deleting existing data...';
        DECLARE @ReportIDsToDelete TABLE (ReportID INT);
        INSERT INTO @ReportIDsToDelete (ReportID)
        SELECT ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId;

        IF EXISTS (SELECT 1 FROM @ReportIDsToDelete)
        BEGIN
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID IN (SELECT ReportID FROM @ReportIDsToDelete);
            DELETE FROM dbo.FullDailyReport_Header WHERE ReportID IN (SELECT ReportID FROM @ReportIDsToDelete);
        END

        -- Step 1: Call the V3 overview SP to get header data
        PRINT N'Step 1: Getting header data from usp_GenerateDayTimeReport_Simple_V3...';
        -- This temp table schema now EXACTLY matches the output of the V3 procedure
        CREATE TABLE #TempHeader (
            WorkDate DATE, ShopName NVARCHAR(100), WeekdayName NVARCHAR(20),
            DayTimeRevenue DECIMAL(18,2), NightTimeRevenue DECIMAL(18,2), TotalRevenue DECIMAL(18,2),
            DayTimeBatchCount INT, NightTimeBatchCount INT, TotalBatchCount INT,
            TotalGuestCount INT, BuffetGuestCount INT,
            DayTimeDropInBatch INT, NightTimeDropInBatch INT, TotalDirectFallGuests INT
        );
        INSERT INTO #TempHeader
        EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;

        -- Step 2: Insert the new metrics into the corrected Header table
        PRINT N'Step 2: Inserting new metrics into FullDailyReport_Header...';
        -- The INSERT list now EXACTLY matches the corrected table schema and the temp table
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, 
            DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount,
            TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        )
        SELECT
            @TargetDate, @ShopId, ShopName, WeekdayName,
            DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount,
            TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        FROM #TempHeader;

        DECLARE @ReportID INT = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- Steps 3 & 4 for TimeSlotDetails remain the same...
        PRINT N'Step 3: Getting time slot details...';
        CREATE TABLE #TempDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        INSERT INTO #TempDetails
        EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;

        PRINT N'Step 4: Inserting time slot details...';
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        )
        SELECT 
            @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        FROM #TempDetails;
        
        DROP TABLE #TempHeader;
        DROP TABLE #TempDetails;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempDetails') IS NOT NULL DROP TABLE #TempDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
