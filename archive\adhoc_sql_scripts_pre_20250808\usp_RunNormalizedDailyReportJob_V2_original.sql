CREATE PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2

    @TargetDate DATE = NULL,

    @ShopId INT = 3

AS

BEGIN

    SET NOCOUNT ON;



    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);



    DECLARE @JobName NVARCHAR(255) = N'KTV_Normalized_Full_Report';



    IF EXISTS (SELECT 1 FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId)

    BEGIN

        DECLARE @SkipMessage NVARCHAR(500) = N'Skipped: Data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' and ShopID ' + CAST(@ShopId AS NVARCHAR) + N' already exists.';

        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Skipped', @SkipMessage);

        PRINT @SkipMessage;

        RETURN;

    END



    BEGIN TRANSACTION;



    BEGIN TRY

        -- 步骤 1: 调用V3版总览SP，获取所有Header数据

        PRINT N'Step 1: Getting header data from usp_GenerateDayTimeReport_Simple_V3...';

        CREATE TABLE #TempHeader (

            WorkDate varchar(8), ShopName nvarchar(100), WeekdayName nvarchar(20),

            TotalRevenue decimal(18,2), DayTimeRevenue decimal(18,2), NightTimeRevenue decimal(18,2),

            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,

            DayTimeDropInBatch int, NightTimeDropInBatch int, TotalGuestCount int,

            BuffetGuestCount int, TotalDropInGuests int, 

            MealBatchCount INT, MealDirectFallBatchCount INT, MealDirectFallRevenue DECIMAL(18,2),

            Night_FreeMeal_Subtotal int, Night_FreeMeal_Amount decimal(18,2), Night_After20_Revenue decimal(18,2)

        );

        INSERT INTO #TempHeader

        EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;



        -- 步骤 2: 将所有新指标插入Header表

        PRINT N'Step 2: Inserting all new metrics into FullDailyReport_Header...';

        INSERT INTO dbo.FullDailyReport_Header (

            ReportDate, ShopID, ShopName, Weekday, 

            TotalRevenue, DayTimeRevenue, NightTimeRevenue,

            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,

            DayTimeDirectFall, NightTimeDropInBatch, 

            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,

            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,

            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue

        )

        SELECT

            @TargetDate, @ShopId, ShopName, WeekdayName,

            TotalRevenue, DayTimeRevenue, NightTimeRevenue,

            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,

            DayTimeDropInBatch, NightTimeDropInBatch,

            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,

            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,

            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue

        FROM #TempHeader;



        DECLARE @ReportID INT = SCOPE_IDENTITY();

        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);



        -- 步骤 3: 调用时段详情SP，获取Details数据

        PRINT N'Step 3: Getting time slot details from usp_GetTimeSlotDetails_WithDirectFall...';

        CREATE TABLE #TempDetails (

            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,

            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,

            PreviousSlot_DirectFall INT

        );

        INSERT INTO #TempDetails

        EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;



        -- 步骤 4: 将Details数据插入详情表

        PRINT N'Step 4: Inserting data into FullDailyReport_TimeSlotDetails...';

        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (

            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 

            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall

        )

        SELECT 

            @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 

            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall

        FROM #TempDetails;

        

        PRINT N'Time slot details inserted.';



        DROP TABLE #TempHeader;

        DROP TABLE #TempDetails;



        COMMIT TRANSACTION;



        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Final modular report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);

        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);

        PRINT @SuccessMessage;



    END TRY

    BEGIN CATCH

        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;



        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;

        IF OBJECT_ID('tempdb..#TempDetails') IS NOT NULL DROP TABLE #TempDetails;



        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();

        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing final modular report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;

        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);

        

        PRINT @ErrorLogMessage;

        RAISERROR (@ErrorMessage, 16, 1);

    END CATCH

END