-- ====================================================================
-- KTV标准化每日报告存储过程V2 - 支持重复执行版本
-- 修改内容：支持重复执行，先删除再插入
-- 创建时间: 2025-07-25
-- ====================================================================

USE OperateData;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_RunNormalizedDailyReportJob_V2', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2;
GO

CREATE PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2
    @TargetDate DATE = NULL,
    @ShopId INT = 3,
    @ForceReRun BIT = 0 -- 新增参数：强制重新执行（删除后重新插入）
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Normalized_Full_Report';

    -- 检查当天的数据是否已经存在
    IF EXISTS (SELECT 1 FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId)
    BEGIN
        IF @ForceReRun = 1
        BEGIN
            -- 强制重新执行：删除现有数据（包括主表和详情表）
            DECLARE @ReportIDs TABLE (ReportID INT);
            
            -- 获取要删除的ReportID
            INSERT INTO @ReportIDs 
            SELECT ReportID 
            FROM dbo.FullDailyReport_Header 
            WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
            
            -- 先删除详情表数据
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails 
            WHERE ReportID IN (SELECT ReportID FROM @ReportIDs);
            
            -- 再删除主表数据
            DELETE FROM dbo.FullDailyReport_Header 
            WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
            
            DECLARE @DeleteMessage NVARCHAR(500) = N'Deleted: Existing data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' and ShopID ' + CAST(@ShopId AS NVARCHAR) + N' has been deleted for re-run.';
            INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Deleted', @DeleteMessage);
            PRINT @DeleteMessage;
        END
        ELSE
        BEGIN
            -- 不强制重新执行：跳过
            DECLARE @SkipMessage NVARCHAR(500) = N'Skipped: Data for report date ' + CONVERT(NVARCHAR, @TargetDate) + N' and ShopID ' + CAST(@ShopId AS NVARCHAR) + N' already exists. Use @ForceReRun=1 to re-run.';
            INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Skipped', @SkipMessage);
            PRINT @SkipMessage;
            RETURN;
        END
    END

    BEGIN TRANSACTION;

    BEGIN TRY
        -- 步骤 1: 调用V3版总览SP，获取所有Header数据
        PRINT N'Step 1: Getting header data from usp_GenerateDayTimeReport_Simple_V3...';
        CREATE TABLE #TempHeader (
            WorkDate varchar(8), ShopName nvarchar(100), WeekdayName nvarchar(20),
            TotalRevenue decimal(18,2), DayTimeRevenue decimal(18,2), NightTimeRevenue decimal(18,2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            DayTimeDropInBatch int, NightTimeDropInBatch int, TotalGuestCount int,
            BuffetGuestCount int, TotalDropInGuests int, 
            MealBatchCount INT, MealDirectFallBatchCount INT, MealDirectFallRevenue DECIMAL(18,2),
            Night_FreeMeal_Subtotal int, Night_FreeMeal_Amount decimal(18,2), Night_After20_Revenue decimal(18,2)
        );
        INSERT INTO #TempHeader
        EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;

        -- 步骤 2: 将所有新指标插入Header表
        PRINT N'Step 2: Inserting all new metrics into FullDailyReport_Header...';
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, 
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDirectFall, NightTimeDropInBatch, 
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue
        )
        SELECT
            @TargetDate, @ShopId, ShopName, WeekdayName,
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDropInBatch, NightTimeDropInBatch,
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue
        FROM #TempHeader;

        DECLARE @ReportID INT = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- 步骤 3: 调用时段详情SP，获取Details数据
        PRINT N'Step 3: Getting time slot details from usp_GetTimeSlotDetails_WithDirectFall...';
        CREATE TABLE #TempDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        INSERT INTO #TempDetails
        EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;

        -- 步骤 4: 将Details数据插入详情表
        PRINT N'Step 4: Inserting data into FullDailyReport_TimeSlotDetails...';
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        )
        SELECT 
            @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        FROM #TempDetails;
        
        PRINT N'Time slot details inserted.';

        -- 清理临时表
        DROP TABLE #TempHeader;
        DROP TABLE #TempDetails;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Final modular report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

    END TRY
    BEGIN CATCH
        -- 如果发生错误，回滚事务
        IF @@TRANCOUNT > 0 
            ROLLBACK TRANSACTION;

        -- 清理临时表
        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempDetails') IS NOT NULL DROP TABLE #TempDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;

        -- 重新引发错误，以便SQL Agent能捕获到失败状态
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO
