-- =================================================================================
-- Name: usp_RunUnifiedDailyReportJob (v4 - Corrected Night Details Insert)
-- =================================================================================
CREATE PROCEDURE dbo.usp_RunUnifiedDailyReportJob
    @TargetDate DATE = NULL,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report';
    DECLARE @ReportID INT;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- === Step 0: Clean up existing data ===
        PRINT N'Step 0: Deleting existing data for ' + CONVERT(NVARCHAR, @TargetDate) + N'...';
        
        SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId;

        IF @ReportID IS NOT NULL
        BEGIN
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;
            PRINT N'Deleted existing data for ReportID: ' + CAST(@ReportID AS NVARCHAR);
        END

        -- === Step 1: Generate and Insert Header Data ===
        PRINT N'Step 1: Generating Header data...';
        CREATE TABLE #TempHeader (
            WorkDate DATE, ShopName NVARCHAR(100), WeekdayName NVARCHAR(20),
            DayTimeRevenue DECIMAL(18,2), NightTimeRevenue DECIMAL(18,2), TotalRevenue DECIMAL(18,2),
            DayTimeBatchCount INT, NightTimeBatchCount INT, TotalBatchCount INT,
            TotalGuestCount INT, BuffetGuestCount INT,
            DayTimeDropInBatch INT, NightTimeDropInBatch INT, TotalDirectFallGuests INT
        );
        INSERT INTO #TempHeader
        EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;

        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        )
        SELECT
            @TargetDate, @ShopId, ShopName, WeekdayName, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        FROM #TempHeader;

        SET @ReportID = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- === Step 2: Generate and Insert FULL Night Details Data ===
        PRINT N'Step 2: Generating FULL Night Details data...';
        -- The temp table now matches the FULL output of the expanded V7
        CREATE TABLE #TempNightDetails (
            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
            FreeConsumption_BatchCount int,
            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
            DiscountFree_BatchCount INT, DiscountFree_Revenue DECIMAL(18, 2)
        );
        INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;

        -- The INSERT statement now explicitly selects the correct columns from #TempNightDetails
        INSERT INTO dbo.FullDailyReport_NightDetails (
            ReportID, FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others,
            DiscountFree_BatchCount, DiscountFree_Revenue
        ) SELECT
            @ReportID, 
            TND.FreeMeal_KPlus, TND.FreeMeal_Special, TND.FreeMeal_Meituan, TND.FreeMeal_Douyin, 
            TND.FreeMeal_BatchCount, TND.FreeMeal_Revenue, TND.Buyout_BatchCount, TND.Buyout_Revenue, 
            TND.Changyin_BatchCount, TND.Changyin_Revenue, TND.FreeConsumption_BatchCount, 
            TND.NonPackage_Special, TND.NonPackage_Meituan, TND.NonPackage_Douyin, TND.NonPackage_Others,
            TND.DiscountFree_BatchCount, TND.DiscountFree_Revenue 
        FROM #TempNightDetails TND;
        PRINT N'Night Details data inserted.';

        -- === Step 3: Generate and Insert Time Slot Details Data ===
        PRINT N'Step 3: Generating Time Slot Details data...';
        CREATE TABLE #TempTimeSlotDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        INSERT INTO #TempTimeSlotDetails
        EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        ) SELECT 
            @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall FROM #TempTimeSlotDetails;
        PRINT N'Time Slot Details data inserted.';

        -- Cleanup
        DROP TABLE #TempHeader;
        DROP TABLE #TempNightDetails;
        DROP TABLE #TempTimeSlotDetails;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;

        -- Cleanup temp tables if they exist
        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempNightDetails') IS NOT NULL DROP TABLE #TempNightDetails;
        IF OBJECT_ID('tempdb..#TempTimeSlotDetails') IS NOT NULL DROP TABLE #TempTimeSlotDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END