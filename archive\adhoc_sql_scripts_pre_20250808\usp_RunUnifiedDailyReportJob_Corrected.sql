-- =================================================================================
-- 修正版本: usp_RunUnifiedDailyReportJob_Corrected
-- 基于原有逻辑，只添加缺失的 DiscountFree 字段支持
-- 保留所有现有字段的完整插入逻辑
-- =================================================================================

CREATE OR ALTER PROCEDURE dbo.usp_RunUnifiedDailyReportJob_Corrected
    @TargetDate DATE = NULL,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report_Corrected';
    DECLARE @ReportID INT;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- === Step 0: Clean up existing data ===
        PRINT N'Step 0: Deleting existing data for ' + CONVERT(NVARCHAR, @TargetDate) + N'...';
        SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
        IF @ReportID IS NOT NULL
        BEGIN
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;
            DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;
        END

        -- === Step 1: Generate and Insert Header Data ===
        PRINT N'Step 1: Generating Header data...';
        CREATE TABLE #TempHeader (
            WorkDate DATE, ShopName NVARCHAR(100), WeekdayName NVARCHAR(20),
            DayTimeRevenue DECIMAL(18,2), NightTimeRevenue DECIMAL(18,2), TotalRevenue DECIMAL(18,2),
            DayTimeBatchCount INT, NightTimeBatchCount INT, TotalBatchCount INT,
            TotalGuestCount INT, BuffetGuestCount INT,
            DayTimeDropInBatch INT, NightTimeDropInBatch INT, TotalDirectFallGuests INT
        );
        INSERT INTO #TempHeader EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;
        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests
        ) SELECT @TargetDate, @ShopId, ShopName, WeekdayName, DayTimeRevenue, NightTimeRevenue, TotalRevenue,
            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,
            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests FROM #TempHeader;
        SET @ReportID = SCOPE_IDENTITY();
        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);

        -- === Step 2: Generate and Insert COMPLETE Night Details Data ===
        PRINT N'Step 2: Generating COMPLETE Night Details data with DiscountFree fields...';
        -- 临时表结构包含所有字段，包括新增的DiscountFree字段
        CREATE TABLE #TempNightDetails (
            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
            FreeConsumption_BatchCount int,
            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
            DiscountFree_BatchCount INT, DiscountFree_Revenue DECIMAL(18, 2)  -- 新增字段
        );
        
        -- 调用修正版本的存储过程
        INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Corrected @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;
        
        -- 完整字段插入 - 保留所有现有字段的逻辑
        INSERT INTO dbo.FullDailyReport_NightDetails (
            ReportID, 
            FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, 
            Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, 
            FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others,
            DiscountFree_BatchCount, DiscountFree_Revenue  -- 新增字段
        ) SELECT 
            @ReportID, 
            FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin, 
            FreeMeal_BatchCount, FreeMeal_Revenue, 
            Buyout_BatchCount, Buyout_Revenue, 
            Changyin_BatchCount, Changyin_Revenue, 
            FreeConsumption_BatchCount, 
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others,
            DiscountFree_BatchCount, DiscountFree_Revenue  -- 新增字段
        FROM #TempNightDetails;
        
        PRINT N'Complete Night Details data inserted (all fields including DiscountFree).';

        -- === Step 3: Generate and Insert Time Slot Details Data ===
        PRINT N'Step 3: Generating Time Slot Details data...';
        CREATE TABLE #TempTimeSlotDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );
        INSERT INTO #TempTimeSlotDetails EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;
        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        ) SELECT @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall FROM #TempTimeSlotDetails;
        PRINT N'Time Slot Details data inserted.';

        -- Cleanup
        DROP TABLE #TempHeader;
        DROP TABLE #TempNightDetails;
        DROP TABLE #TempTimeSlotDetails;

        COMMIT TRANSACTION;

        DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Complete unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
        PRINT @SuccessMessage;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempNightDetails') IS NOT NULL DROP TABLE #TempNightDetails;
        IF OBJECT_ID('tempdb..#TempTimeSlotDetails') IS NOT NULL DROP TABLE #TempTimeSlotDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing complete unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);
        
        PRINT @ErrorLogMessage;
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =================================================================================
-- 使用说明
-- =================================================================================

/*
🎯 修正说明：

1. 保留了所有现有字段的完整逻辑，包括：
   - FreeMeal_* 系列字段
   - NonPackage_Special/Meituan/Douyin 字段
   - 所有其他现有字段

2. 只添加了缺失的 DiscountFree 字段：
   - DiscountFree_BatchCount: COUNT(CASE WHEN rt.AccOkZD > 0 THEN 1 ELSE NULL END)
   - DiscountFree_Revenue: SUM(CASE WHEN rt.AccOkZD > 0 THEN rt.AccOkZD ELSE 0 END)

3. 完整字段插入到 FullDailyReport_NightDetails 表

🚀 部署步骤：

1. 先部署修正的存储过程：
   EXEC sp_executesql N'-- 部署 usp_GenerateSimplifiedDailyReport_V7_Final_Corrected'

2. 然后部署修正的主存储过程：
   EXEC sp_executesql N'-- 部署 usp_RunUnifiedDailyReportJob_Corrected'

3. 测试执行：
   EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected @TargetDate = '2025-07-29', @ShopId = 11

⚠️ 注意：
- 所有现有字段逻辑保持不变
- 只新增了 DiscountFree 相关的两个字段
- 确保 FullDailyReport_NightDetails 表包含所有必要的字段
*/
