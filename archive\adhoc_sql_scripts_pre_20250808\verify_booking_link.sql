-- =============================================
-- 验证脚本 (V4 - 最终版，先拉取后处理)
-- =============================================

PRINT N'--- 步骤 1: 准备临时数据表 ---';

CREATE TABLE #AllBookings_Raw (BookNo VARCHAR(20), ComeDate VARCHAR(20), ShopID INT, BookStatus INT, isdelete BIT);
CREATE TABLE #AllOccupancies_Raw (BookNo VARCHAR(20), ComeDate VARCHAR(20), ShopID INT, RmNo VARCHAR(20));

DECLARE @Sql NVARCHAR(MAX);

PRINT N'正在获取所有预订数据...';
SET @Sql = N'SELECT BookNo, ComeDate, ShopID, BookStatus, isdelete FROM OPENQUERY([cloudRms2019], ''SELECT BookNo, ComeDate, ShopID, BookStatus, isdelete FROM rms2019.dbo.bookcacheinfo'')';
INSERT INTO #AllBookings_Raw EXEC sp_executesql @Sql;
SET @Sql = N'SELECT BookNo, ComeDate, ShopID, BookStatus, isdelete FROM OPENQUERY([cloudRms2019], ''SELECT BookNo, ComeDate, ShopID, BookStatus, isdelete FROM rms2019.dbo.bookhistory'')';
INSERT INTO #AllBookings_Raw EXEC sp_executesql @Sql;

PRINT N'正在获取所有待客数据...';
SET @Sql = N'SELECT BookNo, ComeDate, ShopID, RmNo FROM OPENQUERY([cloudRms2019], ''SELECT BookNo, ComeDate, ShopID, RmNo FROM rms2019.dbo.opencacheinfo'')';
INSERT INTO #AllOccupancies_Raw EXEC sp_executesql @Sql;
SET @Sql = N'SELECT BookNo, ComeDate, ShopID, RmNo FROM OPENQUERY([cloudRms2019], ''SELECT BookNo, ComeDate, ShopID, RmNo FROM rms2019.dbo.openhistory'')';
INSERT INTO #AllOccupancies_Raw EXEC sp_executesql @Sql;

PRINT N'
--- 步骤 2: 在本地进行筛选和分析 ---';

-- a. 筛选出我们关心的有效预订和待客记录
SELECT BookNo INTO #ValidBookings FROM #AllBookings_Raw 
WHERE ShopID = 11 AND ComeDate = '20250731' AND BookStatus = 0 AND (isdelete = 0 OR isdelete IS NULL);

SELECT BookNo, RmNo INTO #TargetOccupancies FROM #AllOccupancies_Raw
WHERE ShopID = 11 AND ComeDate = '20250731';

-- b. 进行关联并统计
;WITH LinkAnalysis AS (
    SELECT
        occ.RmNo,
        occ.BookNo AS OccupancyBookNo,
        b.BookNo AS BookingBookNo,
        CASE 
            WHEN b.BookNo IS NOT NULL THEN '预订到店客'
            ELSE '现场客 (或无效预订)'
        END AS CustomerType
    FROM #TargetOccupancies occ
    LEFT JOIN #ValidBookings b ON occ.BookNo = b.BookNo
)
SELECT 
    CustomerType AS '客户类型',
    COUNT(DISTINCT RmNo) AS '房间数'
FROM LinkAnalysis
GROUP BY CustomerType;

-- c. 输出“现场客”的样本数据
PRINT N'
--- 步骤 3: 查看“现场客”的 BookNo 样本 (TOP 10) ---';
SELECT TOP 10
    RmNo AS '房间号',
    OccupancyBookNo AS '待客记录中的BookNo'
FROM (
    SELECT
        occ.RmNo,
        occ.BookNo AS OccupancyBookNo,
        b.BookNo AS BookingBookNo
    FROM #TargetOccupancies occ
    LEFT JOIN #ValidBookings b ON occ.BookNo = b.BookNo
) AS DetailedAnalysis
WHERE BookingBookNo IS NULL;

-- 4. 清理
DROP TABLE #AllBookings_Raw;
DROP TABLE #AllOccupancies_Raw;
DROP TABLE #ValidBookings;
DROP TABLE #TargetOccupancies;
GO