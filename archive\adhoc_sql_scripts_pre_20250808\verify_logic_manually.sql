-- =============================================
-- 最终逻辑验证脚本 (V2)
-- 目的：跨数据库关联，获取最精确的离店时间，验证“时间段重叠”逻辑
-- =============================================

-- 1. 定义我们要分析的目标参数
DECLARE @TargetDate DATE = '20250731';
DECLARE @ShopID INT = 11;
DECLARE @TargetSlotName NVARCHAR(50) = '11:50-14:50';

PRINT N'--- 正在使用最终精确逻辑，独立验证时间段: ' + @TargetSlotName + ' ---';

-- 2. 准备数据
CREATE TABLE #AllBookings (ComeTime VARCHAR(20), Numbers INT, RmNo VARCHAR(20));
CREATE TABLE #AllOccupancies (ComeTime VARCHAR(20), CloseTime VARCHAR(20), Numbers INT, RmNo VARCHAR(20));

DECLARE @Sql NVARCHAR(MAX);
DECLARE @TargetDateStr VARCHAR(8) = FORMAT(@TargetDate, 'yyyyMMdd');
DECLARE @ShopIDStr VARCHAR(10) = CAST(@ShopID AS VARCHAR);

-- 获取预订数据 (逻辑不变)
SET @Sql = N'SELECT ComeTime, Numbers, RmNo FROM OPENQUERY([cloudRms2019], ''SELECT ComeTime, Numbers, RmNo FROM rms2019.dbo.bookcacheinfo WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ' AND BookStatus = 1'')';
INSERT INTO #AllBookings EXEC sp_executesql @Sql;
SET @Sql = N'SELECT ComeTime, Numbers, RmNo FROM OPENQUERY([cloudRms2019], ''SELECT ComeTime, Numbers, RmNo FROM rms2019.dbo.bookhistory WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''')';
INSERT INTO #AllBookings EXEC sp_executesql @Sql;

-- 获取待客数据 (V2 - 跨库关联获取OutTime)
-- a. 获取 opencacheinfo (实时在店，OutTime为NULL)
SET @Sql = N'SELECT ComeTime, NULL AS CloseTime, Numbers, RmNo FROM OPENQUERY([cloudRms2019], ''SELECT ComeTime, Numbers, RmNo FROM rms2019.dbo.opencacheinfo WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''')';
INSERT INTO #AllOccupancies EXEC sp_executesql @Sql;

-- b. 获取 openhistory 并关联 Dbfood.fdinv
SET @Sql = N'
SELECT 
    h.ComeTime, 
    f.OutTime AS CloseTime, 
    h.Numbers, 
    h.RmNo 
FROM 
    OPENQUERY([cloudRms2019], ''SELECT ComeTime, Numbers, RmNo, Invno FROM rms2019.dbo.openhistory WHERE ComeDate = ''''' + @TargetDateStr + ''''' AND ShopID = ' + @ShopIDStr + ''') h
LEFT JOIN 
    [cloudRms2019].Dbfood.dbo.fdinv f ON h.Invno = f.InvNo;'
INSERT INTO #AllOccupancies EXEC sp_executesql @Sql;


-- 3. 使用精确逻辑进行计算
;WITH TargetSlot AS (
    SELECT BegTime, EndTime FROM dbo.timeinfo WHERE TimeName = @TargetSlotName
)
SELECT 
    '预订 (Bookings)' AS Metric,
    COUNT(DISTINCT b.RmNo) AS RoomCount,
    ISNULL(SUM(b.Numbers), 0) AS GuestCount
FROM TargetSlot ts
LEFT JOIN #AllBookings b ON CAST(LEFT(REPLACE(b.ComeTime, ':', ''), 4) AS INT) >= ts.BegTime AND CAST(LEFT(REPLACE(b.ComeTime, ':', ''), 4) AS INT) < ts.EndTime

UNION ALL

SELECT 
    '待客 (Occupied) - 最终精确逻辑' AS Metric,
    COUNT(DISTINCT o.RmNo) AS RoomCount,
    ISNULL(SUM(o.Numbers), 0) AS GuestCount
FROM TargetSlot ts
LEFT JOIN #AllOccupancies o ON 
    CAST(LEFT(REPLACE(o.ComeTime, ':', ''), 4) AS INT) < ts.EndTime 
    AND 
    (o.CloseTime IS NULL OR o.CloseTime = '' OR CAST(LEFT(REPLACE(o.CloseTime, ':', ''), 4) AS INT) > ts.BegTime);

-- 4. 清理
DROP TABLE #AllBookings;
DROP TABLE #AllOccupancies;
GO