
-- 查询并验证生成的报表数据

-- 1. 查询总览表头
SELECT 
    ReportDate AS '报表日期',
    ShopID AS '店铺ID',
    Weekday AS '星期',
    TotalRevenue AS '总营业额'
FROM 
    dbo.DynamicReport_Header
WHERE 
    ReportDate = '20250731' AND ShopID = 11;

-- 2. 查询时段详情
SELECT 
    d.TimeSlotName AS '时间段',
    d.BookedRooms AS '预订房间数',
    d.BookedGuests AS '预订人数',
    d.OccupiedRooms AS '待客房间数',
    d.OccupiedGuests AS '待客人数',
    FORMAT(d.OccupancyRate, 'P') AS '开房率' -- 格式化为百分比
FROM 
    dbo.DynamicReport_TimeSlotDetails d
JOIN 
    dbo.DynamicReport_Header h ON d.HeaderReportID = h.ReportID
WHERE 
    h.ReportDate = '20250731' AND h.ShopID = 11
ORDER BY
    -- 自定义排序以确保时间段正确显示
    CASE 
        WHEN ISNUMERIC(LEFT(d.TimeSlotName, 2)) = 1 THEN CAST(LEFT(d.TimeSlotName, 2) AS INT)
        ELSE 99 -- 将非数字开头的时段（如“午夜”）排在后面
    END;

