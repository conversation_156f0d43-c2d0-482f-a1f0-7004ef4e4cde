ALTER PROCEDURE dbo.usp_GenerateFullDailyReport
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL
AS
BEGIN
    SET NOCOUNT ON;

    -- ====================================================================
    -- 步骤 1: 参数处理
    -- ====================================================================
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- ====================================================================
    -- 步骤 2: 动态构建白天分时段统计的列
    -- ====================================================================
    DECLARE @PivotColumns nvarchar(MAX);
    DECLARE @PivotSelectColumns nvarchar(MAX);

    SELECT @PivotColumns = STUFF((
        SELECT
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.CtNo = 2 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_K+') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.AliPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_特权预约') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.MTPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_美团') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.DZPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_抖音') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.CtNo = 1 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_房费') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_小计')
        FROM (
            SELECT DISTINCT ti.TimeName, ti.BegTime
            FROM dbo.shoptimeinfo sti
            JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
            WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000
        ) AS t
        ORDER BY BegTime
        FOR XML PATH(''), TYPE
    ).value('.', 'nvarchar(MAX)'), 1, 1, '');

    SELECT @PivotSelectColumns = STUFF((
        SELECT ',' + QUOTENAME(t.TimeName + '_K+') + ',' + QUOTENAME(t.TimeName + '_特权预约') + ',' + QUOTENAME(t.TimeName + '_美团') + ',' + QUOTENAME(t.TimeName + '_抖音') + ',' + QUOTENAME(t.TimeName + '_房费') + ',' + QUOTENAME(t.TimeName + '_小计')
        FROM (SELECT DISTINCT ti.TimeName, ti.BegTime FROM dbo.shoptimeinfo sti JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000) AS t
        ORDER BY BegTime FOR XML PATH('')
    ), 1, 1, '');


    -- ====================================================================
    -- 步骤 3: 构建并执行完整的动态 SQL 查询
    -- ====================================================================
    DECLARE @DynamicSQL nvarchar(MAX);

    SET @DynamicSQL = N'
    -- CTE 1: 预处理总览和直落指标
    WITH OverviewAndDropInData AS (
        SELECT
            rt.WorkDate, b.ShopName, DATENAME(weekday, CAST(rt.WorkDate AS date)) AS WeekdayName,
            SUM(rt.TotalAmount) AS TotalRevenue, SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END) AS DayTimeRevenue, SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END) AS NightTimeRevenue,
            COUNT(rt.InvNo) AS TotalBatchCount, COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount, COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
            SUM(rt.Numbers) AS TotalGuestCount, SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END) AS DayTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END) AS NightTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END) AS TotalDropInGuests
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
        LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_end ON sti_end.TimeNo = ti_end.TimeNo
        WHERE rt.ShopId = @ShopId_Param AND CAST(rt.WorkDate AS date) >= @BeginDate_Param AND CAST(rt.WorkDate AS date) <= @EndDate_Param AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.WorkDate, b.ShopName
    ),
    -- CTE 2: 预处理白天分时段指标
    DayTimePivotedData AS (
        SELECT rt.WorkDate';

    IF ISNULL(@PivotColumns, '') <> ''
    BEGIN
        SET @DynamicSQL = @DynamicSQL + ', ' + @PivotColumns;
    END

    SET @DynamicSQL = @DynamicSQL + N'
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN dbo.timeinfo AS ti ON rt.Beg_Key = ti.TimeNo
        WHERE rt.ShopId = @ShopId_Param AND CAST(rt.WorkDate AS date) >= @BeginDate_Param AND CAST(rt.WorkDate AS date) <= @EndDate_Param AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.WorkDate
    ),
    -- CTE 3: 预处理夜晚详细指标
    NightTimeDetailData AS (
        SELECT
            WorkDate,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' AND Channel = ''K+'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_KPlus,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' AND Channel = ''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Special,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' AND Channel = ''美团'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Meituan,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' AND Channel = ''抖音'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Douyin,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Subtotal,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''自由餐'' THEN TotalAmount ELSE 0 END), 0) AS Night_FreeMeal_Amount,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''非自由餐'' AND Channel = ''K+'' THEN 1 ELSE 0 END), 0) AS Night_After20_KPlus,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''非自由餐'' AND Channel = ''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_After20_Special,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''非自由餐'' AND Channel = ''美团'' THEN 1 ELSE 0 END), 0) AS Night_After20_Meituan,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''非自由餐'' AND Channel = ''抖音'' THEN 1 ELSE 0 END), 0) AS Night_After20_Douyin,
            ISNULL(SUM(CASE WHEN ConsumptionType = ''非自由餐'' AND Channel = ''房费'' THEN 1 ELSE 0 END), 0) AS ''Night_After20_RoomFee'', -- 修正：添加中文别名
            ISNULL(SUM(CASE WHEN ConsumptionType = ''非自由餐'' AND Channel = ''其他'' THEN 1 ELSE 0 END), 0) AS ''Night_After20_Others'', -- 修正：添加中文别名
            ISNULL(SUM(CASE WHEN ConsumptionType = ''非自由餐'' THEN TotalAmount ELSE 0 END), 0) AS ''Night_After20_Revenue'' -- 修正：添加中文别名
        FROM (
            SELECT *, CASE WHEN rt.CtNo = 19 THEN ''自由餐'' ELSE ''非自由餐'' END AS ConsumptionType,
                      CASE WHEN rt.MTPay > 0 THEN ''美团'' WHEN rt.DZPay > 0 THEN ''抖音'' WHEN rt.AliPay > 0 THEN ''特权预约'' WHEN rt.CtNo = 2 THEN ''K+'' WHEN rt.CtNo = 1 THEN ''房费'' ELSE ''其他'' END AS Channel
            FROM dbo.RmCloseInfo_Test rt
            WHERE rt.OpenDateTime >= DATEADD(hour, 20, CAST(CAST(rt.WorkDate AS date) AS datetime))
        ) AS NightShiftData
        WHERE ShopId = @ShopId_Param AND CAST(WorkDate AS date) >= @BeginDate_Param AND CAST(WorkDate AS date) <= @EndDate_Param
        GROUP BY WorkDate
    )
    -- 最终联接所有 CTE 的结果
    SELECT
        ovd.WorkDate AS ''日期'', ovd.ShopName AS ''门店'', ovd.WeekdayName AS ''星期'',
        -- 总览指标
        ISNULL(ovd.TotalRevenue, 0) AS ''营收_总收入'', ISNULL(ovd.DayTimeRevenue, 0) AS ''营收_白天档'', ISNULL(ovd.NightTimeRevenue, 0) AS ''营收_晚上档'',
        ISNULL(ovd.TotalBatchCount, 0) AS ''带客_全天总批数'', ISNULL(ovd.DayTimeBatchCount, 0) AS ''带客_白天档_总批次'', ISNULL(ovd.NightTimeBatchCount, 0) AS ''带客_晚上档_总批次'',
        ISNULL(ovd.DayTimeDropInBatch, 0) AS ''带客_白天档_直落'', ISNULL(ovd.NightTimeDropInBatch, 0) AS ''带客_晚上档_直落'',
        ISNULL(ovd.TotalGuestCount, 0) AS ''用餐_总人数'', ISNULL(ovd.BuffetGuestCount, 0) AS ''用餐_自助餐人数'', ISNULL(ovd.TotalDropInGuests, 0) AS ''用餐_直落人数''';

    IF ISNULL(@PivotSelectColumns, '') <> ''
    BEGIN
        SET @DynamicSQL = @DynamicSQL + ', ' + @PivotSelectColumns;
    END

    SET @DynamicSQL = @DynamicSQL + N'
        , ntd.Night_FreeMeal_KPlus AS ''晚间_自由餐_K+''
        , ntd.Night_FreeMeal_Special AS ''晚间_自由餐_特权预约''
        , ntd.Night_FreeMeal_Meituan AS ''晚间_自由餐_美团''
        , ntd.Night_FreeMeal_Douyin AS ''晚间_自由餐_抖音''
        , ntd.Night_FreeMeal_Subtotal AS ''晚间_自由餐_小计''
        , ntd.Night_FreeMeal_Amount AS ''晚间_自由餐_消费金额''
        , ntd.Night_After20_KPlus AS ''晚间_20点后_K+''
        , ntd.Night_After20_Special AS ''晚间_20点后_特权预约''
        , ntd.Night_After20_Meituan AS ''晚间_20点后_美团''
        , ntd.Night_After20_Douyin AS ''晚间_20点后_抖音''
        , ntd.Night_After20_RoomFee AS ''晚间_20点后_房费''
        , ntd.Night_After20_Others AS ''晚间_20点后_其他''
        , ntd.Night_After20_Revenue AS ''晚间_20点后_营业额''
    FROM
        OverviewAndDropInData AS ovd
    LEFT JOIN
        DayTimePivotedData AS dtp ON ovd.WorkDate = dtp.WorkDate
    LEFT JOIN
        NightTimeDetailData AS ntd ON ovd.WorkDate = ntd.WorkDate
    ORDER BY
        ovd.WorkDate;
    ';

    -- 打印动态 SQL 进行调试 (您可以注释掉这一行)
    -- PRINT @DynamicSQL;

    -- 使用 sp_executesql 执行动态 SQL，并传入参数
    EXEC sp_executesql @DynamicSQL,
        N'@BeginDate_Param date, @EndDate_Param date, @ShopId_Param int',
        @BeginDate_Param = @BeginDate,
        @EndDate_Param = @EndDate,
        @ShopId_Param = @ShopId;

END