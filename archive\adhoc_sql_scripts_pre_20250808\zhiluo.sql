-- 定义目标查询的门店和日期
DECLARE @TargetShopId int = 3; -- 天河店
DECLARE @TargetWorkDate varchar(8) = '20250502';
DECLARE @TargetDate date = CAST(@TargetWorkDate AS date);

-- 准备当天所有生效的业务时间档，并获取每个档的“下一个档”的开始时间
WITH TimeSlots AS (
    SELECT
        ti.TimeNo, ti.TimeName, ti.BegTime,
        DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@TargetDate AS datetime))) AS SlotStartDateTime,
        -- 使用 LEAD() 窗口函数获取下一个时间档的开始时间
        LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@TargetDate AS datetime))), 1, '2999-12-31') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
    FROM dbo.shoptimeinfo AS sti
    JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
    WHERE sti.ShopId = @TargetShopId
),
-- 筛选出当天所有的“真直落”消费记录
TrueDropInData AS (
    SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
    FROM dbo.RmCloseInfo_Test AS rt
    JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
    JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
    WHERE rt.ShopId = @TargetShopId AND rt.WorkDate = @TargetWorkDate AND rt.OpenDateTime IS NOT NULL
      AND rt.Beg_Key <> rt.End_Key
      AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
      AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
)
-- 最终的分时段统计
SELECT
    ti_main.TimeName AS '时间段',
    COUNT(CASE WHEN rt.CtNo = 2 THEN 1 ELSE NULL END) AS 'K+',
    COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS '特权预约',
    COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS '美团',
    COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS '抖音',
    COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS '房费',
    COUNT(rt.InvNo) AS '小计',
    -- 上一档直落批次 (最终修正版)
    ISNULL(
        (
            SELECT COUNT(tdi.InvNo)
            FROM TrueDropInData AS tdi
            JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo
            WHERE
                -- 条件1: 直落单的 Beg_Key 必须早于当前主查询的时间档
                ts_beg.BegTime < ti_main.BegTime
                -- 条件2: 并且，直落单的结账时间必须晚于当前主查询时间档的【下一个档】的开始时间
                AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime
        ), 0
    ) AS '上一档直落'
FROM
    dbo.RmCloseInfo_Test AS rt
JOIN
    dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
JOIN
    TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
WHERE
    rt.ShopId = @TargetShopId
    AND rt.WorkDate = @TargetWorkDate
    AND rt.OpenDateTime IS NOT NULL
GROUP BY
    rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
ORDER BY
    ti_main.BegTime;
