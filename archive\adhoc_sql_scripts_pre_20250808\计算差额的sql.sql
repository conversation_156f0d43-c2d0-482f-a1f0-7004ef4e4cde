    -- 使用嵌套的 WITH 子句来分步处理逻辑
    -- 第一层: 聚合微信支付信息，和之前一样
    WITH WxPayAggregated AS (
        SELECT
            p.InvNo,
            SUM(p.Tot) AS WxPayTotalForInv,
            STUFF(
                (
                    SELECT ', ' + w.transaction_id
                    FROM wxpayinfo w
                    WHERE w.InvNo = p.InvNo
                    FOR XML PATH('')
                ), 1, 2, ''
            ) AS TransactionIDs
        FROM
            wxpayinfo p
        GROUP BY
            p.InvNo
    ),
    -- 第二层: 计算出包含差额在内的所有账单明细
    AllBillDetails AS (
        SELECT
            r.InvNo,
            r.RmNo,
            r.Tot AS RoomSystemAmount,
            w.WxPayTotalForInv AS WxPayTotalAmount,
            (w.WxPayTotalForInv - r.Tot) AS Difference,
            w.TransactionIDs,
            CONVERT(datetime, r.AccDate + ' ' + r.AccTime) AS CheckoutTime
        FROM
            ROOM r
        INNER JOIN
            WxPayAggregated w ON r.InvNo = w.InvNo
        WHERE
            r.RmStatus = 'A'
            AND DATEDIFF(minute, CONVERT(datetime, r.AccDate + ' ' + r.AccTime), GETDATE()) BETWEEN 0 AND 10
    )
    -- 最终查询: 从计算好的结果中筛选出差额小于等于0的记录
    SELECT
        *
    FROM
        AllBillDetails
    WHERE
        Difference <= 0;