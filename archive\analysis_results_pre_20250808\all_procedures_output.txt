ProcedureName                                                                                                                    ProcedureDefinition                                                                                                                                                                                                                                             
-------------------------------------------------------------------------------------------------------------------------------- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
Add_miniprom_Buffet_Groupby                                                                                                      CREATE PROCEDURE [dbo].[Add_miniprom_Buffet_Groupby]
@ShopId int,
@TimeName nvarchar(50),
@IsMember nvarchar(10),@FdNo nvarchar(10),
@FdCName nvarchar(50),
@FdPrice int,@PriceType nvarchar(50),
@TimeType nvarchar(50)
AS
Begin
	set nocount on
	Ins
BookAndOpenRoomInfo                                                                                                              -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[BookAndOpenRoomInfo]
	@t int=0,
	@in
BookOrder_ExRoyaltyResult                                                                                                        
CREATE proc [dbo].[BookOrder_ExRoyaltyResult]
	@t int =0,--??
	@ComeDate date=''
as
---?????????
begin
	---#InvnoTable?????????????
	---#table???????????

	---???????????????
	if(@ComeDate = '')
		set @ComeDate=DATEADD(DAY,-1,GETDATE())

	--
BookOrder_ExRoyaltyResult1                                                                                                       
CREATE proc [dbo].[BookOrder_ExRoyaltyResult1]
	@t int =0,--??
	@ComeBegin varchar(50)='20190601',
	@ComeEnd varchar(50) ='20190630'
as
---?????????
begin
	---#InvnoTable?????????????
	---#table???????????

	---???????????????


	--19:00????
BusinessCount                                                                                                                    -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[BusinessCount]
	@t int, 
	@ShopId in
Cal_kboss_waiter_ServiceResult                                                                                                   CREATE PROCEDURE Cal_kboss_waiter_ServiceResult
@WorkMonth nvarchar(6)
AS
Begin
	set nocount on
	declare @ShopId int,@CashUserName nvarchar(50),@YGNumber nvarchar(20),@kboss_waiter_ServiceResult int
	declare cursor_order cursor for 
	SELECT ShopId,operator
Cal_RechargeResult                                                                                                               CREATE PROCEDURE Cal_RechargeResult
@WorkMonth nvarchar(6)
AS
Begin
	set nocount on
	declare @ShopId int,@CashUserName nvarchar(50),@YGNumber nvarchar(20),@RechargeResult int
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
Cal_waiter_MemberCardResult                                                                                                      CREATE PROCEDURE Cal_waiter_MemberCardResult
@WorkMonth nvarchar(6)
AS
Begin
	set nocount on
	declare @ShopId int,@CashUserName nvarchar(50),@YGNumber nvarchar(20),@waiter_MemberCardResult int
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop t
Calc_BookingResutl                                                                                                               CREATE PROCEDURE [dbo].[Calc_BookingResutl]
@WorkMonth nvarchar(6)
AS
BEGIN
		SET nocount ON
		DECLARE @FromDate nvarchar(30),@ToDate nvarchar(30)
		SET @FromDate='2022-01-01 09:00:00.000'--@WorkMonth+'01'
		SET @ToDate='2022-02-01 08:59:59.000'--@WorkMont
Calc_BottleResult                                                                                                                CREATE PROCEDURE [dbo].[Calc_BottleResult] --?????????????
@WorkMonth nvarchar(6)
AS
BEGIN
		SET nocount ON
		DECLARE @DepName nvarchar(50),@FromDate nvarchar(20),@ToDate nvarchar(20)
		SET @DepName='????'
		--SET @FromDate=@WorkMonth+'01'
		--SET @ToDate=
Calc_decoratedResult                                                                                                             CREATE PROCEDURE Calc_decoratedResult
@workMonth nvarchar(6)
AS
BEGIN
SET nocount ON
IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
			SELECT * INTO #v_attendance FROM  [*************,2433].hdhr.dbo.v_attendance WHERE kqyf
Calc_drinksRetrieve                                                                                                              CREATE PROCEDURE Calc_drinksRetrieve --????????
@WorkMonth nvarchar(10),
@ShopId INT
AS
BEGIN
	SET nocount ON
	DECLARE @DepName nvarchar(50),@FromDate nvarchar(30),@ToDate nvarchar(30),@TotalNumber INT,@TotalResult INT,@PersonResult INT,@MinNumber IN
Calc_kboss_chief_BookingResult                                                                                                   CREATE PROCEDURE Calc_kboss_chief_BookingResult
@workMonth nvarchar(6)
AS
BEGIN
SET nocount ON
DECLARE @FromDate nvarchar(20),@ToDate nvarchar(20)
		SET @FromDate=@WorkMonth+'01'
		SET @ToDate=@WorkMonth+'31'
IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is 
Calc_kboss_chief_BookingResult_Out                                                                                               CREATE PROCEDURE Calc_kboss_chief_BookingResult_Out
@workMonth nvarchar(6)
AS
BEGIN
SET nocount ON
DECLARE @FromDate nvarchar(20),@ToDate nvarchar(20),@ShopId INT
		SET @ShopId=13
		SET @FromDate=@WorkMonth+'01'
		SET @ToDate=@WorkMonth+'31'
IF OBJECT_ID(N
Calc_kboss_chief_BookingResult_Out_hq                                                                                            CREATE PROCEDURE Calc_kboss_chief_BookingResult_Out_hq
@workMonth nvarchar(6)
AS
BEGIN
SET nocount ON
DECLARE @FromDate nvarchar(20),@ToDate nvarchar(20),@ShopId INT
		SET @ShopId=13
		SET @FromDate=@WorkMonth+'01'
		SET @ToDate=@WorkMonth+'31'
IF OBJECT_I
Calc_kboss_chief_BookingResult_Out_qy                                                                                            CREATE PROCEDURE Calc_kboss_chief_BookingResult_Out_qy
@workMonth nvarchar(6)
AS
BEGIN
SET nocount ON
DECLARE @FromDate nvarchar(20),@ToDate nvarchar(20),@ShopId INT
		SET @ShopId=12
		SET @FromDate=@WorkMonth+'01'
		SET @ToDate=@WorkMonth+'31'
IF OBJECT_I
Calc_kboss_Waiter_BookingResult                                                                                                  CREATE PROCEDURE Calc_kboss_Waiter_BookingResult
@workMonth nvarchar(6),
@ShopId INT
AS
BEGIN
SET nocount ON
DECLARE @FromDate nvarchar(20),@ToDate nvarchar(20)
		SET @FromDate=@WorkMonth+'01'
		SET @ToDate=@WorkMonth+'31'
IF OBJECT_ID(N'tempdb..#userinfo'
Calc_kboss_waiter_BookingResult_kboss                                                                                            CREATE PROCEDURE Calc_kboss_waiter_BookingResult_kboss
@workMonth nvarchar(6)
AS
BEGIN
SET nocount ON
DECLARE @FromDate nvarchar(30),@ToDate nvarchar(30),@ShopId INT
		--SET @ShopId=13
		SET @FromDate='2022-01-01 09:00:00.000'
		SET @ToDate='2022-02-01 09:
Calc_MarketBooking                                                                                                               CREATE PROCEDURE [dbo].[Calc_MarketBooking]
@WorkMonth nvarchar(6)
AS
BEGIN

-- UPDATE TotalResult SET Kboss_waiter_serviceresult=0 WHERE  WorkMonth=@WorkMonth --?????
	SET nocount ON
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop tab
Calc_MarketBooking_bak20230315                                                                                                   create PROCEDURE [dbo].[Calc_MarketBooking_bak20230315]
@WorkMonth nvarchar(6)
AS
BEGIN
	SET nocount ON
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
			select distinct YGNumber into #v_attendance FROM  [183.63.130
Calc_Rec_Bar_Del_Chef_Result                                                                                                     CREATE PROCEDURE	[dbo].[Calc_Rec_Bar_Del_Chef_Result]
@WorkMonth nvarchar(6),
@ShopId INT,
@DepName nvarchar(50)
AS
BEGIN
	SET nocount ON
	DECLARE @DepEName nvarchar(50),@MinNumber INT
	SET @MinNumber=19
	IF @ShopId in (10,13) and @WorkMonth='202201' SET @
Calc_RecBarDelChefResult                                                                                                         CREATE PROCEDURE [dbo].[Calc_RecBarDelChefResult]
@WorkMonth nvarchar(6)
AS
BEGIN
	SET nocount ON
	--??????????
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
		select distinct ShopId,YGNumber,YGName,SJDays into #v
Calc_ResultChild                                                                                                                 CREATE PROCEDURE [dbo].[Calc_ResultChild]
@WorkMonth nvarchar(6)
AS
Begin
	set nocount on
	set @WorkMonth=convert(nvarchar(6),DateAdd(mm,-1,getdate()),112)
	--??????????????????????
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table
Calc_WaiterOrder                                                                                                                 CREATE PROCEDURE [dbo].[Calc_WaiterOrder]  --????????????
@WorkMonth nvarchar(6)
AS
BEGIN
	SET nocount ON
	declare @tot int
	set @tot=0
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
		select distinct ShopId,YGNumber,YGNa
Calc_WaiterOrder_bak                                                                                                             create PROCEDURE [dbo].[Calc_WaiterOrder_bak]
@WorkMonth nvarchar(6)
AS
BEGIN
	SET nocount ON
	declare @tot int
	set @tot=0
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
		select distinct ShopId,YGNumber,YGName into #v_a
Cla_drinksretrieveresult                                                                                                         CREATE PROCEDURE [dbo].[Cla_drinksretrieveresult]
@WorkMonth nvarchar(6)
AS
Begin
	set nocount on
	DECLARE @ShopId int,@Tot int,@MinDays int,@Numbers INT,@sumResult money,@sumResultLt money,@SqlStr nvarchar(2000),@YGNumber nvarchar(20),@YGName nvarcha
Clac_Waiter_Order                                                                                                                CREATE PROCEDURE Clac_Waiter_Order
@WorkMonth nvarchar(6)
AS
Begin
set nocount on
declare @ShopId int,@CashUserName nvarchar(50),@YGNumber nvarchar(20),@WaiterResult int
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
	SEL
date_fdinv                                                                                                                       
-- =============================================
--??????????
-- =============================================
CREATE PROCEDURE [dbo].[date_fdinv]
AS
BEGIN
/* DECLARE @workdate NVARCHAR(100)

Select @workdate=convert(nvarchar(10),getdate()-1,112)
date_rmcloseinfo_test_insert                                                                                                     CREATE PROCEDURE date_rmcloseinfo_test_insert
@workdate nvarchar(8)
AS
Begin
    set nocount on
    
    PRINT '=== ???? date_rmcloseinfo_test_insert ???? ===';
    PRINT '??????: ' + @workdate;
    PRINT '???: RmCloseInfo_Test';
    
    -- ????
date_Rms2019                                                                                                                     
-- =============================================
--????????
-- =============================================
CREATE PROCEDURE [dbo].[date_Rms2019]
AS
BEGIN
 DECLARE @workdate NVARCHAR(100)

Select @workdate=convert(nvarchar(10),getdate()-1,112)  
Department_Name                                                                                                                  -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
create PROCEDURE [dbo].[Department_Name]
AS
BEGIN
	select D
DoResultCode                                                                                                                     CREATE PROCEDURE [dbo].[DoResultCode] AS
DECLARE @WorkDate nvarchar(10)
SET @WorkDate='20220201'

/*
--???????
SELECT ShopId,@WorkDate as WorkDate,'MemberCard' as Source,operator_Id as UserId,operator_Name as CashUserName,Price as PersonResult,otherPrice,0
DoSumIncome                                                                                                                      CREATE PROCEDURE DoSumIncome
@WorkDate nvarchar(10)
AS
Begin
	set nocount on
	declare @i int
	set @i=2
	while @i<13
		Begin
			if @i<>7 exec SumIncome @i,@WorkDate
			set @i=@i+1
		End
	set nocount off
End                                                   
Ex_CheckConsume                                                                                                                  
CREATE PROCEDURE [dbo].[Ex_CheckConsume]  --????
	@shopid int,
	@BegDate nvarchar(8),
	@EndDate nvarchar(8)

AS
BEGIN
	
	SET NOCOUNT ON;   --????????Openid??????????Openid
  select distinct val3 from mims.dbo.memberinfo where memberkey in(select
Ex_GrouponAndType                                                                                                                CREATE Proc [dbo].[Ex_GrouponAndType]
	@t int =0,--????
	@GrouponKey uniqueidentifier =null,
	@DefaultID int =0,
	@ProjectKey uniqueidentifier =null,
	@GrouponName varchar(20)='',
	@IsConFig int=-1,
	@StaType varchar(4000)='',
	@Pageno int=1,
	@Pa
ex_NewAndOldList                                                                                                                 CREATE PROCEDURE ex_NewAndOldList
    @StartDate VARCHAR(8),
    @EndDate VARCHAR(8),
    @Keyword VARCHAR(100) = NULL,
    @ShopID VARCHAR(50) = NULL,
    @PageSize INT = 10,          -- ?????,??10
    @PageNumber INT = 1          -- ????,??1
--   
ex_NewAndOldListExport                                                                                                           -- ??????????? UTF-8 ?????????
CREATE PROCEDURE dbo.ex_NewAndOldListExport
    @StartDate VARCHAR(8),
    @EndDate VARCHAR(8),
    @Keyword VARCHAR(100) = NULL,
    @ShopID VARCHAR(50) = NULL
    
     
AS
BEGIN
    SET NOCOUNT ON;
    -- ??????
ex_public                                                                                                                        -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[ex_public]
	@url nvarchar(200)
AS
B
ex_public1                                                                                                                       -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[ex_public1]
	@url nvarchar(200)
AS

Ex_reporting_HouseWatching_type                                                                                                  -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[Ex_reporting_HouseWatching_type] 
	

Ex_RmCloseInfo_Day                                                                                                               CREATE PROC [dbo].[Ex_RmCloseInfo_Day]
	@t int =0,--??
	@ShopId int =0,
	@Sort VARCHAR(50)='WorkDate', --???????,???order by
	@BeginDate datetime = null,--????
	@EndDate  datetime = null, --????
	@IsMerge bit =1,--????
	@PageIndex int=1,
	@PageSize
Ex_RmCloseInfo_Staff                                                                                                             CREATE proc [dbo].[Ex_RmCloseInfo_Staff]
	@t int =0,--??
	@ShopId int =0,
	@BeginDate datetime = null,--????
	@EndDate  datetime = null, --????
	@CloseUserName nvarchar(10)='',--????
	@IsMerge bit =1,-- ??????????,?????????
	@PageIndex int=1,
	@Pag
ex_Send_sjkj                                                                                                                     -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[ex_Send_sjkj] --???????????

	@top i
ex_SendWechatGroupon                                                                                                             -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[ex_SendWechatGroupon] --?????????????

ex_special_electronization_bill                                                                                                  -- =============================================
-- Author:		jjy
-- Create date: 2021-04-22
-- Description:	???????????????
-- =============================================
CREATE PROCEDURE [dbo].[ex_special_electronization_bill]
	@type nvarchar(10)=
Ex_StaMoney                                                                                                                      
CREATE proc [dbo].[Ex_StaMoney] 
	@t int =0,--??
	@ShowModel int =0,
	@ProjectKey uniqueidentifier =null,--??key
	@GrouponKey uniqueidentifier = null, --?key
	@ID INT
as 
---?????????????
begin
	declare @Total int=0
	declare @FdNo varchar(10)

Ex_StaNumber                                                                                                                     CREATE proc [dbo].[Ex_StaNumber] 
	@t int =0,--??
	@ProjectKey uniqueidentifier =null,--??key
	@ID int=0,
	@ShowModel int=0,---????
	@GrouponKey uniqueidentifier = null --?key
as 
---?????????????
begin
	declare @Total int=0
	declare @ByName varc
Ex_StaPeoNum                                                                                                                     CREATE proc [dbo].[Ex_StaPeoNum] 
	@t int =0,--??
	@ProjectKey uniqueidentifier =null,--??key
	@ShowModel int=0,--????
	@GrouponKey uniqueidentifier = null, --?key
	@ID int=0


as 
---?????????????
begin
	declare @Total int=0
	if(@t=0) --??????
Ex_StaResult                                                                                                                     CREATE proc [dbo].[Ex_StaResult]
	@t int =0,
	@Write int =0,
	@GrouponKey uniqueidentifier=null,--?key
	@IsInsert bit =0,
	@ShowModel int =0,--????:0????,1????
	@Type varchar(50)='0'
as 
begin
	return
	--declare @sql varchar(max)
	--declare @Pro
Ex_StaResult1                                                                                                                    CREATE proc [dbo].[Ex_StaResult1]
	@t int =0,
	@Write int =0,
	@GrouponKey uniqueidentifier=null,--?key
	@IsInsert bit =0,
	@ShowModel int =0,--????:0????,1????
	@Type varchar(50)='0'
as 
begin
	declare @sql varchar(max)
	declare @Proc varchar(ma
Ex_StaRmNoNum                                                                                                                    CREATE proc [dbo].[Ex_StaRmNoNum]
	@t int =0,--??
	@ProjectKey uniqueidentifier =null,--??key
	@ShowModel int =0,
	@ID int =0,--??
	@GrouponKey uniqueidentifier = null --?key
as 
--??????????????
begin
	declare @Total int=0
	if(@t=0) --?????????

Ex_StaWine                                                                                                                       CREATE proc [dbo].[Ex_StaWine]
	@t int =0,--??
	@ProjectKey uniqueidentifier =null,--??key
	@ShowModel int=0,
	@GrouponKey uniqueidentifier = null, --?key
	@ID int=0

as 
--?????????????
begin
 	declare @Total int=0
	declare @FdNo varchar(10)
	
Ex_View_FdCashBak                                                                                                                CREATE  proc [dbo].[Ex_View_FdCashBak]
	@t int = 0,
	@PageIndex int=1, --??
	@PageSize int=7,--????????
	@ShopId int=0,
	@InvNo  varchar(9)='',
	@BeginDate datetime ='',--????
	@EndDate datetime='',--????
	@RmNo  varchar(4)='',
	@FdNo varchar(10)=
Ex_View_FdCashBak1                                                                                                               CREATE  proc [dbo].[Ex_View_FdCashBak1]
	@t int = 0,
	@PageIndex int=1, --??
	@PageSize int=7,--????????
	@ShopId int=0,
	@InvNo  varchar(9)='',
	@BeginDate datetime ='',--????
	@EndDate datetime='',--????
	@RmNo  varchar(4)='',
	@FdNo varchar(10)
GetAbnormalBill                                                                                                                  -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetAbnormalBill]
	@t int=0,
	@InvNo 
getResultUserinfo                                                                                                                CREATE PROCEDURE [dbo].[getResultUserinfo]
@WorkMonth nvarchar(6),
@ShopId INT
AS
BEGIN
	SET nocount ON
	if DatePart(dd,getdate())>16 --???????????
		set @WorkMonth=Convert(nvarchar(6),getdate(),112)
	else
		set @WorkMonth=Convert(nvarchar(6),dateadd(M,-1,
getShopInfo                                                                                                                      CREATE PROCEDURE getShopInfo
	
AS
BEGIN
	select ShopID,ShopName from MIMS.dbo.ShopInfo where IsUse=1 order by ShopID
END
                                                                                                                                 
GetShopResult                                                                                                                    CREATE PROCEDURE GetShopResult
@WorkMonth nvarchar(10)
AS
BEGIN
	SET nocount ON
	SELECT @WorkMonth as WorkMonth,dbo.ReturnShopName(ShopId),sum(ShopResult) FROM (
	SELECT ShopID,sum(otherPrice) as ShopResult FROM ResultChild WHERE left(WorkDate,6)=@WorkMont
getTotalResultByShopIdMonth                                                                                                      CREATE PROCEDURE getTotalResultByShopIdMonth
@WorkMonth nvarchar(6),
@ShopId INT
AS
BEGIN
	SET nocount ON
	SELECT * FROM totalResult WHERE WorkMonth=@WorkMonth AND ShopId=@ShopId
	SET nocount OFF
END                                                         
GetXhTotal                                                                                                                       
CREATE PROCEDURE [dbo].[GetXhTotal]
@WorkDate nvarchar(10)
AS
Begin
	set nocount on
	select ShopId,FdCName,CashType,CashUserName,sum(FdQty) as FdQty into #xh_Total from View_FdCashBak where workdate=@WorkDate and FdCName like '%??%' 
	and FdCName n
Insert_reporting_ddf                                                                                                             -- =============================================
CREATE PROCEDURE [dbo].[Insert_reporting_ddf]
	@Rb_No nvarchar(50)='',
	@ikey nvarchar(50)='',
	@RtNo nvarchar(20)='',
	@bookno nvarchar(20)='',
	@CustTel nvarchar(20)='',
	@RtName nvarchar(20)='',
	
Insert_reporting_membershipCard                                                                                                  CREATE PROCEDURE [dbo].[Insert_reporting_membershipCard]
	@Rvc_No nvarchar(50)='',
	@CardNumber nvarchar(100)='',
	@CustTel  nvarchar(20)='',
	@CardGrade nvarchar(20)='',
	@Appy_Id nvarchar(20)='',
	@Appy_Name nvarchar(20)='',
	@ShopId int=0
AS
BE
InsertPreTable                                                                                                                   
CREATE proc [dbo].[InsertPreTable]
	@Proc varchar(max)='',
	@TableName varchar(30)='',
	@GrouponKey uniqueidentifier='E3478B00-6940-4833-8330-E90E7C6C922D' --?key
as 
begin
	declare @sql varchar(max)='' 
	declare @ByName varchar(50)
	declare @ByN
MIMS_Wxpayinfo_report                                                                                                            
CREATE PROCEDURE MIMS_Wxpayinfo_report 
	@Type int=1,
	@shopid int,
	@BegDate nvarchar(8),
	@EndDate nvarchar(8)
AS
BEGIN
 -- select   CONVERT(varchar(100), getdate(), 112) as inputTime 
 if(@Type=1)	--??????
select shopname,Case when paytype='1
mini_getReceptionResult                                                                                                          CREATE PROCEDURE getReceptionResult
@WorkDate nvarchar(10)
AS
BEGIN
	set nocount ON
	SELECT WorkDate,ShopId,Type,count(1) from (
	SELECT WorkDate,ShopId,'??' as Type from miniprom_FdInv_FdCashbak WHERE FdCName like '%??%' and WorkDate=@WorkDate and CashTyp
mini_Result_addUserInfo                                                                                                          CREATE PROCEDURE [dbo].[mini_Result_addUserInfo]  --?????????
@WorkMonth nvarchar(6),
@ShopId INT,
@DepName nvarchar(50),
@userStr varchar(MAX)
AS
BEGIN
	 SET NOCOUNT ON;
    
    
    -- ???????????
    IF DATEPART(dd, GETDATE()) > 7
        SET @Wor
mini_Result_GetChef                                                                                                              CREATE PROCEDURE [dbo].[mini_Result_GetChef]
@WorkDate nvarchar(10)
AS
Begin
	set nocount on
	declare @d datetime
	set @d=getdate()
	if OBJECT_ID(N'tempdb..#FdInvCash',N'U') is not null drop table #FdInvCash
	--select [????????(??)-1]=DATEDIFF(ms,@d,getdat
mini_Result_GetReceptionCash                                                                                                     CREATE PROCEDURE mini_Result_GetReceptionCash
@WorkDate nvarchar(10)
AS
Begin
	set nocount on
	if OBJECT_ID(N'tempdb..#FdInvCash',N'U') is not null drop table #FdInvCash
	select BiKey,WorkDate,a.ShopId,a.InvNo,FdNo,FdCName,CashType into #FdInvCash from FdI
mini_Result_getYGInfo                                                                                                            CREATE PROCEDURE [dbo].[mini_Result_getYGInfo]
@ShopId INT
AS
BEGIN
	SET nocount ON
	SELECT YGNumber,YGName FROM  [*************,2433].hdhr.dbo.v_staffdep WHERE ShopID=@ShopId 
	AND DEPName NOT IN ('???','??','???','???','???','?????','?????','?????','????
minipram_GetAll_AllOperatingincomeByWorkDate                                                                                     CREATE PROCEDURE minipram_GetAll_AllOperatingincomeByWorkDate
@WorkDate nvarchar(10)
AS
BEGIN
	SET nocount ON
	SELECT * from AllOperatingincomeByWorkDate WHERE WorkDate=@WorkDate
	set nocount off 
END                                                        
minipram_GetAllOperatingincomeByWorkDate                                                                                         CREATE PROCEDURE [dbo].[minipram_GetAllOperatingincomeByWorkDate]
@workdate nvarchar(10)
AS
BEGIN
	SET NOCOUNT ON
	declare @ShopId int,@MemberCount int
	if OBJECT_ID(N'tempdb..#temp_fdinv',N'U') is not null drop table #temp_fdinv
	select ShopId,WorkDate,Ca
minipram_GetBuffet_Data                                                                                                          CREATE PROCEDURE [dbo].[minipram_GetBuffet_Data]
@workdate nvarchar(10)
AS
Begin
	set nocount on
	select a.ShopId,a.WorkDate,
	'' as TimeName,0 as IsMember,'' as FdNo, '' as FdCName,0 as PriceType,'' as TimeType,
	sum(FdQty)as FdQty from miniprom_FdInv_FdC
minipram_GetGroupon_Buffet_Data                                                                                                  CREATE PROCEDURE [dbo].[minipram_GetGroupon_Buffet_Data]
@workdate nvarchar(10)
AS
Begin
	set nocount on
	select ShopId,WorkDate,TimeName,BandType,sum(FdQty)as FdQty from (select 
	a.ShopId,WorkDate,
	(
		case 
		when CashTime<'12:00' then '10:20-
minipram_GetOperatingincomeByWorkDate                                                                                            CREATE PROCEDURE minipram_GetOperatingincomeByWorkDate
@WorkDate nvarchar(10)
AS
BEGIN
	set nocount ON
	SELECT * from operatingincome where WorkDate=@WorkDate order by ShopID
	set nocount OFF
END                                                             
minipram_GetRoom_Data                                                                                                            CREATE PROCEDURE minipram_GetRoom_Data
@WorkDate nvarchar(10)
AS
Begin
	set nocount on
	select * from dbfoodbackups.dbo.RoomStatistics where StatistDate=@WorkDate  order by StoreId,period
	set nocount off
End
                                       
miniprom_get_BBQ                                                                                                                 CREATE PROCEDURE [dbo].[miniprom_get_BBQ]
@FromDate nvarchar(10),
@ToDate nvarchar(10)
AS
BEGIN
	set nocount on
	SELECT case ShopId 
		when 2 then 'bb'
		when 3 then 'th'
		when 4 then 'yd'
		when 5 then 'py'
		when 6 then 'hy'
		when 8 then 'b
miniprom_get_double11                                                                                                            CREATE PROCEDURE miniprom_get_double11
@FromDate nvarchar(10),
@ToDate nvarchar(10)
AS
BEGIN
	set nocount on 
	SELECT WorkDate,ShopId,FdNo,FdCName,sum(FdQty) as FdQty from OperateData.dbo.miniprom_FdInv_FdCashbak 
	where (FdNo='7918' or FdNo='7919') and Wo
new_result_waiter_order_info                                                                                                     CREATE PROCEDURE [dbo].[new_result_waiter_order_info]
@WorkMonth nvarchar(6)
AS
Begin
--???????????
if OBJECT_ID(N'tempdb..#FdInvCash',N'U') is not null drop table #FdInvCash
	select BiKey,WorkDate,a.ShopId,a.InvNo,FdNo,FdCName,CashType,b.FdQty,FdPric
new_sum_result_info                                                                                                              CREATE PROCEDURE [dbo].[new_sum_result_info]
@WorkDate nvarchar(10)
AS
BEGIN
	SET nocount ON
	if OBJECT_ID(N'tempdb..#FdInvCash',N'U') is not null drop table #FdInvCash
	select BiKey,WorkDate,a.ShopId,a.InvNo,FdNo,FdCName,CashType,b.FdQty,FdPrice int
NewCommissionCalculation                                                                                                         CREATE PROCEDURE NewCommissionCalculation
@WorkDate NVARCHAR(6)
AS
BEGIN
	DECLARE @MinDays INT;
	SET @MinDays = 20;
	--???????????????????????
	UPDATE TotalResult SET MenuOrderResult = 0,MenuShareResult = 0,PersonOrderResult = 0,PersonShareResult = 
NewCommissionCalculationV1                                                                                                       CREATE PROCEDURE [dbo].[NewCommissionCalculationV1]
@WorkDate NVARCHAR(6)
AS
BEGIN
	DECLARE @MinDays INT;
	SET @MinDays = 20;
	--???????????????????????
	UPDATE TotalResult SET MenuOrderResult = 0,MenuShareResult = 0,PersonOrderResult = 0,PersonShar
NewCommissionCalculationV2                                                                                                       CREATE PROCEDURE [dbo].[NewCommissionCalculationV2]
@WorkDate NVARCHAR(6)
AS
BEGIN
	DECLARE @MinDays INT;
	SET @MinDays = 20;
	--???????????????????????
	UPDATE TotalResult SET MenuOrderResult = 0,MenuShareResult = 0,PersonOrderResult = 0,PersonShar
NewCommissionCalculationV3                                                                                                       CREATE PROCEDURE [dbo].[NewCommissionCalculationV3]

@WorkDate NVARCHAR(6)

--@IsTestRun BIT = 1 -- ?????????(1),????????0--???
AS

BEGIN
 -- ????:???????,??????????--???
--     IF @IsTestRun = 1
--     BEGIN
--         PRINT '????????,?????????
NewCommissionCalculationV4                                                                                                       CREATE PROCEDURE [dbo].[NewCommissionCalculationV4]

@WorkDate NVARCHAR(6)

--@IsTestRun BIT = 1 -- ?????????(1),????????0--???
AS

BEGIN
 -- ????:???????,??????????--???
--     IF @IsTestRun = 1
--     BEGIN
--         PRINT '????????,?????????
NewWaiterOrderSummary                                                                                                            CREATE PROCEDURE [dbo].[NewWaiterOrderSummary] --?????????
@WorkMonth NVARCHAR(6)
AS
BEGIN
	DECLARE @UserId VARCHAR(10)
	DECLARE @CashUserName NVARCHAR(10)
	DECLARE @ShopId INT,@UserName NVARCHAR(20),@Type NVARCHAR(10),@FdQty INT,@FdPrice INT,@Total 
OperateData_AccountSearch                                                                                                        -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[OperateData_AccountSearch]
	@t int=0,
OperateData_details                                                                                                              -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
create PROCEDURE [dbo].[OperateData_details]
	@t int =0,     
OperateData_FdInv                                                                                                                -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[OperateData_FdInv]
	@t int=0,
	@Star
OperateData_GetExGiftInfo                                                                                                        CREATE proc [dbo].[OperateData_GetExGiftInfo]
	@t int =0,--??
	@MemberKey uniqueidentifier=null
as
---?????????
begin
	--declare @Openid varchar(30) =''--??ID
	--declare @MemberPhoneNumber nvarchar(50)
	--select @Openid=Val3,@MemberPhoneNumber=Memb
OperateData_GetIntegralInfo                                                                                                      CREATE proc [dbo].[OperateData_GetIntegralInfo]
	@t int =0,--??
	@MemberKey uniqueidentifier=null
as
---???????????
begin
	--declare @Openid varchar(30) =''--??ID
	--declare @MemberPhoneNumber nvarchar(50)
	--select @Openid=Val3,@MemberPhoneNumber=
OperateData_GetMemberInfo                                                                                                        CREATE proc [dbo].[OperateData_GetMemberInfo]
	@t int =0,--??
	@Phone nvarchar(50)='',--??????
	@MemberKey uniqueidentifier=null
as 
---??????
begin
	if(@t=0)--??MemberKey????????
		select a.*,isnull(RechargeTotal+ReturnTotal,0) as CanUseTotal,b.Me
OperateData_GetPointInfo                                                                                                         CREATE proc [dbo].[OperateData_GetPointInfo]
	@t int =0,--??
	@MemberKey uniqueidentifier=null
as
---???????????
begin
	--declare @Openid varchar(30) =''--??ID
	--declare @MemberPhoneNumber nvarchar(50)
	--select @Openid=Val3,@MemberPhoneNumber=Mem
OperateData_GetRechargeInfo                                                                                                      CREATE proc [dbo].[OperateData_GetRechargeInfo]
	@t int =0,--??
	@MemberKey uniqueidentifier=null
as
---?????????
begin
	--declare @Openid varchar(30) =''--??ID
	--declare @MemberPhoneNumber nvarchar(50)
	--select @Openid=Val3,@MemberPhoneNumber=Me
OperateData_GetReturnInfo                                                                                                        CREATE proc [dbo].[OperateData_GetReturnInfo]
	@t int =0,--??
	@MemberKey uniqueidentifier=null
as
---???????????
begin
	--declare @Openid varchar(30) =''--??ID
	--declare @MemberPhoneNumber nvarchar(50)
	--select @Openid=Val3,@MemberPhoneNumber=Me
OperateData_Insert                                                                                                               -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[OperateData_Insert]
	@T int=0,
	--Ac
OperateData_StaSon                                                                                                               CREATE proc [dbo].[OperateData_StaSon]
	@t int =0, --??
	@StaSonId int =0,
	@StaSonName varchar(20)='',
	@TypeID int =0,
	@Parameter int =0,
	@ByName varchar(20)='',
	@ShowModel int=-1,
	@ChartTypeID int=0,
	@PageIndex int=1,
	@PageSize int=7


PR_MarkGiftToUsers                                                                                                               
CREATE PROC [dbo].[PR_MarkGiftToUsers]
as
--?????????,???????

--????
SELECT FD.Shopid,FD.InvNo INTO #PCYHDDH FROM OperateData.dbo.fdinv FD 
INNER JOIN OperateData.dbo.fdcashbak FDB ON FD.Shopid=FDB.ShopId AND FD.InvNo COLLATE Chinese_PRC_CI_AS = F
reporting_basic_ex                                                                                                               CREATE proc [dbo].[reporting_basic_ex]
	@t varchar(10)='seldata',
	@Rb_No nvarchar(50)='',--???
	@ApplyName varchar(20)='',--?????
	@Rs_Id int = 0,--????
	@Rp_Id int = 0,--?? 
	@ShopId int=0,
	@Rht_Id int=0,
	@otherPrice money =0,
	@remarks nvarch
reporting_basic_ex_check                                                                                                         CREATE proc [dbo].[reporting_basic_ex_check]
		@Rb_No varchar(50)=''
as
---???????
begin
	select * from reporting_to_examine a join reporting_state b on a.Rs_Id = b.Rs_Id where Rte_apply_No = @Rb_No order by Rte_time
end                              
reporting_basic_ex_details                                                                                                       CREATE proc [dbo].[reporting_basic_ex_details]
	@t varchar(10)='sel',
	@tablename varchar(50)='',
	@Rb_No nvarchar(50)=''--???
as
begin
	declare @sql varchar(4000)=''
	if(@tablename='reporting_HouseWatching')
		begin
			set @sql = 'select a.*,b.Rh
reporting_export                                                                                                                 CREATE PROCEDURE [dbo].[reporting_export]
	@type nvarchar(50)='',
	@Rp_state int =0,
	@Rp_Id int =0,
	@ShopId int =0,
	@Rs_Id int = 0,--????
	@stateTime datetime=null,
	@endTime datetime=null
AS
BEGIN
	declare @sql varchar(2000)=''  ---sql??
	de
returnResult                                                                                                                     CREATE proc [dbo].[returnResult]
	@ID int,
	@Total int =0 
as 

begin
	declare @Name varchar(50) 
	declare @ByName varchar(50) 
	declare @ChartTypeID int =0
	select @Name=StaSonName,@ByName=ByName,@ChartTypeID=ChartTypeID from StaSonInfo where Sta
search_reportinh_basic                                                                                                           -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[search_reportinh_basic]
	@apply_id nv
SendMsg                                                                                                                          

CREATE PROCEDURE [dbo].[SendMsg]
	@url nvarchar(4000)
AS
BEGIN
	
INSERT INTO [MsgManageService].[dbo].[MessageNotSend]
           ([SendUrl]
           ,[SendConent]
           ,[SendType]
           ,[SendTime]
           ,[SendSource]
    
SendMsg_sjkj                                                                                                                     

create PROCEDURE [dbo].[SendMsg_sjkj]
	@url nvarchar(4000)
AS
BEGIN
	
INSERT INTO [MsgManageService].[dbo].[MessageNotSend]
           ([SendUrl]
           ,[SendConent]
           ,[SendType]
           ,[SendTime]
           ,[SendSource]

SendMsg_sum                                                                                                                      

CREATE PROCEDURE [dbo].[SendMsg_sum]
	
AS
BEGIN
	select  count(*)  as cou,  '???'  as name from   [MsgManageService].[dbo].[MessagenotSend] where sendsource='????'and SendTime > '2018-12-28 12:30:36.297' --????
UNION 
select count(*)  as cou,  '?
Sp_CountTotalresultMinimum                                                                                                       CREATE PROC [dbo].[Sp_CountTotalresultMinimum]
@WorkDate varchar(50),
@YGNumbers YGNumberTableType READONLY
as
--?????????totalresult??Minimum????
BEGIN
    -- DECLARE @YearMonth VARCHAR(6)
    --SET @YearMonth = FORMAT(CONVERT(datetime,@WorkDate , 
StaffReport                                                                                                                      CREATE PROC [dbo].[StaffReport]
	@WorkDate DATETIME = NULL
AS
----?RmCloseInfo?????????RmCloseInfo_Day??
BEGIN 
	IF (@WorkDate IS NULL)
		SET @WorkDate = DATEADD(DAY, -1, GETDATE());

	DECLARE @BeginDate DATETIME;
	DECLARE @EndDate DATETIME;

	I
StateName                                                                                                                        -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
create PROCEDURE [dbo].[StateName]
AS
BEGIN
	select * from 
sum_Order_Info                                                                                                                   CREATE PROCEDURE [dbo].[sum_Order_Info]   --???????   
@WorkDate nvarchar(8)
AS
BEGIN
	SET nocount ON
	DECLARE @YGNumber nvarchar(20)
	SET @YGNumber=''
	--???????????
	if OBJECT_ID(N'tempdb..#FdInvCash',N'U') is not null drop table #FdInvCash
		se
SumIncome                                                                                                                        CREATE PROCEDURE [dbo].[SumIncome]
@ShopId int,
@WorkDate nvarchar(10)
AS
Begin
set nocount on
declare @CashTot int,@Cash_TargTot int,@VesaTot int,@GZTot int,@AccOkZDTot int,
@NoPayedTot int,@CheckTot int,@WXPayTot int,
@AliPayTot int,@MTPayTot int,@DZPayT
SumResultChild                                                                                                                   CREATE PROCEDURE [dbo].[SumResultChild] --?????????????? ?????????????
@WorkDate nvarchar(10)
AS
Begin
	set nocount on
	--?????
	delete ResultChild where WorkDate=@WorkDate and Source in ('MemberCard','Service','Booking','KBooking','Recharge','BottleResult
Test_Today_RmCloseInfo_Day                                                                                                       -- =============================================
-- Author:      Cascade AI
-- Create date: 2025-06-23
-- Description: ?????????????
--              ????,?????????????,
--              ????? MIMS ???????
-- ===========================================
TypeName                                                                                                                         -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
create PROCEDURE [dbo].[TypeName]
	 
AS
BEGIN
	select * fr
UpdateTotalResultShopId                                                                                                          CREATE PROCEDURE [dbo].[UpdateTotalResultShopId] --???????
@WorkMonth nvarchar(6)
AS
BEGIN
	SET nocount ON
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
	SELECT * INTO #v_attendance FROM  [*************,2433].hdhr.dbo.v_
UpdateWaiterResult                                                                                                               CREATE PROCEDURE [dbo].[UpdateWaiterResult] --???????????
 @WorkMonth nvarchar(6)
AS
Begin
	set nocount on
	IF OBJECT_ID(N'tempdb..#v_attendance',N'U') is not null drop table #v_attendance
	SELECT distinct ShopId,YGNumber,YGName,SJDays,DEPName,Title 
usp_GenerateBookingDailyReport                                                                                                   CREATE PROCEDURE usp_GenerateBookingDailyReport
    @TargetDate DATE,
    @ShopID INT
AS
BEGIN
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- 1. ????
        DELETE FROM
usp_GenerateDynamicUnifiedDailyReport                                                                                            CREATE PROCEDURE dbo.usp_GenerateDynamicUnifiedDailyReport
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11,
    @lang NVARCHAR(10) = 'EN' -- ????: 'EN' (??), 'ZH' (??)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX) = N''
usp_GetBookingReport                                                                                                             CREATE PROCEDURE dbo.usp_GetBookingReport
    @BeginDate DATE,  -- ??????
    @EndDate DATE,    -- ??????
    @ShopID INT,
    @lang NVARCHAR(10) = 'EN' -- ????: 'EN' (??), 'ZH' (??)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX) = N
usp_Job_GenerateDailyPerformanceReport                                                                                           CREATE PROCEDURE dbo.usp_Job_GenerateDailyPerformanceReport
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @ShopList TABLE (ShopId INT PRIMARY KEY);
    INSERT INTO @ShopList (ShopId) VALUES (2), (3), (4), (5), (6), (8), (9), (10), (11);
    DECLARE @Targe
usp_Job_RunBookingReportForAllShops                                                                                              CREATE PROCEDURE dbo.usp_Job_RunBookingReportForAllShops
AS
BEGIN
    SET NOCOUNT ON;

    -- ????????,????????????
    DECLARE @ShopList TABLE (ShopId INT PRIMARY KEY);
    INSERT INTO @ShopList (ShopId) VALUES (2), (3), (4), (5), (6), (8), (9), (1
usp_Report_CreateDailyPerformance                                                                                                CREATE PROCEDURE dbo.usp_Report_CreateDailyPerformance
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @ReportID INT;
    BEGIN TRANSACTION;
    BEGIN TRY
        PRINT N'?? 0: ????????...';
        SELECT @ReportID
usp_Util_CalculateDayTimeMetrics                                                                                                 CREATE PROCEDURE dbo.usp_Util_CalculateDayTimeMetrics
    @ShopId int,
    @TargetDate date
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @BeginDate datetime = DATEADD(hour, 8, CAST(@TargetDate AS datetime));
    DECLARE @EndDate datetime = DATEADD(hour,
usp_Util_CalculateSimplifiedMetrics                                                                                              CREATE PROCEDURE dbo.usp_Util_CalculateSimplifiedMetrics
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;
    CREATE TABLE #DailyReports (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
  
usp_Util_GetTimeSlotDetailsWithDirectFall                                                                                        CREATE PROCEDURE dbo.usp_Util_GetTimeSlotDetailsWithDirectFall
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @SqlStatement NVARCHAR(MAX);
    DECLARE @ParamDefinition NVARCHAR(500);
    SET @ParamDefinition = N'@pShopId INT
usp_Util_UpdateDirectFallFlags                                                                                                   CREATE PROCEDURE dbo.usp_Util_UpdateDirectFallFlags
    @TargetDate VARCHAR(8),
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;
    PRINT N'Starting direct fall flag update for ShopID: ' + CAST(@ShopId AS NVARCHAR(10)) + N' on Date: ' + @TargetDate;
  
web_9yuan_tj                                                                                                                     create PROCEDURE [dbo].[web_9yuan_tj]  --???9?????
@BegDate nvarchar(8),
@EndDate nvarchar(8),
@shopid int
AS
Begin
	set nocount on

select b.shopid,workdate,rmno,a.invno,fdcname,fdprice,fdqty,Case when cashtype='Z'then '??' when cashtype='N'then '
web_bwxr_tj                                                                                                                      CREATE PROCEDURE [dbo].[web_bwxr_tj]  --??????
@BegDate nvarchar(8),
@EndDate nvarchar(8),
@shopid int
AS
Begin
	set nocount on

select workdate,rmno,a.invno,fdcname,fdprice,fdqty,Case when cashtype='Z'then '??' when cashtype='N'then '??'end as Cas
Web_CwReport_md                                                                                                                  
CREATE PROCEDURE [dbo].[Web_CwReport_md]
    @shopid  int,
	@BegDate nvarchar(8),
	@EndDate nvarchar(8)
AS
BEGIN
	
	SET NOCOUNT ON;
	select a.shopid,fdno,fdcname ,sum(fdqty)as tt  into  #fdcash  from fdcashbak as a join fdinv as b on a.invno COLL
Web_date_food                                                                                                                    
-- =============================================
--????????
-- =============================================
CREATE PROCEDURE [dbo].[Web_date_food]
@fdcname nvarchar(20)
AS
BEGIN

	select  Case  when fdshopid=2 then '???'
				  when fdshopid=3 t
web_dixiao_tj                                                                                                                    CREATE PROCEDURE [dbo].[web_dixiao_tj]  --??????
@BegDate nvarchar(8),
@EndDate nvarchar(8),
@shopid int
AS
Begin
	set nocount on

select b.shopid,workdate,rmno,a.invno,fdcname,fdprice,fdqty,Case when cashtype='Z'then '??' when cashtype='N'then '??
web_fjbz_tj                                                                                                                      

CREATE PROCEDURE web_fjbz_tj  --????????
@BegDate nvarchar(8),
@EndDate nvarchar(8)
AS
Begin
	set nocount on
select *,(tt*commision)as tot from (select  top 1000 a.shopid,fdcname,cashusername,sum(fdqty)as tt,Case 
 when fdprice=388 then 20 
 wh
web_groupon_tj                                                                                                                   CREATE PROCEDURE [dbo].[web_groupon_tj]  --??????
@BegDate nvarchar(8),
@EndDate nvarchar(8),
@shopid int
AS
Begin
	set nocount on
	if @shopid=0  --?0??????
	select a.shopid,fdno,fdcname,fdprice,sum(fdqty)  from fdcashbak as a join fdinv as b on b.
web_hy_opencacheinfo                                                                                                             
CREATE PROCEDURE [dbo].[web_hy_opencacheinfo] --2020-01-01?2022-05-05????????????????
@type int
AS
BEGIN
	
	SET NOCOUNT ON;
	if @type=2 --???4?1?(2021???????????????????,??type=1 )  
	select distinct custtel from opencacheinfo where comedate between '2021
web_kboss_opencacheinfo                                                                                                          CREATE PROCEDURE [dbo].[web_kboss_opencacheinfo] --????????
AS
BEGIN
	
	SET NOCOUNT ON;
	 --2021??????????
	SELECT custtel,val3 from (
		select distinct custtel from opencacheinfo where shopid=4 and len(custtel)=11 and left(custtel,3) between '13' and '19'
web_lh_opencacheinfo                                                                                                             -- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[web_lh_opencacheinfo] --2020-01-01?2022-05-
web_maiduan_tj                                                                                                                   create PROCEDURE [dbo].[web_maiduan_tj]  --??????
@BegDate nvarchar(8),
@EndDate nvarchar(8),
@shopid int
AS
Begin
	set nocount on

select b.shopid,workdate,rmno,a.invno,fdcname,fdprice,fdqty,Case when cashtype='Z'then '??' when cashtype='N'then '?
web_PlatinumCard                                                                                                                 
CREATE PROCEDURE [dbo].[web_PlatinumCard] --?????????
  @type int
AS
BEGIN
	
	SET NOCOUNT ON;
	if @type=1   --????\??
	select FdShopid,fdno,fdcname,fdprice1 from food where (fdcname like '????%'or fdcname like '%???%?%') and fdprice1>0 order by Fd
Web_rms2019_getCustTelByRmNo                                                                                                     CREATE PROCEDURE [dbo].[Web_rms2019_getCustTelByRmNo]  --???????????
@ShopId INT,
@RmNo nvarchar(10)
AS
BEGIN
	SET nocount ON
	/* ???
	SELECT top 1 * FROM cloudRms2019.rms2019.dbo.opencacheinfo 
	WHERE ShopId=@ShopId 
	AND ComeDate BETWEEN CONVERT(nvarchar
Web_rms2019_getRtInfo                                                                                                            CREATE PROCEDURE Web_rms2019_getRtInfo
AS
BEGIN
	SET nocount ON
	SELECT ShopId,RtNo,RtName,NumberMin,NumberMax,SortNumber,IsWechatBook FROM cloudRms2019.rms2019.dbo.RtInfo ORDER BY ShopId,SortNumber
	SET nocount OFF
END                                     
Web_rms2019Tel                                                                                                                   
CREATE PROCEDURE Web_rms2019Tel   --????????
	@custtel nvarchar(11)
AS
BEGIN
	
	SET NOCOUNT ON;

    select ikey from cloudRms2019.rms2019.dbo.bookcacheinfo where custtel=@custtel and checkinstatus=0 

END
                                      
web_sk_tj                                                                                                                        
CREATE PROCEDURE web_sk_tj
	@BegDate nvarchar(8),
	@EndDate nvarchar(8)
AS
BEGIN
	SET NOCOUNT ON;
 select Case when a.shopid=2 then '???'
  when a.shopid=3 then'???'
  when a.shopid=4 then'???'
  when a.shopid=5 then'???'
  when a.shopid=6 then
web_tjjf_tj                                                                                                                      CREATE PROCEDURE [dbo].[web_tjjf_tj]  --??????????????
@WorkMonth nvarchar(8)

AS
Begin
	set nocount on

select Case when shopid=2 then '???'
			when shopid=3 then '???'
			when shopid=4 then '???'
			when shopid=6 then '???'
			when shopid=9 th
web_zdy_search                                                                                                                   CREATE PROCEDURE [dbo].[web_zdy_search]  --????????????  --???2.2????
@BegDate nvarchar(8),
@EndDate nvarchar(8),
@shopid int,
@fdcname nvarchar(18)
AS
Begin
	set nocount on
	if @shopid=0  --?0??????
		select a.shopid,workdate,cashtime,rmno,a.invn
