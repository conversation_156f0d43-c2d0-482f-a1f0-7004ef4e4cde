{"total_orders": 11, "checked_out": 5, "real_direct_fall": 5, "single_slot": 0, "analysis_results": [{"InvNo": "A02426816", "CustName": "张女士", "ComeTime": "13:19:49", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "313", "Remark": "贰位 V返卡扣", "Tot": 152, "预约状态": "预约单时间段 (13:30-16:30)", "CloseDatetime": "2025-07-17 16:06:40.120", "实际消费时长": "2.8小时", "消费分钟数": 166, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["28", "11:50-14:50"]], "跨越时间段数量": 5, "真实直落状态": "✅ 真正直落", "直落类型": "跨越5个时间段"}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeTime": "17:52:59", "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Numbers": 2, "RmNo": "806", "Remark": "贰位，V返", "Tot": 346, "预约状态": "预约单时间段 (18:00-21:00)", "CloseDatetime": "2025-07-17 20:18:57.193", "实际消费时长": "2.4小时", "消费分钟数": 145, "跨越时间段": [["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 8, "真实直落状态": "✅ 真正直落", "直落类型": "跨越8个时间段"}, {"InvNo": "A02426864", "CustName": "梁女士", "ComeTime": "19:28:06", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 3, "RmNo": "801", "Remark": "肆位，其中一位半价", "Tot": 568, "预约状态": "预约单时间段 (19:00-22:00)", "CloseDatetime": "2025-07-17 21:36:24.257", "实际消费时长": "2.1小时", "消费分钟数": 128, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 7, "真实直落状态": "✅ 真正直落", "直落类型": "跨越7个时间段"}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeTime": "20:56:23", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "555", "Remark": "6位，中房畅饮", "Tot": 3543, "预约状态": "预约单时间段 (20:00)", "CloseDatetime": "2025-07-18 02:05:39.290", "实际消费时长": "5.2小时", "消费分钟数": 309, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 6, "真实直落状态": "✅ 真正直落", "直落类型": "跨越6个时间段"}, {"InvNo": "A02426880", "CustName": "林先生", "ComeTime": "22:00:56", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "831", "Remark": "5位，现买", "Tot": 1592, "预约状态": "预约单时间段 (20:00)", "CloseDatetime": "2025-07-18 01:55:13.710", "实际消费时长": "3.9小时", "消费分钟数": 234, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段"}, {"InvNo": "A02426898", "CustName": "李女士", "ComeTime": "11:48:16", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 5, "RmNo": "309", "Remark": "陆位（其中一位儿童）V返", "Tot": null, "预约状态": "预约单时间段 (11:50-14:50)", "CloseDatetime": "未结账", "实际消费时长": "未结账", "真实直落状态": "未结账", "跨越时间段": [], "跨越时间段数量": 0}, {"InvNo": "A02426914", "CustName": "贵宾先生", "ComeTime": "14:02:31", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "666", "Remark": "", "Tot": null, "预约状态": "预约单时间段 (13:30-16:30)", "CloseDatetime": "未结账", "实际消费时长": "未结账", "真实直落状态": "未结账", "跨越时间段": [], "跨越时间段数量": 0}, {"InvNo": "A02426911", "CustName": "段女士", "ComeTime": "13:24:23", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 10, "RmNo": "333", "Remark": "拾位美团1488X1", "Tot": null, "预约状态": "预约单时间段 (13:30-16:30)", "CloseDatetime": "未结账", "实际消费时长": "未结账", "真实直落状态": "未结账", "跨越时间段": [], "跨越时间段数量": 0}, {"InvNo": "A02426904", "CustName": "贵宾女士", "ComeTime": "13:20:02", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "302", "Remark": "贰位在线团购x2", "Tot": null, "预约状态": "预约单时间段 (13:30-16:30)", "CloseDatetime": "未结账", "实际消费时长": "未结账", "真实直落状态": "未结账", "跨越时间段": [], "跨越时间段数量": 0}, {"InvNo": "A02426908", "CustName": "刘宇轩先生", "ComeTime": "13:21:50", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 5, "RmNo": "806", "Remark": "提前购券，同意使用", "Tot": null, "预约状态": "预约单时间段 (13:30-16:30)", "CloseDatetime": "未结账", "实际消费时长": "未结账", "真实直落状态": "未结账", "跨越时间段": [], "跨越时间段数量": 0}, {"InvNo": "A02426895", "CustName": "李女士", "ComeTime": "11:40:46", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 2, "RmNo": "319", "Remark": "寿星免一V返", "Tot": null, "预约状态": "预约单时间段 (11:50-14:50)", "CloseDatetime": "未结账", "实际消费时长": "未结账", "真实直落状态": "未结账", "跨越时间段": [], "跨越时间段数量": 0}]}