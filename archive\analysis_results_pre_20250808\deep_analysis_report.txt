深度分析报告: ShopID=11, WorkDate=20250723

==================================================
====== 案件聚焦：房间号【311】 ======
==================================================

【A. 问题结账单据 (来自 operatedata.RmCloseInfo)】
    InvNo  Tot  Numbers           CloseDatetime
A02427560    0        0 2025-07-23 20:42:37.770

【B. 当日原始开台记录 (来自 rms2019.opencacheinfo)】
    Invno ComeTime  Numbers
A02673284 20:58:11        2
A02427565 19:21:34        4
A02814810 13:22:40        2
A02427502 11:49:19        6
A02814849 16:40:40        2
A02635776 19:02:01        4
A02281273 18:21:21        5
A02635609 11:40:22        3
A02427533 15:11:51        2
A02635699 15:08:52        2

【C. 分析结论】
-> 关键发现：存在结账单，也存在开台记录，但【账单号完全不匹配】。
-> 可能原因：这极有可能是由于 KTV 系统中的“多单合并结账”、“换房”或“换单”等复杂操作导致。
-> 例如，客人可能持有多张开台单(B区)，但最后只用一张新单(A区)进行了总结账。

==================================================
====== 案件聚焦：房间号【902】 ======
==================================================

【A. 问题结账单据 (来自 operatedata.RmCloseInfo)】
    InvNo  Tot  Numbers           CloseDatetime
A02427558    0        0 2025-07-23 20:28:40.497

【B. 当日原始开台记录 (来自 rms2019.opencacheinfo)】
    Invno ComeTime  Numbers
A02789226 18:44:03        6
A02789097 13:29:53        2

【C. 分析结论】
-> 关键发现：存在结账单，也存在开台记录，但【账单号完全不匹配】。
-> 可能原因：这极有可能是由于 KTV 系统中的“多单合并结账”、“换房”或“换单”等复杂操作导致。
-> 例如，客人可能持有多张开台单(B区)，但最后只用一张新单(A区)进行了总结账。

==================================================
====== 案件聚焦：房间号【806】 ======
==================================================

【A. 问题结账单据 (来自 operatedata.RmCloseInfo)】
    InvNo  Tot  Numbers           CloseDatetime
A02427509  457        0 2025-07-23 14:05:32.490

【B. 当日原始开台记录 (来自 rms2019.opencacheinfo)】
    Invno ComeTime  Numbers
A02635757 18:11:59        4
A02427536 12:22:17        3

【C. 分析结论】
-> 关键发现：存在结账单，也存在开台记录，但【账单号完全不匹配】。
-> 可能原因：这极有可能是由于 KTV 系统中的“多单合并结账”、“换房”或“换单”等复杂操作导致。
-> 例如，客人可能持有多张开台单(B区)，但最后只用一张新单(A区)进行了总结账。

==================================================
====== 案件聚焦：房间号【316】 ======
==================================================

【A. 问题结账单据 (来自 operatedata.RmCloseInfo)】
    InvNo  Tot  Numbers           CloseDatetime
A02427499    0        0 2025-07-23 13:59:18.557

【B. 当日原始开台记录 (来自 rms2019.opencacheinfo)】
    Invno ComeTime  Numbers
A02635602 11:31:59        4
A02814814 13:23:28        3
A02427526 11:40:35        6
A02673268 20:08:09        2
A02814862 17:08:03        2
A02427552 18:02:59       10
A02281226 14:57:52        2

【C. 分析结论】
-> 关键发现：存在结账单，也存在开台记录，但【账单号完全不匹配】。
-> 可能原因：这极有可能是由于 KTV 系统中的“多单合并结账”、“换房”或“换单”等复杂操作导致。
-> 例如，客人可能持有多张开台单(B区)，但最后只用一张新单(A区)进行了总结账。