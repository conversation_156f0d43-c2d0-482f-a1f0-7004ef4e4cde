{"table_structure": [{"column_name": "NightDetailID", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "NO", "default_value": null, "is_primary_key": "YES"}, {"column_name": "ReportID", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "NO", "default_value": null, "is_primary_key": "NO"}, {"column_name": "FreeMeal_KPlus", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "FreeMeal_Special", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "FreeMeal_Meituan", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "FreeMeal_<PERSON><PERSON>in", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "FreeMeal_BatchCount", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "FreeMeal_Revenue", "data_type": "decimal", "max_length": null, "precision": 18, "scale": 2, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "Buyout_BatchCount", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "Buyout_Revenue", "data_type": "decimal", "max_length": null, "precision": 18, "scale": 2, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "Changyin_BatchCount", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "Changyin_Revenue", "data_type": "decimal", "max_length": null, "precision": 18, "scale": 2, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "FreeConsumption_BatchCount", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "NonPackage_Special", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "NonPackage_Meituan", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "NonPackage_Douyin", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "NonPackage_Others", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "DiscountFree_BatchCount", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "DiscountFree_Revenue", "data_type": "decimal", "max_length": null, "precision": 18, "scale": 2, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "Night_Verify_BatchCount", "data_type": "int", "max_length": null, "precision": 10, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}, {"column_name": "Night_Verify_Revenue", "data_type": "decimal", "max_length": null, "precision": 18, "scale": 0, "is_nullable": "YES", "default_value": null, "is_primary_key": "NO"}], "procedure_info": {"name": "usp_GenerateSimplifiedDailyReport_V7_Final", "create_date": "2025-07-28 16:52:43.793000", "modify_date": "2025-07-28 16:52:43.793000", "definition": "-- Final, Corrected Version (V8) - Fix Buyout_BatchCount & NonPackage_Others logic\nCREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final\n    @ShopId INT,\n    @BeginDate DATE,\n    @EndDate DATE\nAS\nBEGIN\n    SET NOCOUNT ON;\n\n    CREATE TABLE #DailyReports (\n        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),\n        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),\n        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,\n        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,\n        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),\n        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),\n        <PERSON>yin_BatchCount int, Changyin_Revenue decimal(18, 2),\n        FreeConsumption_BatchCount int,\n        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,\n        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2)\n    );\n\n    DECLARE @CurrentDate DATE = @BeginDate;\n\n    WHILE @CurrentDate <= @EndDate\n    BEGIN\n        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));\n        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));\n\n        -- CTE for base records\n        WITH RecordsWithTimeMode AS (\n            SELECT\n                rt.*,\n                sti.TimeMode,\n                (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,\n                CASE\n                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode\n                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1\n                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2\n                    WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1\n                    ELSE 2\n                END AS RevenueClassificationMode\n            FROM dbo.RmCloseInfo AS rt\n            LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo\n            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime\n        ),\n        -- CTE for package data (night-time only)\n        PackageData AS (\n            SELECT\n                r.InvNo,\n                fdc.FdCName,\n                (fdc.FdPrice * fdc.FdQty) as ItemRevenue\n            FROM RecordsWithTimeMode r\n            JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS\n            WHERE r.TimeMode = 2\n            AND fdc.ShopId = @ShopId\n            AND (fdc.FdCName LIKE N'%买断%' OR fdc.FdCName LIKE N'%畅饮%')\n        )\n        -- Final aggregation and insertion\n        INSERT INTO #DailyReports\n        SELECT\n            @CurrentDate AS ReportDate,\n            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,\n            DATENAME(weekday, @CurrentDate) AS Weekday,\n            \n            -- Core Metrics\n            ISNULL(SUM(Revenue), 0) AS TotalRevenue,\n            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,\n            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,\n            (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,\n            COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,\n            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,\n\n            -- Free Meal Metrics (Night-time)\n            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.CtNo = 2 THEN rt.InvNo END) AS FreeMeal_KPlus,\n            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS FreeMeal_Special,\n            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS FreeMeal_Meituan,\n            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS FreeMeal_Douyin,\n            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.InvNo END) AS FreeMeal_BatchCount,\n            ISNULL(SUM(CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.Revenue ELSE 0 END), 0) AS FreeMeal_Revenue,\n\n            -- Package Metrics (Night-time, from CTE)\n            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') AS Buyout_BatchCount,\n            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%买断%'), 0) AS Buyout_Revenue,\n            ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) AS Changyin_BatchCount,\n            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%畅饮%' AND FdCName NOT LIKE N'%自由畅饮%'), 0) AS Changyin_Revenue,\n            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') AS FreeConsumption_BatchCount,\n\n            -- Non-Package Metrics (Night-time)\n            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS NonPackage_Special,\n            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS NonPackage_Meituan,\n            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS NonPackage_Douyin,\n            \n            -- Corrected NonPackage_Others Logic\n            (COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) - \n            (\n                COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.InvNo END) + \n                (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') + \n                ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) + \n                (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') + \n                COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.AliPay > 0 THEN rt.InvNo END) + \n                COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.MTPay > 0 THEN rt.InvNo END) + \n                COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.DZPay > 0 THEN rt.InvNo END)\n            ) AS NonPackage_Others,\n\n            -- Verification Metrics\n            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS Night_Verify_BatchCount,\n            ISNULL(SUM(CASE WHEN rt.TimeMode = 2 THEN rt.Revenue ELSE 0 END), 0) AS Night_Verify_Revenue\n\n        FROM RecordsWithTimeMode rt;\n\n        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);\n    END\n\n    SELECT * FROM #DailyReports ORDER BY ReportDate;\n\n    DROP TABLE #DailyReports;\n\nEND\n"}, "usage_statistics": {"total_records": 1, "FreeMeal_KPlus_used": 0, "FreeMeal_Special_used": 0, "FreeMeal_Meituan_used": 0, "FreeMeal_Douyin_used": 0, "FreeMeal_BatchCount_used": 0, "FreeMeal_Revenue_used": 0, "Buyout_BatchCount_used": 1, "Buyout_Revenue_used": 1, "Changyin_BatchCount_used": 1, "Changyin_Revenue_used": 1, "FreeConsumption_BatchCount_used": 1, "NonPackage_Special_used": 0, "NonPackage_Meituan_used": 0, "NonPackage_Douyin_used": 0, "NonPackage_Others_used": 1, "DiscountFree_BatchCount_used": 0, "DiscountFree_Revenue_used": 0}, "recommendations": {"unused_fields": [{"field": "FreeMeal_KPlus_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "FreeMeal_Special_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "FreeMeal_<PERSON><PERSON><PERSON>_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "FreeMeal_<PERSON><PERSON><PERSON>_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "FreeMeal_BatchCount_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "FreeMeal_Revenue_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "NonPackage_Special_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "NonPackage_Meituan_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "NonPackage_<PERSON><PERSON><PERSON>_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "DiscountFree_BatchCount_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}, {"field": "DiscountFree_Revenue_used", "usage_rate": 0.0, "suggestion": "可以考虑从插入语句中移除"}], "rarely_used_fields": [], "essential_fields": [{"field": "Buyout_BatchCount_used", "usage_rate": 100.0, "suggestion": "重要字段，建议保留"}, {"field": "Buyout_Revenue_used", "usage_rate": 100.0, "suggestion": "重要字段，建议保留"}, {"field": "<PERSON><PERSON><PERSON>_BatchCount_used", "usage_rate": 100.0, "suggestion": "重要字段，建议保留"}, {"field": "Changyin_Revenue_used", "usage_rate": 100.0, "suggestion": "重要字段，建议保留"}, {"field": "FreeConsumption_BatchCount_used", "usage_rate": 100.0, "suggestion": "重要字段，建议保留"}, {"field": "NonPackage_Others_used", "usage_rate": 100.0, "suggestion": "重要字段，建议保留"}], "optimization_strategies": ["发现 11 个未使用字段，可以从INSERT语句中移除", "建议创建优化版本的存储过程，只插入有意义的数据"]}, "analysis_timestamp": "2025-07-29T14:22:10.226269"}