{"procedure_info": {"name": "usp_RunUnifiedDailyReportJob", "definition": "\n-- =================================================================================\n-- Name: usp_RunUnifiedDailyReportJob (v3 - Expanded Night Details with Discount-Free)\n-- =================================================================================\nCREATE PROCEDURE dbo.usp_RunUnifiedDailyReportJob\n    @TargetDate DATE = NULL,\n    @ShopId INT = 11\nAS\nBEGIN\n    SET NOCOUNT ON;\n\n    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);\n\n    DECLARE @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report';\n    DECLARE @ReportID INT;\n\n    BEGIN TRANSACTION;\n\n    BEGIN TRY\n        -- === Step 0: Clean up existing data ===\n        PRINT N'Step 0: Deleting existing data for ' + CONVERT(NVARCHAR, @TargetDate) + N'...';\n        SELECT @ReportID = ReportID FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId;\n        IF @ReportID IS NOT NULL\n        BEGIN\n            DELETE FROM dbo.FullDailyReport_TimeSlotDetails WHERE ReportID = @ReportID;\n            DELETE FROM dbo.FullDailyReport_NightDetails WHERE ReportID = @ReportID;\n            DELETE FROM dbo.FullDailyReport_Header WHERE ReportID = @ReportID;\n        END\n\n        -- === Step 1: Generate and Insert Header Data ===\n        PRINT N'Step 1: Generating Header data...';\n        CREATE TABLE #TempHeader (\n            WorkDate DATE, ShopName NVARCHAR(100), WeekdayName NVARCHAR(20),\n            DayTimeRevenue DECIMAL(18,2), NightTimeRevenue DECIMAL(18,2), TotalRevenue DECIMAL(18,2),\n            DayTimeBatchCount INT, NightTimeBatchCount INT, TotalBatchCount INT,\n            TotalGuestCount INT, BuffetGuestCount INT,\n            DayTimeDropInBatch INT, NightTimeDropInBatch INT, TotalDirectFallGuests INT\n        );\n        INSERT INTO #TempHeader EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;\n        INSERT INTO dbo.FullDailyReport_Header (\n            ReportDate, ShopID, ShopName, Weekday, DayTimeRevenue, NightTimeRevenue, TotalRevenue,\n            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,\n            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests\n        ) SELECT @TargetDate, @ShopId, ShopName, WeekdayName, DayTimeRevenue, NightTimeRevenue, TotalRevenue,\n            DayTimeBatchCount, NightTimeBatchCount, TotalBatchCount, TotalGuestCount, BuffetGuestCount,\n            DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests FROM #TempHeader;\n        SET @ReportID = SCOPE_IDENTITY();\n        PRINT N'Header data inserted. New ReportID: ' + CAST(@ReportID AS NVARCHAR);\n\n        -- === Step 2: Generate and Insert FULL Night Details Data ===\n        PRINT N'Step 2: Generating FULL Night Details data...';\n        -- The temp table now matches the FULL output of the expanded V7\n        CREATE TABLE #TempNightDetails (\n            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),\n            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),\n            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,\n            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,\n            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),\n            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),\n            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),\n            FreeConsumption_BatchCount int,\n            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,\n            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),\n            DiscountFree_BatchCount INT, DiscountFree_Revenue DECIMAL(18, 2)\n        );\n        INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;\n        -- The INSERT statement now includes the new Discount-Free columns\n   ", "created": "2025-07-29 11:30:15.820000", "last_altered": "2025-07-29 11:30:15.820000"}, "structure_analysis": {"parameters": ["@TargetDate DATE = NULL,", "@ShopId INT = 11"], "variables": ["DECLARE @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report';", "DECLARE @ReportID INT;"], "tables_referenced": ["DBO"], "procedures_called": ["DBO", "DBO"], "functions_used": [], "control_structures": ["IF: -- Name: usp_RunUnifiedDailyReportJob (v3 - Expanded Night Details with Discount-Free)", "IF: CREATE PROCEDURE dbo.usp_RunUnifiedDailyReportJob", "BEGIN: BEGIN", "IF: IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);", "IF: <PERSON><PERSON><PERSON><PERSON> @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report';", "BEGIN: BEGIN TRANSACTION;", "BEGIN: BEGIN TRY", "FOR: PRINT N'Step 0: Deleting existing data for ' + CONVERT(NVARCHAR, @TargetDate) + N'...';", "IF: IF @ReportID IS NOT NULL", "BEGIN: BEGIN", "END: END", "IF: Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),", "IF: INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;", "BEGIN: INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;", "END: INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;"], "error_handling": ["BEGIN TRY"], "transactions": ["BEGIN TRANSACTION;"], "temp_tables": ["CREATE TABLE #TempHeader (", "INSERT INTO #TempHeader EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;", "DayTimeDropInBatch, NightTimeDropInBatch, TotalDirectFallGuests FROM #TempHeader;", "CREATE TABLE #TempNightDetails (", "INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;"]}, "dependencies": [], "permissions": [], "analysis_timestamp": "2025-07-29T14:10:29.121012"}