================================================================================
存储过程深度分析报告: usp_RunUnifiedDailyReportJob
================================================================================

📋 基本信息:
   名称: usp_RunUnifiedDailyReportJob
   创建时间: 2025-07-29 11:30:15.820000
   最后修改: 2025-07-29 11:30:15.820000
   代码行数: 122

📝 参数分析 (2 个):
   Line 6: @TargetDate DATE = NULL,
   Line 7: @ShopId INT = 11

🔧 变量声明 (5 个):
   Line 14: DECLARE @JobName NVARCHAR(255) = N'KTV_Unified_Daily_Report';
   Line 15: DECLARE @ReportID INT;
   Line 103: DECLARE @SuccessMessage NVARCHAR(500) = N'Success: Unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N' processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR);
   Line 114: DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
   Line 115: DECLARE @ErrorLogMessage NVARCHAR(MAX) = N'Failure: Error processing unified report for date ' + CONVERT(NVARCHAR, @TargetDate) + N'. Details: ' + @ErrorMessage;

🗂️ 临时表 (3 个):
   Line 32: TempHeader - CREATE TABLE #TempHeader (
   Line 53: TempNightDetails - CREATE TABLE #TempNightDetails (
   Line 83: TempTimeSlotDetails - CREATE TABLE #TempTimeSlotDetails (

🗃️ 主表操作 (0 个):

🔗 调用的存储过程 (0 个):

💾 事务处理 (3 个):
   Line 17: Transaction started
   Line 101: Transaction committed
   Line 109: Transaction rolled back

⚠️ 错误处理 (4 个):
   Line 19: TRY block started
   Line 107: Error handling block ended
   Line 108: CATCH block started
   Line 120: Error handling block ended

🎯 业务逻辑步骤 (10 个):
   Line 2: -- =================================================================================
   Line 4: -- =================================================================================
   Line 20: -- === Step 0: Clean up existing data ===
   Line 21: PRINT N'Step 0: Deleting existing data for ' + CONVERT(NVARCHAR, @TargetDate) + N'...';
   Line 30: -- === Step 1: Generate and Insert Header Data ===
   Line 31: PRINT N'Step 1: Generating Header data...';
   Line 50: -- === Step 2: Generate and Insert FULL Night Details Data ===
   Line 51: PRINT N'Step 2: Generating FULL Night Details data...';
   Line 81: -- === Step 3: Generate and Insert Time Slot Details Data ===
   Line 82: PRINT N'Step 3: Generating Time Slot Details data...';

🔄 数据流管道 (5 个):
   Line 39: Data pipeline - INSERT INTO #TempHeader EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;
   Line 66: Data pipeline - INSERT INTO #TempNightDetails EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;
   Line 88: Data pipeline - INSERT INTO #TempTimeSlotDetails EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;
   Line 104: Data pipeline - INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Success', @SuccessMessage);
   Line 116: Data pipeline - INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) VALUES (@JobName, @TargetDate, N'Failure', @ErrorLogMessage);