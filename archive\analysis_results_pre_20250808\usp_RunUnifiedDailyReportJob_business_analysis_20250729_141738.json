{"procedures": {"usp_GenerateDayTimeReport_Simple_V3": {"purpose": "生成白天时段报表数据", "input_params": ["@ShopId", "@TargetDate"], "output_structure": ["WorkDate", "ShopName", "WeekdayName", "DayTimeRevenue", "NightTimeRevenue", "TotalRevenue", "DayTimeBatchCount", "NightTimeBatchCount", "TotalBatchCount", "TotalGuestCount", "BuffetGuestCount", "DayTimeDropInBatch", "NightTimeDropInBatch", "TotalDirectFallGuests"]}, "usp_GenerateSimplifiedDailyReport_V7_Final": {"purpose": "生成夜间详细报表数据（包含折扣免费数据）", "input_params": ["@ShopId", "@BeginDate", "@EndDate"], "output_structure": ["ReportDate", "ShopName", "Weekday", "TotalRevenue", "DayTimeRevenue", "NightTimeRevenue", "TotalBatchCount", "DayTimeBatchCount", "NightTimeBatchCount", "FreeMeal_KPlus", "FreeMeal_Special", "FreeMeal_Meituan", "FreeMeal_<PERSON><PERSON>in", "FreeMeal_BatchCount", "FreeMeal_Revenue", "Buyout_BatchCount", "Buyout_Revenue", "Changyin_BatchCount", "Changyin_Revenue", "FreeConsumption_BatchCount", "NonPackage_Special", "NonPackage_Meituan", "NonPackage_Douyin", "NonPackage_Others", "Night_Verify_BatchCount", "Night_Verify_Revenue", "DiscountFree_BatchCount", "DiscountFree_Revenue"]}, "usp_GetTimeSlotDetails_WithDirectFall": {"purpose": "生成时段详细数据（包含直落信息）", "input_params": ["@TargetDate", "@ShopId"], "output_structure": ["TimeSlotName", "TimeSlotOrder", "KPlus_Count", "Special_Count", "<PERSON><PERSON><PERSON>_<PERSON>", "<PERSON><PERSON><PERSON>_Count", "RoomFee_Count", "Subtotal_Count", "PreviousSlot_DirectFall"]}}, "tables": {"FullDailyReport_Header": {"purpose": "存储每日报表头部信息", "key_fields": ["ReportID", "ReportDate", "ShopID"], "operations": ["SELECT", "INSERT", "DELETE"]}, "FullDailyReport_NightDetails": {"purpose": "存储夜间详细数据", "key_fields": ["ReportID", "FreeMeal_*", "Buyout_*", "DiscountFree_*"], "operations": ["INSERT", "DELETE"]}, "FullDailyReport_TimeSlotDetails": {"purpose": "存储时段详细数据", "key_fields": ["ReportID", "TimeSlotName", "TimeSlotOrder"], "operations": ["INSERT", "DELETE"]}, "JobExecutionLog": {"purpose": "记录作业执行日志", "key_fields": ["JobName", "ReportDate", "Status", "Message"], "operations": ["INSERT"]}}, "flow_steps": [{"step": 0, "name": "数据清理", "description": "删除指定日期的现有报表数据", "details": ["根据ReportDate和ShopID查找现有ReportID", "如果存在，删除相关的时段详细数据", "删除相关的夜间详细数据", "删除头部数据"], "risk_points": ["数据完整性", "并发访问"]}, {"step": 1, "name": "生成头部数据", "description": "调用白天报表存储过程生成基础数据", "details": ["创建临时表#TempHeader", "调用usp_GenerateDayTimeReport_Simple_V3获取数据", "插入到FullDailyReport_Header表", "获取新生成的ReportID"], "risk_points": ["存储过程依赖", "数据一致性"]}, {"step": 2, "name": "生成夜间详细数据", "description": "调用夜间报表存储过程生成详细数据", "details": ["创建临时表#TempNightDetails（包含折扣免费字段）", "调用usp_GenerateSimplifiedDailyReport_V7_Final获取数据", "插入到FullDailyReport_NightDetails表"], "risk_points": ["复杂数据结构", "新增字段兼容性"]}, {"step": 3, "name": "生成时段详细数据", "description": "调用时段详细存储过程生成时段数据", "details": ["创建临时表#TempTimeSlotDetails", "调用usp_GetTimeSlotDetails_WithDirectFall获取数据", "插入到FullDailyReport_TimeSlotDetails表"], "risk_points": ["直落逻辑复杂性", "时段计算准确性"]}], "error_handling": {"transaction_management": {"description": "使用事务确保数据一致性", "details": ["BEGIN TRANSACTION在开始时启动事务", "COMMIT TRANSACTION在成功时提交", "ROLLBACK TRANSACTION在错误时回滚"]}, "temp_table_cleanup": {"description": "确保临时表被正确清理", "details": ["正常流程中DROP临时表", "错误处理中检查并DROP临时表"]}, "logging_mechanism": {"description": "记录执行状态到日志表", "details": ["成功时记录Success状态和ReportID", "失败时记录Failure状态和错误详情", "使用RAISERROR重新抛出错误"]}}, "performance": {"strengths": ["使用临时表缓存中间结果", "明确的事务边界", "及时清理临时表", "使用SCOPE_IDENTITY()获取新插入的ID"], "potential_issues": ["三个存储过程的串行执行可能较慢", "大量数据时临时表可能占用较多内存", "没有明显的索引优化策略", "事务时间可能较长"], "optimization_suggestions": ["考虑并行执行部分独立的数据生成步骤", "为关键表添加适当的索引", "监控事务执行时间", "考虑分批处理大量数据"]}, "analysis_timestamp": "2025-07-29T14:17:38.442239"}