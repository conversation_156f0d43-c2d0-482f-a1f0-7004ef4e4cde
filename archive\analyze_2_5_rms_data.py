
import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
DATABASE = 'rms2019'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

TABLES_TO_ANALYZE = ['opencacheinfo', 'openhistory']

def get_schema_and_latest_data(server, db, user, pwd, table_name):
    """获取指定表的结构和最新一条数据"""
    connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={db};UID={user};PWD={pwd};TrustServerCertificate=yes;'
    
    print(f"\n--- Analyzing table: {db}.dbo.{table_name} on {server} ---")

    try:
        with pyodbc.connect(connection_string, timeout=15) as conn:
            cursor = conn.cursor()

            # 1. 获取表结构
            schema_query = f"""
            SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = '{table_name}'
            ORDER BY ORDINAL_POSITION;
            """
            print("\n[Schema]")
            cursor.execute(schema_query)
            for row in cursor.fetchall():
                print(f"- {row.COLUMN_NAME} ({row.DATA_TYPE}{('(' + str(row.CHARACTER_MAXIMUM_LENGTH) + ')') if row.CHARACTER_MAXIMUM_LENGTH else ''})")

            # 2. 获取最新一条数据 (假设IKey是自增主键)
            latest_data_query = f"SELECT TOP 1 * FROM {table_name} ORDER BY IKey DESC;"
            print("\n[Latest Data Record]")
            cursor.execute(latest_data_query)
            row = cursor.fetchone()
            if row:
                columns = [column[0] for column in cursor.description]
                for i, col_name in enumerate(columns):
                    print(f"- {col_name}: {row[i]}")
            else:
                print("No data found in this table.")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    for table in TABLES_TO_ANALYZE:
        get_schema_and_latest_data(SERVER, DATABASE, USERNAME, PASSWORD, table)
