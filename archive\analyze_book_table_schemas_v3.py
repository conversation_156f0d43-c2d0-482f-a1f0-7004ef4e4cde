import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

def get_table_schema(cursor, server_name, db_name, schema_name, table_name):
    query = f"EXEC sp_columns_ex @table_server = '{server_name}', @table_name = '{table_name}', @table_schema = '{schema_name}', @table_catalog = '{db_name}'"
    cursor.execute(query)
    return cursor.fetchall()

def analyze_schemas():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} ---')

            # 获取本地表结构
            local_bookcache_schema = get_table_schema(cursor, '@@SERVERNAME', 'operatedata', 'dbo', 'bookcacheinfo')
            local_bookhistory_schema = get_table_schema(cursor, '@@SERVERNAME', 'operatedata', 'dbo', 'bookhistory')

            # 获取远程表结构
            remote_bookcache_schema = get_table_schema(cursor, 'cloudRms2019', 'rms2019', 'dbo', 'bookcacheinfo')
            remote_bookhistory_schema = get_table_schema(cursor, 'cloudRms2019', 'rms2019', 'dbo', 'bookhistory')

            print("\n--- Local bookcacheinfo Schema ---")
            for row in local_bookcache_schema:
                print(f"({row.COLUMN_NAME}, {row.TYPE_NAME}, {row.LENGTH})")

            print("\n--- Remote bookcacheinfo Schema ---")
            for row in remote_bookcache_schema:
                print(f"({row.COLUMN_NAME}, {row.TYPE_NAME}, {row.LENGTH})")

            print("\n--- Local bookhistory Schema ---")
            for row in local_bookhistory_schema:
                print(f"({row.COLUMN_NAME}, {row.TYPE_NAME}, {row.LENGTH})")

            print("\n--- Remote bookhistory Schema ---")
            for row in remote_bookhistory_schema:
                print(f"({row.COLUMN_NAME}, {row.TYPE_NAME}, {row.LENGTH})")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    analyze_schemas()
