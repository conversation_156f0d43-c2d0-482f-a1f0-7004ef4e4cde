import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

def get_local_table_schema(cursor, db_name, schema_name, table_name):
    # 直接查询本地信息
    query = f"""
    SELECT COLUMN_NAME, DATA_TYPE
    FROM {db_name}.INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = '{schema_name}' AND TABLE_NAME = '{table_name}'
    """
    cursor.execute(query)
    return cursor.fetchall()

def get_remote_table_schema(cursor, linked_server, db_name, schema_name, table_name):
    # 使用 sp_columns_ex 查询链接服务器
    query = f"EXEC sp_columns_ex @table_server = '{linked_server}', @table_name = '{table_name}', @table_schema = '{schema_name}', @table_catalog = '{db_name}'"
    cursor.execute(query)
    # 提取列名和类型
    return [(row.COLUMN_NAME, row.TYPE_NAME) for row in cursor.fetchall()]

def analyze_schemas():
    # 明确连接到 rms2019 数据库
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;DATABASE=rms2019;"
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER}/rms2019 ---')

            # --- bookcacheinfo ---
            local_cache_schema = get_local_table_schema(cursor, 'rms2019', 'dbo', 'bookcacheinfo')
            remote_cache_schema = get_remote_table_schema(cursor, 'cloudRms2019', 'rms2019', 'dbo', 'bookcacheinfo')
            
            # --- bookhistory ---
            local_history_schema = get_local_table_schema(cursor, 'rms2019', 'dbo', 'bookhistory')
            remote_history_schema = get_remote_table_schema(cursor, 'cloudRms2019', 'rms2019', 'dbo', 'bookhistory')

            # 转换为集合以便比较
            local_cache_cols = {row[0].lower() for row in local_cache_schema}
            remote_cache_cols = {row[0].lower() for row in remote_cache_schema}
            
            local_history_cols = {row[0].lower() for row in local_history_schema}
            remote_history_cols = {row[0].lower() for row in remote_history_schema}

            print("\n--- Analyzing bookcacheinfo (Source: cloudRms2019.rms2019, Target: localhost.rms2019) ---")
            print(f"Missing in local: {sorted(list(remote_cache_cols - local_cache_cols))}")
            print(f"Extra in local:   {sorted(list(local_cache_cols - remote_cache_cols))}")

            print("\n--- Analyzing bookhistory (Source: cloudRms2019.rms2019, Target: localhost.rms2019) ---")
            print(f"Missing in local: {sorted(list(remote_history_cols - local_history_cols))}")
            print(f"Extra in local:   {sorted(list(local_history_cols - remote_history_cols))}")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    analyze_schemas()
