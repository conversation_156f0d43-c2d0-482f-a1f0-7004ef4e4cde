import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

def get_schema_details(cursor, query):
    cursor.execute(query)
    # 创建一个字典，键是列名，值是(数据类型, 长度)
    schema_dict = {}
    for row in cursor.fetchall():
        # 将列名统一转为小写以便比较
        schema_dict[row.COLUMN_NAME.lower()] = (row.DATA_TYPE, row.CHARACTER_MAXIMUM_LENGTH)
    return schema_dict

def analyze_detailed_schemas():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;DATABASE=rms2019;"
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- 成功连接到 {SERVER}/rms2019 ---')

            # --- 定义查询 ---
            # 本地查询
            local_query_template = """SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH 
                                     FROM INFORMATION_SCHEMA.COLUMNS 
                                     WHERE TABLE_NAME = '{table_name}' AND TABLE_SCHEMA = 'dbo'"""
            # 远程查询 (通过 OPENQUERY)
            remote_query_template = """SELECT * FROM OPENQUERY(cloudRms2019, 
                                         'SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH 
                                          FROM rms2019.INFORMATION_SCHEMA.COLUMNS 
                                          WHERE TABLE_NAME = \'{table_name}\' AND TABLE_SCHEMA = \'dbo\''')"""

            tables_to_compare = ['bookcacheinfo', 'bookhistory']

            for table in tables_to_compare:
                print(f"\n--- 正在比较表: {table} ---")
                local_schema = get_schema_details(cursor, local_query_template.format(table_name=table))
                remote_schema = get_schema_details(cursor, remote_query_template.format(table_name=table))

                all_columns = sorted(list(local_schema.keys() | remote_schema.keys()))

                mismatches_found = False
                for col in all_columns:
                    local_spec = local_schema.get(col)
                    remote_spec = remote_schema.get(col)

                    if local_spec != remote_spec:
                        mismatches_found = True
                        print(f"  - 列 '{col}' 不匹配:")
                        print(f"    -> 本地 (2.5): {local_spec}")
                        print(f"    -> 远程 (193): {remote_spec}")
                
                if not mismatches_found:
                    print("所有列的类型和长度均匹配。")

    except Exception as e:
        print(f"执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    analyze_detailed_schemas()
