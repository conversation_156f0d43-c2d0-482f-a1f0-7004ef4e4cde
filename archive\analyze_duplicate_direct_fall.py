

import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 查询参数 ---
TARGET_SHOP_ID = 11
TARGET_DATES = ['20250722', '20250723'] 

def analyze_duplicates_and_count_unique():
    """找出包含多个直落项的账单，并精确统计唯一的直落账单批次数。"""
    cnxn = None
    try:
        # 1. 构建SQL查询，这次我们需要获取InvNo以进行分组
        placeholders = ', '.join('?' * len(TARGET_DATES))
        sql_query = f"""SELECT 
                         rc.InvNo, fb.FdCName 
                     FROM 
                         dbo.RmCloseInfo rc
                     JOIN 
                         dbo.FdCashBak fb ON rc.InvNo = fb.InvNo
                     WHERE 
                         rc.ShopId = ? 
                         AND rc.WorkDate IN ({placeholders}) 
                         AND rc.OpenDateTime IS NOT NULL 
                         AND DATEPART(hour, rc.OpenDateTime) < 20
                         AND fb.FdCName LIKE ?;"""
        
        params = [TARGET_SHOP_ID] + TARGET_DATES + ['%直落%']

        # 2. 连接数据库并执行查询
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} 并获取所有直落下单项... ---")
        cnxn = pyodbc.connect(conn_str)
        df = pd.read_sql_query(sql_query, cnxn, params=params)
        print(f"查询完成，共找到 {len(df)} 条与‘直落’相关的下单记录。")

        if df.empty:
            print("在指定日期范围的白天账单中，没有找到任何名称包含‘直落’的下单项。")
            return

        # 3. 分析包含多个直落项的账单
        print("\n--- 步骤A: 查找包含多个‘直落’项的账单 ---")
        invoice_item_counts = df.groupby('InvNo').size()
        multi_item_invoices = invoice_item_counts[invoice_item_counts > 1].index.tolist()

        if not multi_item_invoices:
            print("分析发现：所有账单都只包含一个‘直落’下单项。")
        else:
            print(f"分析发现：有 {len(multi_item_invoices)} 张账单包含多个‘直落’下单项，详情如下：")
            # 筛选出这些账单的详细信息并打印
            duplicate_details = df[df['InvNo'].isin(multi_item_invoices)].sort_values('InvNo')
            print(duplicate_details.to_string(index=False))

        # 4. 精确统计唯一的账单批次数
        unique_invoice_count = df['InvNo'].nunique()
        print("\n--- 步骤B: 精确统计直落账单批次 (已去重) ---")
        print(f"之前的统计方式（计算所有下单项）结果为: {len(df)} 次")
        print(f"修正后的统计方式（计算唯一账单数）结果为: {unique_invoice_count} 次")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    analyze_duplicates_and_count_unique()
