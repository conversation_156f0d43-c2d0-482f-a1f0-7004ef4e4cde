
import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_DATE = '20250724'
SHOP_ID = 11

# --- Query Definition ---
# This query finds all invoices with '直落' in the item name and then classifies them
# into Day, Evening, or Other based on the time logic from the V3 procedure.
SQL_ANALYZE_ITEM_LOGIC_BY_TIME = f"""
WITH DirectFallInvoices AS (
    -- Step 1: Find all unique invoices that contain a '直落' item.
    SELECT DISTINCT ti.invno
    FROM rmcloseinfo ti
    JOIN FdCashBak fdc ON ti.invno = fdc.InvNo COLLATE Chinese_PRC_CI_AS
    WHERE ti.WorkDate = '{TARGET_DATE}' 
      AND ti.ShopId = {SHOP_ID}
      AND fdc.FdCName LIKE N'%直落%'
      AND fdc.ShopId = {SHOP_ID}
)
-- Step 2: Join back to get the start time (Beg_Key) for each invoice and classify.
SELECT 
    -- DayTimeDropInBatch (BegTime < 17:00)
    COUNT(DISTINCT CASE WHEN ti_beg.BegTime < 1700 THEN rci.InvNo END) AS DayTimeDirectFall_ItemName,
    
    -- NightTimeDropInBatch (17:00 <= BegTime < 20:00)
    COUNT(DISTINCT CASE WHEN ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN rci.InvNo END) AS EveningDirectFall_ItemName,

    -- Other Time (Neither of the above, e.g., after 20:00)
    COUNT(DISTINCT CASE WHEN ti_beg.BegTime >= 2000 OR ti_beg.BegTime IS NULL THEN rci.InvNo END) AS OtherTimeDirectFall_ItemName

FROM DirectFallInvoices dfi
JOIN dbo.RmCloseInfo AS rci ON dfi.invno = rci.InvNo
LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rci.Shopid = sti_beg.Shopid AND rci.Beg_Key = sti_beg.TimeNo
LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
WHERE rci.WorkDate = '{TARGET_DATE}' AND rci.Shopid = {SHOP_ID};
"""

# --- Main Execution Logic ---
def analyze_item_logic_by_time():
    """
    Connects to the database and runs the analysis query.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            print(f"--- Analyzing Item-Based Direct Fall by Time for {TARGET_DATE} ---")
            
            df = pd.read_sql(SQL_ANALYZE_ITEM_LOGIC_BY_TIME, conn)
            
            if df.empty:
                print("Query returned no data.")
            else:
                day_count = df.iloc[0]['DayTimeDirectFall_ItemName']
                evening_count = df.iloc[0]['EveningDirectFall_ItemName']
                other_count = df.iloc[0]['OtherTimeDirectFall_ItemName']
                total_count = day_count + evening_count + other_count

                print(f"  - Day-time (before 17:00): {day_count}")
                print(f"  - Evening (17:00 - 20:00): {evening_count}")
                print(f"  - Other Times (after 20:00): {other_count}")
                print("  ---------------------")
                print(f"  - TOTAL by Item Name: {total_count}")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"DATABASE ERROR: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    analyze_item_logic_by_time()
