
import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 要分析的目标数据 ---
TARGET_SHOP_ID = 11
TARGET_WORK_DATE = '20250723' # 已修正为正确的 YYYYMMDD 格式

def analyze_missing_data():
    """
    查询并分析指定门店和日期的数据，找出缺失值。
    V3: 修正了 WorkDate 的查询格式为 YYYYMMDD
    """
    cnxn = None
    try:
        # 1. 构建SQL查询语句 (已修正表名和日期格式)
        sql_query = """
        SELECT * 
        FROM dbo.RmCloseInfo
        WHERE 
            ShopId = ? AND WorkDate = ?;
        """

        # 2. 连接数据库并执行查询
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"正在连接到数据库: {DATABASE}...")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")
        
        print(f"正在查询 dbo.RmCloseInfo 中 ShopID={TARGET_SHOP_ID} 在 WorkDate='{TARGET_WORK_DATE}' 的数据...")
        df = pd.read_sql_query(sql_query, cnxn, params=[TARGET_SHOP_ID, TARGET_WORK_DATE])
        
        if df.empty:
            print(f"\n--- 未找到数据 ---")
            print(f"在 dbo.RmCloseInfo 表中，依然没有找到 ShopID={TARGET_SHOP_ID} 且 WorkDate='{TARGET_WORK_DATE}' 的任何记录。请确认数据已存在。")
            return

        total_records = len(df)
        print(f"查询完成，共找到 {total_records} 条记录。")

        # 3. 分析缺失值
        print("\n--- 缺失值分析报告 ---")
        missing_values = df.isnull().sum()
        
        analysis_report = pd.DataFrame({
            '列名': df.columns,
            '数据类型': df.dtypes,
            '总记录数': total_records,
            '缺失数量': missing_values,
            '缺失率 (%)': (missing_values / total_records * 100).round(2)
        })
        
        missing_report = analysis_report[analysis_report['缺失数量'] > 0].sort_values(by='缺失数量', ascending=False).reset_index(drop=True)

        if missing_report.empty:
            print("恭喜！在查询到的数据中，所有列都没有缺失值。")
        else:
            print(f"在 {total_records} 条记录中，发现以下列存在缺失值：")
            print(missing_report.to_string())

    except Exception as e:
        print(f"\n--- 发生未知错误！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    analyze_missing_data()
