#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 usp_GenerateSimplifiedDailyReport_V7_Final 存储过程和 FullDailyReport_NightDetails 表
目标：优化数据插入逻辑，只保存必要的字段
"""

import pyodbc
import pandas as pd
import json
from datetime import datetime

class NightDetailsAnalyzer:
    def __init__(self):
        self.connection = None
        
    def connect_database(self):
        """连接到数据库"""
        try:
            conn_str = f"""
            DRIVER={{ODBC Driver 17 for SQL Server}};
            SERVER=192.168.2.5;
            DATABASE=operatedata;
            UID=sa;
            PWD=Musicbox123;
            TrustServerCertificate=yes;
            """
            
            self.connection = pyodbc.connect(conn_str)
            print("✅ 数据库连接成功")
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {str(e)}")
            return False
    
    def get_table_structure(self, table_name):
        """获取表结构"""
        try:
            query = """
            SELECT 
                c.COLUMN_NAME,
                c.DATA_TYPE,
                c.CHARACTER_MAXIMUM_LENGTH,
                c.NUMERIC_PRECISION,
                c.NUMERIC_SCALE,
                c.IS_NULLABLE,
                c.COLUMN_DEFAULT,
                CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END as IS_PRIMARY_KEY
            FROM INFORMATION_SCHEMA.COLUMNS c
            LEFT JOIN (
                SELECT ku.COLUMN_NAME
                FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
                    ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
                AND tc.TABLE_NAME = ?
            ) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
            WHERE c.TABLE_NAME = ?
            ORDER BY c.ORDINAL_POSITION
            """
            
            cursor = self.connection.cursor()
            cursor.execute(query, table_name, table_name)
            columns = cursor.fetchall()
            
            table_info = []
            for col in columns:
                table_info.append({
                    'column_name': col[0],
                    'data_type': col[1],
                    'max_length': col[2],
                    'precision': col[3],
                    'scale': col[4],
                    'is_nullable': col[5],
                    'default_value': col[6],
                    'is_primary_key': col[7]
                })
            
            return table_info
            
        except Exception as e:
            print(f"❌ 获取表结构失败: {str(e)}")
            return []
    
    def get_procedure_definition(self, proc_name):
        """获取存储过程定义"""
        try:
            query = """
            SELECT 
                p.name as procedure_name,
                p.create_date,
                p.modify_date,
                m.definition
            FROM sys.procedures p
            INNER JOIN sys.sql_modules m ON p.object_id = m.object_id
            WHERE p.name = ?
            """
            
            cursor = self.connection.cursor()
            cursor.execute(query, proc_name)
            result = cursor.fetchone()
            
            if result:
                return {
                    'name': result[0],
                    'create_date': result[1],
                    'modify_date': result[2],
                    'definition': result[3]
                }
            else:
                print(f"❌ 未找到存储过程: {proc_name}")
                return None
                
        except Exception as e:
            print(f"❌ 获取存储过程定义失败: {str(e)}")
            return None
    
    def analyze_procedure_output_structure(self, definition):
        """分析存储过程的输出结构"""
        if not definition:
            return []
        
        # 查找SELECT语句中的字段
        lines = definition.split('\n')
        output_fields = []
        in_final_select = False
        
        for i, line in enumerate(lines):
            line_clean = line.strip()
            line_upper = line_clean.upper()
            
            # 查找最终的SELECT语句（通常在存储过程末尾）
            if 'SELECT' in line_upper and ('FROM' in line_upper or i < len(lines) - 5):
                # 这可能是最终的输出SELECT
                in_final_select = True
                continue
            
            if in_final_select and line_clean:
                if line_clean.startswith('FROM') or line_clean.startswith('WHERE') or line_clean.startswith('ORDER'):
                    break
                
                # 提取字段名
                if ',' in line_clean or line_clean.endswith(','):
                    field = line_clean.replace(',', '').strip()
                    if field and not field.startswith('--'):
                        output_fields.append(field)
        
        return output_fields
    
    def get_sample_data_from_table(self, table_name, limit=5):
        """获取表的样本数据"""
        try:
            query = f"SELECT TOP {limit} * FROM {table_name} ORDER BY ReportID DESC"
            cursor = self.connection.cursor()
            cursor.execute(query)
            
            # 获取列名
            columns = [column[0] for column in cursor.description]
            
            # 获取数据
            rows = cursor.fetchall()
            
            sample_data = []
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    row_dict[columns[i]] = value
                sample_data.append(row_dict)
            
            return sample_data, columns
            
        except Exception as e:
            print(f"❌ 获取样本数据失败: {str(e)}")
            return [], []
    
    def analyze_data_usage_patterns(self, table_name):
        """分析数据使用模式"""
        try:
            # 分析每个字段的非空率和数据分布
            query = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN FreeMeal_KPlus IS NOT NULL AND FreeMeal_KPlus > 0 THEN 1 END) as FreeMeal_KPlus_used,
                COUNT(CASE WHEN FreeMeal_Special IS NOT NULL AND FreeMeal_Special > 0 THEN 1 END) as FreeMeal_Special_used,
                COUNT(CASE WHEN FreeMeal_Meituan IS NOT NULL AND FreeMeal_Meituan > 0 THEN 1 END) as FreeMeal_Meituan_used,
                COUNT(CASE WHEN FreeMeal_Douyin IS NOT NULL AND FreeMeal_Douyin > 0 THEN 1 END) as FreeMeal_Douyin_used,
                COUNT(CASE WHEN FreeMeal_BatchCount IS NOT NULL AND FreeMeal_BatchCount > 0 THEN 1 END) as FreeMeal_BatchCount_used,
                COUNT(CASE WHEN FreeMeal_Revenue IS NOT NULL AND FreeMeal_Revenue > 0 THEN 1 END) as FreeMeal_Revenue_used,
                COUNT(CASE WHEN Buyout_BatchCount IS NOT NULL AND Buyout_BatchCount > 0 THEN 1 END) as Buyout_BatchCount_used,
                COUNT(CASE WHEN Buyout_Revenue IS NOT NULL AND Buyout_Revenue > 0 THEN 1 END) as Buyout_Revenue_used,
                COUNT(CASE WHEN Changyin_BatchCount IS NOT NULL AND Changyin_BatchCount > 0 THEN 1 END) as Changyin_BatchCount_used,
                COUNT(CASE WHEN Changyin_Revenue IS NOT NULL AND Changyin_Revenue > 0 THEN 1 END) as Changyin_Revenue_used,
                COUNT(CASE WHEN FreeConsumption_BatchCount IS NOT NULL AND FreeConsumption_BatchCount > 0 THEN 1 END) as FreeConsumption_BatchCount_used,
                COUNT(CASE WHEN NonPackage_Special IS NOT NULL AND NonPackage_Special > 0 THEN 1 END) as NonPackage_Special_used,
                COUNT(CASE WHEN NonPackage_Meituan IS NOT NULL AND NonPackage_Meituan > 0 THEN 1 END) as NonPackage_Meituan_used,
                COUNT(CASE WHEN NonPackage_Douyin IS NOT NULL AND NonPackage_Douyin > 0 THEN 1 END) as NonPackage_Douyin_used,
                COUNT(CASE WHEN NonPackage_Others IS NOT NULL AND NonPackage_Others > 0 THEN 1 END) as NonPackage_Others_used,
                COUNT(CASE WHEN DiscountFree_BatchCount IS NOT NULL AND DiscountFree_BatchCount > 0 THEN 1 END) as DiscountFree_BatchCount_used,
                COUNT(CASE WHEN DiscountFree_Revenue IS NOT NULL AND DiscountFree_Revenue > 0 THEN 1 END) as DiscountFree_Revenue_used
            FROM {table_name}
            """
            
            cursor = self.connection.cursor()
            cursor.execute(query)
            result = cursor.fetchone()
            
            if result:
                usage_stats = {}
                columns = [column[0] for column in cursor.description]
                for i, value in enumerate(result):
                    usage_stats[columns[i]] = value
                return usage_stats
            
            return {}
            
        except Exception as e:
            print(f"❌ 分析数据使用模式失败: {str(e)}")
            return {}
    
    def generate_optimization_recommendations(self, table_structure, usage_stats, proc_definition):
        """生成优化建议"""
        recommendations = {
            'unused_fields': [],
            'rarely_used_fields': [],
            'essential_fields': [],
            'optimization_strategies': []
        }
        
        total_records = usage_stats.get('total_records', 0)
        
        if total_records == 0:
            recommendations['optimization_strategies'].append("表中没有数据，无法分析使用模式")
            return recommendations
        
        # 分析字段使用率
        for field_name, usage_count in usage_stats.items():
            if field_name == 'total_records':
                continue
                
            usage_rate = (usage_count / total_records) * 100 if total_records > 0 else 0
            
            if usage_rate == 0:
                recommendations['unused_fields'].append({
                    'field': field_name,
                    'usage_rate': usage_rate,
                    'suggestion': '可以考虑从插入语句中移除'
                })
            elif usage_rate < 10:
                recommendations['rarely_used_fields'].append({
                    'field': field_name,
                    'usage_rate': usage_rate,
                    'suggestion': '使用率较低，可以考虑条件插入'
                })
            else:
                recommendations['essential_fields'].append({
                    'field': field_name,
                    'usage_rate': usage_rate,
                    'suggestion': '重要字段，建议保留'
                })
        
        # 生成优化策略
        if recommendations['unused_fields']:
            recommendations['optimization_strategies'].append(
                f"发现 {len(recommendations['unused_fields'])} 个未使用字段，可以从INSERT语句中移除"
            )
        
        if recommendations['rarely_used_fields']:
            recommendations['optimization_strategies'].append(
                f"发现 {len(recommendations['rarely_used_fields'])} 个低使用率字段，可以考虑条件插入"
            )
        
        recommendations['optimization_strategies'].append(
            "建议创建优化版本的存储过程，只插入有意义的数据"
        )
        
        return recommendations
    
    def print_analysis_report(self, table_structure, proc_info, usage_stats, recommendations):
        """打印分析报告"""
        print("=" * 80)
        print("FullDailyReport_NightDetails 表和存储过程优化分析")
        print("=" * 80)
        
        print(f"\n📊 表结构分析 (FullDailyReport_NightDetails):")
        print(f"   总字段数: {len(table_structure)}")
        
        # 按类型分组显示字段
        field_types = {}
        for field in table_structure:
            data_type = field['data_type']
            if data_type not in field_types:
                field_types[data_type] = []
            field_types[data_type].append(field['column_name'])
        
        for data_type, fields in field_types.items():
            print(f"   {data_type}: {len(fields)} 个字段")
            for field in fields[:5]:  # 只显示前5个
                print(f"      • {field}")
            if len(fields) > 5:
                print(f"      ... 还有 {len(fields) - 5} 个字段")
        
        print(f"\n🔧 存储过程信息:")
        if proc_info:
            print(f"   名称: {proc_info['name']}")
            print(f"   创建时间: {proc_info['create_date']}")
            print(f"   最后修改: {proc_info['modify_date']}")
        
        print(f"\n📈 数据使用统计:")
        total_records = usage_stats.get('total_records', 0)
        print(f"   总记录数: {total_records}")
        
        if total_records > 0:
            print(f"\n   字段使用率分析:")
            for field_name, usage_count in usage_stats.items():
                if field_name != 'total_records':
                    usage_rate = (usage_count / total_records) * 100
                    status = "🔴" if usage_rate == 0 else "🟡" if usage_rate < 10 else "🟢"
                    print(f"   {status} {field_name.replace('_used', '')}: {usage_rate:.1f}% ({usage_count}/{total_records})")
        
        print(f"\n💡 优化建议:")
        
        if recommendations['unused_fields']:
            print(f"\n   🔴 未使用字段 ({len(recommendations['unused_fields'])} 个):")
            for field in recommendations['unused_fields']:
                print(f"      • {field['field'].replace('_used', '')}: {field['suggestion']}")
        
        if recommendations['rarely_used_fields']:
            print(f"\n   🟡 低使用率字段 ({len(recommendations['rarely_used_fields'])} 个):")
            for field in recommendations['rarely_used_fields']:
                print(f"      • {field['field'].replace('_used', '')}: {field['usage_rate']:.1f}% - {field['suggestion']}")
        
        if recommendations['essential_fields']:
            print(f"\n   🟢 重要字段 ({len(recommendations['essential_fields'])} 个):")
            for field in recommendations['essential_fields'][:5]:  # 只显示前5个
                print(f"      • {field['field'].replace('_used', '')}: {field['usage_rate']:.1f}%")
            if len(recommendations['essential_fields']) > 5:
                print(f"      ... 还有 {len(recommendations['essential_fields']) - 5} 个重要字段")
        
        print(f"\n🚀 优化策略:")
        for i, strategy in enumerate(recommendations['optimization_strategies'], 1):
            print(f"   {i}. {strategy}")
    
    def generate_optimized_insert_statement(self, recommendations, table_structure):
        """生成优化的插入语句"""
        # 获取需要保留的字段
        essential_fields = [field['field'].replace('_used', '') for field in recommendations['essential_fields']]
        
        # 添加必要的系统字段
        system_fields = ['ReportID']
        
        # 组合所有需要的字段
        all_needed_fields = system_fields + essential_fields
        
        # 生成INSERT语句
        insert_statement = f"""
-- 优化后的插入语句（只插入有意义的数据）
INSERT INTO dbo.FullDailyReport_NightDetails (
    {', '.join(all_needed_fields)}
) SELECT @ReportID, {', '.join(essential_fields)} FROM #TempNightDetails;
"""
        
        return insert_statement, all_needed_fields
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("🔍 开始分析 FullDailyReport_NightDetails 表和相关存储过程...")
        
        if not self.connect_database():
            return
        
        try:
            # 1. 分析表结构
            print("\n📊 分析表结构...")
            table_structure = self.get_table_structure('FullDailyReport_NightDetails')
            
            # 2. 获取存储过程定义
            print("🔧 获取存储过程定义...")
            proc_info = self.get_procedure_definition('usp_GenerateSimplifiedDailyReport_V7_Final')
            
            # 3. 分析数据使用模式
            print("📈 分析数据使用模式...")
            usage_stats = self.analyze_data_usage_patterns('FullDailyReport_NightDetails')
            
            # 4. 生成优化建议
            print("💡 生成优化建议...")
            recommendations = self.generate_optimization_recommendations(
                table_structure, usage_stats, proc_info
            )
            
            # 5. 打印分析报告
            self.print_analysis_report(table_structure, proc_info, usage_stats, recommendations)
            
            # 6. 生成优化的插入语句
            if recommendations['essential_fields']:
                print(f"\n📝 生成优化的插入语句:")
                optimized_insert, needed_fields = self.generate_optimized_insert_statement(
                    recommendations, table_structure
                )
                print(optimized_insert)
            
            # 7. 保存完整的存储过程代码
            if proc_info:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                proc_file = f"usp_GenerateSimplifiedDailyReport_V7_Final_{timestamp}.sql"
                
                with open(proc_file, 'w', encoding='utf-8') as f:
                    f.write(proc_info['definition'])
                
                print(f"\n✅ 存储过程代码已保存到: {proc_file}")
            
            # 8. 保存分析结果
            analysis_result = {
                'table_structure': table_structure,
                'procedure_info': proc_info,
                'usage_statistics': usage_stats,
                'recommendations': recommendations,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            result_file = f"night_details_optimization_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 分析结果已保存到: {result_file}")
            
        except Exception as e:
            print(f"❌ 分析过程中发生错误: {str(e)}")
        
        finally:
            if self.connection:
                self.connection.close()
                print("✅ 数据库连接已关闭")

def main():
    analyzer = NightDetailsAnalyzer()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
