
import pyodbc

def get_procedure_definition(server, database, username, password, procedure_name):
    """Retrieves the source code for a specific stored procedure."""
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
    
    try:
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()

            print(f"--- Analyzing source code for: {procedure_name} ---")
            
            get_def_query = """
            SELECT m.definition
            FROM sys.sql_modules m
            INNER JOIN sys.objects o ON m.object_id = o.object_id
            WHERE o.name = ? AND o.type = 'P';
            """
            cursor.execute(get_def_query, procedure_name)
            definition = cursor.fetchone()

            if definition:
                print(definition[0])
            else:
                print(f"Could not retrieve definition for '{procedure_name}'.")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database connection error: {sqlstate}")
        print(ex)

if __name__ == '__main__':
    # First, analyze StaffReport
    get_procedure_definition(
        server='192.168.2.5',
        database='operatedata',
        username='sa',
        password='Musicbox123',
        procedure_name='StaffReport'
    )
    # Then, analyze Test_Today_RmCloseInfo_Day
    get_procedure_definition(
        server='192.168.2.5',
        database='operatedata',
        username='sa',
        password='Musicbox123',
        procedure_name='Test_Today_RmCloseInfo_Day'
    )
