import pyodbc

# Database connection parameters
server = '193.112.2.229'
database = 'dbfood'
username = 'sa'
password = 'Musicbox@123'

# Connection string (single line to avoid syntax issues)
conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;Connection Timeout=10"

tables_to_analyze = ['ROOM', 'wxpayinfo']
connection = None

try:
    print(f"正在连接到数据库 {database}...")
    connection = pyodbc.connect(conn_str)
    cursor = connection.cursor()
    print("连接成功。\n")

    for table_name in tables_to_analyze:
        print(f"--- 正在分析表结构: {table_name} ---")

        sql_query = """
        SELECT
            COLUMN_NAME,
            DATA_TYPE,
            CHARACTER_MAXIMUM_LENGTH,
            NUMERIC_PRECISION,
            NUMERIC_SCALE,
            IS_NULLABLE,
            COLUMN_DEFAULT
        FROM
            INFORMATION_SCHEMA.COLUMNS
        WHERE
            TABLE_NAME = ?
        ORDER BY
            ORDINAL_POSITION;
        """

        cursor.execute(sql_query, table_name)
        rows = cursor.fetchall()

        if not rows:
            print(f"表 '{table_name}' 不存在或没有列信息。\n")
            continue

        # 打印表头
        print(f"{ '列名':<25} {'数据类型':<15} {'长度/精度':<12} {'可否为空':<10} {'默认值'}")
        print("-" * 80)

        # 打印每一列的信息
        for row in rows:
            col_name = row.COLUMN_NAME
            data_type = row.DATA_TYPE
            
            length_precision = 'N/A'
            if row.CHARACTER_MAXIMUM_LENGTH is not None:
                length_precision = str(row.CHARACTER_MAXIMUM_LENGTH)
            elif row.NUMERIC_PRECISION is not None:
                length_precision = f"{row.NUMERIC_PRECISION},{row.NUMERIC_SCALE}"

            is_nullable = row.IS_NULLABLE
            default_val = row.COLUMN_DEFAULT if row.COLUMN_DEFAULT is not None else '无'
            
            print(f"{col_name:<25} {data_type:<15} {length_precision:<12} {is_nullable:<10} {default_val}")

        print("\n" + "="*80 + "\n")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"数据库错误: {ex}")
except Exception as e:
    print(f"发生未知错误: {e}")
finally:
    if connection:
        connection.close()
        print("数据库连接已关闭。")
