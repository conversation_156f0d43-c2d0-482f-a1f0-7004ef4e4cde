#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库存储过程深度分析脚本
目标：分析 usp_RunUnifiedDailyReportJob 存储过程
"""

import pyodbc
import pandas as pd
import json
from datetime import datetime
import re

class DatabaseAnalyzer:
    def __init__(self, server, database, username, password):
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.connection = None
        
    def connect(self):
        """连接到SQL Server数据库"""
        try:
            # 构建连接字符串
            conn_str = f"""
            DRIVER={{ODBC Driver 17 for SQL Server}};
            SERVER={self.server};
            DATABASE={self.database};
            UID={self.username};
            PWD={self.password};
            TrustServerCertificate=yes;
            """
            
            self.connection = pyodbc.connect(conn_str)
            print(f"✅ 成功连接到数据库: {self.server}/{self.database}")
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {str(e)}")
            # 尝试备用驱动
            try:
                conn_str = f"""
                DRIVER={{SQL Server}};
                SERVER={self.server};
                DATABASE={self.database};
                UID={self.username};
                PWD={self.password};
                """
                self.connection = pyodbc.connect(conn_str)
                print(f"✅ 使用备用驱动成功连接到数据库: {self.server}/{self.database}")
                return True
            except Exception as e2:
                print(f"❌ 备用驱动也连接失败: {str(e2)}")
                return False
    
    def get_procedure_definition(self, proc_name):
        """获取存储过程定义"""
        try:
            query = """
            SELECT 
                ROUTINE_NAME,
                ROUTINE_DEFINITION,
                CREATED,
                LAST_ALTERED
            FROM INFORMATION_SCHEMA.ROUTINES 
            WHERE ROUTINE_TYPE = 'PROCEDURE' 
            AND ROUTINE_NAME = ?
            """
            
            cursor = self.connection.cursor()
            cursor.execute(query, proc_name)
            result = cursor.fetchone()
            
            if result:
                return {
                    'name': result[0],
                    'definition': result[1],
                    'created': result[2],
                    'last_altered': result[3]
                }
            else:
                # 尝试从sys.sql_modules获取
                query2 = """
                SELECT 
                    p.name,
                    m.definition,
                    p.create_date,
                    p.modify_date
                FROM sys.procedures p
                INNER JOIN sys.sql_modules m ON p.object_id = m.object_id
                WHERE p.name = ?
                """
                cursor.execute(query2, proc_name)
                result = cursor.fetchone()
                
                if result:
                    return {
                        'name': result[0],
                        'definition': result[1],
                        'created': result[2],
                        'last_altered': result[3]
                    }
                else:
                    print(f"❌ 未找到存储过程: {proc_name}")
                    return None
                    
        except Exception as e:
            print(f"❌ 获取存储过程定义失败: {str(e)}")
            return None
    
    def analyze_procedure_structure(self, definition):
        """分析存储过程结构"""
        if not definition:
            return {}
            
        analysis = {
            'parameters': [],
            'variables': [],
            'tables_referenced': [],
            'procedures_called': [],
            'functions_used': [],
            'control_structures': [],
            'error_handling': [],
            'transactions': [],
            'temp_tables': []
        }
        
        lines = definition.split('\n')
        
        for line in lines:
            line = line.strip()
            line_upper = line.upper()
            
            # 参数分析
            if line_upper.startswith('@') and ('=' in line or 'AS' in line_upper):
                analysis['parameters'].append(line)
            
            # 变量声明
            if line_upper.startswith('DECLARE @'):
                analysis['variables'].append(line)
            
            # 表引用
            table_patterns = [
                r'FROM\s+(\w+)',
                r'JOIN\s+(\w+)',
                r'UPDATE\s+(\w+)',
                r'INSERT\s+INTO\s+(\w+)',
                r'DELETE\s+FROM\s+(\w+)'
            ]
            for pattern in table_patterns:
                matches = re.findall(pattern, line_upper)
                for match in matches:
                    if match not in analysis['tables_referenced']:
                        analysis['tables_referenced'].append(match)
            
            # 存储过程调用
            if 'EXEC' in line_upper or 'EXECUTE' in line_upper:
                proc_match = re.search(r'EXEC(?:UTE)?\s+(\w+)', line_upper)
                if proc_match:
                    analysis['procedures_called'].append(proc_match.group(1))
            
            # 控制结构
            control_keywords = ['IF', 'WHILE', 'FOR', 'CASE', 'BEGIN', 'END']
            for keyword in control_keywords:
                if keyword in line_upper:
                    analysis['control_structures'].append(f"{keyword}: {line}")
            
            # 错误处理
            if any(keyword in line_upper for keyword in ['TRY', 'CATCH', 'RAISERROR', 'THROW']):
                analysis['error_handling'].append(line)
            
            # 事务处理
            if any(keyword in line_upper for keyword in ['BEGIN TRAN', 'COMMIT', 'ROLLBACK']):
                analysis['transactions'].append(line)
            
            # 临时表
            if '#' in line and any(keyword in line_upper for keyword in ['CREATE', 'DROP', 'INSERT', 'SELECT']):
                analysis['temp_tables'].append(line)
        
        return analysis
    
    def get_procedure_dependencies(self, proc_name):
        """获取存储过程依赖关系"""
        try:
            query = """
            SELECT 
                d.referencing_entity_name,
                d.referenced_entity_name,
                d.referenced_schema_name,
                d.referenced_class_desc
            FROM sys.sql_expression_dependencies d
            INNER JOIN sys.objects o ON d.referencing_id = o.object_id
            WHERE o.name = ?
            """
            
            cursor = self.connection.cursor()
            cursor.execute(query, proc_name)
            dependencies = cursor.fetchall()
            
            return [{
                'referencing': dep[0],
                'referenced': dep[1],
                'schema': dep[2],
                'type': dep[3]
            } for dep in dependencies]
            
        except Exception as e:
            print(f"❌ 获取依赖关系失败: {str(e)}")
            return []
    
    def get_procedure_permissions(self, proc_name):
        """获取存储过程权限信息"""
        try:
            query = """
            SELECT 
                p.permission_name,
                p.state_desc,
                pr.name as principal_name,
                pr.type_desc as principal_type
            FROM sys.database_permissions p
            INNER JOIN sys.objects o ON p.major_id = o.object_id
            INNER JOIN sys.database_principals pr ON p.grantee_principal_id = pr.principal_id
            WHERE o.name = ?
            """
            
            cursor = self.connection.cursor()
            cursor.execute(query, proc_name)
            permissions = cursor.fetchall()
            
            return [{
                'permission': perm[0],
                'state': perm[1],
                'principal': perm[2],
                'principal_type': perm[3]
            } for perm in permissions]
            
        except Exception as e:
            print(f"❌ 获取权限信息失败: {str(e)}")
            return []
    
    def generate_analysis_report(self, proc_name):
        """生成完整的分析报告"""
        print(f"\n🔍 开始深度分析存储过程: {proc_name}")
        print("=" * 60)
        
        # 获取存储过程定义
        proc_info = self.get_procedure_definition(proc_name)
        if not proc_info:
            return None
        
        # 结构分析
        structure_analysis = self.analyze_procedure_structure(proc_info['definition'])
        
        # 依赖关系
        dependencies = self.get_procedure_dependencies(proc_name)
        
        # 权限信息
        permissions = self.get_procedure_permissions(proc_name)
        
        # 生成报告
        report = {
            'procedure_info': proc_info,
            'structure_analysis': structure_analysis,
            'dependencies': dependencies,
            'permissions': permissions,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        # 打印分析结果
        self.print_analysis_report(report)
        
        return report
    
    def print_analysis_report(self, report):
        """打印分析报告"""
        proc_info = report['procedure_info']
        structure = report['structure_analysis']
        
        print(f"\n📋 存储过程基本信息:")
        print(f"   名称: {proc_info['name']}")
        print(f"   创建时间: {proc_info['created']}")
        print(f"   最后修改: {proc_info['last_altered']}")
        
        print(f"\n📊 结构分析:")
        print(f"   参数数量: {len(structure['parameters'])}")
        print(f"   变量数量: {len(structure['variables'])}")
        print(f"   引用表数量: {len(structure['tables_referenced'])}")
        print(f"   调用存储过程数量: {len(structure['procedures_called'])}")
        
        if structure['parameters']:
            print(f"\n📝 参数列表:")
            for param in structure['parameters']:
                print(f"   {param}")
        
        if structure['tables_referenced']:
            print(f"\n🗃️ 引用的表:")
            for table in structure['tables_referenced']:
                print(f"   {table}")
        
        if structure['procedures_called']:
            print(f"\n🔗 调用的存储过程:")
            for proc in structure['procedures_called']:
                print(f"   {proc}")
        
        if structure['error_handling']:
            print(f"\n⚠️ 错误处理:")
            for error in structure['error_handling']:
                print(f"   {error}")
        
        if structure['transactions']:
            print(f"\n💾 事务处理:")
            for trans in structure['transactions']:
                print(f"   {trans}")
        
        if report['dependencies']:
            print(f"\n🔗 依赖关系:")
            for dep in report['dependencies']:
                print(f"   {dep['referenced']} ({dep['type']})")
        
        print(f"\n📄 完整存储过程代码:")
        print("-" * 60)
        print(proc_info['definition'])
        print("-" * 60)
    
    def save_report_to_file(self, report, filename):
        """保存分析报告到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            print(f"✅ 分析报告已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存报告失败: {str(e)}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("✅ 数据库连接已关闭")

def main():
    # 数据库连接参数
    server = "192.168.2.5"
    database = "operatedata"
    username = "sa"
    password = "Musicbox123"
    
    # 目标存储过程
    target_procedure = "usp_RunUnifiedDailyReportJob"
    
    # 创建分析器实例
    analyzer = DatabaseAnalyzer(server, database, username, password)
    
    try:
        # 连接数据库
        if not analyzer.connect():
            return
        
        # 生成分析报告
        report = analyzer.generate_analysis_report(target_procedure)
        
        if report:
            # 保存报告到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"usp_RunUnifiedDailyReportJob_analysis_{timestamp}.json"
            analyzer.save_report_to_file(report, filename)
            
            # 保存存储过程代码到单独文件
            code_filename = f"usp_RunUnifiedDailyReportJob_code_{timestamp}.sql"
            try:
                with open(code_filename, 'w', encoding='utf-8') as f:
                    f.write(report['procedure_info']['definition'])
                print(f"✅ 存储过程代码已保存到: {code_filename}")
            except Exception as e:
                print(f"❌ 保存代码文件失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {str(e)}")
    
    finally:
        # 关闭连接
        analyzer.close()

if __name__ == "__main__":
    main()
