#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KTV业务数据分析 - 实现时间段重叠检测和直落分析
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta
import json

class KTVBusinessAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def get_time_slots(self):
        """获取时间段配置"""
        try:
            conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            query = f"""
            SELECT st.*, t.TimeName, t.BegTime, t.EndTime
            FROM shoptimeinfo st
            LEFT JOIN timeinfo t ON st.timeno = t.timeno
            WHERE st.shopid = {self.shop_id}
            ORDER BY t.BegTime
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # 过滤有效的时间段
            df = df.dropna(subset=['BegTime', 'EndTime'])
            return df
            
        except Exception as e:
            print(f"获取时间段配置失败: {e}")
            return pd.DataFrame()
    
    def get_close_data(self, work_date):
        """获取结账数据"""
        try:
            conn = self.get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
            
            query = f"""
            SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
            FROM rmcloseinfo 
            WHERE shopid = {self.shop_id} AND WorkDate = '{work_date}'
            ORDER BY CloseDatetime
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            return df
            
        except Exception as e:
            print(f"获取结账数据失败: {e}")
            return pd.DataFrame()
    
    def parse_time_slot(self, beg_time, end_time, base_date):
        """解析时间段为datetime对象"""
        try:
            beg_time = int(beg_time)
            end_time = int(end_time)
            
            beg_hour = beg_time // 100
            beg_min = beg_time % 100
            end_hour = end_time // 100
            end_min = end_time % 100
            
            start_dt = base_date.replace(hour=beg_hour, minute=beg_min, second=0, microsecond=0)
            
            # 如果结束时间小于开始时间，说明跨天了
            if end_time < beg_time:
                end_dt = (base_date + timedelta(days=1)).replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
            else:
                end_dt = base_date.replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
            
            return start_dt, end_dt
            
        except Exception as e:
            print(f"解析时间段失败: {e}")
            return None, None
    
    def time_overlap(self, start1, end1, start2, end2):
        """检查两个时间段是否重叠"""
        return start1 < end2 and end1 > start2
    
    def identify_channel(self, vesa_name):
        """识别消费渠道"""
        if pd.isna(vesa_name) or vesa_name == '':
            return 'K+'
        
        vesa_str = str(vesa_name).lower()
        if '美团' in vesa_str or 'meituan' in vesa_str:
            return '美团'
        elif '抖音' in vesa_str or 'douyin' in vesa_str:
            return '抖音'
        elif '特权' in vesa_str:
            return '特权预约'
        else:
            return 'K+'
    
    def estimate_open_time(self, close_time, duration_hours=2):
        """估算开台时间（简化版本，实际应该从开台数据获取）"""
        return close_time - timedelta(hours=duration_hours)
    
    def analyze_business_data(self, work_date):
        """分析业务数据"""
        print(f"\n=== 分析营业日期: {work_date} ===")
        
        # 获取基础数据
        time_slots = self.get_time_slots()
        close_data = self.get_close_data(work_date)
        
        if time_slots.empty or close_data.empty:
            print("没有足够的数据进行分析")
            return {}
        
        print(f"时间段配置: {len(time_slots)} 个")
        print(f"结账记录: {len(close_data)} 条")
        
        # 添加渠道信息
        close_data['Channel'] = close_data['VesaName'].apply(self.identify_channel)
        
        # 估算开台时间
        close_data['EstimatedOpenTime'] = close_data['CloseDatetime'].apply(
            lambda x: self.estimate_open_time(pd.to_datetime(x))
        )
        
        # 基准日期
        base_date = datetime.strptime(work_date, '%Y%m%d')
        
        # 分析结果
        analysis_result = {
            'work_date': work_date,
            'shop_id': self.shop_id,
            'total_revenue': int(close_data['Tot'].sum()),
            'total_orders': len(close_data),
            'time_slot_analysis': {}
        }
        
        print(f"\n总营业额: ¥{analysis_result['total_revenue']:,}")
        print(f"总订单数: {analysis_result['total_orders']}")
        
        # 分析每个时间段
        print(f"\n=== 时间段重叠分析 ===")
        
        for _, slot in time_slots.iterrows():
            slot_start, slot_end = self.parse_time_slot(
                slot['BegTime'], slot['EndTime'], base_date
            )
            
            if slot_start is None or slot_end is None:
                continue
            
            slot_name = f"{int(slot['BegTime']):04d}-{int(slot['EndTime']):04d}"
            
            # 找到与此时间段重叠的订单
            overlapping_orders = []
            direct_fall_count = 0  # 直落数量
            
            for _, order in close_data.iterrows():
                estimated_open = order['EstimatedOpenTime']
                close_time = pd.to_datetime(order['CloseDatetime'])
                
                # 检查是否与时间段重叠
                if self.time_overlap(estimated_open, close_time, slot_start, slot_end):
                    overlapping_orders.append(order)
                    
                    # 检查是否为直落（开台时间早于时间段开始）
                    if estimated_open < slot_start:
                        direct_fall_count += 1
            
            # 统计该时间段的数据
            if overlapping_orders:
                slot_df = pd.DataFrame(overlapping_orders)
                
                # 按渠道统计
                channel_stats = {}
                for channel in slot_df['Channel'].unique():
                    channel_data = slot_df[slot_df['Channel'] == channel]
                    channel_stats[channel] = {
                        'count': len(channel_data),
                        'revenue': int(channel_data['Tot'].sum())
                    }
                
                slot_analysis = {
                    'slot_name': slot.get('TimeName', slot_name),
                    'time_range': f"{slot_start.strftime('%H:%M')}-{slot_end.strftime('%H:%M')}",
                    'total_orders': len(slot_df),
                    'total_revenue': int(slot_df['Tot'].sum()),
                    'direct_fall_count': direct_fall_count,
                    'channels': channel_stats
                }
                
                analysis_result['time_slot_analysis'][slot_name] = slot_analysis
                
                print(f"\n【{slot_analysis['slot_name']} ({slot_analysis['time_range']})】")
                print(f"  订单数: {slot_analysis['total_orders']}")
                print(f"  营业额: ¥{slot_analysis['total_revenue']:,}")
                print(f"  直落数: {slot_analysis['direct_fall_count']}")
                print(f"  渠道分布:")
                for channel, stats in channel_stats.items():
                    print(f"    {channel}: {stats['count']}单, ¥{stats['revenue']:,}")
        
        return analysis_result
    
    def generate_report_table(self, analysis_result):
        """生成类似图片的报表格式"""
        if not analysis_result:
            return "无数据"
        
        print(f"\n=== 营业报表 ({analysis_result['work_date']}) ===")
        print(f"门店: 名堂店 (ID: {analysis_result['shop_id']})")
        print(f"总营业额: ¥{analysis_result['total_revenue']:,}")
        print(f"总订单数: {analysis_result['total_orders']}")
        
        # 表格头部
        print("\n" + "="*80)
        print(f"{'时间段':<15} {'订单数':<8} {'营业额':<12} {'直落数':<8} {'K+':<8} {'美团':<8} {'抖音':<8} {'特权':<8}")
        print("="*80)
        
        # 表格内容
        for slot_key, slot_data in analysis_result['time_slot_analysis'].items():
            channels = slot_data['channels']
            
            k_plus = channels.get('K+', {})
            meituan = channels.get('美团', {})
            douyin = channels.get('抖音', {})
            privilege = channels.get('特权预约', {})
            
            print(f"{slot_data['time_range']:<15} "
                  f"{slot_data['total_orders']:<8} "
                  f"¥{slot_data['total_revenue']:<11,} "
                  f"{slot_data['direct_fall_count']:<8} "
                  f"{k_plus.get('count', 0):<8} "
                  f"{meituan.get('count', 0):<8} "
                  f"{douyin.get('count', 0):<8} "
                  f"{privilege.get('count', 0):<8}")
        
        print("="*80)

def main():
    analyzer = KTVBusinessAnalyzer()
    
    # 分析指定日期
    dates_to_analyze = ['20250717', '20250716', '20250715']
    
    for date in dates_to_analyze:
        try:
            result = analyzer.analyze_business_data(date)
            
            if result:
                analyzer.generate_report_table(result)
                
                # 保存结果
                with open(f'business_analysis_{date}.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2, default=str)
                
                print(f"\n分析结果已保存到: business_analysis_{date}.json")
            
            print("\n" + "="*100 + "\n")
            
        except Exception as e:
            print(f"分析日期 {date} 时出错: {e}")

if __name__ == "__main__":
    main()
