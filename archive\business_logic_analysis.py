#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
usp_RunUnifiedDailyReportJob 存储过程业务逻辑深度分析
"""

import pyodbc
import json
from datetime import datetime

def analyze_called_procedures():
    """分析被调用的存储过程"""
    procedures = {
        'usp_GenerateDayTimeReport_Simple_V3': {
            'purpose': '生成白天时段报表数据',
            'input_params': ['@ShopId', '@TargetDate'],
            'output_structure': [
                'WorkDate', 'ShopName', 'WeekdayName',
                'DayTimeRevenue', 'NightTimeRevenue', 'TotalRevenue',
                'DayTimeBatchCount', 'NightTimeBatchCount', 'TotalBatchCount',
                'TotalGuestCount', 'BuffetGuestCount',
                'DayTimeDropInBatch', 'NightTimeDropInBatch', 'TotalDirectFallGuests'
            ]
        },
        'usp_GenerateSimplifiedDailyReport_V7_Final': {
            'purpose': '生成夜间详细报表数据（包含折扣免费数据）',
            'input_params': ['@ShopId', '@BeginDate', '@EndDate'],
            'output_structure': [
                'ReportDate', 'ShopName', 'Weekday',
                'TotalRevenue', 'DayTimeRevenue', 'NightTimeRevenue',
                'TotalBatchCount', 'DayTimeBatchCount', 'NightTimeBatchCount',
                'FreeMeal_KPlus', 'FreeMeal_Special', 'FreeMeal_Meituan', 'FreeMeal_Douyin',
                'FreeMeal_BatchCount', 'FreeMeal_Revenue',
                'Buyout_BatchCount', 'Buyout_Revenue',
                'Changyin_BatchCount', 'Changyin_Revenue',
                'FreeConsumption_BatchCount',
                'NonPackage_Special', 'NonPackage_Meituan', 'NonPackage_Douyin', 'NonPackage_Others',
                'Night_Verify_BatchCount', 'Night_Verify_Revenue',
                'DiscountFree_BatchCount', 'DiscountFree_Revenue'
            ]
        },
        'usp_GetTimeSlotDetails_WithDirectFall': {
            'purpose': '生成时段详细数据（包含直落信息）',
            'input_params': ['@TargetDate', '@ShopId'],
            'output_structure': [
                'TimeSlotName', 'TimeSlotOrder', 'KPlus_Count', 'Special_Count',
                'Meituan_Count', 'Douyin_Count', 'RoomFee_Count', 'Subtotal_Count',
                'PreviousSlot_DirectFall'
            ]
        }
    }
    return procedures

def analyze_data_tables():
    """分析涉及的数据表"""
    tables = {
        'FullDailyReport_Header': {
            'purpose': '存储每日报表头部信息',
            'key_fields': ['ReportID', 'ReportDate', 'ShopID'],
            'operations': ['SELECT', 'INSERT', 'DELETE']
        },
        'FullDailyReport_NightDetails': {
            'purpose': '存储夜间详细数据',
            'key_fields': ['ReportID', 'FreeMeal_*', 'Buyout_*', 'DiscountFree_*'],
            'operations': ['INSERT', 'DELETE']
        },
        'FullDailyReport_TimeSlotDetails': {
            'purpose': '存储时段详细数据',
            'key_fields': ['ReportID', 'TimeSlotName', 'TimeSlotOrder'],
            'operations': ['INSERT', 'DELETE']
        },
        'JobExecutionLog': {
            'purpose': '记录作业执行日志',
            'key_fields': ['JobName', 'ReportDate', 'Status', 'Message'],
            'operations': ['INSERT']
        }
    }
    return tables

def analyze_business_flow():
    """分析业务流程"""
    flow_steps = [
        {
            'step': 0,
            'name': '数据清理',
            'description': '删除指定日期的现有报表数据',
            'details': [
                '根据ReportDate和ShopID查找现有ReportID',
                '如果存在，删除相关的时段详细数据',
                '删除相关的夜间详细数据',
                '删除头部数据'
            ],
            'risk_points': ['数据完整性', '并发访问']
        },
        {
            'step': 1,
            'name': '生成头部数据',
            'description': '调用白天报表存储过程生成基础数据',
            'details': [
                '创建临时表#TempHeader',
                '调用usp_GenerateDayTimeReport_Simple_V3获取数据',
                '插入到FullDailyReport_Header表',
                '获取新生成的ReportID'
            ],
            'risk_points': ['存储过程依赖', '数据一致性']
        },
        {
            'step': 2,
            'name': '生成夜间详细数据',
            'description': '调用夜间报表存储过程生成详细数据',
            'details': [
                '创建临时表#TempNightDetails（包含折扣免费字段）',
                '调用usp_GenerateSimplifiedDailyReport_V7_Final获取数据',
                '插入到FullDailyReport_NightDetails表'
            ],
            'risk_points': ['复杂数据结构', '新增字段兼容性']
        },
        {
            'step': 3,
            'name': '生成时段详细数据',
            'description': '调用时段详细存储过程生成时段数据',
            'details': [
                '创建临时表#TempTimeSlotDetails',
                '调用usp_GetTimeSlotDetails_WithDirectFall获取数据',
                '插入到FullDailyReport_TimeSlotDetails表'
            ],
            'risk_points': ['直落逻辑复杂性', '时段计算准确性']
        }
    ]
    return flow_steps

def analyze_error_handling():
    """分析错误处理机制"""
    error_handling = {
        'transaction_management': {
            'description': '使用事务确保数据一致性',
            'details': [
                'BEGIN TRANSACTION在开始时启动事务',
                'COMMIT TRANSACTION在成功时提交',
                'ROLLBACK TRANSACTION在错误时回滚'
            ]
        },
        'temp_table_cleanup': {
            'description': '确保临时表被正确清理',
            'details': [
                '正常流程中DROP临时表',
                '错误处理中检查并DROP临时表'
            ]
        },
        'logging_mechanism': {
            'description': '记录执行状态到日志表',
            'details': [
                '成功时记录Success状态和ReportID',
                '失败时记录Failure状态和错误详情',
                '使用RAISERROR重新抛出错误'
            ]
        }
    }
    return error_handling

def analyze_performance_considerations():
    """分析性能考虑因素"""
    performance = {
        'strengths': [
            '使用临时表缓存中间结果',
            '明确的事务边界',
            '及时清理临时表',
            '使用SCOPE_IDENTITY()获取新插入的ID'
        ],
        'potential_issues': [
            '三个存储过程的串行执行可能较慢',
            '大量数据时临时表可能占用较多内存',
            '没有明显的索引优化策略',
            '事务时间可能较长'
        ],
        'optimization_suggestions': [
            '考虑并行执行部分独立的数据生成步骤',
            '为关键表添加适当的索引',
            '监控事务执行时间',
            '考虑分批处理大量数据'
        ]
    }
    return performance

def generate_comprehensive_business_analysis():
    """生成综合业务分析报告"""
    
    print("=" * 80)
    print("usp_RunUnifiedDailyReportJob 存储过程深度业务分析")
    print("=" * 80)
    
    print("\n🎯 存储过程概述:")
    print("   这是一个统一的日报生成作业存储过程，负责整合多个数据源")
    print("   生成完整的KTV日常营业报表，包括头部汇总、夜间详情和时段详情")
    
    # 分析调用的存储过程
    procedures = analyze_called_procedures()
    print(f"\n🔗 调用的存储过程分析 ({len(procedures)} 个):")
    for proc_name, info in procedures.items():
        print(f"\n   📋 {proc_name}:")
        print(f"      用途: {info['purpose']}")
        print(f"      输入参数: {', '.join(info['input_params'])}")
        print(f"      输出字段数: {len(info['output_structure'])}")
    
    # 分析数据表
    tables = analyze_data_tables()
    print(f"\n🗃️ 涉及的数据表分析 ({len(tables)} 个):")
    for table_name, info in tables.items():
        print(f"\n   📊 {table_name}:")
        print(f"      用途: {info['purpose']}")
        print(f"      关键字段: {', '.join(info['key_fields'])}")
        print(f"      操作类型: {', '.join(info['operations'])}")
    
    # 分析业务流程
    flow_steps = analyze_business_flow()
    print(f"\n🔄 业务流程分析 ({len(flow_steps)} 个步骤):")
    for step in flow_steps:
        print(f"\n   📌 步骤 {step['step']}: {step['name']}")
        print(f"      描述: {step['description']}")
        print(f"      详细步骤:")
        for detail in step['details']:
            print(f"        • {detail}")
        print(f"      风险点: {', '.join(step['risk_points'])}")
    
    # 分析错误处理
    error_handling = analyze_error_handling()
    print(f"\n⚠️ 错误处理机制分析:")
    for mechanism, info in error_handling.items():
        print(f"\n   🛡️ {mechanism.replace('_', ' ').title()}:")
        print(f"      描述: {info['description']}")
        print(f"      详细:")
        for detail in info['details']:
            print(f"        • {detail}")
    
    # 分析性能考虑
    performance = analyze_performance_considerations()
    print(f"\n⚡ 性能分析:")
    print(f"\n   ✅ 优势:")
    for strength in performance['strengths']:
        print(f"      • {strength}")
    
    print(f"\n   ⚠️ 潜在问题:")
    for issue in performance['potential_issues']:
        print(f"      • {issue}")
    
    print(f"\n   💡 优化建议:")
    for suggestion in performance['optimization_suggestions']:
        print(f"      • {suggestion}")
    
    # 生成关键业务规则总结
    print(f"\n📋 关键业务规则总结:")
    print("   1. 数据完整性: 每次执行前清理现有数据，确保不重复")
    print("   2. 事务一致性: 整个过程在一个事务中执行，要么全成功要么全失败")
    print("   3. 日志记录: 每次执行都记录到JobExecutionLog表")
    print("   4. 默认参数: 如果不指定日期，默认处理前一天的数据")
    print("   5. 店铺过滤: 默认处理店铺ID=11的数据")
    print("   6. 数据结构: 支持最新的折扣免费(DiscountFree)业务逻辑")
    
    print(f"\n🎯 业务价值:")
    print("   • 自动化日报生成，减少人工操作")
    print("   • 统一数据格式，便于分析和决策")
    print("   • 完整的错误处理和日志记录")
    print("   • 支持最新的业务需求（如直落、折扣免费等）")
    
    return {
        'procedures': procedures,
        'tables': tables,
        'flow_steps': flow_steps,
        'error_handling': error_handling,
        'performance': performance,
        'analysis_timestamp': datetime.now().isoformat()
    }

def main():
    print("🔍 开始深度业务逻辑分析...")
    
    # 生成分析报告
    analysis_result = generate_comprehensive_business_analysis()
    
    # 保存分析结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"usp_RunUnifiedDailyReportJob_business_analysis_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n✅ 业务分析报告已保存到: {filename}")

if __name__ == "__main__":
    main()
