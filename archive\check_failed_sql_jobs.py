
import pyodbc
import datetime

# Database connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=msdb;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# SQL query to find the most recent failed job execution for each job
# We look for run_status = 0 (Failed) and get the latest occurrence for each job name.
# This version joins with sysjobsteps to correctly retrieve the command.
SQL_QUERY = """
WITH FailedJobHistory AS (
    SELECT
        job_id,
        run_date,
        run_time,
        step_id,
        step_name,
        message,
        ROW_NUMBER() OVER(PARTITION BY job_id ORDER BY run_date DESC, run_time DESC) as rn
    FROM
        dbo.sysjobhistory
    WHERE
        run_status = 0 -- 0 indicates failure
        AND step_id > 0
)
SELECT
    j.name AS job_name,
    h.run_date,
    h.run_time,
    h.step_name,
    h.message,
    s.command
FROM
    FailedJobHistory h
INNER JOIN
    dbo.sysjobs j ON h.job_id = j.job_id
INNER JOIN
    dbo.sysjobsteps s ON h.job_id = s.job_id AND h.step_id = s.step_id
WHERE
    h.rn = 1
ORDER BY
    h.run_date DESC, h.run_time DESC;
"""

def format_datetime(date_int, time_int):
    """Converts SQL Server's date and time integers to a datetime object."""
    s_date = str(date_int)
    s_time = str(time_int).zfill(6)
    try:
        dt_str = f"{s_date[:4]}-{s_date[4:6]}-{s_date[6:]} {s_time[:2]}:{s_time[2:4]}:{s_time[4:]}"
        return datetime.datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
    except (ValueError, IndexError):
        return None

def check_failed_jobs():
    """Connects to the database and checks for recently failed SQL agent jobs."""
    conn = None
    try:
        print("正在连接到数据库 'msdb'...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print("正在查询失败的定时任务...")
        cursor.execute(SQL_QUERY)
        rows = cursor.fetchall()

        if not rows:
            print("\n查询完成。没有找到任何失败的定时任务记录。")
            return

        print(f"\n查询到 {len(rows)} 个最近运行失败的定时任务：")
        print("-" * 80)

        for row in rows:
            run_dt = format_datetime(row.run_date, row.run_time)
            print(f"任务名称: {row.job_name}")
            if run_dt:
                print(f"失败时间: {run_dt.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"失败步骤: {row.step_name}")
            print(f"错误信息: {row.message.strip()}")
            print(f"执行的命令: {row.command.strip()}")
            print("-" * 80)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

if __name__ == "__main__":
    check_failed_jobs()
