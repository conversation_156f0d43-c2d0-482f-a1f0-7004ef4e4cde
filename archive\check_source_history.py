import pyodbc
import datetime

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

def check_source_history_data():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;DATABASE=rms2019;"
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- 成功连接到 {SERVER}/rms2019 ---')

            # --- 计算日期 ---
            yesterday = datetime.date.today() - datetime.timedelta(days=1)
            business_date_start = datetime.datetime.combine(yesterday, datetime.time(9, 0))
            business_date_end = business_date_start + datetime.timedelta(days=1)

            print(f"\n正在查询源服务器 (cloudRms2019) 上 {yesterday} 的 history 数据...")
            
            # --- 查询 openhistory ---
            openhistory_sql = f"""SELECT COUNT(*) FROM cloudRms2019.rms2019.dbo.openhistory 
                                 WHERE BookDateTime >= '{business_date_start}' AND BookDateTime < '{business_date_end}'"""
            cursor.execute(openhistory_sql)
            open_count = cursor.fetchone()[0]
            print(f"- 源 openhistory 表中找到 {open_count} 条记录。")

            # --- 查询 bookhistory ---
            bookhistory_sql = f"""SELECT COUNT(*) FROM cloudRms2019.rms2019.dbo.bookhistory 
                                 WHERE CAST(BookDateTime AS DATE) = '{yesterday}'"""
            cursor.execute(bookhistory_sql)
            book_count = cursor.fetchone()[0]
            print(f"- 源 bookhistory 表中找到 {book_count} 条记录。")

            print("\n查询完成。")

    except Exception as e:
        print(f"执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    check_source_history_data()
