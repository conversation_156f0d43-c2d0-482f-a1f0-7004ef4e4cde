
import pyodbc

# Database connection details - connecting to master is sufficient
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=master;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# SQL query to get the SQL Server edition information
# We cast both properties to avoid ODBC type errors.
SQL_QUERY = "SELECT CAST(SERVERPROPERTY('Edition') AS NVARCHAR(128)) AS Edition, CAST(SERVERPROPERTY('EngineEdition') AS INT) AS EngineEdition;"

def check_sql_server_edition():
    """Connects to the database and checks the SQL Server edition."""
    conn = None
    try:
        print("正在连接到数据库 'master' 以检查 SQL Server 版本...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print("正在执行版本查询...")
        cursor.execute(SQL_QUERY)
        row = cursor.fetchone()

        if row:
            print("\n查询成功！")
            print(f"SQL Server 版本 (Edition): {row.Edition}")
            print(f"引擎类型 (EngineEdition): {row.EngineEdition}")
            
            # EngineEdition for Express is 4
            if row.EngineEdition == 4:
                print("\n[结论] 您正在使用的是 SQL Server Express 版本。")
                print("此版本不包含 SQL Server Agent 功能，因此无法创建或运行任何定时任务。")
                print("这就是为什么所有任务都显示'从未运行'并且找不到 Agent 服务的原因。")
            else:
                print("\n[结论] 您使用的不是 SQL Server Express 版本。")
                print("此版本应该支持 SQL Server Agent。如果 Agent 服务未运行，请在服务器上检查 'SQL Server 配置管理器'。")

        else:
            print("\n查询失败，无法获取版本信息。")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

if __name__ == "__main__":
    check_sql_server_edition()
