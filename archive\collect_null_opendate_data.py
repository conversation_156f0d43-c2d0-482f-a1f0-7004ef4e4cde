import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 输出文件名 ---
OUTPUT_FILENAME = 'null_opendate_records.csv'

def collect_null_opendate_data():
    """
    查询 RmCloseInfo_Test 表中 OpenDateTime 为空的特定门店数据，并保存为 CSV。
    V2: 移除了不存在的 RoomNo 和 Cashier 列。
    """
    cnxn = None
    try:
        # 1. 构建SQL查询语句 (修正版)
        sql_query = """
        SELECT 
            InvNo,          -- 单号
            WorkDate,       -- 工作日期
            ShopId,         -- 门店ID
            TotalAmount,    -- 总金额
            Numbers,        -- 人数
            CtNo,           -- 合同号/套餐类型
            OpenDateTime,   -- 开房时间 (应为NULL)
            CloseDatetime  -- 结账时间
        FROM 
            dbo.RmCloseInfo_Test
        WHERE 
            OpenDateTime IS NULL
            AND ShopId IN (3, 11); -- 假设名堂是3，天河是11
        """

        # 2. 连接数据库并执行查询
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"正在连接到数据库: {DATABASE}...")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！正在执行查询...")
        
        df = pd.read_sql_query(sql_query, cnxn)
        
        if df.empty:
            print("查询完成，没有找到任何 OpenDateTime 为 NULL 的相关记录。")
            return

        print(f"查询完成，找到了 {len(df)} 条 OpenDateTime 为 NULL 的记录。")

        # 3. 保存为CSV文件
        print(f"正在将数据保存到文件: {OUTPUT_FILENAME}...")
        df.to_csv(OUTPUT_FILENAME, index=False, encoding='utf-8-sig')
        
        print("\n--- 成功！ ---")
        print(f"数据已成功保存，请查看文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 发生未知错误！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    collect_null_opendate_data()