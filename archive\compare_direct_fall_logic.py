import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_DATE = '2025-07-24'
SHOP_ID = 11

# --- Query Definitions ---

# Query A: Based on the complex behavioral logic from usp_GenerateDayTimeReport_Simple_V3
SQL_BEHAVIOR_LOGIC = f"""
WITH BehavioralMetrics AS (
    SELECT
        -- DayTimeDropInBatch
        SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END) AS DayTimeDropIn_Behavior,
        
        -- NightTimeDropInBatch
        SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END) AS NightTimeDropIn_Behavior

    FROM dbo.RmCloseInfo AS rt
    LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.Shopid = sti_beg.Shopid AND rt.Beg_Key = sti_beg.TimeNo
    LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
    LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.Shopid = sti_end.Shopid AND rt.End_Key = sti_end.TimeNo
    WHERE rt.Shopid = {SHOP_ID} 
    AND rt.WorkDate = '{TARGET_DATE}'
)
SELECT 
    ISNULL(DayTimeDropIn_Behavior, 0) AS DayTimeDropIn_Behavior,
    ISNULL(NightTimeDropIn_Behavior, 0) AS NightTimeDropIn_Behavior
FROM BehavioralMetrics;
"""

# Query B: Based on the simple item name lookup from FdCashBak
SQL_ITEM_NAME_LOGIC = f"""
WITH TargetInvoices AS (
    SELECT invno
    FROM rmcloseinfo
    WHERE WorkDate = '{TARGET_DATE}' AND ShopId = {SHOP_ID}
)
SELECT 
    COUNT(DISTINCT ti.invno) AS TotalDirectFall_ItemName
FROM TargetInvoices ti
JOIN FdCashBak fdc ON ti.invno = fdc.InvNo COLLATE Chinese_PRC_CI_AS
WHERE fdc.FdCName LIKE N'%直落%' AND fdc.ShopId = {SHOP_ID};
"""

# --- Main Execution Logic ---
def compare_logic():
    """
    Connects to the database, runs both queries, and prints a comparison.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            print(f"--- Comparison for Direct Fall Logic on {TARGET_DATE} ---")
            
            print("\nQuerying based on BEHAVIORAL logic (V3 procedure)...")
            df_behavior = pd.read_sql(SQL_BEHAVIOR_LOGIC, conn)
            day_behavior_count = df_behavior.iloc[0]['DayTimeDropIn_Behavior']
            night_behavior_count = df_behavior.iloc[0]['NightTimeDropIn_Behavior']
            total_behavior_count = day_behavior_count + night_behavior_count

            print(f"  - Day-time Drop-in Batches: {day_behavior_count}")
            print(f"  - Night-time Drop-in Batches: {night_behavior_count}")
            print(f"  - TOTAL by Behavior: {total_behavior_count}")

            print("\nQuerying based on ITEM NAME logic (FdCName LIKE '%直落%')...")
            df_item = pd.read_sql(SQL_ITEM_NAME_LOGIC, conn)
            item_name_count = df_item.iloc[0]['TotalDirectFall_ItemName']
            print(f"  - TOTAL by Item Name: {item_name_count}")

            print("\n--- ANALYSIS ---")
            print(f"The difference between the two methods is: {abs(total_behavior_count - item_name_count)}")
            if total_behavior_count > item_name_count:
                print("The BEHAVIORAL logic finds more instances of 'direct fall' than the item name search.")
            elif item_name_count > total_behavior_count:
                print("The ITEM NAME search finds more instances of 'direct fall' than the behavioral logic.")
            else:
                print("The two methods produce the exact same count.")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"DATABASE ERROR: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    compare_logic()
