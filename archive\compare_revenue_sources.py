

import pyodbc
import pandas as pd

# --- Configuration ---
DB_CONFIG = {
    'server': '192.168.2.5',
    'database': 'operatedata',
    'username': 'sa',
    'password': 'Musicbox123'
}

# --- Test Parameters ---
TEST_SHOP_ID = 11
TEST_DATE = '2025-07-24'

# --- Main Logic ---
def compare_revenue_sources():
    """Fetches revenue from both RmCloseInfo_Day and the SP for comparison."""
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}"
    
    try:
        with pyodbc.connect(conn_str) as conn:
            print(f"--- Comparing revenue for Shop ID {TEST_SHOP_ID} on {TEST_DATE} ---\n")

            # 1. Get revenue from RmCloseInfo_Day table
            print("1. Querying RmCloseInfo_Day table...")
            query_table = "SELECT Turnover FROM RmCloseInfo_Day WHERE Shopid = ? AND WorkDate = ?"
            params_table = [TEST_SHOP_ID, TEST_DATE]
            df_table = pd.read_sql(query_table, conn, params=params_table)
            
            if not df_table.empty:
                revenue_from_table = df_table['Turnover'].iloc[0]
                print(f"   - Revenue from RmCloseInfo_Day: {revenue_from_table}")
            else:
                print("   - No data found in RmCloseInfo_Day for the specified criteria.")
                revenue_from_table = 0

            # 2. Get revenue from the stored procedure
            print("\n2. Executing usp_GenerateDayTimeReport_Simple_V3_fixed SP...")
            sp_name = 'usp_GenerateDayTimeReport_Simple_V3_fixed'
            sp_exec_sql = f"EXEC {sp_name} @ShopId=?, @TargetDate=?"
            params_sp = [TEST_SHOP_ID, TEST_DATE]
            df_sp = pd.read_sql(sp_exec_sql, conn, params=params_sp)

            if not df_sp.empty:
                revenue_from_sp = df_sp['TotalRevenue'].iloc[0]
                print(f"   - Revenue from Stored Procedure: {revenue_from_sp}")
            else:
                print("   - No data returned from the stored procedure.")
                revenue_from_sp = 0
            
            # 3. Print comparison
            print("\n--- Comparison Summary ---")
            print(f"Table (RmCloseInfo_Day): {revenue_from_table}")
            print(f"SP (usp_..._fixed):    {revenue_from_sp}")
            if revenue_from_table != revenue_from_sp:
                print("\nConclusion: The revenue figures DO NOT MATCH.")
            else:
                print("\nConclusion: The revenue figures match.")

    except Exception as e:
        print(f"\nAn error occurred: {e}")

if __name__ == '__main__':
    compare_revenue_sources()

