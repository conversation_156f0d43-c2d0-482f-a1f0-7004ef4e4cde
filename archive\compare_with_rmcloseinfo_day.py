

import pyodbc
import pandas as pd

# --- Configuration ---
DB_CONFIG = {
    'server': '***********',
    'database': 'operatedata',
    'username': 'sa',
    'password': 'Musicbox123'
}
SP_SCRIPT_FILE = 'usp_GenerateDayTimeReport_Simple_V3_final_fix.sql'
SP_NAME = 'usp_GenerateDayTimeReport_Simple_V3_final_fix'

# --- Test Parameters ---
TEST_SHOP_ID = 11
TEST_DATE = '2025-07-24'

# --- Expected Value from 3.sql ---
EXPECTED_TURNOVER_FROM_RMINFO_DAY = 127512

# --- Main Logic ---
def compare_with_rmcloseinfo_day():
    """Creates/updates the SP and compares its output with the provided RmCloseInfo_Day value."""
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}"
    
    try:
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()

            # 1. Create/Update the Stored Procedure
            print(f"--- Creating/Updating Stored Procedure from '{SP_SCRIPT_FILE}' ---")
            with open(SP_SCRIPT_FILE, 'r', encoding='utf-8') as f:
                sql_script = f.read().replace('GO', '')
            
            # Replace CREATE OR ALTER with CREATE for compatibility
            sql_script = sql_script.replace('CREATE OR ALTER PROCEDURE', 'CREATE PROCEDURE')

            drop_sql = f"IF OBJECT_ID('{SP_NAME}', 'P') IS NOT NULL DROP PROCEDURE {SP_NAME}"
            cursor.execute(drop_sql)
            conn.commit()

            cursor.execute(sql_script)
            conn.commit()
            print("Stored Procedure updated successfully.")

            # 2. Execute the Stored Procedure
            print(f"\n--- Executing '{SP_NAME}' for Shop ID {TEST_SHOP_ID} on {TEST_DATE} ---")
            sp_exec_sql = f"EXEC {SP_NAME} @ShopId=?, @TargetDate=?"
            params = [TEST_SHOP_ID, TEST_DATE]
            
            df_sp = pd.read_sql(sp_exec_sql, conn, params=params)

            if not df_sp.empty:
                revenue_from_sp = df_sp['TotalRevenue'].iloc[0]
                print(f"   - TotalRevenue from SP: {revenue_from_sp}")
            else:
                print("   - No data returned from the stored procedure.")
                revenue_from_sp = 0
            
            # 3. Compare
            print("\n--- Comparison ---")
            print(f"Expected Turnover (from RmCloseInfo_Day): {EXPECTED_TURNOVER_FROM_RMINFO_DAY}")
            print(f"Calculated TotalRevenue (from SP):        {revenue_from_sp}")

            if EXPECTED_TURNOVER_FROM_RMINFO_DAY == revenue_from_sp:
                print("\nConclusion: The calculated TotalRevenue MATCHES the expected Turnover.")
            else:
                print("\nConclusion: The calculated TotalRevenue DOES NOT MATCH the expected Turnover.")
                print("Difference: ", EXPECTED_TURNOVER_FROM_RMINFO_DAY - revenue_from_sp)

    except Exception as e:
        print(f"\nAn error occurred: {e}")

if __name__ == '__main__':
    compare_with_rmcloseinfo_day()

