#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的白天档直落分析
业务规则：只有20点前进场的白天档自助餐才有直落概念
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta
import json

class DaytimeDirectFallAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
        # 白天档时间段配置（20点前的时间段）
        self.daytime_slots = {
            '02': {'name': '13:30-16:30', 'timetype': 2, 'start': '13:30', 'end': '16:30'},
            '07': {'name': '15:00-18:00', 'timetype': 12, 'start': '15:00', 'end': '18:00'},
            '25': {'name': '17:00-20:00', 'timetype': 2, 'start': '17:00', 'end': '20:00'},
            '28': {'name': '11:50-14:50', 'timetype': 12, 'start': '11:50', 'end': '14:50'},
            '29': {'name': '18:10-21:10', 'timetype': 1, 'start': '18:10', 'end': '21:10'},
            '37': {'name': '18:00-21:00', 'timetype': 1, 'start': '18:00', 'end': '21:00'},
            '39': {'name': '19:00-22:00', 'timetype': 12, 'start': '19:00', 'end': '22:00'},
            '46': {'name': '19:00-21:30', 'timetype': 12, 'start': '19:00', 'end': '21:30'}
        }
        
        # 夜场时间段（20点后，无直落概念）
        self.night_slots = {
            '05': {'name': '20:00', 'timetype': 126},
            '14': {'name': '01:00', 'timetype': 6}
        }
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def is_daytime_entry(self, come_time):
        """判断是否是白天档进场（20点前）"""
        try:
            time_obj = datetime.strptime(come_time, '%H:%M:%S').time()
            cutoff_time = datetime.strptime('20:00:00', '%H:%M:%S').time()
            return time_obj < cutoff_time
        except:
            return False
    
    def can_direct_fall_by_timetype(self, from_timetype, to_timetype):
        """根据timetype判断是否可以直落"""
        # timetype中有重叠数字表示可以直落
        # 例如：timetype=12的时间段可以互相直落
        return from_timetype == to_timetype or (from_timetype & to_timetype) > 0
    
    def get_full_data_20250717(self):
        """获取20250717的完整数据"""
        try:
            # 获取开台数据
            conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            open_query = f"""
            SELECT 
                Ikey, BookNo, ShopId, CustName, CustTel, ComeDate, ComeTime,
                Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName,
                RmNo, Invno, Remark
            FROM opencacheinfo
            WHERE shopid = {self.shop_id} AND ComeDate = '20250717'
            ORDER BY ComeTime
            """
            
            open_df = pd.read_sql(open_query, conn)
            conn.close()
            
            # 获取结账数据（本地dbfood数据库）
            conn = self.get_connection('193.112.2.229', 'dbfood', 'sa', 'Musicbox@123')
            
            # 获取所有结账记录
            close_query = f"""
            SELECT InvNo, CloseDatetime, Tot, VesaName
            FROM rmcloseinfo
            WHERE InvNo IN (SELECT DISTINCT Invno FROM [{conn.getinfo(pyodbc.SQL_DATABASE_NAME)}].[dbo].[rmcloseinfo])
            """
            
            # 简化查询，直接获取结账数据
            close_query = "SELECT InvNo, CloseDatetime, Tot, VesaName FROM rmcloseinfo"
            close_df = pd.read_sql(close_query, conn)
            conn.close()
            
            # 关联数据
            merged_df = pd.merge(open_df, close_df, left_on='Invno', right_on='InvNo', how='left')
            
            return merged_df
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return pd.DataFrame()
    
    def analyze_20250717_direct_fall(self):
        """分析20250717的直落情况"""
        print(f"\n{'='*80}")
        print(f"20250717名堂店白天档直落分析")
        print(f"业务规则：只有20点前进场的白天档自助餐才有直落概念")
        print(f"{'='*80}")
        
        # 获取数据
        df = self.get_full_data_20250717()
        
        if df.empty:
            print("没有获取到数据")
            return {}
        
        print(f"\n📊 获取到 {len(df)} 个开台记录")
        
        # 显示白天档时间段配置
        print(f"\n⏰ 白天档时间段配置 (可能有直落):")
        print(f"{'='*60}")
        for slot_no, config in sorted(self.daytime_slots.items()):
            print(f"  {config['name']:<15} TimeType={config['timetype']:<3} ({config['start']}-{config['end']})")
        
        print(f"\n🌙 夜场时间段 (无直落概念):")
        print(f"{'='*60}")
        for slot_no, config in sorted(self.night_slots.items()):
            print(f"  {config['name']:<15} TimeType={config['timetype']:<3}")
        
        analysis_results = []
        daytime_orders = []
        night_orders = []
        
        for _, row in df.iterrows():
            result = {
                'InvNo': row['Invno'],
                'CustName': row['CustName'],
                'ComeTime': row['ComeTime'],
                'Beg_Name': row['Beg_Name'],
                'End_Name': row['End_Name'],
                'Numbers': row['Numbers'],
                'RmNo': row['RmNo'],
                'Remark': row['Remark'],
                'CloseDatetime': row['CloseDatetime'],
                'Tot': row['Tot']
            }
            
            # 判断是否白天档进场
            is_daytime = self.is_daytime_entry(row['ComeTime'])
            result['进场类型'] = '白天档' if is_daytime else '夜场'
            
            if is_daytime:
                daytime_orders.append(result)
                
                # 分析白天档的直落情况
                if pd.notna(row['Beg_Name']) and pd.notna(row['End_Name']):
                    if row['Beg_Name'] != row['End_Name']:
                        result['预约状态'] = f"预约直落 ({row['Beg_Name']} → {row['End_Name']})"
                        
                        # 检查是否符合直落规则
                        from_slot = None
                        to_slot = None
                        for slot_no, config in self.daytime_slots.items():
                            if config['name'] == row['Beg_Name']:
                                from_slot = config
                            if config['name'] == row['End_Name']:
                                to_slot = config
                        
                        if from_slot and to_slot:
                            can_fall = self.can_direct_fall_by_timetype(from_slot['timetype'], to_slot['timetype'])
                            result['直落可行性'] = '✅ 可直落' if can_fall else '❌ 不可直落'
                            result['TimeType组合'] = f"{from_slot['timetype']} → {to_slot['timetype']}"
                        else:
                            result['直落可行性'] = '❓ 时间段未找到'
                            result['TimeType组合'] = 'N/A'
                    else:
                        result['预约状态'] = f"预约单时间段 ({row['Beg_Name']})"
                        result['直落可行性'] = '非直落预约'
                        result['TimeType组合'] = 'N/A'
                else:
                    result['预约状态'] = '预约信息缺失'
                    result['直落可行性'] = '无法判断'
                    result['TimeType组合'] = 'N/A'
                
                # 分析实际消费时长
                if pd.notna(row['CloseDatetime']):
                    try:
                        come_datetime = datetime.strptime(f"20250717 {row['ComeTime']}", '%Y%m%d %H:%M:%S')
                        close_datetime = pd.to_datetime(row['CloseDatetime'])
                        
                        duration = close_datetime - come_datetime
                        duration_hours = duration.total_seconds() / 3600
                        
                        result['实际消费时长'] = f"{duration_hours:.1f}小时"
                        
                        # 白天档直落需要≥3小时
                        if duration_hours >= 3.0:
                            result['时长判断'] = '✅ 符合直落时长'
                            if result['直落可行性'] == '✅ 可直落':
                                result['最终判断'] = '✅ 真正直落'
                            else:
                                result['最终判断'] = '❌ 预约不支持直落'
                        else:
                            result['时长判断'] = f'❌ 时长不足 (需≥3.0h)'
                            result['最终判断'] = '单时间段消费'
                    except Exception as e:
                        result['实际消费时长'] = f"计算错误: {e}"
                        result['时长判断'] = '计算错误'
                        result['最终判断'] = '无法判断'
                else:
                    result['实际消费时长'] = '未结账'
                    result['时长判断'] = '未结账'
                    result['最终判断'] = '未结账'
            else:
                night_orders.append(result)
                result['预约状态'] = f"夜场预约 ({row['Beg_Name']})"
                result['直落可行性'] = '夜场无直落概念'
                result['最终判断'] = '夜场消费'
                result['TimeType组合'] = 'N/A'
                
                # 夜场也计算消费时长，但不判断直落
                if pd.notna(row['CloseDatetime']):
                    try:
                        come_datetime = datetime.strptime(f"20250717 {row['ComeTime']}", '%Y%m%d %H:%M:%S')
                        close_datetime = pd.to_datetime(row['CloseDatetime'])
                        duration = close_datetime - come_datetime
                        duration_hours = duration.total_seconds() / 3600
                        result['实际消费时长'] = f"{duration_hours:.1f}小时"
                        result['时长判断'] = '夜场无时长要求'
                    except:
                        result['实际消费时长'] = '计算错误'
                        result['时长判断'] = '计算错误'
                else:
                    result['实际消费时长'] = '未结账'
                    result['时长判断'] = '未结账'
            
            analysis_results.append(result)
        
        # 统计分析
        total_orders = len(analysis_results)
        daytime_count = len(daytime_orders)
        night_count = len(night_orders)
        
        # 只统计白天档的直落情况
        real_direct_fall = len([r for r in daytime_orders if r.get('最终判断') == '✅ 真正直落'])
        
        print(f"\n📈 分析结果统计:")
        print(f"  总订单数: {total_orders}")
        print(f"  白天档订单: {daytime_count}")
        print(f"  夜场订单: {night_count}")
        print(f"  白天档真正直落: {real_direct_fall}")
        print(f"  白天档直落率: {real_direct_fall/daytime_count*100:.1f}%" if daytime_count > 0 else "  白天档直落率: N/A")
        
        # 详细分析
        print(f"\n🔍 白天档订单详细分析:")
        print(f"{'='*160}")
        print(f"{'订单号':<12} {'客户':<10} {'开台时间':<10} {'预约状态':<25} {'直落可行性':<12} {'消费时长':<10} {'最终判断':<12} {'金额':<8}")
        print("-" * 160)
        
        for result in daytime_orders:
            print(f"{result['InvNo']:<12} "
                  f"{result['CustName']:<10} "
                  f"{result['ComeTime'][:5]:<10} "
                  f"{result['预约状态']:<25} "
                  f"{result['直落可行性']:<12} "
                  f"{result['实际消费时长']:<10} "
                  f"{result['最终判断']:<12} "
                  f"¥{result['Tot']:<7}" if pd.notna(result['Tot']) else f"{'未结账':<8}")
        
        # 真正直落订单详情
        direct_fall_orders = [r for r in daytime_orders if r.get('最终判断') == '✅ 真正直落']
        if direct_fall_orders:
            print(f"\n✅ 真正直落订单详情:")
            print(f"{'='*80}")
            for order in direct_fall_orders:
                print(f"\n【{order['InvNo']} - {order['CustName']}】")
                print(f"  开台时间: {order['ComeTime']}")
                print(f"  预约状态: {order['预约状态']}")
                print(f"  TimeType组合: {order['TimeType组合']}")
                print(f"  实际消费时长: {order['实际消费时长']}")
                print(f"  结账时间: {order['CloseDatetime']}")
                print(f"  金额: ¥{order['Tot']}")
                print(f"  备注: {order['Remark']}")
        else:
            print(f"\n❌ 20250717没有找到真正的直落订单")
        
        # 夜场订单概览
        if night_orders:
            print(f"\n🌙 夜场订单概览 (无直落概念):")
            print(f"{'='*80}")
            for order in night_orders[:5]:  # 只显示前5个
                print(f"  {order['InvNo']} - {order['CustName']} - {order['ComeTime'][:5]} - {order['实际消费时长']}")
            if len(night_orders) > 5:
                print(f"  ... 还有{len(night_orders)-5}个夜场订单")
        
        return {
            'total_orders': total_orders,
            'daytime_count': daytime_count,
            'night_count': night_count,
            'real_direct_fall': real_direct_fall,
            'analysis_results': analysis_results
        }

def main():
    analyzer = DaytimeDirectFallAnalyzer()
    
    try:
        # 分析20250717的直落情况
        result = analyzer.analyze_20250717_direct_fall()
        
        # 保存结果
        if result:
            with open('daytime_direct_fall_20250717.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n✅ 分析完成！")
            print(f"📄 详细结果已保存到: daytime_direct_fall_20250717.json")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
