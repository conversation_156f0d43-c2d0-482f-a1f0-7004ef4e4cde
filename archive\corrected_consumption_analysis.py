#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正后的基于实际消费时长的KTV分析
只分析对应日期的结账数据
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta
import json

class CorrectedConsumptionAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
        # 名堂店时间段配置
        self.time_slots = {
            '02': {'name': '13:30-16:30', 'start': '13:30', 'end': '16:30'},
            '05': {'name': '20:00', 'start': '20:00', 'end': '06:00'},  # 跨天
            '07': {'name': '15:00-18:00', 'start': '15:00', 'end': '18:00'},
            '14': {'name': '01:00', 'start': '01:00', 'end': '06:00'},
            '25': {'name': '17:00-20:00', 'start': '17:00', 'end': '20:00'},
            '28': {'name': '11:50-14:50', 'start': '11:50', 'end': '14:50'},
            '29': {'name': '18:10-21:10', 'start': '18:10', 'end': '21:10'},
            '37': {'name': '18:00-21:00', 'start': '18:00', 'end': '21:00'},
            '39': {'name': '19:00-22:00', 'start': '19:00', 'end': '22:00'},
            '46': {'name': '19:00-21:30', 'start': '19:00', 'end': '21:30'}
        }
        
        # 用户提供的样本订单及其对应的结账数据
        self.sample_data = {
            'A02426816': {'date': '20250717', 'close_time': '2025-07-17 16:06:40.120', 'tot': 152},
            'A02426850': {'date': '20250717', 'close_time': '2025-07-17 20:18:57.193', 'tot': 346},
            'A02426864': {'date': '20250717', 'close_time': '2025-07-17 21:36:24.257', 'tot': 568},
            'A02426869': {'date': '20250717', 'close_time': '2025-07-18 02:05:39.290', 'tot': 3543},
            'A02426880': {'date': '20250717', 'close_time': '2025-07-18 01:55:13.710', 'tot': 1592},
            # 其他订单可能还未结账
            'A02426898': {'date': '20250718', 'close_time': None, 'tot': None},
            'A02426914': {'date': '20250718', 'close_time': None, 'tot': None},
            'A02426911': {'date': '20250718', 'close_time': None, 'tot': None},
            'A02426904': {'date': '20250718', 'close_time': None, 'tot': None},
            'A02426908': {'date': '20250718', 'close_time': None, 'tot': None},
            'A02426895': {'date': '20250718', 'close_time': None, 'tot': None}
        }
        
        # 从INSERT语句提取的开台信息
        self.open_data = {
            'A02426864': {
                'CustName': '梁女士',
                'ComeTime': '19:28:06',
                'Beg_Name': '19:00-22:00',
                'End_Name': '19:00-22:00',
                'Numbers': 3,
                'RmNo': '801',
                'Remark': '肆位，其中一位半价'
            },
            'A02426880': {
                'CustName': '林先生',
                'ComeTime': '22:00:56',
                'Beg_Name': '20:00',
                'End_Name': '20:00',
                'Numbers': 2,
                'RmNo': '831',
                'Remark': '5位，现买'
            },
            'A02426898': {
                'CustName': '李女士',
                'ComeTime': '11:48:16',
                'Beg_Name': '11:50-14:50',
                'End_Name': '11:50-14:50',
                'Numbers': 5,
                'RmNo': '309',
                'Remark': '陆位（其中一位儿童）V返'
            },
            'A02426869': {
                'CustName': '贵宾小姐女士女士',
                'ComeTime': '20:56:23',
                'Beg_Name': '20:00',
                'End_Name': '20:00',
                'Numbers': 2,
                'RmNo': '555',
                'Remark': '6位，中房畅饮'
            },
            'A02426914': {
                'CustName': '贵宾先生',
                'ComeTime': '14:02:31',
                'Beg_Name': '13:30-16:30',
                'End_Name': '13:30-16:30',
                'Numbers': 2,
                'RmNo': '666',
                'Remark': ''
            },
            'A02426911': {
                'CustName': '段女士',
                'ComeTime': '13:24:23',
                'Beg_Name': '13:30-16:30',
                'End_Name': '13:30-16:30',
                'Numbers': 10,
                'RmNo': '333',
                'Remark': '拾位美团1488X1'
            },
            'A02426816': {
                'CustName': '张女士',
                'ComeTime': '13:19:49',
                'Beg_Name': '13:30-16:30',
                'End_Name': '13:30-16:30',
                'Numbers': 2,
                'RmNo': '313',
                'Remark': '贰位 V返卡扣'
            },
            'A02426904': {
                'CustName': '贵宾女士',
                'ComeTime': '13:20:02',
                'Beg_Name': '13:30-16:30',
                'End_Name': '13:30-16:30',
                'Numbers': 2,
                'RmNo': '302',
                'Remark': '贰位在线团购x2'
            },
            'A02426908': {
                'CustName': '刘宇轩先生',
                'ComeTime': '13:21:50',
                'Beg_Name': '13:30-16:30',
                'End_Name': '13:30-16:30',
                'Numbers': 5,
                'RmNo': '806',
                'Remark': '提前购券，同意使用'
            },
            'A02426850': {
                'CustName': '吴先生',
                'ComeTime': '17:52:59',
                'Beg_Name': '18:00-21:00',
                'End_Name': '18:00-21:00',
                'Numbers': 2,
                'RmNo': '806',
                'Remark': '贰位，V返'
            },
            'A02426895': {
                'CustName': '李女士',
                'ComeTime': '11:40:46',
                'Beg_Name': '11:50-14:50',
                'End_Name': '11:50-14:50',
                'Numbers': 2,
                'RmNo': '319',
                'Remark': '寿星免一V返'
            }
        }
    
    def time_to_minutes(self, time_str):
        """将时间字符串转换为分钟数（从00:00开始计算）"""
        if not time_str or time_str == '':
            return None
        
        try:
            hour, minute = map(int, time_str.split(':'))
            # 处理跨天情况，如果小时数小于6，认为是第二天
            if hour < 6:
                hour += 24
            return hour * 60 + minute
        except:
            return None
    
    def get_overlapping_time_slots(self, start_time, end_time):
        """获取消费时间跨越的所有时间段"""
        overlapping_slots = []
        
        start_minutes = self.time_to_minutes(start_time)
        end_minutes = self.time_to_minutes(end_time)
        
        if start_minutes is None or end_minutes is None:
            return overlapping_slots
        
        for slot_no, slot_info in self.time_slots.items():
            slot_start = self.time_to_minutes(slot_info['start'])
            slot_end = self.time_to_minutes(slot_info['end'])
            
            if slot_start is None or slot_end is None:
                continue
            
            # 检查是否有重叠
            if slot_end < slot_start:  # 跨天时间段
                # 跨天时间段的重叠判断
                if (start_minutes <= slot_end or start_minutes >= slot_start) or \
                   (end_minutes <= slot_end or end_minutes >= slot_start) or \
                   (start_minutes <= slot_start and end_minutes >= slot_end):
                    overlapping_slots.append((slot_no, slot_info['name']))
            else:  # 同一天时间段
                # 标准重叠判断
                if not (end_minutes <= slot_start or start_minutes >= slot_end):
                    overlapping_slots.append((slot_no, slot_info['name']))
        
        return overlapping_slots
    
    def analyze_sample_consumption(self):
        """分析样本订单的实际消费情况"""
        print(f"\n{'='*80}")
        print(f"基于实际消费时长的样本分析")
        print(f"样本订单: {len(self.sample_data)} 个")
        print(f"{'='*80}")
        
        analysis_results = []
        
        for invno, close_info in self.sample_data.items():
            if invno not in self.open_data:
                continue
                
            open_info = self.open_data[invno]
            
            result = {
                'InvNo': invno,
                'CustName': open_info['CustName'],
                'ComeTime': open_info['ComeTime'],
                'Beg_Name': open_info['Beg_Name'],
                'End_Name': open_info['End_Name'],
                'Numbers': open_info['Numbers'],
                'RmNo': open_info['RmNo'],
                'Remark': open_info['Remark'],
                'Tot': close_info['tot']
            }
            
            # 预约状态
            if open_info['Beg_Name'] != open_info['End_Name']:
                result['预约状态'] = f"预约直落 ({open_info['Beg_Name']} → {open_info['End_Name']})"
            else:
                result['预约状态'] = f"预约单时间段 ({open_info['Beg_Name']})"
            
            # 分析实际消费
            if close_info['close_time']:
                try:
                    # 构建完整的开台时间
                    come_datetime = datetime.strptime(f"{close_info['date']} {open_info['ComeTime']}", '%Y%m%d %H:%M:%S')
                    close_datetime = datetime.strptime(close_info['close_time'], '%Y-%m-%d %H:%M:%S.%f')
                    
                    # 计算消费时长
                    duration = close_datetime - come_datetime
                    duration_hours = duration.total_seconds() / 3600
                    
                    result['CloseDatetime'] = close_info['close_time']
                    result['实际消费时长'] = f"{duration_hours:.1f}小时"
                    result['消费分钟数'] = int(duration.total_seconds() / 60)
                    
                    # 获取消费时间跨越的时间段
                    start_time = open_info['ComeTime'][:5]  # HH:MM
                    end_time = close_datetime.strftime('%H:%M')
                    
                    overlapping_slots = self.get_overlapping_time_slots(start_time, end_time)
                    result['跨越时间段'] = overlapping_slots
                    result['跨越时间段数量'] = len(overlapping_slots)
                    
                    # 判断是否真正直落
                    if len(overlapping_slots) > 1:
                        result['真实直落状态'] = '✅ 真正直落'
                        result['直落类型'] = f"跨越{len(overlapping_slots)}个时间段"
                    else:
                        result['真实直落状态'] = '单时间段消费'
                        result['直落类型'] = '非直落'
                    
                except Exception as e:
                    result['实际消费时长'] = f"计算错误: {e}"
                    result['真实直落状态'] = '无法判断'
                    result['跨越时间段'] = []
                    result['跨越时间段数量'] = 0
            else:
                result['CloseDatetime'] = '未结账'
                result['实际消费时长'] = '未结账'
                result['真实直落状态'] = '未结账'
                result['跨越时间段'] = []
                result['跨越时间段数量'] = 0
            
            analysis_results.append(result)
        
        # 统计分析
        total_orders = len(analysis_results)
        checked_out = len([r for r in analysis_results if r['真实直落状态'] not in ['未结账', '无法判断']])
        real_direct_fall = len([r for r in analysis_results if r['真实直落状态'] == '✅ 真正直落'])
        single_slot = len([r for r in analysis_results if r['真实直落状态'] == '单时间段消费'])
        
        print(f"\n📈 分析结果统计:")
        print(f"  总订单数: {total_orders}")
        print(f"  已结账订单: {checked_out}")
        print(f"  真正直落订单: {real_direct_fall}")
        print(f"  单时间段消费: {single_slot}")
        print(f"  真实直落率: {real_direct_fall/checked_out*100:.1f}%" if checked_out > 0 else "  真实直落率: N/A")
        
        # 详细分析
        print(f"\n🔍 详细分析:")
        print(f"{'='*140}")
        print(f"{'订单号':<12} {'客户':<10} {'开台时间':<10} {'结账时间':<10} {'消费时长':<10} {'跨越时间段':<8} {'状态':<12} {'金额':<8}")
        print("-" * 140)
        
        for result in analysis_results:
            if result['CloseDatetime'] != '未结账':
                close_time = datetime.strptime(result['CloseDatetime'], '%Y-%m-%d %H:%M:%S.%f').strftime('%H:%M')
            else:
                close_time = '未结账'
            
            print(f"{result['InvNo']:<12} "
                  f"{result['CustName']:<10} "
                  f"{result['ComeTime'][:5]:<10} "
                  f"{close_time:<10} "
                  f"{result['实际消费时长']:<10} "
                  f"{result['跨越时间段数量']:<8} "
                  f"{result['真实直落状态']:<12} "
                  f"¥{result['Tot']:<7}" if result['Tot'] else f"{'未结账':<8}")
        
        # 直落订单详情
        direct_fall_orders = [r for r in analysis_results if r['真实直落状态'] == '✅ 真正直落']
        if direct_fall_orders:
            print(f"\n✅ 真正直落订单详情:")
            print(f"{'='*80}")
            for order in direct_fall_orders:
                print(f"\n【{order['InvNo']} - {order['CustName']}】")
                print(f"  开台时间: {order['ComeTime']}")
                print(f"  结账时间: {order['CloseDatetime']}")
                print(f"  消费时长: {order['实际消费时长']}")
                print(f"  预约情况: {order['预约状态']}")
                print(f"  跨越时间段: {[slot[1] for slot in order['跨越时间段']]}")
                print(f"  金额: ¥{order['Tot']}")
                print(f"  备注: {order['Remark']}")
        
        # 按时间段统计
        print(f"\n⏰ 时间段统计:")
        print(f"{'='*80}")
        
        time_slot_stats = {}
        for result in analysis_results:
            if result['真实直落状态'] not in ['未结账', '无法判断']:
                for slot_no, slot_name in result['跨越时间段']:
                    if slot_name not in time_slot_stats:
                        time_slot_stats[slot_name] = {'count': 0, 'revenue': 0}
                    time_slot_stats[slot_name]['count'] += 1
                    time_slot_stats[slot_name]['revenue'] += result['Tot'] if result['Tot'] else 0
        
        for slot_name, stats in sorted(time_slot_stats.items()):
            print(f"  {slot_name}: {stats['count']}次消费, ¥{stats['revenue']}")
        
        return {
            'total_orders': total_orders,
            'checked_out': checked_out,
            'real_direct_fall': real_direct_fall,
            'single_slot': single_slot,
            'analysis_results': analysis_results
        }

def main():
    analyzer = CorrectedConsumptionAnalyzer()
    
    try:
        # 基于实际消费时长的分析
        result = analyzer.analyze_sample_consumption()
        
        # 保存结果
        if result:
            with open('corrected_consumption_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n✅ 分析完成！")
            print(f"📄 详细结果已保存到: corrected_consumption_analysis.json")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
