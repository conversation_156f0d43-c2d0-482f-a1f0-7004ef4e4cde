#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整版联合报表存储过程（包含白天档详细时段数据）
"""

import pyodbc

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def create_complete_unified_procedure(connection):
    """创建完整版联合报表存储过程"""
    print("🚀 创建完整版联合报表存储过程...")
    
    # 先删除存储过程（如果存在）
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateCompleteUnifiedDailyReport', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateCompleteUnifiedDailyReport"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateCompleteUnifiedDailyReport
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    -- 创建临时表存储白天档完整数据
    CREATE TABLE #TempDaytimeReport (
        [日期] DATE,
        [门店] NVARCHAR(100),
        [星期] NVARCHAR(20),
        [营收_总收入] DECIMAL(18,2),
        [营收_白天档] DECIMAL(18,2),
        [营收_晚上档] DECIMAL(18,2),
        [全天总批数] INT,
        [白天档_总批次] INT,
        [晚上档_总批次] INT,
        [白天档_直落] INT,
        [晚上档_直落] INT,
        [自助餐人数] INT,
        [直落人数] INT,
        [11:50-14:50_K+] INT,
        [11:50-14:50_特权预约] INT,
        [11:50-14:50_美团] INT,
        [11:50-14:50_抖音] INT,
        [11:50-14:50_房费] INT,
        [11:50-14:50_小计] INT,
        [11:50-14:50_上档直落] INT,
        [13:30-16:30_K+] INT,
        [13:30-16:30_特权预约] INT,
        [13:30-16:30_美团] INT,
        [13:30-16:30_抖音] INT,
        [13:30-16:30_房费] INT,
        [13:30-16:30_小计] INT,
        [13:30-16:30_上档直落] INT,
        [15:00-18:00_K+] INT,
        [15:00-18:00_特权预约] INT,
        [15:00-18:00_美团] INT,
        [15:00-18:00_抖音] INT,
        [15:00-18:00_房费] INT,
        [15:00-18:00_小计] INT,
        [15:00-18:00_上档直落] INT,
        [17:00-20:00_K+] INT,
        [17:00-20:00_特权预约] INT,
        [17:00-20:00_美团] INT,
        [17:00-20:00_抖音] INT,
        [17:00-20:00_房费] INT,
        [17:00-20:00_小计] INT,
        [17:00-20:00_上档直落] INT,
        [18:00-21:00_K+] INT,
        [18:00-21:00_特权预约] INT,
        [18:00-21:00_美团] INT,
        [18:00-21:00_抖音] INT,
        [18:00-21:00_房费] INT,
        [18:00-21:00_小计] INT,
        [18:00-21:00_上档直落] INT,
        [18:10-21:10_K+] INT,
        [18:10-21:10_特权预约] INT,
        [18:10-21:10_美团] INT,
        [18:10-21:10_抖音] INT,
        [18:10-21:10_房费] INT,
        [18:10-21:10_小计] INT,
        [18:10-21:10_上档直落] INT,
        [19:00-22:00_K+] INT,
        [19:00-22:00_特权预约] INT,
        [19:00-22:00_美团] INT,
        [19:00-22:00_抖音] INT,
        [19:00-22:00_房费] INT,
        [19:00-22:00_小计] INT,
        [19:00-22:00_上档直落] INT,
        [19:00-21:30_K+] INT,
        [19:00-21:30_特权预约] INT,
        [19:00-21:30_美团] INT,
        [19:00-21:30_抖音] INT,
        [19:00-21:30_房费] INT,
        [19:00-21:30_小计] INT,
        [19:00-21:30_上档直落] INT,
        [k+餐批次] INT,
        [k+餐直落批数] INT,
        [17点 18点 19点档直落] INT
    );

    -- 插入白天档数据
    INSERT INTO #TempDaytimeReport
    EXEC dbo.usp_GenerateDaytimePivotedReport 
        @BeginDate = @BeginDate, 
        @EndDate = @EndDate, 
        @ShopId = @ShopId;

    -- 输出完整联合报表
    SELECT 
        -- 基础信息（来自白天档）
        dt.[日期],
        dt.[门店],
        dt.[星期],
        dt.[营收_总收入],
        dt.[营收_白天档],
        dt.[营收_晚上档],
        dt.[全天总批数],
        dt.[白天档_总批次],
        dt.[晚上档_总批次],
        dt.[白天档_直落],
        dt.[晚上档_直落],
        dt.[自助餐人数],
        dt.[直落人数],
        
        -- 白天档详细时段数据
        dt.[11:50-14:50_K+],
        dt.[11:50-14:50_特权预约],
        dt.[11:50-14:50_美团],
        dt.[11:50-14:50_抖音],
        dt.[11:50-14:50_房费],
        dt.[11:50-14:50_小计],
        dt.[11:50-14:50_上档直落],
        dt.[13:30-16:30_K+],
        dt.[13:30-16:30_特权预约],
        dt.[13:30-16:30_美团],
        dt.[13:30-16:30_抖音],
        dt.[13:30-16:30_房费],
        dt.[13:30-16:30_小计],
        dt.[13:30-16:30_上档直落],
        dt.[15:00-18:00_K+],
        dt.[15:00-18:00_特权预约],
        dt.[15:00-18:00_美团],
        dt.[15:00-18:00_抖音],
        dt.[15:00-18:00_房费],
        dt.[15:00-18:00_小计],
        dt.[15:00-18:00_上档直落],
        dt.[17:00-20:00_K+],
        dt.[17:00-20:00_特权预约],
        dt.[17:00-20:00_美团],
        dt.[17:00-20:00_抖音],
        dt.[17:00-20:00_房费],
        dt.[17:00-20:00_小计],
        dt.[17:00-20:00_上档直落],
        dt.[18:00-21:00_K+],
        dt.[18:00-21:00_特权预约],
        dt.[18:00-21:00_美团],
        dt.[18:00-21:00_抖音],
        dt.[18:00-21:00_房费],
        dt.[18:00-21:00_小计],
        dt.[18:00-21:00_上档直落],
        dt.[18:10-21:10_K+],
        dt.[18:10-21:10_特权预约],
        dt.[18:10-21:10_美团],
        dt.[18:10-21:10_抖音],
        dt.[18:10-21:10_房费],
        dt.[18:10-21:10_小计],
        dt.[18:10-21:10_上档直落],
        dt.[19:00-22:00_K+],
        dt.[19:00-22:00_特权预约],
        dt.[19:00-22:00_美团],
        dt.[19:00-22:00_抖音],
        dt.[19:00-22:00_房费],
        dt.[19:00-22:00_小计],
        dt.[19:00-22:00_上档直落],
        dt.[19:00-21:30_K+],
        dt.[19:00-21:30_特权预约],
        dt.[19:00-21:30_美团],
        dt.[19:00-21:30_抖音],
        dt.[19:00-21:30_房费],
        dt.[19:00-21:30_小计],
        dt.[19:00-21:30_上档直落],
        dt.[k+餐批次],
        dt.[k+餐直落批数],
        dt.[17点 18点 19点档直落],
        
        -- 晚上档数据（使用注释中的正确名称）
        ISNULL(nd.FreeMeal_KPlus, 0) AS [k+自由餐_k+],
        ISNULL(nd.FreeMeal_Special, 0) AS [k+自由餐_特权预约],
        ISNULL(nd.FreeMeal_Meituan, 0) AS [k+自由餐_美团],
        ISNULL(nd.FreeMeal_Douyin, 0) AS [k+自由餐_抖音],
        ISNULL(nd.FreeMeal_BatchCount, 0) AS [k+自由餐_小计],
        ISNULL(nd.FreeMeal_Revenue, 0) AS [k+自由餐_营业额],
        
        ISNULL(nd.Buyout_BatchCount, 0) AS [20点后_买断],
        ISNULL(nd.Buyout_Revenue, 0) AS [20点后_买断_营业额],
        
        ISNULL(nd.Changyin_BatchCount, 0) AS [20点后_畅饮],
        ISNULL(nd.Changyin_Revenue, 0) AS [20点后_畅饮_营业额],
        
        ISNULL(nd.FreeConsumption_BatchCount, 0) AS [20点后_自由消套餐],
        
        ISNULL(nd.NonPackage_Special, 0) AS [20点后_促销套餐_特权预约],
        ISNULL(nd.NonPackage_Meituan, 0) AS [20点后_促销套餐_美团],
        ISNULL(nd.NonPackage_Douyin, 0) AS [20点后_促销套餐_抖音],
        ISNULL(nd.NonPackage_Others, 0) AS [20点后其他批次],
        
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [招待批次],
        ISNULL(nd.DiscountFree_Revenue, 0) AS [招待金额],
        
        ISNULL(nd.Night_Verify_BatchCount, 0) AS [20点后_批次小计],
        ISNULL(nd.Night_Verify_Revenue, 0) AS [20点后_营收金额]
        
    FROM #TempDaytimeReport dt
    LEFT JOIN dbo.FullDailyReport_Header h ON dt.[日期] = h.ReportDate AND h.ShopID = @ShopId
    LEFT JOIN dbo.FullDailyReport_NightDetails nd ON h.ReportID = nd.ReportID
    ORDER BY dt.[日期];

    DROP TABLE #TempDaytimeReport;
END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 完整版联合报表存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建完整版存储过程失败: {str(e)}")
        return False

def test_complete_procedure(connection):
    """测试完整版联合报表存储过程"""
    print("\n🧪 测试完整版联合报表存储过程...")
    
    try:
        cursor = connection.cursor()
        
        # 测试完整版存储过程
        test_query = """
        EXEC dbo.usp_GenerateCompleteUnifiedDailyReport 
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24', 
            @ShopId = 11
        """
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        data = cursor.fetchone()
        
        print(f"✅ 完整版存储过程执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        
        if data:
            print(f"\n📋 基础信息:")
            basic_fields = columns[:13]  # 前13个是基础信息
            for col in basic_fields:
                col_index = columns.index(col)
                val = data[col_index] if col_index < len(data) else "N/A"
                print(f"   {col}: {val}")
            
            print(f"\n📋 晚上档数据:")
            night_fields = [col for col in columns if any(keyword in col for keyword in ['k+自由餐', '20点后', '招待'])]
            for col in night_fields:
                col_index = columns.index(col)
                val = data[col_index] if col_index < len(data) else "N/A"
                print(f"   {col}: {val}")
            
            print(f"\n📋 白天档时段数据（示例）:")
            time_slot_fields = [col for col in columns if any(time in col for time in ['11:50', '13:30', '15:00'])][:6]
            for col in time_slot_fields:
                col_index = columns.index(col)
                val = data[col_index] if col_index < len(data) else "N/A"
                print(f"   {col}: {val}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试完整版存储过程失败: {str(e)}")
        return False

def main():
    print("🚀 开始创建和测试完整版联合报表存储过程...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 创建完整版联合报表存储过程
        success = create_complete_unified_procedure(connection)
        
        if success:
            # 测试存储过程
            test_success = test_complete_procedure(connection)
            
            if test_success:
                print(f"\n🎉 完整版联合报表存储过程创建和测试完成！")
                print(f"📦 存储过程名: usp_GenerateCompleteUnifiedDailyReport")
                print(f"📋 参数: @BeginDate, @EndDate, @ShopId (默认11)")
                print(f"🏷️ 特点: 包含白天档所有时段数据 + 晚上档优化字段名")
                print(f"📊 包含: 基础信息 + 白天档详细时段 + 晚上档业务数据")
                
                print(f"\n📋 现在你有两个联合报表存储过程:")
                print(f"   1. usp_GenerateUnifiedDailyReport_Simple - 简化版（32个字段）")
                print(f"   2. usp_GenerateCompleteUnifiedDailyReport - 完整版（包含所有白天档时段数据）")
            else:
                print("\n❌ 完整版存储过程测试失败")
        else:
            print("\n❌ 完整版存储过程创建失败")
    
    except Exception as e:
        print(f"❌ 过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
