
import pyodbc

SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

# 最纯粹的、不含GO的SQL语句
SQL_COMMAND = """
IF OBJECT_ID('RoomStatusHourlyDetail', 'U') IS NULL
BEGIN
    CREATE TABLE RoomStatusHourlyDetail (
        DetailID INT IDENTITY(1,1) PRIMARY KEY,
        LogID INT NOT NULL,
        RmStatus VARCHAR(10) NOT NULL,
        StatusCount INT NOT NULL,
        CONSTRAINT FK_RoomStatusHourlyDetail_LogID FOREIGN KEY (LogID) REFERENCES RoomStatisticsHourly(LogID) ON DELETE CASCADE
    );
    PRINT 'Table RoomStatusHourlyDetail created successfully.';
END
ELSE
BEGIN
    PRINT 'Table RoomStatusHourlyDetail already exists.';
END
"""

def create_table_directly():
    connection_string = (
        f'DRIVER={{ODBC Driver 17 for SQL Server}};'
        f'SERVER={SERVER};'
        f'DATABASE={DATABASE};'
        f'UID={USERNAME};'
        f'PWD={PASSWORD};'
        f'TrustServerCertificate=yes;'
    )
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Connected to {SERVER} ---')
            cursor.execute(SQL_COMMAND)
            print("--- Detail table creation script executed. ---")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    create_table_directly()
