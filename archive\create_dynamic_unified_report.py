#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建动态联合报表存储过程（支持动态时段获取和SQL结果展示）
"""

import pyodbc

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def create_dynamic_unified_procedure(connection):
    """创建动态联合报表存储过程"""
    print("🚀 创建动态联合报表存储过程...")
    
    # 先删除存储过程（如果存在）
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateDynamicUnifiedDailyReport', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateDynamicUnifiedDailyReport"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateDynamicUnifiedDailyReport
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX) = N'';
    DECLARE @pivot_columns NVARCHAR(MAX) = N'';

    -- 1. 动态获取该店铺的白天档时段（与usp_GenerateDaytimePivotedReport相同逻辑）
    SELECT @pivot_columns = @pivot_columns + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.KPlus_Count END), 0) AS [' + ti.TimeName + '_K+]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Special_Count END), 0) AS [' + ti.TimeName + '_特权预约]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Meituan_Count END), 0) AS [' + ti.TimeName + '_美团]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Douyin_Count END), 0) AS [' + ti.TimeName + '_抖音]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.RoomFee_Count END), 0) AS [' + ti.TimeName + '_房费]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Subtotal_Count END), 0) AS [' + ti.TimeName + '_小计]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.PreviousSlot_DirectFall END), 0) AS [' + ti.TimeName + '_上档直落]'
    FROM dbo.shoptimeinfo sti
    JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
    WHERE sti.Shopid = @ShopId AND sti.TimeMode = 1 -- 白天档时段
    ORDER BY ti.BegTime;

    -- 2. 构建动态SQL查询
    SET @sql = N'
    SELECT
        -- 基础信息
        h.ReportDate AS [日期],
        h.ShopName AS [门店],
        h.Weekday AS [星期],
        ISNULL(h.TotalRevenue, 0) AS [营收_总收入],
        ISNULL(h.DayTimeRevenue, 0) AS [营收_白天档],
        ISNULL(h.NightTimeRevenue, 0) AS [营收_晚上档],
        ISNULL(h.TotalBatchCount, 0) AS [全天总批数],
        ISNULL(h.DayTimeBatchCount, 0) AS [白天档_总批次],
        ISNULL(h.NightTimeBatchCount, 0) AS [晚上档_总批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [白天档_直落],
        ISNULL(h.NightTimeDropInBatch, 0) AS [晚上档_直落],
        ISNULL(h.BuffetGuestCount, 0) AS [自助餐人数],
        ISNULL(h.TotalDirectFallGuests, 0) AS [直落人数]'
        + @pivot_columns + ',
        -- 白天档汇总字段
        ISNULL(h.DayTimeBatchCount, 0) AS [k+餐批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [k+餐直落批数],
        ISNULL(h.NightTimeDropInBatch, 0) AS [17点 18点 19点档直落],
        
        -- 晚上档数据（使用注释中的正确名称）
        ISNULL(nd.FreeMeal_KPlus, 0) AS [k+自由餐_k+],
        ISNULL(nd.FreeMeal_Special, 0) AS [k+自由餐_特权预约],
        ISNULL(nd.FreeMeal_Meituan, 0) AS [k+自由餐_美团],
        ISNULL(nd.FreeMeal_Douyin, 0) AS [k+自由餐_抖音],
        ISNULL(nd.FreeMeal_BatchCount, 0) AS [k+自由餐_小计],
        ISNULL(nd.FreeMeal_Revenue, 0) AS [k+自由餐_营业额],
        
        ISNULL(nd.Buyout_BatchCount, 0) AS [20点后_买断],
        ISNULL(nd.Buyout_Revenue, 0) AS [20点后_买断_营业额],
        
        ISNULL(nd.Changyin_BatchCount, 0) AS [20点后_畅饮],
        ISNULL(nd.Changyin_Revenue, 0) AS [20点后_畅饮_营业额],
        
        ISNULL(nd.FreeConsumption_BatchCount, 0) AS [20点后_自由消套餐],
        
        ISNULL(nd.NonPackage_Special, 0) AS [20点后_促销套餐_特权预约],
        ISNULL(nd.NonPackage_Meituan, 0) AS [20点后_促销套餐_美团],
        ISNULL(nd.NonPackage_Douyin, 0) AS [20点后_促销套餐_抖音],
        ISNULL(nd.NonPackage_Others, 0) AS [20点后其他批次],
        
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [招待批次],
        ISNULL(nd.DiscountFree_Revenue, 0) AS [招待金额],
        
        ISNULL(nd.Night_Verify_BatchCount, 0) AS [20点后_批次小计],
        ISNULL(nd.Night_Verify_Revenue, 0) AS [20点后_营收金额]
        
    FROM
        dbo.FullDailyReport_Header AS h
    LEFT JOIN
        dbo.FullDailyReport_TimeSlotDetails AS d ON h.ReportID = d.ReportID
    LEFT JOIN
        dbo.FullDailyReport_NightDetails AS nd ON h.ReportID = nd.ReportID
    WHERE 
        h.ReportDate BETWEEN ''' + CONVERT(NVARCHAR, @BeginDate, 23) + ''' AND ''' + CONVERT(NVARCHAR, @EndDate, 23) + '''
        AND h.ShopID = ' + CAST(@ShopId AS NVARCHAR) + '
    GROUP BY
        h.ReportDate, h.ShopName, h.Weekday, h.TotalRevenue, h.DayTimeRevenue, h.NightTimeRevenue,
        h.TotalBatchCount, h.DayTimeBatchCount, h.NightTimeBatchCount, h.DayTimeDropInBatch, 
        h.NightTimeDropInBatch, h.BuffetGuestCount, h.TotalDirectFallGuests,
        nd.FreeMeal_KPlus, nd.FreeMeal_Special, nd.FreeMeal_Meituan, nd.FreeMeal_Douyin,
        nd.FreeMeal_BatchCount, nd.FreeMeal_Revenue, nd.Buyout_BatchCount, nd.Buyout_Revenue,
        nd.Changyin_BatchCount, nd.Changyin_Revenue, nd.FreeConsumption_BatchCount,
        nd.NonPackage_Special, nd.NonPackage_Meituan, nd.NonPackage_Douyin, nd.NonPackage_Others,
        nd.DiscountFree_BatchCount, nd.DiscountFree_Revenue, nd.Night_Verify_BatchCount, nd.Night_Verify_Revenue
    ORDER BY h.ReportDate';

    -- 3. 执行动态SQL
    EXEC sp_executesql @sql;
END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 动态联合报表存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建动态存储过程失败: {str(e)}")
        return False

def test_dynamic_procedure(connection):
    """测试动态联合报表存储过程并显示完整结果"""
    print("\n🧪 测试动态联合报表存储过程...")
    
    try:
        cursor = connection.cursor()
        
        # 测试动态存储过程
        test_query = """
        EXEC dbo.usp_GenerateDynamicUnifiedDailyReport 
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24', 
            @ShopId = 11
        """
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取所有数据
        rows = cursor.fetchall()
        
        print(f"✅ 动态存储过程执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        print(f"📊 返回行数: {len(rows)}")
        
        # 显示完整的SQL结果
        print(f"\n📋 完整SQL结果:")
        print("=" * 120)
        
        # 显示列头
        header = " | ".join([f"{col:15}" for col in columns[:8]])  # 显示前8列作为示例
        print(header)
        print("-" * len(header))
        
        # 显示数据行
        for row in rows:
            row_data = " | ".join([f"{str(val):15}" for val in row[:8]])  # 显示前8列数据
            print(row_data)
        
        print("=" * 120)
        
        # 详细显示所有字段和数据
        if rows:
            print(f"\n📋 详细字段数据（第一行）:")
            for i, (col, val) in enumerate(zip(columns, rows[0]), 1):
                print(f"   {i:2d}. {col}: {val}")
        
        # 保存结果到文件
        print(f"\n💾 保存结果到CSV文件...")
        import csv
        filename = f"动态联合报表_店铺{11}_20250724.csv"
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            # 写入列头
            writer.writerow(columns)
            # 写入数据
            for row in rows:
                writer.writerow(row)
        
        print(f"✅ 结果已保存到: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试动态存储过程失败: {str(e)}")
        return False

def show_procedure_sql(connection):
    """显示存储过程的完整SQL定义"""
    print("\n📄 显示存储过程SQL定义...")
    
    try:
        cursor = connection.cursor()
        
        # 获取存储过程定义
        query = """
        SELECT OBJECT_DEFINITION(OBJECT_ID('dbo.usp_GenerateDynamicUnifiedDailyReport'))
        """
        
        cursor.execute(query)
        proc_def = cursor.fetchone()
        
        if proc_def and proc_def[0]:
            print("📋 存储过程完整定义:")
            print("=" * 80)
            print(proc_def[0])
            print("=" * 80)
            
            # 保存到文件
            with open('usp_GenerateDynamicUnifiedDailyReport.sql', 'w', encoding='utf-8') as f:
                f.write(proc_def[0])
            print("✅ 存储过程定义已保存到: usp_GenerateDynamicUnifiedDailyReport.sql")
        else:
            print("❌ 未找到存储过程定义")
            
    except Exception as e:
        print(f"❌ 获取存储过程定义失败: {str(e)}")

def main():
    print("🚀 开始创建动态联合报表存储过程...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 创建动态联合报表存储过程
        success = create_dynamic_unified_procedure(connection)
        
        if success:
            # 显示存储过程SQL定义
            show_procedure_sql(connection)
            
            # 测试存储过程并显示完整结果
            test_success = test_dynamic_procedure(connection)
            
            if test_success:
                print(f"\n🎉 动态联合报表存储过程创建和测试完成！")
                print(f"📦 存储过程名: usp_GenerateDynamicUnifiedDailyReport")
                print(f"📋 参数: @BeginDate, @EndDate, @ShopId (默认11)")
                print(f"🔧 特点: 动态获取店铺时段配置，支持不同店铺的时段差异")
                print(f"📊 输出: 完整的SQL结果集，可直接查看和导出")
                print(f"💾 文件: 结果已保存为CSV文件，存储过程定义已保存为SQL文件")
                
                print(f"\n📋 使用方法:")
                print(f"   直接执行: EXEC dbo.usp_GenerateDynamicUnifiedDailyReport '2025-07-24', '2025-07-24', 11")
                print(f"   查看结果: 存储过程会直接返回SQL结果集")
                print(f"   导出数据: 可以直接复制结果或使用生成的CSV文件")
            else:
                print("\n❌ 动态存储过程测试失败")
        else:
            print("\n❌ 动态存储过程创建失败")
    
    except Exception as e:
        print(f"❌ 过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
