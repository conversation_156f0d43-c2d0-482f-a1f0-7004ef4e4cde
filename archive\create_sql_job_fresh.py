
import pyodbc

# --- 连接配置 ---
SERVER = '193.112.2.229'
DATABASE = 'msdb'  # 作业相关的操作需要在 msdb 数据库中执行
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

# --- SQL 创建作业的语句 ---
SQL_COMMAND = """
DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'LogRoomStats_EveryHour';

-- 如果作业已存在，则先删除
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    EXEC dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
END

-- 添加作业
EXEC dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @description = N'每小时统计一次KTV房间状态（总数、坏房、可用数）并记录到RoomStatisticsHourly表中。';

-- 添加作业步骤
EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'Execute Room Statistics Logging',
    @subsystem = N'TSQL',
    @command = N'EXEC dbfood.dbo.usp_LogHourlyRoomStatistics;',
    @database_name = N'dbfood';

-- 创建执行计划
EXEC dbo.sp_add_schedule
    @schedule_name = N'Run Every Hour',
    @freq_type = 4, -- 每天
    @freq_interval = 1,
    @freq_subday_type = 8, -- 小时
    @freq_subday_interval = 1; -- 每1小时

-- 将计划附加到作业
EXEC dbo.sp_attach_schedule
    @job_name = @jobName,
    @schedule_name = N'Run Every Hour';

-- 将作业分配给当前服务器
EXEC dbo.sp_add_jobserver
    @job_name = @jobName,
    @server_name = N'(local)';
"""

def create_sql_job_safely():
    """连接数据库并执行创建SQL Agent作业的语句"""
    connection_string = (
        'DRIVER={ODBC Driver 17 for SQL Server};'
        f'SERVER={SERVER};'
        f'DATABASE={DATABASE};'
        f'UID={USERNAME};'
        f'PWD={PASSWORD};'
        f'TrustServerCertificate=yes;'
    )

    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} (msdb database) ---')
            cursor.execute(SQL_COMMAND)
            print("--- SQL Server Agent job 'LogRoomStats_EveryHour' created successfully. ---")

    except pyodbc.Error as ex:
        print(f"A database error occurred: {ex}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    create_sql_job_safely()
