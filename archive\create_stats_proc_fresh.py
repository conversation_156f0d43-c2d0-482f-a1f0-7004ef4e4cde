
import pyodbc

# --- 连接配置 ---
SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

# --- SQL 创建存储过程语句 ---
SQL_COMMAND = """
IF OBJECT_ID('usp_LogHourlyRoomStatistics', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE usp_LogHourlyRoomStatistics;
END
GO

CREATE PROCEDURE usp_LogHourlyRoomStatistics
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @TotalRoomsBeforeFilter INT;
    DECLARE @ValidRoomsCount INT;
    DECLARE @BadRoomsCount INT;

    -- 计算过滤前的总房数
    SELECT @TotalRoomsBeforeFilter = COUNT(*) FROM dbo.ROOM;

    -- 在排除了房间名带B的房间后，进行统计
    SELECT 
        @ValidRoomsCount = COUNT(*),
        @BadRoomsCount = SUM(CASE WHEN RmStatus = 'B' THEN 1 ELSE 0 END)
    FROM 
        dbo.ROOM
    WHERE 
        RmNo NOT LIKE '%B';

    -- 插入统计结果到新表
    INSERT INTO dbo.RoomStatisticsHourly (
        TotalRoomsBeforeFilter,
        ValidRoomsCount,
        BadRoomsCount,
        AvailableRoomsCount
    ) 
    VALUES (
        @TotalRoomsBeforeFilter,
        ISNULL(@ValidRoomsCount, 0),
        ISNULL(@BadRoomsCount, 0),
        ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0)
    );
END
"""

def create_procedure_safely():
    """连接数据库并执行创建存储过程的语句"""
    connection_string = (
        'DRIVER={ODBC Driver 17 for SQL Server};'
        f'SERVER={SERVER};'
        f'DATABASE={DATABASE};'
        f'UID={USERNAME};'
        f'PWD={PASSWORD};'
        'TrustServerCertificate=yes;'
    )

    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} ---')
            
            # 按GO分割命令
            commands = SQL_COMMAND.split('GO')
            for command in commands:
                if command.strip():
                    cursor.execute(command)
            
            print("--- Stored procedure usp_LogHourlyRoomStatistics created successfully. ---")

    except pyodbc.Error as ex:
        print(f"A database error occurred: {ex}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    create_procedure_safely()
