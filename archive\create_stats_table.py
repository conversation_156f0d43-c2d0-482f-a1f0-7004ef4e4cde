
import pyodbc

# --- Connection Configuration ---
SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

# --- SQL Query ---
SQL_QUERY = """
IF OBJECT_ID('RoomStatisticsHourly', 'U') IS NOT NULL
BEGIN
    PRINT 'Table RoomStatisticsHourly already exists.';
END
ELSE
BEGIN
    CREATE TABLE RoomStatisticsHourly (
        LogID INT IDENTITY(1,1) PRIMARY KEY,
        LogTime DATETIME NOT NULL DEFAULT GETDATE(),
        TotalRoomsBeforeFilter INT,
        ValidRoomsCount INT,
        BadRoomsCount INT,
        AvailableRoomsCount INT
    );
    PRINT 'Table RoomStatisticsHourly created successfully.';
END
"

def create_table():
    """Connects to the database and creates the statistics table."""
    connection_string = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}} প্রশিক্ষক
        f"SERVER={SERVER};"
        f"DATABASE={DATABASE};"
        f"UID={USERNAME};"
        f"PWD={PASSWORD};"
        f"TrustServerCertificate=yes;"
    )

    try:
        with pyodbc.connect(connection_string, timeout=10) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} ---
')
            
            cursor.execute(SQL_QUERY)
            conn.commit()
            print("--- Table creation script executed successfully ---")

    except pyodbc.Error as ex:
        print(f"A database error occurred: {ex}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    create_table()
