
import pyodbc

# --- 连接配置 ---
SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

# --- SQL 建表语句 ---
SQL_COMMAND = """
IF OBJECT_ID('RoomStatisticsHourly', 'U') IS NOT NULL
BEGIN
    PRINT 'Table RoomStatisticsHourly already exists.';
END
ELSE
BEGIN
    CREATE TABLE RoomStatisticsHourly (
        LogID INT IDENTITY(1,1) PRIMARY KEY,
        LogTime DATETIME NOT NULL DEFAULT GETDATE(),
        TotalRoomsBeforeFilter INT,
        ValidRoomsCount INT,
        BadRoomsCount INT,
        AvailableRoomsCount INT
    );
    PRINT 'Table RoomStatisticsHourly created successfully.';
END
"""

def create_table_safely():
    """使用最稳健的方式连接数据库并执行建表语句"""
    # 使用最简单、最不可能出错的字符串拼接方式
    connection_string = (
        'DRIVER={ODBC Driver 17 for SQL Server};'
        'SERVER=' + SERVER + ';'
        'DATABASE=' + DATABASE + ';'
        'UID=' + USERNAME + ';'
        'PWD=' + PASSWORD + ';'
        'TrustServerCertificate=yes;'
    )

    try:
        print(f"Attempting to connect to {SERVER}...")
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            print(f'--- Successfully connected to {SERVER} ---')
            cursor = conn.cursor()
            print("Executing table creation script...")
            cursor.execute(SQL_COMMAND)
            print("--- Script executed. Table should be created or already exist. ---")

    except pyodbc.Error as ex:
        print(f"A database error occurred: {ex}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    create_table_safely()
