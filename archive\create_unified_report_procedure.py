#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建联合报表存储过程并测试
"""

import pyodbc

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def create_unified_report_procedure(connection):
    """创建联合报表存储过程"""
    print("🚀 创建联合报表存储过程...")
    
    # 先删除存储过程（如果存在）
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateUnifiedDailyReport', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateUnifiedDailyReport"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateUnifiedDailyReport
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    -- 创建临时表存储白天档数据
    CREATE TABLE #DaytimeData (
        [日期] DATE,
        [门店] NVARCHAR(100),
        [星期] NVARCHAR(20),
        [营收_总收入] DECIMAL(18,2),
        [营收_白天档] DECIMAL(18,2),
        [营收_晚上档] DECIMAL(18,2),
        [全天总批数] INT,
        [白天档_总批次] INT,
        [晚上档_总批次] INT,
        [白天档_直落] INT,
        [晚上档_直落] INT,
        [自助餐人数] INT,
        [直落人数] INT,
        -- 动态时段字段会通过INSERT INTO添加
        [其他白天档字段] NVARCHAR(MAX)  -- 占位符，实际会被动态字段替换
    );

    -- 获取白天档数据（使用现有存储过程）
    INSERT INTO #DaytimeData
    EXEC dbo.usp_GenerateDaytimePivotedReport 
        @BeginDate = @BeginDate, 
        @EndDate = @EndDate, 
        @ShopId = @ShopId;

    -- 输出联合报表
    SELECT 
        -- 基础信息（来自白天档）
        dt.[日期],
        dt.[门店],
        dt.[星期],
        dt.[营收_总收入],
        dt.[营收_白天档],
        dt.[营收_晚上档],
        dt.[全天总批数],
        dt.[白天档_总批次],
        dt.[晚上档_总批次],
        dt.[白天档_直落],
        dt.[晚上档_直落],
        dt.[自助餐人数],
        dt.[直落人数],
        
        -- 晚上档数据（使用注释中的正确名称）
        ISNULL(nd.FreeMeal_KPlus, 0) AS [k+自由餐_k+],
        ISNULL(nd.FreeMeal_Special, 0) AS [k+自由餐_特权预约],
        ISNULL(nd.FreeMeal_Meituan, 0) AS [k+自由餐_美团],
        ISNULL(nd.FreeMeal_Douyin, 0) AS [k+自由餐_抖音],
        ISNULL(nd.FreeMeal_BatchCount, 0) AS [k+自由餐_小计],
        ISNULL(nd.FreeMeal_Revenue, 0) AS [k+自由餐_营业额],
        
        ISNULL(nd.Buyout_BatchCount, 0) AS [20点后_买断],
        ISNULL(nd.Buyout_Revenue, 0) AS [20点后_买断_营业额],
        
        ISNULL(nd.Changyin_BatchCount, 0) AS [20点后_畅饮],
        ISNULL(nd.Changyin_Revenue, 0) AS [20点后_畅饮_营业额],
        
        ISNULL(nd.FreeConsumption_BatchCount, 0) AS [20点后_自由消套餐],
        
        ISNULL(nd.NonPackage_Special, 0) AS [20点后_促销套餐_特权预约],
        ISNULL(nd.NonPackage_Meituan, 0) AS [20点后_促销套餐_美团],
        ISNULL(nd.NonPackage_Douyin, 0) AS [20点后_促销套餐_抖音],
        ISNULL(nd.NonPackage_Others, 0) AS [20点后其他批次],
        
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [招待批次],
        ISNULL(nd.DiscountFree_Revenue, 0) AS [招待金额],
        
        ISNULL(nd.Night_Verify_BatchCount, 0) AS [20点后_批次小计],
        ISNULL(nd.Night_Verify_Revenue, 0) AS [20点后_营收金额]
        
    FROM #DaytimeData dt
    LEFT JOIN dbo.FullDailyReport_Header h ON dt.[日期] = h.ReportDate AND h.ShopID = @ShopId
    LEFT JOIN dbo.FullDailyReport_NightDetails nd ON h.ReportID = nd.ReportID
    ORDER BY dt.[日期];

    DROP TABLE #DaytimeData;
END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 联合报表存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建存储过程失败: {str(e)}")
        return False

def create_simplified_unified_procedure(connection):
    """创建简化版联合报表存储过程（避免临时表问题）"""
    print("🚀 创建简化版联合报表存储过程...")
    
    # 先删除存储过程（如果存在）
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateUnifiedDailyReport_Simple', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateUnifiedDailyReport_Simple"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateUnifiedDailyReport_Simple
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    -- 直接查询联合数据，不使用临时表
    SELECT 
        -- 基础信息
        h.ReportDate AS [日期],
        h.ShopName AS [门店],
        h.Weekday AS [星期],
        ISNULL(h.TotalRevenue, 0) AS [营收_总收入],
        ISNULL(h.DayTimeRevenue, 0) AS [营收_白天档],
        ISNULL(h.NightTimeRevenue, 0) AS [营收_晚上档],
        ISNULL(h.TotalBatchCount, 0) AS [全天总批数],
        ISNULL(h.DayTimeBatchCount, 0) AS [白天档_总批次],
        ISNULL(h.NightTimeBatchCount, 0) AS [晚上档_总批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [白天档_直落],
        ISNULL(h.NightTimeDropInBatch, 0) AS [晚上档_直落],
        ISNULL(h.BuffetGuestCount, 0) AS [自助餐人数],
        ISNULL(h.TotalDirectFallGuests, 0) AS [直落人数],
        
        -- 晚上档数据（使用注释中的正确名称）
        ISNULL(nd.FreeMeal_KPlus, 0) AS [k+自由餐_k+],
        ISNULL(nd.FreeMeal_Special, 0) AS [k+自由餐_特权预约],
        ISNULL(nd.FreeMeal_Meituan, 0) AS [k+自由餐_美团],
        ISNULL(nd.FreeMeal_Douyin, 0) AS [k+自由餐_抖音],
        ISNULL(nd.FreeMeal_BatchCount, 0) AS [k+自由餐_小计],
        ISNULL(nd.FreeMeal_Revenue, 0) AS [k+自由餐_营业额],
        
        ISNULL(nd.Buyout_BatchCount, 0) AS [20点后_买断],
        ISNULL(nd.Buyout_Revenue, 0) AS [20点后_买断_营业额],
        
        ISNULL(nd.Changyin_BatchCount, 0) AS [20点后_畅饮],
        ISNULL(nd.Changyin_Revenue, 0) AS [20点后_畅饮_营业额],
        
        ISNULL(nd.FreeConsumption_BatchCount, 0) AS [20点后_自由消套餐],
        
        ISNULL(nd.NonPackage_Special, 0) AS [20点后_促销套餐_特权预约],
        ISNULL(nd.NonPackage_Meituan, 0) AS [20点后_促销套餐_美团],
        ISNULL(nd.NonPackage_Douyin, 0) AS [20点后_促销套餐_抖音],
        ISNULL(nd.NonPackage_Others, 0) AS [20点后其他批次],
        
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [招待批次],
        ISNULL(nd.DiscountFree_Revenue, 0) AS [招待金额],
        
        ISNULL(nd.Night_Verify_BatchCount, 0) AS [20点后_批次小计],
        ISNULL(nd.Night_Verify_Revenue, 0) AS [20点后_营收金额]
        
    FROM dbo.FullDailyReport_Header h
    LEFT JOIN dbo.FullDailyReport_NightDetails nd ON h.ReportID = nd.ReportID
    WHERE h.ReportDate BETWEEN @BeginDate AND @EndDate
        AND h.ShopID = @ShopId
    ORDER BY h.ReportDate;
END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 简化版联合报表存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建简化版存储过程失败: {str(e)}")
        return False

def test_unified_procedure(connection):
    """测试联合报表存储过程"""
    print("\n🧪 测试联合报表存储过程...")
    
    try:
        cursor = connection.cursor()
        
        # 测试简化版存储过程
        test_query = """
        EXEC dbo.usp_GenerateUnifiedDailyReport_Simple 
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24', 
            @ShopId = 11
        """
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        data = cursor.fetchone()
        
        print(f"✅ 存储过程执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        
        print(f"\n📋 字段列表:")
        for i, col in enumerate(columns, 1):
            print(f"   {i:2d}. {col}")
        
        if data:
            print(f"\n📋 数据预览（前10个字段）:")
            for i, (col, val) in enumerate(zip(columns[:10], data[:10])):
                print(f"   {col}: {val}")
            
            print(f"\n📋 晚上档数据预览:")
            night_fields = [col for col in columns if any(keyword in col for keyword in ['k+自由餐', '20点后', '招待'])]
            for col in night_fields:
                col_index = columns.index(col)
                val = data[col_index] if col_index < len(data) else "N/A"
                print(f"   {col}: {val}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试存储过程失败: {str(e)}")
        return False

def main():
    print("🚀 开始创建和测试联合报表存储过程...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 创建简化版联合报表存储过程
        success = create_simplified_unified_procedure(connection)
        
        if success:
            # 测试存储过程
            test_success = test_unified_procedure(connection)
            
            if test_success:
                print(f"\n🎉 联合报表存储过程创建和测试完成！")
                print(f"📦 存储过程名: usp_GenerateUnifiedDailyReport_Simple")
                print(f"📋 参数: @BeginDate, @EndDate, @ShopId (默认11)")
                print(f"🏷️ 特点: 使用FullDailyReport_NightDetails表注释作为字段名")
                print(f"📊 包含: 基础信息 + 优化的晚上档字段名")
            else:
                print("\n❌ 存储过程测试失败")
        else:
            print("\n❌ 存储过程创建失败")
    
    except Exception as e:
        print(f"❌ 过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
