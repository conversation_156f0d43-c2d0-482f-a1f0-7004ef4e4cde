import pyodbc
import pandas as pd
import sys

# --- 数据源 1: operatedata (用于获取缺失InvNo列表) ---
OPERATEDATA_SERVER = '192.168.2.5'
OPERATEDATA_DB = 'operatedata'
OPERATEDATA_USER = 'sa'
OPERATEDATA_PASS = 'Musicbox123'

# --- 数据源 2: 名堂本地数据库 (用于调查) ---
RMS_SERVER = '193.112.2.229'
RMS_USER = 'sa'
RMS_PASS = 'Musicbox@123'
RMS_DB = 'rms2019'      # 开台数据所在库
FOOD_DB = 'Dbfood'       # 结账和账单详情所在库

# --- 查询参数 ---
TARGET_SHOP_ID = 11
TARGET_WORK_DATE = '20250723'

# --- 输出文件名 ---
OUTPUT_FILENAME = 'missing_invno_investigation_report.csv'

def get_missing_invoices():
    """第一步：从 operatedata 获取缺失 OpenDateTime 的 InvNo 列表。"""
    print("--- 步骤 1: 从 operatedata 获取缺失 InvNo 列表 ---")
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={OPERATEDATA_SERVER};DATABASE={OPERATEDATA_DB};UID={OPERATEDATA_USER};PWD={OPERATEDATA_PASS}'
        cnxn = pyodbc.connect(conn_str)
        sql_query = "SELECT InvNo FROM dbo.RmCloseInfo WHERE ShopId = ? AND WorkDate = ? AND OpenDateTime IS NULL;"
        df = pd.read_sql_query(sql_query, cnxn, params=[TARGET_SHOP_ID, TARGET_WORK_DATE])
        invoice_list = df['InvNo'].tolist()
        print(f"成功获取到 {len(invoice_list)} 个需要调查的 InvNo。")
        return invoice_list
    finally:
        if cnxn: cnxn.close()

def investigate_invoices(invoice_list):
    """第二步：连接到名堂本地数据库，逐一核查每个InvNo。"""
    print(f"\n--- 步骤 2: 连接到服务器 {RMS_SERVER} 进行调查 ---")
    cnxn = None
    investigation_results = []
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={RMS_SERVER};DATABASE={RMS_DB};UID={RMS_USER};PWD=**********'
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("数据库连接成功！开始逐一核查...")

        for i, invno in enumerate(invoice_list):
            sys.stdout.write(f"\r正在核查: {i+1}/{len(invoice_list)} (InvNo: {invno}) ")
            sys.stdout.flush()

            # 检查 opencacheinfo (开台数据源)
            cursor.execute(f"SELECT COUNT(*) FROM {RMS_DB}.dbo.opencacheinfo WHERE InvNo = ?", invno)
            found_in_opencache = cursor.fetchone()[0] > 0

            # 检查 rmcloseinfo (结账数据)
            cursor.execute(f"SELECT COUNT(*) FROM {FOOD_DB}.dbo.rmcloseinfo WHERE InvNo = ?", invno)
            found_in_rmcloseinfo = cursor.fetchone()[0] > 0

            # 检查 fdinv (账单详情)
            cursor.execute(f"SELECT COUNT(*) FROM {FOOD_DB}.dbo.fdinv WHERE InvNo = ?", invno)
            found_in_fdinv = cursor.fetchone()[0] > 0
            
            investigation_results.append({
                '缺失的InvNo': invno,
                '是否存在于_rms2019_opencacheinfo': '是' if found_in_opencache else '否',
                '是否存在于_Dbfood_rmcloseinfo': '是' if found_in_rmcloseinfo else '否',
                '是否存在于_Dbfood_fdinv': '是' if found_in_fdinv else '否'
            })
        
        print("\n所有InvNo核查完毕！")
        return pd.DataFrame(investigation_results)

    finally:
        if cnxn: cnxn.close()

if __name__ == '__main__':
    try:
        missing_invoices = get_missing_invoices()
        if not missing_invoices:
            print("没有需要调查的InvNo，程序退出。")
            sys.exit(0)
        
        report_df = investigate_invoices(missing_invoices)
        
        print(f"\n--- 步骤 3: 生成并保存调查报告 ---")
        report_df.to_csv(OUTPUT_FILENAME, index=False, encoding='utf-8-sig')
        
        print("\n--- 最终报告 ---")
        print(report_df.to_string())
        print(f"\n调查报告已成功保存至文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)
