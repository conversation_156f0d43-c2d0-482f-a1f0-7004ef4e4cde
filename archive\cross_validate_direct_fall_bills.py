import pyodbc
import pandas as pd
import sys

# --- 数据源 1: operatedata (获取线索InvNo) ---
OPERATEDATA_SERVER = '192.168.2.5'
OPERATEDATA_DB = 'operatedata'
OPERATEDATA_USER = 'sa'
OPERATEDATA_PASS = 'Musicbox123'

# --- 数据源 2: 名堂本地数据库 (交叉验证) ---
RMS_SERVER = '193.112.2.229'
RMS_USER = 'sa'
RMS_PASS = 'Musicbox@123'
RMS_DB = 'rms2019'
FOOD_DB = 'Dbfood'

# --- 查询参数 ---
TARGET_SHOP_ID = 11
TARGET_DATES = ['20250718', '20250719', '20250720']

# --- 输出文件名 ---
OUTPUT_FILENAME = 'cross_validation_report_0718_0720.csv'

def get_clue_invoices():
    """第一步：从 operatedata 获取包含‘直落’的账单InvNo列表。"""
    print("--- 步骤 1: 从 operatedata 获取‘直落’账单列表 ---")
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={OPERATEDATA_SERVER};DATABASE={OPERATEDATA_DB};UID={OPERATEDATA_USER};PWD={OPERATEDATA_PASS}'
        cnxn = pyodbc.connect(conn_str)
        placeholders = ', '.join('?' * len(TARGET_DATES))
        sql_query = f"""SELECT DISTINCT rc.InvNo FROM dbo.RmCloseInfo_Test rc
                         JOIN dbo.FdCashBak fb ON rc.InvNo = fb.InvNo
                         WHERE rc.ShopId = ? AND rc.WorkDate IN ({placeholders}) AND fb.FdCName LIKE ?;"""
        params = [TARGET_SHOP_ID] + TARGET_DATES + ['%直落%']
        df = pd.read_sql_query(sql_query, cnxn, params=params)
        invoice_list = df['InvNo'].tolist()
        print(f"成功获取到 {len(invoice_list)} 张‘直落’账单作为调查线索。")
        return invoice_list
    finally:
        if cnxn: cnxn.close()

def cross_validate_invoices(invoice_list):
    """第二步：连接到名堂本地库，交叉验证每个InvNo。"""
    print(f"\n--- 步骤 2: 连接到服务器 {RMS_SERVER} 进行交叉验证 ---")
    cnxn = None
    validation_results = []
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={RMS_SERVER};DATABASE={RMS_DB};UID={RMS_USER};PWD=**********'
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("数据库连接成功！开始逐一核查...")

        for i, invno in enumerate(invoice_list):
            sys.stdout.write(f"\r正在核查: {i+1}/{len(invoice_list)} (InvNo: {invno}) ")
            sys.stdout.flush()

            # 验证 fdinv
            cursor.execute(f"SELECT COUNT(*) FROM {FOOD_DB}.dbo.fdinv WHERE InvNo = ?", invno)
            found_in_fdinv = cursor.fetchone()[0] > 0

            # 验证 opencacheinfo
            cursor.execute(f"SELECT COUNT(*) FROM {RMS_DB}.dbo.opencacheinfo WHERE Invno = ?", invno)
            found_in_opencache = cursor.fetchone()[0] > 0
            
            validation_results.append({
                '账单号_InvNo': invno,
                '是否存在于_Dbfood_fdinv': '是' if found_in_fdinv else '否',
                '是否存在于_rms2019_opencacheinfo': '是' if found_in_opencache else '否'
            })
        
        print("\n所有账单核查完毕！")
        return pd.DataFrame(validation_results)

    finally:
        if cnxn: cnxn.close()

if __name__ == '__main__':
    try:
        clue_invoices = get_clue_invoices()
        if not clue_invoices:
            print("未能获取任何调查线索，程序退出。")
            sys.exit(0)
        
        report_df = cross_validate_invoices(clue_invoices)
        
        print(f"\n--- 步骤 3: 生成并保存交叉验证报告 ---")
        report_df.to_csv(OUTPUT_FILENAME, index=False, encoding='utf-8-sig')
        
        print("\n--- 交叉验证报告摘要 ---")
        # 打印一个汇总统计，更有洞察力
        summary = report_df.groupby(['是否存在于_Dbfood_fdinv', '是否存在于_rms2019_opencacheinfo']).size().reset_index(name='账单数量')
        print(summary.to_string(index=False))

        print(f"\n详细报告已成功保存至文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)
