
import pyodbc
import sys

server = "192.168.2.5"
user = "sa"
password = "Musicbox123"
database = "operatedata"

conn = None
try:
    print(f"正在尝试使用 pyodbc 连接到数据库: server='{server}', database='{database}', user='{user}'...")
    
    # 使用从项目中其他脚本学到的连接字符串格式
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={server};"
        f"DATABASE={database};"
        f"UID={user};"
        f"PWD={password};"
        f"TrustServerCertificate=yes;"
        f"LoginTimeout=10;"
    )
    
    conn = pyodbc.connect(conn_str)
    
    cursor = conn.cursor()
    cursor.execute("SELECT @@VERSION")
    version_row = cursor.fetchone()
    
    print("数据库连接成功！")
    if version_row:
        print(f"SQL Server 版本: {version_row[0].splitlines()[0]}")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"数据库连接失败。错误 (SQLSTATE: {sqlstate}): {ex}", file=sys.stderr)
    # 检查常见的驱动问题
    if 'IM002' in sqlstate:
        print("错误提示: ODBC Driver 17 for SQL Server 未找到。", file=sys.stderr)
        print("请确保您已经安装了微软的 ODBC Driver 17 for SQL Server。", file=sys.stderr)
    sys.exit(1)

except Exception as e:
    print(f"发生未知错误: {e}", file=sys.stderr)
    sys.exit(1)

finally:
    if conn:
        conn.close()
        print("数据库连接已关闭。")
