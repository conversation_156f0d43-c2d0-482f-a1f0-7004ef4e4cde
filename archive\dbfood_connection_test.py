'''
import pyodbc
import sys

# Database connection parameters
server = '193.112.2.229'
database = 'dbfood'
username = 'sa'
password = 'Musicbox@123'

# Connection string
# Increased timeout to 10 seconds for potentially slow remote connections
conn_str = (
    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
    f"SERVER={server};"
    f"DATABASE={database};"
    f"UID={username};"
    f"PWD={password};"
    f"TrustServerCertificate=yes;"
    f"Connection Timeout=10;"
)

# Open the output file first
with open('connection_test_output.txt', 'w', encoding='utf-8') as f:
    # Redirect print statements to the file
    original_stdout = sys.stdout
    sys.stdout = f

    print(f"正在尝试连接到 {server} 上的 {database}...")

    try:
        # Establish the connection
        cnxn = pyodbc.connect(conn_str)
        print("数据库连接成功！")
        # Close the connection
        cnxn.close()
    except pyodbc.Error as ex:
        print(f"数据库连接失败。")
        # The ex object contains detailed error information
        print(f"错误详情: {ex}")
    except Exception as e:
        print(f"发生了未知错误: {e}")
    finally:
        # Restore original stdout
        sys.stdout = original_stdout

print("脚本执行完毕，结果已写入 connection_test_output.txt")

'''