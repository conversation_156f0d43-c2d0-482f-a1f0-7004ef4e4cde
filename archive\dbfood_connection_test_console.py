'''
import pyodbc

# Database connection parameters
server = '193.112.2.229'
database = 'dbfood'
username = 'sa'
password = 'Musicbox@123'

# Connection string with a 10-second timeout
conn_str = (
    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
    f"SERVER={server};"
    f"DATABASE={database};"
    f"UID={username};"
    f"PWD={password};"
    f"TrustServerCertificate=yes;"
    f"Connection Timeout=10;"
)

print(f"正在尝试连接到 {server} 上的 {database}...")

try:
    cnxn = pyodbc.connect(conn_str)
    print("数据库连接成功！")
    cnxn.close()
except pyodbc.Error as ex:
    print(f"数据库连接失败。")
    print(f"错误详情: {ex}")
except Exception as e:
    print(f"发生了未知错误: {e}")

'''