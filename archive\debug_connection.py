'''
import pyodbc

# --- 连接信息 ---
# 请替换为您的实际数据库服务器、数据库、用户名和密码
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'

# --- 尝试使用不同的驱动程序 --- 
drivers = [
    '{ODBC Driver 17 for SQL Server}',
    '{SQL Server}',
    '{SQL Server Native Client 11.0}',
    '{ODBC Driver 13 for SQL Server}',
]

conn_str_template = 'DRIVER={driver};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'

cnxn = None
for driver in drivers:
    try:
        conn_str = conn_str_template.format(
            driver=driver, 
            server=server, 
            database=database, 
            username=username, 
            password=password
        )
        print(f"正在尝试使用驱动: {driver}")
        cnxn = pyodbc.connect(conn_str, timeout=5)
        print("连接成功！")
        
        # --- 您可以在这里执行一些简单的查询来进一步测试 ---
        cursor = cnxn.cursor()
        cursor.execute("SELECT @@VERSION;")
        row = cursor.fetchone()
        if row:
            print("数据库版本:", row[0])
        cursor.close()
        
        break  # 连接成功，跳出循环
    except pyodbc.Error as ex:
        print(f"使用驱动 {driver} 连接失败。")
        # 打印部分错误信息以帮助诊断
        print(f"错误信息: {str(ex)[:150]}...") 
    except Exception as e:
        print(f"发生了预料之外的错误: {e}")

if cnxn:
    cnxn.close()
    print("连接已关闭。")
else:
    print("\n所有驱动均尝试失败，请检查：")
    print("1. 数据库服务器地址和数据库名称是否正确。")
    print("2. 用户名和密码是否正确。")
    print("3. 网络连接是否正常，防火墙是否允许端口1433。")
    print("4. 是否安装了至少一种ODBC驱动程序。")

'''