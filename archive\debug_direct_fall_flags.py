import pyodbc

# --- 数据库连接配置 ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
# --- 配置结束 ---

# --- 诊断参数 (已根据用户反馈修正) ---
target_date = '20250724' # 使用 YYYYMMDD 格式
shop_id = 11
# --- 参数结束 ---

# 构建连接字符串
connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

sql_query = f"""
SELECT 
    COUNT(*) AS Total_Records,
    SUM(CASE WHEN IsDirectFall = 1 THEN 1 ELSE 0 END) AS DirectFall_Flagged_Records,
    SUM(CASE WHEN IsDirectFall = 0 THEN 1 ELSE 0 END) AS NonDirectFall_Flagged_Records
FROM 
    dbo.RmCloseInfo
WHERE 
    WorkDate = ? AND ShopId = ?;
"""

connection = None
try:
    connection = pyodbc.connect(connection_string)
    cursor = connection.cursor()
    
    print(f"--- Running Diagnostic Query (Corrected Format) ---")
    print(f"Target Date: {target_date}")
    print(f"Shop ID: {shop_id}")
    print("----------------------------------")

    cursor.execute(sql_query, target_date, shop_id)
    row = cursor.fetchone()

    if row and row.Total_Records > 0:
        print(f"Total records for this day: {row.Total_Records}")
        print(f"Records flagged as IsDirectFall = 1: {row.DirectFall_Flagged_Records}")
        print(f"Records flagged as IsDirectFall = 0: {row.NonDirectFall_Flagged_Records}")
        print("----------------------------------")
        if row.DirectFall_Flagged_Records == 0:
            print("DIAGNOSIS: The tagging procedure did NOT flag any record as direct fall.")
            print("This points to an issue in `usp_UpdateDirectFallFlag_ByName` or the source data in FdCashBak.")
        else:
            print("DIAGNOSIS: Tagging seems OK. The problem is likely in the reporting procedure `usp_GenerateDayTimeReport_Simple_V4_Optimized`.")
    else:
        print("No records found for the specified date and shop. The date format was correct, but there is no data in the source table for this day.")

except pyodbc.Error as ex:
    print(f"Database query failed: {ex}")

finally:
    if connection:
        connection.close()