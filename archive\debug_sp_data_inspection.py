

import pyodbc
from datetime import datetime

# --- Configuration ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

shop_id = 3
target_date = '2025-05-11'

# --- SQL Queries ---
GET_HEADER_DATA_SQL = """
    EXEC dbo.usp_GenerateFullDailyReport @ShopId=?, @BeginDate=?, @EndDate=?;
"""

def debug_data_types():
    """Connects to the DB, fetches data, and prints it to inspect for type issues."""
    cnxn = None
    try:
        print(f"Connecting to {server}...")
        cnxn = pyodbc.connect(cnxn_str)
        cursor = cnxn.cursor()
        print("Connection successful.")

        print(f"\n--- INSPECTING DATA FOR DATE: {target_date} ---")

        # --- Step 1: Inspecting the Header Data ---
        print("\nStep 1: Fetching data from usp_GenerateFullDailyReport...")
        cursor.execute(GET_HEADER_DATA_SQL, shop_id, target_date, target_date)
        header_row = cursor.fetchone()
        
        if header_row:
            print("Header Data Row Content:")
            # Print each column and its Python type
            for i, col in enumerate(header_row):
                print(f"  - Col {i+1}: Value='{col}', Type={type(col)}")
        else:
            print("WARNING: usp_GenerateFullDailyReport returned no data for this date.")

        print("\n--- DEBUGGING COMPLETE ---")

    except pyodbc.Error as ex:
        print("\n--- !!! DATABASE ERROR CAUGHT !!! ---")
        print(f"SQLSTATE: {ex.args[0]}")
        print("Full Error Message:")
        print(ex)

    finally:
        if cnxn:
            cnxn.close()
            print("\nDatabase connection closed.")

if __name__ == "__main__":
    debug_data_types()

