

import pyodbc
from datetime import datetime

# --- Configuration ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

shop_id = 3
target_date = '2025-05-11'

# --- SQL Queries from the Stored Procedure ---
GET_HEADER_DATA_SQL = """
    EXEC dbo.usp_GenerateFullDailyReport @ShopId=?, @BeginDate=?, @EndDate=?;
"""

GET_TIMESLOT_DETAILS_SQL = """
    WITH TimeSlots AS (
        SELECT
            ti.TimeNo, ti.TimeName, ti.BegTime,
            DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(? AS datetime))) AS SlotStartDateTime,
            LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(? AS datetime))), 1, '2999-12-31') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
        FROM dbo.shoptimeinfo AS sti
        JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
        WHERE sti.ShopId = ?
    ),
    TrueDropInData AS (
        SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
        JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
        WHERE rt.ShopId = ? AND rt.WorkDate = ? AND rt.OpenDateTime IS NOT NULL
          AND rt.Beg_Key <> rt.End_Key
          AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
          AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
    )
    SELECT
        ti_main.TimeName,
        ti_main.BegTime
    FROM dbo.RmCloseInfo_Test AS rt
    JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
    JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
    WHERE rt.ShopId = ? AND rt.WorkDate = ? AND rt.OpenDateTime IS NOT NULL
    GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
    ORDER BY ti_main.BegTime;
"""

def debug_queries():
    """Connects to the DB and runs the queries step-by-step to find the error."""
    cnxn = None
    try:
        print(f"Connecting to {server}...")
        cnxn = pyodbc.connect(cnxn_str)
        cursor = cnxn.cursor()
        print("Connection successful.")

        print(f"\n--- DEBUGGING FOR DATE: {target_date} ---")

        # --- Step 1: Debugging the Header SP ---
        print("\nStep 1: Executing usp_GenerateFullDailyReport...")
        cursor.execute(GET_HEADER_DATA_SQL, shop_id, target_date, target_date)
        header_row = cursor.fetchone()
        print("Step 1 successful. Header data fetched.")
        if not header_row:
            print("WARNING: usp_GenerateFullDailyReport returned no data for this date.")

        # --- Step 2: Debugging the Timeslot Details Query ---
        print("\nStep 2: Executing Timeslot Details Query...")
        params = (target_date, target_date, shop_id, shop_id, target_date, shop_id, target_date)
        cursor.execute(GET_TIMESLOT_DETAILS_SQL, params)
        cursor.fetchall()
        print("Step 2 successful. Timeslot data fetched.")

        print("\n--- DEBUGGING COMPLETE: NO ERRORS FOUND ---")

    except pyodbc.Error as ex:
        print("\n--- !!! DATABASE ERROR CAUGHT !!! ---")
        print("An error occurred during the execution.")
        print(f"SQLSTATE: {ex.args[0]}")
        print("Full Error Message:")
        print(ex)

    finally:
        if cnxn:
            cnxn.close()
            print("\nDatabase connection closed.")

if __name__ == "__main__":
    debug_queries()
