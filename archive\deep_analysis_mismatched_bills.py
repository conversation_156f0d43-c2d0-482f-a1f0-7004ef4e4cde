import pyodbc
import pandas as pd
import sys

# --- 数据源 1: operatedata (获取结账数据) ---
OPERATEDATA_SERVER = '192.168.2.5'
OPERATEDATA_DB = 'operatedata'
OPERATEDATA_USER = 'sa'
OPERATEDATA_PASS = 'Musicbox123'

# --- 数据源 2: 名堂本地数据库 (获取开台数据) ---
RMS_SERVER = '193.112.2.229'
RMS_USER = 'sa'
RMS_PASS = 'Musicbox@123'
RMS_DB = 'rms2019'

# --- 分析参数 ---
TARGET_SHOP_ID = 11
TARGET_WORK_DATE = '20250723'
TARGET_ROOMS = ['311', '902', '806', '316']

# --- 输出文件名 ---
OUTPUT_FILENAME = 'deep_analysis_report.txt'

def get_checkout_data(cnxn):
    """从 operatedata 获取指定房间的问题结账数据。"""
    print("--- 正在从 operatedata.dbo.RmCloseInfo 获取问题结账单... ---")
    # V2: 将 TotalAmount 修正为 Tot
    sql = "SELECT InvNo, Tot, Numbers, CloseDatetime FROM dbo.RmCloseInfo WHERE ShopId = ? AND WorkDate = ? AND OpenDateTime IS NULL;"
    df = pd.read_sql(sql, cnxn, params=[TARGET_SHOP_ID, TARGET_WORK_DATE])
    print(f"获取到 {len(df)} 条缺失开台时间的结账记录。")
    return df

def get_opening_data(cnxn):
    """从 rms2019.dbo.opencacheinfo 获取指定房间的原始开台数据。"""
    print(f"--- 正在从 rms2019.dbo.opencacheinfo 获取 {TARGET_ROOMS} 的原始开台单... ---")
    placeholders = ', '.join('?' * len(TARGET_ROOMS))
    sql = f"SELECT RmNo, Invno, ComeDate, ComeTime, Numbers FROM dbo.opencacheinfo WHERE RmNo IN ({placeholders}) AND ComeDate = ?;"
    params = TARGET_ROOMS + [TARGET_WORK_DATE]
    df = pd.read_sql(sql, cnxn, params=params)
    print(f"获取到 {len(df)} 条相关的原始开台记录。")
    return df

def get_room_map(cnxn, inv_list):
    """根据InvNo列表，从Dbfood.fdinv获取InvNo到RmNo的映射。"""
    print("--- 正在从 Dbfood.fdinv 构建 InvNo -> RmNo 的映射... ---")
    placeholders = ', '.join('?' * len(inv_list))
    sql = f"SELECT DISTINCT InvNo, RmNo FROM Dbfood.dbo.fdinv WHERE InvNo IN ({placeholders});"
    df = pd.read_sql(sql, cnxn, params=inv_list)
    return pd.Series(df.RmNo.values, index=df.InvNo).to_dict()


if __name__ == '__main__':
    report_lines = [f"深度分析报告: ShopID={TARGET_SHOP_ID}, WorkDate={TARGET_WORK_DATE}"]
    try:
        # 1. 获取结账数据
        with pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={OPERATEDATA_SERVER};DATABASE={OPERATEDATA_DB};UID={OPERATEDATA_USER};PWD={OPERATEDATA_PASS}') as cnxn1:
            df_checkout = get_checkout_data(cnxn1)
        
        if df_checkout.empty:
            print("未找到任何需要分析的结账数据，程序退出。")
            sys.exit(0)

        # 2. 获取开台数据和房间映射
        with pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={RMS_SERVER};DATABASE={RMS_DB};UID={RMS_USER};PWD=**********') as cnxn2:
            df_opening = get_opening_data(cnxn2)
            room_map = get_room_map(cnxn2, df_checkout['InvNo'].tolist())

        # 将RmNo添加到结账数据中
        df_checkout['RmNo'] = df_checkout['InvNo'].map(room_map)

        # 3. 逐个房间进行分析和报告生成
        print("\n--- 开始生成深度分析报告 ---")
        for room in TARGET_ROOMS:
            report_lines.append(f"\n==================================================")
            report_lines.append(f"====== 案件聚焦：房间号【{room}】 ======")
            report_lines.append(f"==================================================")

            # 筛选出当前房间的相关数据
            room_checkouts = df_checkout[df_checkout['RmNo'] == room]
            room_openings = df_opening[df_opening['RmNo'] == room]

            report_lines.append("\n【A. 问题结账单据 (来自 operatedata.RmCloseInfo)】")
            if not room_checkouts.empty:
                report_lines.append(room_checkouts[['InvNo', 'Tot', 'Numbers', 'CloseDatetime']].to_string(index=False))
            else:
                report_lines.append("未找到与此房间相关的、缺失开台时间的结账单。")

            report_lines.append("\n【B. 当日原始开台记录 (来自 rms2019.opencacheinfo)】")
            if not room_openings.empty:
                report_lines.append(room_openings[['Invno', 'ComeTime', 'Numbers']].to_string(index=False))
            else:
                report_lines.append("警告：此房间在当天没有任何原始开台记录！")

            report_lines.append("\n【C. 分析结论】")
            if not room_checkouts.empty and not room_openings.empty:
                report_lines.append("-> 关键发现：存在结账单，也存在开台记录，但【账单号完全不匹配】。")
                report_lines.append("-> 可能原因：这极有可能是由于 KTV 系统中的“多单合并结账”、“换房”或“换单”等复杂操作导致。")
                report_lines.append("-> 例如，客人可能持有多张开台单(B区)，但最后只用一张新单(A区)进行了总结账。")
            elif not room_checkouts.empty and room_openings.empty:
                report_lines.append("-> 关键发现：存在结账单，但【完全没有任何原始开台记录】。")
                report_lines.append("-> 可能原因：这是一个更严重的问题，说明账单的产生可能绕过了正常的开台流程。")
            else:
                report_lines.append("-> 未发现明显异常关联。")

        # 4. 保存报告
        final_report = "\n".join(report_lines)
        print("\n--- 分析完成，正在保存报告... ---")
        with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
            f.write(final_report)
        
        print("\n--- 最终报告预览 ---")
        print(final_report)
        print(f"\n深度分析报告已完整保存至文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)