

import pyodbc
import pandas as pd

# --- Configuration ---
DB_CONFIG = {
    'server': '***********',
    'database': 'operatedata',
    'username': 'sa',
    'password': 'Musicbox123'
}

# --- Test Parameters ---
TEST_SHOP_ID = 11
TEST_DATE = '2025-07-24'

# --- Helper Function to execute SQL script ---
def execute_sql_script(script_path, sp_name):
    conn = None
    try:
        conn = pyodbc.connect(f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}")
        cursor = conn.cursor()
        print(f"--- Creating/Updating Stored Procedure from '{script_path}' ---")
        with open(script_path, 'r', encoding='utf-8') as f:
            sql_script = f.read().replace('GO', '')
        
        sql_script = sql_script.replace('CREATE OR ALTER PROCEDURE', 'CREATE PROCEDURE')

        drop_sql = f"IF OBJECT_ID('{sp_name}', 'P') IS NOT NULL DROP PROCEDURE {sp_name}"
        cursor.execute(drop_sql)
        conn.commit()

        cursor.execute(sql_script)
        conn.commit()
        cursor.execute("DBCC FREEPROCCACHE;")
        conn.commit()
        print(f"Stored Procedure '{sp_name}' updated successfully.")
    except Exception as e:
        print(f"Error in execute_sql_script for {sp_name}: {e}")
    finally:
        if conn:
            conn.close()

# --- Main Logic ---
def deploy_and_test_sps():
    # --- Test usp_RunDailyReportJob ---
    print("\n===== Testing usp_RunDailyReportJob =====")
    execute_sql_script('usp_RunDailyReportJob_modified.sql', 'usp_RunDailyReportJob')

    conn = None
    try:
        conn = pyodbc.connect(f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}")
        cursor = conn.cursor()
        print(f"\n--- Executing usp_RunDailyReportJob for Shop ID {TEST_SHOP_ID} on {TEST_DATE} ---")
        sp_exec_sql = "EXEC usp_RunDailyReportJob @ShopId=?, @TargetDate=?"
        params = [TEST_SHOP_ID, TEST_DATE]
        cursor.execute(sp_exec_sql, params)
        conn.commit()
        print("usp_RunDailyReportJob executed. Checking KTV_Simplified_Daily_Report...")

        df_simplified = pd.read_sql(f"SELECT * FROM KTV_Simplified_Daily_Report WHERE ReportDate = '{TEST_DATE}' AND ShopName = (SELECT ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = {TEST_SHOP_ID})", conn)
        if not df_simplified.empty:
            print("Data found in KTV_Simplified_Daily_Report after execution:")
            print(df_simplified.to_string())
        else:
            print("No data found in KTV_Simplified_Daily_Report.")
    except Exception as e:
        print(f"An error occurred during usp_RunDailyReportJob execution: {e}")
    finally:
        if conn:
            conn.close()

    # --- Test usp_RunNormalizedDailyReportJob_V2 ---
    print("\n===== Testing usp_RunNormalizedDailyReportJob_V2 =====")
    execute_sql_script('usp_RunNormalizedDailyReportJob_V2_modified.sql', 'usp_RunNormalizedDailyReportJob_V2')

    conn = None
    try:
        conn = pyodbc.connect(f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}")
        cursor = conn.cursor()
        print(f"\n--- Executing usp_RunNormalizedDailyReportJob_V2 for Shop ID {TEST_SHOP_ID} on {TEST_DATE} ---")
        sp_exec_sql = "EXEC usp_RunNormalizedDailyReportJob_V2 @ShopId=?, @TargetDate=?"
        params = [TEST_SHOP_ID, TEST_DATE]
        cursor.execute(sp_exec_sql, params)
        conn.commit()
        print("usp_RunNormalizedDailyReportJob_V2 executed. Checking FullDailyReport_Header and FullDailyReport_TimeSlotDetails...")

        df_header = pd.read_sql(f"SELECT * FROM FullDailyReport_Header WHERE ReportDate = '{TEST_DATE}' AND ShopID = {TEST_SHOP_ID}", conn)
        if not df_header.empty:
            print("Data found in FullDailyReport_Header after execution:")
            print(df_header.to_string())
            report_id = df_header['ReportID'].iloc[0]
            df_details = pd.read_sql(f"SELECT * FROM FullDailyReport_TimeSlotDetails WHERE ReportID = {report_id}", conn)
            if not df_details.empty:
                print("\nData found in FullDailyReport_TimeSlotDetails:")
                print(df_details.to_string())
            else:
                print("No data found in FullDailyReport_TimeSlotDetails.")
        else:
            print("No data found in FullDailyReport_Header.")
    except Exception as e:
        print(f"An error occurred during usp_RunNormalizedDailyReportJob_V2 execution: {e}")
    finally:
        if conn:
            conn.close()

    # --- Testing Idempotency (re-running the jobs) ---
    print("\n--- Testing Idempotency (re-running the jobs) ---")
    # Re-run usp_RunDailyReportJob
    conn = None
    try:
        conn = pyodbc.connect(f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}")
        cursor = conn.cursor()
        print("Re-running usp_RunDailyReportJob...")
        sp_exec_sql = "EXEC usp_RunDailyReportJob @ShopId=?, @TargetDate=?"
        params = [TEST_SHOP_ID, TEST_DATE]
        cursor.execute(sp_exec_sql, params)
        conn.commit()
        print("usp_RunDailyReportJob re-run completed.")
    except Exception as e:
        print(f"An error occurred during usp_RunDailyReportJob re-run: {e}")
    finally:
        if conn:
            conn.close()

    # Re-run usp_RunNormalizedDailyReportJob_V2
    conn = None
    try:
        conn = pyodbc.connect(f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}")
        cursor = conn.cursor()
        print("Re-running usp_RunNormalizedDailyReportJob_V2...")
        sp_exec_sql = "EXEC usp_RunNormalizedDailyReportJob_V2 @ShopId=?, @TargetDate=?"
        params = [TEST_SHOP_ID, TEST_DATE]
        cursor.execute(sp_exec_sql, params)
        conn.commit()
        print("usp_RunNormalizedDailyReportJob_V2 re-run completed.")
    except Exception as e:
        print(f"An error occurred during usp_RunNormalizedDailyReportJob_V2 re-run: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    deploy_and_test_sps()
