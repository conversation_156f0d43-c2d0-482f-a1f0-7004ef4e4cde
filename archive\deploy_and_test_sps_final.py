#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KTV存储过程部署和测试脚本
描述：连接数据库，部署可重复执行的存储过程，并进行测试
数据库：192.168.2.5, operatedata
测试参数：ShopId=11, Date=20250724
"""

import pyodbc
import datetime
import os

# 数据库连接配置
DB_CONFIG = {
    'server': '192.168.2.5',
    'database': 'operatedata',
    'username': 'sa',
    'password': 'Musicbox123',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

# 测试参数
TEST_SHOP_ID = 11
TEST_DATE = '20250724'

class KTVDatabaseManager:
    def __init__(self):
        self.connection = None
        self.cursor = None
        self.connect()
    
    def connect(self):
        """连接数据库"""
        try:
            connection_string = (
                f"DRIVER={DB_CONFIG['driver']};"
                f"SERVER={DB_CONFIG['server']};"
                f"DATABASE={DB_CONFIG['database']};"
                f"UID={DB_CONFIG['username']};"
                f"PWD={DB_CONFIG['password']};"
                f"TrustServerCertificate=yes;"
            )
            self.connection = pyodbc.connect(connection_string)
            self.cursor = self.connection.cursor()
            print("✓ 数据库连接成功")
        except Exception as e:
            print(f"✗ 数据库连接失败: {str(e)}")
            raise
    
    def execute_sql_script(self, sql_content):
        """执行SQL脚本"""
        try:
            # 分割SQL语句（按GO分隔）
            statements = sql_content.split('GO\n')
            
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        self.cursor.execute(statement)
                        self.connection.commit()
                    except Exception as e:
                        # 忽略某些非关键错误
                        if "already exists" not in str(e).lower():
                            print(f"警告: {str(e)}")
                            self.connection.rollback()
            
            print("✓ SQL脚本执行完成")
            return True
        except Exception as e:
            print(f"✗ SQL脚本执行失败: {str(e)}")
            return False
    
    def deploy_procedures(self):
        """部署存储过程"""
        print("\n" + "="*50)
        print("开始部署可重复执行的存储过程")
        print("="*50)
        
        # usp_RunDailyReportJob 存储过程
        sp1_sql = f"""
USE OperateData;
GO

-- 删除已存在的存储过程
IF OBJECT_ID('dbo.usp_RunDailyReportJob', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_RunDailyReportJob;
GO

CREATE PROCEDURE dbo.usp_RunDailyReportJob
    @TargetDate DATE = NULL,
    @ShopId INT = 3,
    @ForceReRun BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL
        SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @ShopName NVARCHAR(100);
    SELECT @ShopName = ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId;
    
    IF @ShopName IS NULL
    BEGIN
        RAISERROR(N'无效的门店ID: %d', 16, 1, @ShopId);
        RETURN;
    END

    IF EXISTS (SELECT 1 FROM dbo.KTV_Simplified_Daily_Report WHERE ReportDate = @TargetDate AND ShopName = @ShopName)
    BEGIN
        IF @ForceReRun = 1
        BEGIN
            DELETE FROM dbo.KTV_Simplified_Daily_Report 
            WHERE ReportDate = @TargetDate AND ShopName = @ShopName;
            
            INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])
            VALUES (N'KTV_Daily_Report', @TargetDate, N'Deleted', 
                   N'Deleted existing data for re-run: ' + CONVERT(NVARCHAR, @TargetDate));
        END
        ELSE
        BEGIN
            PRINT N'数据已存在，跳过执行。使用 @ForceReRun=1 强制重新执行';
            RETURN;
        END
    END

    BEGIN TRANSACTION;
    BEGIN TRY
        CREATE TABLE #TempReportData (
            ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
            TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
            FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
            Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
            Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
            FreeConsumption_BatchCount int,
            NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
            Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2)
        );

        INSERT INTO #TempReportData
        EXEC dbo.usp_GenerateSimplifiedDailyReport @ShopId = @ShopId, @BeginDate = @TargetDate, @EndDate = @TargetDate;

        INSERT INTO dbo.KTV_Simplified_Daily_Report (
            ReportDate, ShopName, Weekday, TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount, FreeMeal_KPlus, FreeMeal_Special,
            FreeMeal_Meituan, FreeMeal_Douyin, FreeMeal_BatchCount, FreeMeal_Revenue, Buyout_BatchCount,
            Buyout_Revenue, Changyin_BatchCount, Changyin_Revenue, FreeConsumption_BatchCount,
            NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others,
            Night_Verify_BatchCount, Night_Verify_Revenue
        )
        SELECT * FROM #TempReportData;

        DROP TABLE #TempReportData;
        COMMIT TRANSACTION;

        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])
        VALUES (N'KTV_Daily_Report', @TargetDate, N'Success', 
               N'Report processed successfully for date: ' + CONVERT(NVARCHAR, @TargetDate));

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message])
        VALUES (N'KTV_Daily_Report', @TargetDate, N'Failure', @ErrorMessage);
        
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO
"""

        # usp_RunNormalizedDailyReportJob_V2 存储过程
        sp2_sql = f"""
USE OperateData;
GO

-- 删除已存在的存储过程
IF OBJECT_ID('dbo.usp_RunNormalizedDailyReportJob_V2', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2;
GO

CREATE PROCEDURE dbo.usp_RunNormalizedDailyReportJob_V2
    @TargetDate DATE = NULL,
    @ShopId INT = 3,
    @ForceReRun BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    IF @TargetDate IS NULL SET @TargetDate = CAST(DATEADD(day, -1, GETDATE()) AS DATE);

    DECLARE @JobName NVARCHAR(255) = N'KTV_Normalized_Full_Report';

    IF EXISTS (SELECT 1 FROM dbo.FullDailyReport_Header WHERE ReportDate = @TargetDate AND ShopID = @ShopId)
    BEGIN
        IF @ForceReRun = 1
        BEGIN
            DECLARE @ReportIDs TABLE (ReportID INT);
            
            INSERT INTO @ReportIDs 
            SELECT ReportID 
            FROM dbo.FullDailyReport_Header 
            WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
            
            DELETE FROM dbo.FullDailyReport_TimeSlotDetails 
            WHERE ReportID IN (SELECT ReportID FROM @ReportIDs);
            
            DELETE FROM dbo.FullDailyReport_Header 
            WHERE ReportDate = @TargetDate AND ShopID = @ShopId;
            
            INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) 
            VALUES (@JobName, @TargetDate, N'Deleted', 
                   N'Deleted existing data for re-run: ' + CONVERT(NVARCHAR, @TargetDate));
        END
        ELSE
        BEGIN
            PRINT N'数据已存在，跳过执行。使用 @ForceReRun=1 强制重新执行';
            RETURN;
        END
    END

    BEGIN TRANSACTION;
    BEGIN TRY
        CREATE TABLE #TempHeader (
            WorkDate varchar(8), ShopName nvarchar(100), WeekdayName nvarchar(20),
            TotalRevenue decimal(18,2), DayTimeRevenue decimal(18,2), NightTimeRevenue decimal(18,2),
            TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
            DayTimeDropInBatch int, NightTimeDropInBatch int, TotalGuestCount int,
            BuffetGuestCount int, TotalDropInGuests int, 
            MealBatchCount INT, MealDirectFallBatchCount INT, MealDirectFallRevenue DECIMAL(18,2),
            Night_FreeMeal_Subtotal int, Night_FreeMeal_Amount decimal(18,2), Night_After20_Revenue decimal(18,2)
        );

        INSERT INTO #TempHeader
        EXEC dbo.usp_GenerateDayTimeReport_Simple_V3 @ShopId = @ShopId, @TargetDate = @TargetDate;

        INSERT INTO dbo.FullDailyReport_Header (
            ReportDate, ShopID, ShopName, Weekday, 
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDirectFall, NightTimeDropInBatch, 
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue
        )
        SELECT
            @TargetDate, @ShopId, ShopName, WeekdayName,
            TotalRevenue, DayTimeRevenue, NightTimeRevenue,
            TotalBatchCount, DayTimeBatchCount, NightTimeBatchCount,
            DayTimeDropInBatch, NightTimeDropInBatch,
            TotalGuestCount, BuffetGuestCount, TotalDropInGuests,
            MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue,
            Night_FreeMeal_Subtotal, Night_FreeMeal_Amount, Night_After20_Revenue
        FROM #TempHeader;

        DECLARE @ReportID INT = SCOPE_IDENTITY();

        CREATE TABLE #TempDetails (
            TimeSlotName NVARCHAR(50), TimeSlotOrder INT, KPlus_Count INT, Special_Count INT,
            Meituan_Count INT, Douyin_Count INT, RoomFee_Count INT, Subtotal_Count INT,
            PreviousSlot_DirectFall INT
        );

        INSERT INTO #TempDetails
        EXEC dbo.usp_GetTimeSlotDetails_WithDirectFall @TargetDate = @TargetDate, @ShopId = @ShopId;

        INSERT INTO dbo.FullDailyReport_TimeSlotDetails (
            ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        )
        SELECT 
            @ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, 
            Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall
        FROM #TempDetails;

        DROP TABLE #TempHeader;
        DROP TABLE #TempDetails;

        COMMIT TRANSACTION;

        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) 
        VALUES (@JobName, @TargetDate, N'Success', 
               N'Final report processed successfully. ReportID: ' + CAST(@ReportID AS NVARCHAR));

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 
            ROLLBACK TRANSACTION;

        IF OBJECT_ID('tempdb..#TempHeader') IS NOT NULL DROP TABLE #TempHeader;
        IF OBJECT_ID('tempdb..#TempDetails') IS NOT NULL DROP TABLE #TempDetails;

        DECLARE @ErrorMessage NVARCHAR(MAX) = ERROR_MESSAGE();
        INSERT INTO dbo.JobExecutionLog (JobName, ReportDate, [Status], [Message]) 
        VALUES (@JobName, @TargetDate, N'Failure', @ErrorMessage);
        
        RAISERROR (@ErrorMessage, 16, 1);
    END CATCH
END
GO
"""

        # 执行存储过程部署
        print("部署 usp_RunDailyReportJob...")
        self.execute_sql_script(sp1_sql)
        
        print("部署 usp_RunNormalizedDailyReportJob_V2...")
        self.execute_sql_script(sp2_sql)
    
    def test_procedures(self, shop_id, test_date):
        """测试存储过程"""
        print(f"\n" + "="*50)
        print(f"开始测试存储过程")
        print(f"测试参数: ShopId={shop_id}, Date={test_date}")
        print("="*50)
        
        # 转换日期格式
        date_obj = datetime.datetime.strptime(test_date, '%Y%m%d').date()
        
        # 测试1：usp_RunDailyReportJob 首次执行
        print(f"\n测试1: usp_RunDailyReportJob 首次执行")
        print("-" * 40)
        try:
            self.cursor.execute("EXEC dbo.usp_RunDailyReportJob @TargetDate = ?, @ShopId = ?", date_obj, shop_id)
            self.connection.commit()
            print("✓ 首次执行成功")
        except Exception as e:
            if "already exists" in str(e).lower():
                print("⚠ 数据已存在，跳过")
            else:
                print(f"✗ 执行失败: {str(e)}")
        
        # 测试2：usp_RunDailyReportJob 强制重新执行
        print(f"\n测试2: usp_RunDailyReportJob 强制重新执行")
        print("-" * 40)
        try:
            self.cursor.execute("EXEC dbo.usp_RunDailyReportJob @TargetDate = ?, @ShopId = ?, @ForceReRun = 1", 
                              date_obj, shop_id)
            self.connection.commit()
            print("✓ 强制重新执行成功")
        except Exception as e:
            print(f"✗ 执行失败: {str(e)}")
        
        # 测试3：usp_RunNormalizedDailyReportJob_V2 首次执行
        print(f"\n测试3: usp_RunNormalizedDailyReportJob_V2 首次执行")
        print("-" * 40)
        try:
            self.cursor.execute("EXEC dbo.usp_RunNormalizedDailyReportJob_V2 @TargetDate = ?, @ShopId = ?", 
                              date_obj, shop_id)
            self.connection.commit()
            print("✓ 首次执行成功")
        except Exception as e:
            if "already exists" in str(e).lower():
                print("⚠ 数据已存在，跳过")
            else:
                print(f"✗ 执行失败: {str(e)}")
        
        # 测试4：usp_RunNormalizedDailyReportJob_V2 强制重新执行
        print(f"\n测试4: usp_RunNormalizedDailyReportJob_V2 强制重新执行")
        print("-" * 40)
        try:
            self.cursor.execute("EXEC dbo.usp_RunNormalizedDailyReportJob_V2 @TargetDate = ?, @ShopId = ?, @ForceReRun = 1", 
                              date_obj, shop_id)
            self.connection.commit()
            print("✓ 强制重新执行成功")
        except Exception as e:
            print(f"✗ 执行失败: {str(e)}")
    
    def verify_data(self, shop_id, test_date):
        """验证数据"""
        print(f"\n" + "="*50)
        print(f"验证测试数据")
        print("="*50)
        
        date_obj = datetime.datetime.strptime(test_date, '%Y%m%d').date()
        
        # 验证 usp_RunDailyReportJob 数据
        print(f"\n验证 usp_RunDailyReportJob 数据:")
        try:
            self.cursor.execute("""
                SELECT 
                    ReportDate,
                    ShopName,
                    TotalRevenue,
                    TotalBatchCount,
                    FreeMeal_BatchCount,
                    Buyout_BatchCount,
                    Changyin_BatchCount
                FROM dbo.KTV_Simplified_Daily_Report 
                WHERE ReportDate = ? AND ShopName = (SELECT ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = ?)
            """, date_obj, shop_id)
            
            rows = self.cursor.fetchall()
            if rows:
                print("✓ 找到数据:")
                for row in rows:
                    print(f"  日期: {row.ReportDate}, 门店: {row.ShopName}")
                    print(f"  总收入: ¥{row.TotalRevenue}, 总批次: {row.TotalBatchCount}")
                    print(f"  自助餐: {row.FreeMeal_BatchCount}, 买断: {row.Buyout_BatchCount}, 畅饮: {row.Changyin_BatchCount}")
            else:
                print("⚠ 未找到数据")
        except Exception as e:
            print(f"✗ 查询失败: {str(e)}")
        
        # 验证 usp_RunNormalizedDailyReportJob_V2 数据
        print(f"\n验证 usp_RunNormalizedDailyReportJob_V2 数据:")
        try:
            self.cursor.execute("""
                SELECT 
                    ReportDate,
                    ShopName,
                    TotalRevenue,
                    TotalBatchCount,
                    DayTimeDirectFall,
                    MealDirectFallBatchCount,
                    MealDirectFallRevenue
                FROM dbo.FullDailyReport_Header 
                WHERE ReportDate = ? AND ShopID = ?
            """, date_obj, shop_id)
            
            rows = self.cursor.fetchall()
            if rows:
                print("✓ 找到数据:")
                for row in rows:
                    print(f"  日期: {row.ReportDate}, 门店: {row.ShopName}")
                    print(f"  总收入: ¥{row.TotalRevenue}, 总批次: {row.TotalBatchCount}")
                    print(f"  直落批次: {row.DayTimeDirectFall}, 自助餐直落: {row.MealDirectFallBatchCount}")
                    print(f"  自助餐直落收入: ¥{row.MealDirectFallRevenue}")
            else:
                print("⚠ 未找到数据")
        except Exception as e:
            print(f"✗ 查询失败: {str(e)}")
        
        # 查看执行日志
        print(f"\n最新执行日志:")
        try:
            self.cursor.execute("""
                SELECT TOP 5
                    JobName,
                    ReportDate,
                    [Status],
                    [Message],
                    ExecutionTime
                FROM dbo.JobExecutionLog 
                WHERE ReportDate = ?
                ORDER BY ExecutionTime DESC
            """, date_obj)
            
            rows = self.cursor.fetchall()
            if rows:
                print("✓ 执行日志:")
                for row in rows:
                    print(f"  {row.JobName} | {row.ReportDate} | {row.Status} | {row.ExecutionTime}")
                    if row.Message:
                        print(f"    {row.Message[:100]}...")
            else:
                print("⚠ 未找到执行日志")
        except Exception as e:
            print(f"✗ 查询日志失败: {str(e)}")
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("\n✓ 数据库连接已关闭")

def main():
    """主函数"""
    print("KTV存储过程部署和测试脚本")
    print("="*50)
    print(f"测试参数: ShopId={TEST_SHOP_ID}, Date={TEST_DATE}")
    
    try:
        # 创建数据库管理器
        db_manager = KTVDatabaseManager()
        
        # 部署存储过程
        db_manager.deploy_procedures()
        
        # 测试存储过程
        db_manager.test_procedures(TEST_SHOP_ID, TEST_DATE)
        
        # 验证数据
        db_manager.verify_data(TEST_SHOP_ID, TEST_DATE)
        
        print("\n" + "="*50)
        print("所有操作完成！")
        print("="*50)
        
    except Exception as e:
        print(f"\n✗ 发生错误: {str(e)}")
    finally:
        if 'db_manager' in locals():
            db_manager.close()

if __name__ == "__main__":
    main()
