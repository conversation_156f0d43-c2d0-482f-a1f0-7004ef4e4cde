import os
import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=***********;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- Get paths to SQL files ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
REPORT_PROC_PATH = os.path.join(SCRIPT_DIR, 'usp_GenerateSimplifiedDailyReport_V7_Final.sql')
JOB_PROC_PATH = os.path.join(SCRIPT_DIR, 'usp_RunDailyReportJob_modified.sql')

# --- SQL Commands ---
SQL_EXEC_JOB = "EXEC dbo.usp_RunDailyReportJob @TargetDate = '2025-07-24', @ShopId = 11"
SQL_VERIFY_DATA = """
SELECT * 
FROM dbo.KTV_Simplified_Daily_Report 
WHERE ReportDate = '2025-07-24' AND ShopName = (SELECT ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = 11);
"""

# --- Main Execution Logic ---
def deploy_and_verify_all():
    """
    The definitive, robust, end-to-end script.
    It deploys BOTH procedures using a strict DROP-CREATE pattern, then runs and verifies.
    """
    try:
        print("--- Reading SQL files... ---")
        with open(REPORT_PROC_PATH, 'r', encoding='utf-8') as f:
            sql_report_proc = f.read()
        with open(JOB_PROC_PATH, 'r', encoding='utf-8') as f:
            sql_job_proc = f.read()

        sql_drop_report_proc = "IF OBJECT_ID('dbo.usp_GenerateSimplifiedDailyReport_V7_Final', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final"
        sql_drop_job_proc = "IF OBJECT_ID('dbo.usp_RunDailyReportJob', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_RunDailyReportJob"

        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()

            print("\nSTEP 1: DEPLOYING PROCEDURES (Strict DROP-CREATE)")
            print("  - Deploying Report Generator...")
            cursor.execute(sql_drop_report_proc)
            cursor.execute(sql_report_proc)
            print("  - Deploying Job Runner...")
            cursor.execute(sql_drop_job_proc)
            cursor.execute(sql_job_proc)
            print("PROCEDURES DEPLOYED SUCCESSFULLY.")

            print("\nSTEP 2: EXECUTING THE JOB...")
            cursor.execute(SQL_EXEC_JOB)
            print("Job execution command sent.")

            print("\nSTEP 3: FINAL VERIFICATION...")
            df = pd.read_sql(SQL_VERIFY_DATA, conn)
            
            print("\n" + "="*20 + " FINAL VERIFICATION RESULT " + "="*20)
            if df.empty:
                print("VERIFICATION FAILED: No data was inserted.")
            else:
                print("DATA FOUND! Please verify the values below:")
                print(df.to_string())
            print("="*65)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"DATABASE ERROR: {sqlstate}")
        print(ex)
    except FileNotFoundError as e:
        print(f"FILE ERROR: Could not find a required SQL file: {e.filename}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    deploy_and_verify_all()