
import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 步骤 1: 创建同步开台数据的存储过程 ---
SQL_STEP_1 = """
USE operatedata;
GO
IF OBJECT_ID('usp_Sync_RMS_DailyOpenData', 'P') IS NOT NULL DROP PROCEDURE usp_Sync_RMS_DailyOpenData;
GO
CREATE PROCEDURE usp_Sync_RMS_DailyOpenData
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @TargetBusinessDate DATE = CAST(DATEADD(hour, -9, GETDATE() - 1) AS DATE);
    DECLARE @StartDate DATETIME = DATEADD(hour, 9, CAST(@TargetBusinessDate AS DATETIME));
    DECLARE @EndDate DATETIME = DATEADD(hour, 9, CAST(DATEADD(day, 1, @TargetBusinessDate) AS DATETIME));
    BEGIN TRY
        MERGE INTO rms2019.dbo.opencacheinfo AS T
        USING (SELECT * FROM cloudRms2019.rms2019.dbo.opencacheinfo WHERE BookDateTime >= @StartDate AND BookDateTime < @EndDate) AS S
        ON T.Ikey = S.Ikey
        WHEN MATCHED THEN UPDATE SET T.CheckinStatus=S.CheckinStatus, T.Invno=S.Invno, T.RmNo=S.RmNo, T.WorkDate=@TargetBusinessDate
        WHEN NOT MATCHED BY TARGET THEN INSERT (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark,WorkDate) VALUES(S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark,@TargetBusinessDate);
        MERGE INTO rms2019.dbo.openhistory AS T
        USING (SELECT * FROM cloudRms2019.rms2019.dbo.openhistory WHERE BookDateTime >= @StartDate AND BookDateTime < @EndDate) AS S
        ON T.Ikey = S.Ikey
        WHEN NOT MATCHED BY TARGET THEN INSERT (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark,WorkDate) VALUES(S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark,@TargetBusinessDate);
    END TRY
    BEGIN CATCH
        PRINT 'Error in usp_Sync_RMS_DailyOpenData: ' + ERROR_MESSAGE();
    END CATCH
END
""")

# --- 步骤 2: 创建同步预订数据的存储过程 ---
SQL_STEP_2 = """
USE operatedata;
GO
IF OBJECT_ID('usp_Sync_RMS_DailyBookData', 'P') IS NOT NULL DROP PROCEDURE usp_Sync_RMS_DailyBookData;
GO
CREATE PROCEDURE usp_Sync_RMS_DailyBookData
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @TargetNaturalDate DATE = GETDATE() - 1;
    BEGIN TRY
        MERGE INTO rms2019.dbo.bookcacheinfo AS T
        USING (SELECT * FROM cloudRms2019.rms2019.dbo.bookcacheinfo WHERE CAST(BookDateTime AS DATE) = @TargetNaturalDate) AS S
        ON T.Ikey = S.Ikey
        WHEN MATCHED THEN UPDATE SET T.BookStatus=S.BookStatus, T.CheckinStatus=S.CheckinStatus, T.Invno=S.Invno, T.RmNo=S.RmNo
        WHEN NOT MATCHED BY TARGET THEN INSERT (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark) VALUES(S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark);
        MERGE INTO rms2019.dbo.bookhistory AS T
        USING (SELECT * FROM cloudRms2019.rms2019.dbo.bookhistory WHERE CAST(BookDateTime AS DATE) = @TargetNaturalDate) AS S
        ON T.Ikey = S.Ikey
        WHEN NOT MATCHED BY TARGET THEN INSERT (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark) VALUES(S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark);
    END TRY
    BEGIN CATCH
        PRINT 'Error in usp_Sync_RMS_DailyBookData: ' + ERROR_MESSAGE();
    END CATCH
END
""")

# --- 步骤 3: 创建定时作业 ---
SQL_STEP_3 = """
USE msdb;
GO
DECLARE @jobId BINARY(16), @jobName NVARCHAR(128) = N'RMS_Daily_Data_Sync';
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL) EXEC dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
EXEC dbo.sp_add_job @job_name=@jobName, @description=N'每日早上6点，从门店服务器同步开台和预订数据到总部。';
EXEC dbo.sp_add_jobstep @job_name=@jobName, @step_name=N'Step 1 - Sync Open Data', @command=N'EXEC operatedata.dbo.usp_Sync_RMS_DailyOpenData;', @on_success_action=3;
EXEC dbo.sp_add_jobstep @job_name=@jobName, @step_name=N'Step 2 - Sync Book Data', @command=N'EXEC operatedata.dbo.usp_Sync_RMS_DailyBookData;';
EXEC dbo.sp_add_schedule @schedule_name=N'Daily at 06:00 AM', @freq_type=4, @freq_interval=1, @active_start_time=60000;
EXEC dbo.sp_attach_schedule @job_name=@jobName, @schedule_name=N'Daily at 06:00 AM';
EXEC dbo.sp_add_jobserver @job_name=@jobName, @server_name = N'(local)';
""")

ALL_STEPS = [SQL_STEP_1, SQL_STEP_2, SQL_STEP_3]

def deploy_system():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} ---')
            
            for i, step_sql in enumerate(ALL_STEPS):
                print(f"--- Executing Step {i+1}... ---")
                # 使用GO作为分隔符来正确处理批处理
                commands = step_sql.strip().split('GO\n')
                for cmd in commands:
                    if cmd.strip():
                        cursor.execute(cmd)
                print(f"--- Step {i+1} completed successfully. ---")
            
            print("Deployment of the new synchronization system is complete.")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    deploy_system()
