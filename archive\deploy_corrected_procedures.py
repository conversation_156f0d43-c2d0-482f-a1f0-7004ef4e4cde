#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署修正后的存储过程
"""

import pyodbc
from datetime import datetime

def connect_database():
    """连接到数据库"""
    try:
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER=192.168.2.5;
        DATABASE=operatedata;
        UID=sa;
        PWD=Musicbox123;
        TrustServerCertificate=yes;
        """
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def deploy_procedure_from_file(connection, file_path, proc_name):
    """从文件部署存储过程"""
    try:
        print(f"\n📄 读取文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 移除注释和空行，分割SQL语句
        sql_statements = []
        current_statement = []
        
        for line in sql_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('--') and not line.startswith('/*'):
                current_statement.append(line)
                if line.upper().endswith('GO') or line.upper() == 'END':
                    if current_statement:
                        sql_statements.append('\n'.join(current_statement))
                        current_statement = []
        
        # 如果还有剩余的语句
        if current_statement:
            sql_statements.append('\n'.join(current_statement))
        
        cursor = connection.cursor()
        
        for i, statement in enumerate(sql_statements):
            if statement.strip() and 'CREATE PROCEDURE' in statement.upper():
                print(f"🔧 部署存储过程: {proc_name}")
                try:
                    cursor.execute(statement)
                    connection.commit()
                    print(f"✅ 存储过程 {proc_name} 部署成功")
                    return True
                except Exception as e:
                    print(f"❌ 部署存储过程失败: {str(e)}")
                    return False
        
        print(f"⚠️ 未找到有效的CREATE PROCEDURE语句")
        return False
        
    except Exception as e:
        print(f"❌ 读取文件或部署过程中发生错误: {str(e)}")
        return False

def verify_procedure_deployment(connection, proc_name):
    """验证存储过程部署"""
    try:
        query = f"""
        SELECT 
            p.name,
            p.create_date,
            p.modify_date,
            CASE WHEN m.definition IS NOT NULL THEN 'EXISTS' ELSE 'NOT_EXISTS' END as status
        FROM sys.procedures p
        LEFT JOIN sys.sql_modules m ON p.object_id = m.object_id
        WHERE p.name = '{proc_name}'
        """
        
        cursor = connection.cursor()
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result:
            print(f"✅ 存储过程验证成功:")
            print(f"   名称: {result[0]}")
            print(f"   创建时间: {result[1]}")
            print(f"   修改时间: {result[2]}")
            print(f"   状态: {result[3]}")
            return True
        else:
            print(f"❌ 存储过程 {proc_name} 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 验证存储过程时发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始部署修正后的存储过程...")
    
    # 连接数据库
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 部署计划
        deployment_plan = [
            {
                'file': 'usp_GenerateSimplifiedDailyReport_V7_Final_Corrected.sql',
                'proc_name': 'usp_GenerateSimplifiedDailyReport_V7_Final_Corrected',
                'description': '修正版本的夜间报表生成存储过程（添加DiscountFree字段）'
            },
            {
                'file': 'usp_RunUnifiedDailyReportJob_Corrected.sql',
                'proc_name': 'usp_RunUnifiedDailyReportJob_Corrected',
                'description': '修正版本的统一日报作业存储过程（完整字段插入）'
            }
        ]
        
        successful_deployments = 0
        
        for plan in deployment_plan:
            print(f"\n" + "=" * 60)
            print(f"📦 部署: {plan['description']}")
            print("=" * 60)
            
            # 部署存储过程
            success = deploy_procedure_from_file(
                connection, 
                plan['file'], 
                plan['proc_name']
            )
            
            if success:
                # 验证部署
                verify_success = verify_procedure_deployment(
                    connection, 
                    plan['proc_name']
                )
                
                if verify_success:
                    successful_deployments += 1
                    print(f"🎉 {plan['proc_name']} 部署并验证成功")
                else:
                    print(f"⚠️ {plan['proc_name']} 部署成功但验证失败")
            else:
                print(f"❌ {plan['proc_name']} 部署失败")
        
        # 部署总结
        print(f"\n" + "=" * 60)
        print("📋 部署总结")
        print("=" * 60)
        print(f"计划部署: {len(deployment_plan)} 个存储过程")
        print(f"成功部署: {successful_deployments} 个存储过程")
        print(f"部署状态: {'✅ 全部成功' if successful_deployments == len(deployment_plan) else '⚠️ 部分失败'}")
        
        if successful_deployments == len(deployment_plan):
            print(f"\n🎯 下一步操作:")
            print("1. 运行测试脚本验证功能:")
            print("   python test_corrected_procedures.py")
            print("2. 或直接执行存储过程:")
            print("   EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected @TargetDate = '2025-07-29', @ShopId = 11")
        else:
            print(f"\n⚠️ 请检查失败的存储过程并重新部署")
        
    except Exception as e:
        print(f"❌ 部署过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
