
import pyodbc

# --- 配置 ---
SERVER = '***********'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# 要按顺序执行的SQL文件列表
SQL_FILES = [
    r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\create_sync_open_data_proc.sql',
    r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\create_sync_book_data_proc.sql',
    r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\create_rms_sync_job.sql'
]

def execute_sql_files_sequentially(files):
    connection_string = (
        f'DRIVER={{ODBC Driver 17 for SQL Server}} மற்றும்'
        f'SERVER={SERVER};'
        f'UID={USERNAME};'
        f'PWD={PASSWORD};'
        f'TrustServerCertificate=yes;'
    )

    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} ---
')
            
            for i, file_path in enumerate(files):
                print(f"--- Executing script {i+1}/{len(files)}: {file_path} ---")
                with open(file_path, 'r', encoding='utf-8') as f:
                    sql_script = f.read()
                
                # 按GO分割命令
                commands = sql_script.split('GO\n')
                for cmd in commands:
                    if cmd.strip():
                        # 切换数据库的操作需要特别处理
                        if cmd.strip().upper().startswith('USE '):
                            conn.execute(cmd)
                            # 打印当前数据库上下文
                            # current_db = conn.execute("SELECT DB_NAME();").fetchone()[0]
                            # print(f"Database context changed to: {current_db}")
                        else:
                            cursor.execute(cmd)
                print(f"--- Script {i+1} executed successfully. ---
")
            
            print("Deployment of the new synchronization system is complete.")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    execute_sql_files_sequentially(SQL_FILES)
