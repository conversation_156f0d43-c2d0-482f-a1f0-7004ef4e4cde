#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版KTV业务分析 - 解决直落识别和补时问题
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta
import json

class EnhancedKTVAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def get_open_close_data(self, work_date):
        """获取开台和结账的关联数据"""
        try:
            # 获取结账数据
            conn = self.get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
            
            close_query = f"""
            SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
            FROM rmcloseinfo 
            WHERE shopid = {self.shop_id} AND WorkDate = '{work_date}'
            ORDER BY CloseDatetime
            """
            
            close_df = pd.read_sql(close_query, conn)
            conn.close()
            
            # 获取开台数据
            conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            open_query = f"""
            SELECT Ikey, BookNo, ShopId, CustName, ComeDate, ComeTime, RmNo
            FROM opencacheinfo
            WHERE shopid = {self.shop_id} AND ComeDate = '{work_date}'
            ORDER BY ComeTime
            """
            
            open_df = pd.read_sql(open_query, conn)
            conn.close()
            
            # 处理开台时间
            if not open_df.empty:
                open_df['OpenDateTime'] = pd.to_datetime(open_df['ComeDate'] + ' ' + open_df['ComeTime'])
            
            return open_df, close_df
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def get_time_slots(self):
        """获取时间段配置"""
        try:
            conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            query = f"""
            SELECT st.*, t.TimeName, t.BegTime, t.EndTime
            FROM shoptimeinfo st
            LEFT JOIN timeinfo t ON st.timeno = t.timeno
            WHERE st.shopid = {self.shop_id}
            ORDER BY t.BegTime
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # 过滤有效的时间段
            df = df.dropna(subset=['BegTime', 'EndTime'])
            return df
            
        except Exception as e:
            print(f"获取时间段配置失败: {e}")
            return pd.DataFrame()
    
    def parse_time_slot(self, beg_time, end_time, base_date):
        """解析时间段为datetime对象"""
        try:
            beg_time = int(beg_time)
            end_time = int(end_time)
            
            beg_hour = beg_time // 100
            beg_min = beg_time % 100
            end_hour = end_time // 100
            end_min = end_time % 100
            
            start_dt = base_date.replace(hour=beg_hour, minute=beg_min, second=0, microsecond=0)
            
            # 如果结束时间小于开始时间，说明跨天了
            if end_time < beg_time:
                end_dt = (base_date + timedelta(days=1)).replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
            else:
                end_dt = base_date.replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
            
            return start_dt, end_dt
            
        except Exception as e:
            print(f"解析时间段失败: {e}")
            return None, None
    
    def time_overlap(self, start1, end1, start2, end2):
        """检查两个时间段是否重叠"""
        return start1 < end2 and end1 > start2
    
    def identify_channel(self, vesa_name):
        """识别消费渠道"""
        if pd.isna(vesa_name) or vesa_name == '':
            return 'K+'
        
        vesa_str = str(vesa_name).lower()
        if '美团' in vesa_str or 'meituan' in vesa_str:
            return '美团'
        elif '抖音' in vesa_str or 'douyin' in vesa_str:
            return '抖音'
        elif '特权' in vesa_str:
            return '特权预约'
        else:
            return 'K+'
    
    def match_open_close(self, open_df, close_df):
        """匹配开台和结账数据"""
        matched_data = []
        
        for _, close_row in close_df.iterrows():
            close_time = pd.to_datetime(close_row['CloseDatetime'])
            
            # 简化匹配逻辑：假设平均消费时长2-4小时
            # 在实际业务中，应该通过房间号、客户信息等进行精确匹配
            possible_opens = open_df[
                (open_df['OpenDateTime'] >= close_time - timedelta(hours=6)) &
                (open_df['OpenDateTime'] <= close_time - timedelta(minutes=30))
            ]
            
            if not possible_opens.empty:
                # 选择最接近的开台记录
                best_match = possible_opens.iloc[0]
                
                matched_data.append({
                    'InvNo': close_row['InvNo'],
                    'OpenDateTime': best_match['OpenDateTime'],
                    'CloseDatetime': close_time,
                    'Tot': close_row['Tot'],
                    'VesaName': close_row['VesaName'],
                    'Channel': self.identify_channel(close_row['VesaName']),
                    'RmNo': best_match.get('RmNo', ''),
                    'CustName': best_match.get('CustName', ''),
                    'Duration': (close_time - best_match['OpenDateTime']).total_seconds() / 3600  # 小时
                })
            else:
                # 没有匹配的开台记录，使用估算
                estimated_open = close_time - timedelta(hours=2)
                matched_data.append({
                    'InvNo': close_row['InvNo'],
                    'OpenDateTime': estimated_open,
                    'CloseDatetime': close_time,
                    'Tot': close_row['Tot'],
                    'VesaName': close_row['VesaName'],
                    'Channel': self.identify_channel(close_row['VesaName']),
                    'RmNo': '',
                    'CustName': '',
                    'Duration': 2.0,  # 估算2小时
                    'Estimated': True
                })
        
        return pd.DataFrame(matched_data)
    
    def analyze_time_slots(self, work_date):
        """分析时间段数据"""
        print(f"\n=== 分析营业日期: {work_date} ===")
        
        # 获取数据
        open_df, close_df = self.get_open_close_data(work_date)
        time_slots = self.get_time_slots()
        
        if close_df.empty:
            print("没有结账数据")
            return {}
        
        print(f"开台记录: {len(open_df)} 条")
        print(f"结账记录: {len(close_df)} 条")
        print(f"时间段配置: {len(time_slots)} 个")
        
        # 匹配开台和结账数据
        matched_df = self.match_open_close(open_df, close_df)
        
        if matched_df.empty:
            print("没有匹配的数据")
            return {}
        
        print(f"匹配成功: {len(matched_df)} 条")
        
        # 基准日期
        base_date = datetime.strptime(work_date, '%Y%m%d')
        
        # 分析结果
        analysis_result = {
            'work_date': work_date,
            'shop_id': self.shop_id,
            'total_revenue': int(matched_df['Tot'].sum()),
            'total_orders': len(matched_df),
            'avg_duration': round(matched_df['Duration'].mean(), 2),
            'time_slot_analysis': {}
        }
        
        print(f"\n总营业额: ¥{analysis_result['total_revenue']:,}")
        print(f"总订单数: {analysis_result['total_orders']}")
        print(f"平均消费时长: {analysis_result['avg_duration']} 小时")
        
        # 分析每个时间段
        print(f"\n=== 时间段重叠分析 ===")
        
        for _, slot in time_slots.iterrows():
            slot_start, slot_end = self.parse_time_slot(
                slot['BegTime'], slot['EndTime'], base_date
            )
            
            if slot_start is None or slot_end is None:
                continue
            
            slot_name = f"{int(slot['BegTime']):04d}-{int(slot['EndTime']):04d}"
            
            # 找到与此时间段重叠的订单
            overlapping_orders = []
            direct_fall_count = 0
            
            for _, order in matched_df.iterrows():
                open_time = order['OpenDateTime']
                close_time = order['CloseDatetime']
                
                # 检查是否与时间段重叠
                if self.time_overlap(open_time, close_time, slot_start, slot_end):
                    overlapping_orders.append(order)
                    
                    # 检查是否为直落（开台时间早于时间段开始）
                    if open_time < slot_start:
                        direct_fall_count += 1
            
            # 统计该时间段的数据
            if overlapping_orders:
                slot_df = pd.DataFrame(overlapping_orders)
                
                # 按渠道统计
                channel_stats = {}
                for channel in slot_df['Channel'].unique():
                    channel_data = slot_df[slot_df['Channel'] == channel]
                    channel_stats[channel] = {
                        'count': len(channel_data),
                        'revenue': int(channel_data['Tot'].sum())
                    }
                
                slot_analysis = {
                    'slot_name': slot.get('TimeName', slot_name),
                    'time_range': f"{slot_start.strftime('%H:%M')}-{slot_end.strftime('%H:%M')}",
                    'total_orders': len(slot_df),
                    'total_revenue': int(slot_df['Tot'].sum()),
                    'direct_fall_count': direct_fall_count,
                    'avg_duration': round(slot_df['Duration'].mean(), 2),
                    'channels': channel_stats
                }
                
                analysis_result['time_slot_analysis'][slot_name] = slot_analysis
                
                print(f"\n【{slot_analysis['slot_name']} ({slot_analysis['time_range']})】")
                print(f"  订单数: {slot_analysis['total_orders']}")
                print(f"  营业额: ¥{slot_analysis['total_revenue']:,}")
                print(f"  直落数: {slot_analysis['direct_fall_count']}")
                print(f"  平均时长: {slot_analysis['avg_duration']} 小时")
                print(f"  渠道分布:")
                for channel, stats in channel_stats.items():
                    print(f"    {channel}: {stats['count']}单, ¥{stats['revenue']:,}")
        
        return analysis_result
    
    def generate_excel_report(self, analysis_result):
        """生成Excel格式的报表"""
        if not analysis_result:
            return
        
        try:
            # 创建报表数据
            report_data = []
            
            for slot_key, slot_data in analysis_result['time_slot_analysis'].items():
                channels = slot_data['channels']
                
                row = {
                    '时间段': slot_data['time_range'],
                    '时段名称': slot_data['slot_name'],
                    '订单数': slot_data['total_orders'],
                    '营业额': slot_data['total_revenue'],
                    '直落数': slot_data['direct_fall_count'],
                    '平均时长': slot_data['avg_duration'],
                    'K+订单': channels.get('K+', {}).get('count', 0),
                    'K+金额': channels.get('K+', {}).get('revenue', 0),
                    '美团订单': channels.get('美团', {}).get('count', 0),
                    '美团金额': channels.get('美团', {}).get('revenue', 0),
                    '抖音订单': channels.get('抖音', {}).get('count', 0),
                    '抖音金额': channels.get('抖音', {}).get('revenue', 0),
                    '特权订单': channels.get('特权预约', {}).get('count', 0),
                    '特权金额': channels.get('特权预约', {}).get('revenue', 0),
                }
                
                report_data.append(row)
            
            # 创建DataFrame并保存
            report_df = pd.DataFrame(report_data)
            
            filename = f"KTV营业报表_{analysis_result['work_date']}.xlsx"
            report_df.to_excel(filename, index=False, engine='openpyxl')
            
            print(f"\nExcel报表已生成: {filename}")
            
        except Exception as e:
            print(f"生成Excel报表失败: {e}")

def main():
    analyzer = EnhancedKTVAnalyzer()
    
    # 分析指定日期
    dates_to_analyze = ['20250717', '20250716']
    
    for date in dates_to_analyze:
        try:
            result = analyzer.analyze_time_slots(date)
            
            if result:
                # 生成Excel报表
                analyzer.generate_excel_report(result)
                
                # 保存JSON结果
                with open(f'enhanced_analysis_{date}.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2, default=str)
                
                print(f"\n详细分析结果已保存到: enhanced_analysis_{date}.json")
            
            print("\n" + "="*100 + "\n")
            
        except Exception as e:
            print(f"分析日期 {date} 时出错: {e}")

if __name__ == "__main__":
    main()
