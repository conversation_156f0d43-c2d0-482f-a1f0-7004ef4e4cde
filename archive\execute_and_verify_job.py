
import pyodbc
import time
import datetime

# --- 配置 ---
# 数据库连接
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=msdb;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
# 要执行的任务名称
JOB_NAME = 'Daily_Dynamic_Report_ForAllShops'
# 要检查的目标数据库和表
TARGET_DB = 'operatedata'
TARGET_TABLE = 'DynamicReport_Header'
# 等待任务执行的时间（秒）
WAIT_SECONDS = 20

# --- 脚本 ---

def execute_and_verify():
    """手动触发一个SQL Agent任务，并验证其是否成功将数据写入目标表。"""
    conn = None
    try:
        # --- 第一步：连接数据库并触发任务 ---
        print(f"正在连接到数据库 'msdb'...")
        conn = pyodbc.connect(CONN_STR)
        # 设置 autocommit=True 很重要，因为 sp_start_job 是一个异步触发器
        conn.autocommit = True 
        cursor = conn.cursor()

        print(f"\n第一步：正在手动触发定时任务 '{JOB_NAME}'...")
        try:
            cursor.execute(f"EXEC dbo.sp_start_job @job_name = ?", JOB_NAME)
            print(f"任务 '{JOB_NAME}' 已成功触发。")
        except pyodbc.Error as job_ex:
            # 检查是否因为 Agent 未运行而失败
            if 'SQLServerAgent is not currently running' in str(job_ex):
                print("\n[测试失败] 无法启动任务，因为 SQL Server Agent 服务当前未运行。")
                print("请先在服务器上启动该服务，然后重试。")
                return
            else:
                raise # 抛出其他任务启动错误

        # --- 第二步：等待任务执行 ---
        print(f"\n第二步：等待 {WAIT_SECONDS} 秒，以便任务有时间执行完成...")
        time.sleep(WAIT_SECONDS)
        print("等待结束。")

        # --- 第三步：验证数据 ---
        print(f"\n第三步：正在检查 '{TARGET_DB}.dbo.{TARGET_TABLE}' 表中是否有新数据...")
        # 切换数据库上下文以查询目标表
        conn.execute(f"USE {TARGET_DB}")
        
        # 计算昨天的日期，格式应为 'YYYY-MM-DD'
        yesterday = datetime.date.today() - datetime.timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')
        print(f"将要检查日期为 '{yesterday_str}' 的数据。")

        # 查询目标表中是否有昨天的数据
        # 假设日期列名为 ReportDate
        query = f"SELECT COUNT(*) FROM dbo.{TARGET_TABLE} WHERE ReportDate = ?"
        cursor.execute(query, yesterday_str)
        row_count = cursor.fetchone()[0]

        # --- 第四步：报告结果 ---
        print("\n--- 测试结果 ---")
        if row_count > 0:
            print(f"[成功] 在 '{TARGET_TABLE}' 表中找到了 {row_count} 条日期为 '{yesterday_str}' 的记录。")
            print("测试通过，数据已成功插入！")
        else:
            print(f"[失败] 在 '{TARGET_TABLE}' 表中没有找到任何日期为 '{yesterday_str}' 的记录。")
            print("请检查：")
            print("1. SQL Server Agent 服务是否已启动并正在运行。")
            print(f"2. 任务 '{JOB_NAME}' 的执行历史，查看是否有错误信息。")
            print(f"3. 存储过程 usp_Job_GenerateDailyPerformanceReport 是否能独立成功运行。")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"\n[测试中断] 数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"\n[测试中断] 发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

if __name__ == "__main__":
    execute_and_verify()
