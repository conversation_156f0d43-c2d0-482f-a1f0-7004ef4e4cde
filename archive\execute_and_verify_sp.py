

import os
import pyodbc
import pandas as pd

# --- Dynamically construct the path to the SQL file ---
# Get the directory where this script is located
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# Join the directory with the SQL file name
SQL_FILE_PATH = os.path.join(SCRIPT_DIR, 'usp_GenerateSimplifiedDailyReport_V7_Final.sql')

# --- Connection Details ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=***********;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- SQL Statements ---
# 1. Read the content of the modified stored procedure
with open(SQL_FILE_PATH, 'r', encoding='utf-8') as f:
    sql_create_procedure = f.read()

# 2. SQL to drop the procedure if it exists
sql_drop_procedure = "IF OBJECT_ID('dbo.usp_GenerateSimplifiedDailyReport_V7_Final', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final"

# 3. SQL to execute the procedure and get the report
sql_exec_procedure = "EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final @ShopId=11, @BeginDate='2025-07-24', @EndDate='2025-07-24'"


# --- Main Execution Logic ---
def deploy_and_run_sp():
    """
    Connects to the database, deploys the stored procedure, executes it, and prints the results.
    """
    try:
        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()
            
            print("Step 1: Dropping existing procedure (if any)...")
            cursor.execute(sql_drop_procedure)
            
            print("Step 2: Creating the new version of the procedure...")
            cursor.execute(sql_create_procedure)
            
            print("Step 3: Executing the procedure to get the report...")
            report_df = pd.read_sql(sql_exec_procedure, conn)
            
            print("\n--- Report Results ---")
            if report_df.empty:
                print("The query returned no results.")
            else:
                print(report_df.to_string())

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database Error Occurred: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    deploy_and_run_sp()

