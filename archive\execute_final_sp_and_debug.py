import pyodbc

# --- Configuration ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

# --- Parameters ---
shop_id = 3
target_date = '2025-05-16'
procedure_name = 'dbo.usp_RunNormalizedDailyReportJob_V2'

def execute_and_debug_sp():
    """Connects to the DB and executes the final SP to catch and analyze errors."""
    cnxn = None
    try:
        print(f"Connecting to {server}...")
        cnxn = pyodbc.connect(cnxn_str)
        cursor = cnxn.cursor()
        print("Connection successful.")

        print(f"\n--- EXECUTING: {procedure_name} for date {target_date} ---")
        
        # We use SET NOCOUNT OFF to ensure we get all messages from the SP
        sql = f"""
            SET NOCOUNT OFF;
            EXEC {procedure_name} @TargetDate = ?, @ShopId = ?;
        """
        
        cursor.execute(sql, target_date, shop_id)
        
        # The cursor might have messages (from PRINT statements) or result sets.
        # We loop through them to make sure we don't miss anything.
        print("\n--- Stored Procedure Output ---")
        while True:
            try:
                # Try to fetch a row. If there's a result set, this will work.
                rows = cursor.fetchall()
                if rows:
                    print("Result Set Found (unexpected, but logging first row):")
                    print(rows[0])
            except pyodbc.ProgrammingError:
                # This error means there are no more results to fetch in the current set.
                pass

            # Move to the next result set (or end if none left)
            if not cursor.nextset():
                break

        print("\n--- EXECUTION SUCCEEDED (at Python level) ---")

    except pyodbc.Error as ex:
        print("\n--- !!! DATABASE ERROR CAUGHT !!! ---")
        print(f"SQLSTATE: {ex.args[0]}")
        print("Full Error Message:")
        print(ex)

    finally:
        if cnxn:
            cnxn.close()
            print("\nDatabase connection closed.")

if __name__ == "__main__":
    execute_and_debug_sp()
