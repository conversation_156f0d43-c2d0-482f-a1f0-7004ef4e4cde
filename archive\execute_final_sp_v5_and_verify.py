import pyodbc

# --- Configuration ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

# --- Parameters ---
shop_id = 3
target_date = '2025-05-16'
procedure_name = 'dbo.usp_RunNormalizedDailyReportJob_V2'

def execute_and_verify_final_sp():
    """Connects to the DB and executes the final, corrected SP (V5 logic)."""
    cnxn = None
    try:
        print(f"Connecting to {server}...\n")
        cnxn = pyodbc.connect(cnxn_str, autocommit=False) # Disable autocommit to see transaction messages
        cursor = cnxn.cursor()
        print("Connection successful.")

        print(f"--- EXECUTING FINAL PROCEDURE: {procedure_name} for date {target_date} ---")
        print("This will attempt to insert data into all 3 tables within a single transaction.\n")
        
        sql = f"""
            SET NOCOUNT ON;
            EXEC {procedure_name} @TargetDate = ?, @ShopId = ?;
        """
        
        # We are executing a stored procedure that doesn't return rows, but prints messages.
        # The messages are retrieved via the connection's messages attribute in pyodbc.
        # Errors, however, will be raised as exceptions.
        cursor.execute(sql, target_date, shop_id)
        
        # Manually commit the transaction if the SP was successful
        cnxn.commit()
        print("\n--- TRANSACTION COMMITTED SUCCESSFULLY ---")
        print("The stored procedure executed without raising a fatal error.")

    except pyodbc.Error as ex:
        print("\n--- !!! DATABASE ERROR CAUGHT !!! ---")
        print("The stored procedure failed and the transaction was rolled back.")
        print(f"SQLSTATE: {ex.args[0]}")
        print("Full Error Message:")
        print(ex)
        try:
            cnxn.rollback()
            print("Transaction has been rolled back.")
        except pyodbc.Error as rb_ex:
            print(f"Error during rollback: {rb_ex}")

    finally:
        if cnxn:
            cnxn.close()
            print("\nDatabase connection closed.")

if __name__ == "__main__":
    execute_and_verify_final_sp()
