import pyodbc
import pandas as pd

# --- Connection Details ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- Optimized SQL Query to find Free Changyin invoices ---
SQL_OPTIMIZED_FREE_CHANGYIN = """
WITH TargetInvoices AS (
    SELECT invno
    FROM rmcloseinfo
    WHERE WorkDate = '20250724' AND ShopId = 11
),
FdData AS (
    SELECT InvNo, FdCName
    FROM FdCashBak
    WHERE ShopId = 11 AND FdCName LIKE N'%自由畅饮%'
)
SELECT DISTINCT ti.invno
FROM TargetInvoices ti
JOIN FdData fdc ON ti.invno = fdc.InvNo COLLATE Chinese_PRC_CI_AS;
"""

# --- Main Execution Logic ---
def execute_optimized_query():
    """
    Connects to the database, executes the optimized query for Free Changyin, and prints the results.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            print("--- Running Optimized Query for: 自由畅饮 Invoices ---")
            df = pd.read_sql(SQL_OPTIMIZED_FREE_CHANGYIN, conn)
            print(f"Found {len(df)} distinct invoices.")
            if not df.empty:
                print(df.to_string(index=False))

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database Error Occurred: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    execute_optimized_query()