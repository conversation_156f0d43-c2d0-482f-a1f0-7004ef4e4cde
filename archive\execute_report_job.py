import pyodbc

# --- Connection Details ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- SQL to Execute the Job ---
# We will run the job for a specific test date to avoid affecting real data.
SQL_EXEC_JOB = "EXEC dbo.usp_RunDailyReportJob @TargetDate = '2025-07-24', @ShopId = 11"

# --- Main Execution Logic ---
def run_report_job():
    """
    Connects to the database and executes the daily report job for a test date.
    """
    try:
        # Using autocommit=True so that the procedure's transaction works correctly.
        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()
            print(f"--- Executing the report job for date 2025-07-24 ---")
            cursor.execute(SQL_EXEC_JOB)
            print("--- Job execution command sent successfully. ---")
            # The procedure itself prints success or failure messages.

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database Error Occurred: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    run_report_job()