

import pyodbc
import sys

# 根据项目文件分析得出的 Dbfood 数据库连接信息
DBFOOD_SERVER = "193.112.2.229"
DBFOOD_DATABASE = "Dbfood"
DBFOOD_USER = "sa"
DBFOOD_PASSWORD = "Musicbox@123"

def get_dbfood_connection():
    """建立到 Dbfood 数据库的连接"""
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={DBFOOD_SERVER};"
        f"DATABASE={DBFOOD_DATABASE};"
        f"UID={DBFOOD_USER};"
        f"PWD={DBFOOD_PASSWORD};"
        f"TrustServerCertificate=yes;"
        f"LoginTimeout=10;"
    )
    try:
        conn = pyodbc.connect(conn_str, autocommit=True)
        return conn
    except pyodbc.Error as ex:
        print(f"数据库连接失败: {ex}", file=sys.stderr)
        return None

def get_table_schema(conn, table_name):
    """获取指定表的结构信息"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"EXEC sp_columns @table_name = ?", (table_name,))
        columns = cursor.fetchall()
        if not columns:
            return f"表 '{table_name}' 不存在或没有列。"
        
        schema_info = f"'{table_name}' 表结构:\n"
        schema_info += "{:<20} {:<15} {:<10}\n".format('列名', '数据类型', '长度')
        schema_info += "-" * 45 + "\n"
        for col in columns:
            schema_info += "{:<20} {:<15} {:<10}\n".format(col[3], col[5], str(col[7]))
        return schema_info
    except pyodbc.Error as e:
        return f"获取表 '{table_name}' 结构失败: {e}"

def main():
    print(f"正在连接到数据库 '{DBFOOD_DATABASE}' on {DBFOOD_SERVER}...")
    conn = get_dbfood_connection()
    
    if conn:
        print("连接成功！开始分析 foinv 表...\n")
        
        # 分析 `fdinv` 表
        fdinv_schema = get_table_schema(conn, 'fdinv')
        print(fdinv_schema)
        
        conn.close()
        print("\n勘探完成，连接已关闭。")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()

