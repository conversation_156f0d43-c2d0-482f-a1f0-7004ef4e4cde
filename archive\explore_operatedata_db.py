import pyodbc
import sys

# Connection details for the operatedata database
OPERATEDATA_SERVER = "192.168.2.5"
OPERATEDATA_DATABASE = "operatedata"
OPERATEDATA_USER = "sa"
OPERATEDATA_PASSWORD = "Musicbox123"

def get_operatedata_connection():
    """Establishes a connection to the operatedata database."""
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={OPERATEDATA_SERVER};"
        f"DATABASE={OPERATEDATA_DATABASE};"
        f"UID={OPERATEDATA_USER};"
        f"PWD={OPERATEDATA_PASSWORD};"
        f"TrustServerCertificate=yes;"
        f"LoginTimeout=10;"
    )
    try:
        conn = pyodbc.connect(conn_str, autocommit=True)
        return conn
    except pyodbc.Error as ex:
        print(f"Database connection failed: {ex}", file=sys.stderr)
        return None

def get_table_schema(conn, table_name):
    """Retrieves the schema for a specific table."""
    try:
        cursor = conn.cursor()
        cursor.execute(f"EXEC sp_columns @table_name = ?", (table_name,))
        columns = cursor.fetchall()
        if not columns:
            return f"Table '{table_name}' does not exist or has no columns."
        
        schema_info = f"Schema for table '{table_name}':\n"
        schema_info += "{:<20} {:<15} {:<10}\n".format('Column Name', 'Data Type', 'Length')
        schema_info += "-" * 45 + "\n"
        for col in columns:
            # Column indices from sp_columns: 3=COLUMN_NAME, 5=TYPE_NAME, 7=COLUMN_SIZE
            schema_info += "{:<20} {:<15} {:<10}\n".format(col[3], col[5], str(col[7]))
        return schema_info
    except pyodbc.Error as e:
        return f"Failed to get schema for table '{table_name}': {e}"

def main():
    print(f"Connecting to database '{OPERATEDATA_DATABASE}' on {OPERATEDATA_SERVER}...")
    conn = get_operatedata_connection()
    
    if conn:
        print("Connection successful! Inspecting candidate real-time tables...\n")
        
        candidate_tables = ["FdCash_xh", "TempFd", "Waiter_Order_Info", "OrderInfo"]
        
        for table in candidate_tables:
            print(f"--- Schema for '{table}' ---")
            schema = get_table_schema(conn, table)
            print(schema)
            print("\n" + "="*50 + "\n")
        
        conn.close()
        print("\nExploration of candidate tables complete. Connection closed.")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()