
import pyodbc
import sys

# Connection details for the remote database
RMS_SERVER = "193.112.2.229"
RMS_DATABASE = "rms2019"
RMS_USER = "sa"
RMS_PASSWORD = "Musicbox@123"

def get_rms_connection():
    """Establishes a connection to the remote rms2019 database."""
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={RMS_SERVER};"
        f"DATABASE={RMS_DATABASE};"
        f"UID={RMS_USER};"
        f"PWD={RMS_PASSWORD};"
        f"TrustServerCertificate=yes;"
        f"LoginTimeout=10;"
    )
    try:
        conn = pyodbc.connect(conn_str, autocommit=True)
        return conn
    except pyodbc.Error as ex:
        print(f"Database connection failed: {ex}", file=sys.stderr)
        return None

def get_table_schema(conn, table_name):
    """Retrieves the schema for a specific table."""
    try:
        cursor = conn.cursor()
        cursor.execute(f"EXEC sp_columns @table_name = ?", (table_name,))
        columns = cursor.fetchall()
        if not columns:
            return f"Table '{table_name}' does not exist or has no columns."
        
        schema_info = f"Schema for table '{table_name}':\n"
        schema_info += "{:<20} {:<15} {:<10}\n".format('Column Name', 'Data Type', 'Length')
        schema_info += "-" * 45 + "\n"
        for col in columns:
            # Column indices from sp_columns: 3=COLUMN_NAME, 5=TYPE_NAME, 7=COLUMN_SIZE
            schema_info += "{:<20} {:<15} {:<10}\n".format(col[3], col[5], str(col[7]))
        return schema_info
    except pyodbc.Error as e:
        return f"Failed to get schema for table '{table_name}': {e}"

def main():
    print(f"Connecting to remote database '{RMS_DATABASE}' on {RMS_SERVER}...")
    conn = get_rms_connection()
    
    if conn:
        print("Connection successful! Finalizing data source exploration...\n")
        
        # 1. Investigate 'rtinfo' table for room count
        print("--- Investigating 'rtinfo' table (for total room count) ---")
        rtinfo_schema = get_table_schema(conn, 'rtinfo')
        print(rtinfo_schema)
        
        conn.close()
        print("\nExploration complete. Connection closed.")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
