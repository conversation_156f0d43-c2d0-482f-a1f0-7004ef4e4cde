

import pyodbc
import sys

# 从之前的分析中获取的远程数据库连接信息
RMS_SERVER = "193.112.2.229"
RMS_DATABASE = "rms2019"
RMS_USER = "sa"
RMS_PASSWORD = "Musicbox@123"

def get_rms_connection():
    """建立到远程 rms2019 数据库的连接"""
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={RMS_SERVER};"
        f"DATABASE={RMS_DATABASE};"
        f"UID={RMS_USER};"
        f"PWD={RMS_PASSWORD};"
        f"TrustServerCertificate=yes;"
        f"LoginTimeout=10;"
    )
    try:
        conn = pyodbc.connect(conn_str, autocommit=True)
        return conn
    except pyodbc.Error as ex:
        print(f"数据库连接失败: {ex}", file=sys.stderr)
        return None

def get_table_schema(conn, table_name):
    """获取指定表的结构信息"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"EXEC sp_columns @table_name = ?", (table_name,))
        columns = cursor.fetchall()
        if not columns:
            return f"表 '{table_name}' 不存在或没有列。"
        
        schema_info = f"'{table_name}' 表结构:\n"
        schema_info += "{:<20} {:<15} {:<10}\n".format('列名', '数据类型', '长度')
        schema_info += "-" * 45 + "\n"
        for col in columns:
            schema_info += "{:<20} {:<15} {:<10}\n".format(col[3], col[5], str(col[7]))
        return schema_info
    except pyodbc.Error as e:
        return f"获取表 '{table_name}' 结构失败: {e}"

def main():
    print(f"正在连接到远程数据库 '{RMS_DATABASE}' on {RMS_SERVER}...")
    conn = get_rms_connection()
    
    if conn:
        print("连接成功！开始进行数据勘探...\n")
        
        # 1. 获取 openhistory 表结构
        print("--- 1. 分析 'openhistory' 表 --- ")
        openhistory_schema = get_table_schema(conn, 'openhistory')
        print(openhistory_schema)
        print("\n" + "="*50 + "\n")
        
        # 2. 获取 opencacheinfo 表结构
        print("--- 2. 分析 'opencacheinfo' 表 --- ")
        opencacheinfo_schema = get_table_schema(conn, 'opencacheinfo')
        print(opencacheinfo_schema)
        
        conn.close()
        print("\n数据勘探完成，连接已关闭。")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()

