
import pyodbc

# Database connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# List of (database, table) tuples to explore
TABLES_TO_EXPLORE = [
    ('MIMS', 'memberinfo'),
    ('MIMS', 'ShopInfo'),
    ('operatedata', 'RmCloseInfo'),
    ('operatedata', 'FdCashBak'),
    ('operatedata', 'timeinfo')
]

def explore_schemas():
    """Connects to the database and prints the schema of specified tables."""
    conn = None
    try:
        print("正在连接到数据库服务器...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print("--- 开始探查源数据表结构 ---")
        for db_name, table_name in TABLES_TO_EXPLORE:
            print(f"\n========== 正在分析: [{db_name}].[dbo].[{table_name}] ==========")
            query = f"""
            SELECT 
                COLUMN_NAME, 
                DATA_TYPE, 
                ISNULL(CHARACTER_MAXIMUM_LENGTH, '-') AS MAX_LENGTH
            FROM {db_name}.INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ?
            ORDER BY ORDINAL_POSITION;
            """
            try:
                cursor.execute(query, table_name)
                rows = cursor.fetchall()
                if not rows:
                    print(f"错误：无法找到表 '{table_name}' 或其列信息。")
                    continue
                
                print(f"{ '列名':<30} {'数据类型':<20} {'最大长度':<10}")
                print("-" * 65)
                for row in rows:
                    print(f"{row.COLUMN_NAME:<30} {row.DATA_TYPE:<20} {str(row.MAX_LENGTH):<10}")

            except pyodbc.Error as ex_inner:
                print(f"查询表 {db_name}.{table_name} 的结构时出错: {ex_inner}")
        
        print("\n========== 远程表 (rms2019) 结构推断 ==========")
        print("基于之前的存储过程分析，我们推断出以下关键远程表字段：")
        print("  - bookhistory/bookcacheinfo: ComeDate, ShopID, RmNo, Beg_Key, End_Key, Numbers, isdelete")
        print("  - opencacheinfo/openhistory: ComeDate, ShopID, RmNo, OpenDateTime, CloseDatetime, InvNo, Beg_Key, End_Key, Numbers")
        print("--- 探查完成 ---")

    except pyodbc.Error as ex:
        print(f"数据库连接或查询出错: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")