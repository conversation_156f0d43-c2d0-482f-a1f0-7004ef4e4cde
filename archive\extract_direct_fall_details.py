

import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 查询参数 ---
TARGET_SHOP_ID = 11
TARGET_DATES = ['20250718', '20250719', '20250720'] 

# --- 输出文件名 ---
OUTPUT_FILENAME = 'direct_fall_bill_details_0718_0720.csv'

def extract_bill_details():
    """提取指定日期范围内，包含‘直落’下单项的所有账单的全部消费明细。"""
    cnxn = None
    try:
        # 1. 首先，找到所有在指定日期范围内包含‘直落’项的账单InvNo
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} ---")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        print("--- 步骤A: 正在查找包含‘直落’项的账单列表... ---")
        placeholders = ', '.join('?' * len(TARGET_DATES))
        sql_find_invoices = f"""SELECT DISTINCT 
                                   rc.InvNo 
                               FROM 
                                   dbo.RmCloseInfo_Test rc
                               JOIN 
                                   dbo.FdCashBak fb ON rc.InvNo = fb.InvNo
                               WHERE 
                                   rc.ShopId = ? 
                                   AND rc.WorkDate IN ({placeholders}) 
                                   AND fb.FdCName LIKE ?;"""
        params_find_invoices = [TARGET_SHOP_ID] + TARGET_DATES + ['%直落%']
        df_invoices = pd.read_sql_query(sql_find_invoices, cnxn, params=params_find_invoices)
        
        invoice_list = df_invoices['InvNo'].tolist()
        if not invoice_list:
            print("在指定的时间范围内，没有找到任何包含‘直落’下单项的账单。")
            return

        print(f"找到了 {len(invoice_list)} 张包含‘直落’项的账单，正在提取这些账单的全部消费明细...")

        # 2. 根据获取到的InvNo列表，提取所有相关的消费明细
        placeholders_details = ', '.join('?' * len(invoice_list))
        sql_get_details = f"""SELECT 
                                 rc.WorkDate, 
                                 fb.InvNo, 
                                 rc.OpenDateTime, 
                                 rc.CloseDatetime, 
                                 fb.FdCName, 
                                 fb.FdQty, 
                                 fb.FdPrice
                             FROM 
                                 dbo.FdCashBak fb
                             JOIN
                                 dbo.RmCloseInfo_Test rc ON fb.InvNo = rc.InvNo AND rc.WorkDate IN ({placeholders})
                             WHERE 
                                 fb.InvNo IN ({placeholders_details})
                             ORDER BY
                                 rc.WorkDate, fb.InvNo, fb.CashTime;"""
        params_get_details = TARGET_DATES + invoice_list
        df_details = pd.read_sql_query(sql_get_details, cnxn, params=params_get_details)

        # 3. 保存为CSV文件
        print(f"\n--- 步骤B: 正在将 {len(df_details)} 条详细消费记录保存到文件... ---")
        df_details.to_csv(OUTPUT_FILENAME, index=False, encoding='utf-8-sig')
        
        print("\n--- 成功！ ---")
        print(f"所有相关账单的详细消费数据已成功保存，请查看文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    extract_bill_details()

