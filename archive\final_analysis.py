#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KTV最终业务分析 - 实现完整的时间段重叠和直落分析
解决补时vs真直落的识别问题
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta
import json
import numpy as np

class FinalKTVAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def get_business_data(self, work_date):
        """获取完整的业务数据"""
        print(f"获取 {work_date} 的业务数据...")
        
        # 获取结账数据
        conn = self.get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
        
        close_query = f"""
        SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
        FROM rmcloseinfo 
        WHERE shopid = {self.shop_id} AND WorkDate = '{work_date}'
        ORDER BY CloseDatetime
        """
        
        close_df = pd.read_sql(close_query, conn)
        conn.close()
        
        # 获取时间段配置
        conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
        
        time_query = f"""
        SELECT st.*, t.TimeName, t.BegTime, t.EndTime
        FROM shoptimeinfo st
        LEFT JOIN timeinfo t ON st.timeno = t.timeno
        WHERE st.shopid = {self.shop_id}
        ORDER BY t.BegTime
        """
        
        time_df = pd.read_sql(time_query, conn)
        conn.close()
        
        # 过滤有效时间段
        time_df = time_df.dropna(subset=['BegTime', 'EndTime'])
        
        return close_df, time_df
    
    def parse_time_slot(self, beg_time, end_time, base_date):
        """解析时间段"""
        try:
            beg_time = int(beg_time)
            end_time = int(end_time)
            
            beg_hour = beg_time // 100
            beg_min = beg_time % 100
            end_hour = end_time // 100
            end_min = end_time % 100
            
            start_dt = base_date.replace(hour=beg_hour, minute=beg_min, second=0, microsecond=0)
            
            # 处理跨天时间段
            if end_time < beg_time:
                end_dt = (base_date + timedelta(days=1)).replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
            else:
                end_dt = base_date.replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
            
            return start_dt, end_dt
            
        except Exception as e:
            print(f"解析时间段失败: {e}")
            return None, None
    
    def identify_channel(self, vesa_name):
        """识别消费渠道"""
        if pd.isna(vesa_name) or vesa_name == '':
            return 'K+'
        
        vesa_str = str(vesa_name).lower()
        if '美团' in vesa_str:
            return '美团'
        elif '抖音' in vesa_str:
            return '抖音'
        elif '特权' in vesa_str:
            return '特权预约'
        else:
            return 'K+'
    
    def estimate_consumption_period(self, close_time, avg_duration=2.5):
        """估算消费时段"""
        close_dt = pd.to_datetime(close_time)
        
        # 根据结账时间和平均消费时长估算开台时间
        estimated_open = close_dt - timedelta(hours=avg_duration)
        
        return estimated_open, close_dt
    
    def time_overlap(self, start1, end1, start2, end2):
        """检查两个时间段是否重叠"""
        return start1 < end2 and end1 > start2
    
    def classify_direct_fall(self, estimated_open, close_time, slot_start, slot_end):
        """分类直落类型"""
        # 如果开台时间早于时间段开始，且结账时间在时间段内或之后
        if estimated_open < slot_start and close_time >= slot_start:
            
            # 检查是否可能是补时
            # 如果结账时间远超时间段结束时间，可能是补时
            if close_time > slot_end + timedelta(hours=1):
                return 'possible_overtime'  # 可能补时
            else:
                return 'true_direct_fall'   # 真直落
        
        return 'normal'  # 正常消费
    
    def analyze_complete_business(self, work_date):
        """完整业务分析"""
        print(f"\n{'='*60}")
        print(f"KTV业务数据分析 - {work_date}")
        print(f"门店: 名堂店 (ID: {self.shop_id})")
        print(f"{'='*60}")
        
        # 获取数据
        close_df, time_df = self.get_business_data(work_date)
        
        if close_df.empty:
            print("没有结账数据")
            return {}
        
        print(f"\n数据概览:")
        print(f"  结账记录: {len(close_df)} 条")
        print(f"  时间段配置: {len(time_df)} 个")
        print(f"  总营业额: ¥{close_df['Tot'].sum():,}")
        
        # 添加渠道信息
        close_df['Channel'] = close_df['VesaName'].apply(self.identify_channel)
        
        # 估算消费时段
        consumption_data = []
        for _, row in close_df.iterrows():
            estimated_open, close_time = self.estimate_consumption_period(row['CloseDatetime'])
            
            consumption_data.append({
                'InvNo': row['InvNo'],
                'EstimatedOpen': estimated_open,
                'CloseTime': close_time,
                'Tot': row['Tot'],
                'Channel': row['Channel'],
                'VesaName': row['VesaName']
            })
        
        consumption_df = pd.DataFrame(consumption_data)
        
        # 基准日期
        base_date = datetime.strptime(work_date, '%Y%m%d')
        
        # 分析结果
        analysis_result = {
            'work_date': work_date,
            'shop_id': self.shop_id,
            'summary': {
                'total_revenue': int(close_df['Tot'].sum()),
                'total_orders': len(close_df),
                'channel_distribution': close_df['Channel'].value_counts().to_dict()
            },
            'time_slot_analysis': {}
        }
        
        print(f"\n渠道分布:")
        for channel, count in analysis_result['summary']['channel_distribution'].items():
            channel_revenue = close_df[close_df['Channel'] == channel]['Tot'].sum()
            print(f"  {channel}: {count}单, ¥{channel_revenue:,}")
        
        print(f"\n{'='*60}")
        print("时间段重叠分析")
        print(f"{'='*60}")
        
        # 分析每个时间段
        for _, slot in time_df.iterrows():
            slot_start, slot_end = self.parse_time_slot(
                slot['BegTime'], slot['EndTime'], base_date
            )
            
            if slot_start is None or slot_end is None:
                continue
            
            slot_key = f"{int(slot['BegTime']):04d}-{int(slot['EndTime']):04d}"
            slot_name = slot.get('TimeName', slot_key)
            
            # 找到重叠的消费记录
            overlapping_records = []
            direct_fall_analysis = {
                'true_direct_fall': 0,
                'possible_overtime': 0,
                'normal': 0
            }
            
            for _, record in consumption_df.iterrows():
                estimated_open = record['EstimatedOpen']
                close_time = record['CloseTime']
                
                # 检查时间段重叠
                if self.time_overlap(estimated_open, close_time, slot_start, slot_end):
                    overlapping_records.append(record)
                    
                    # 分类直落类型
                    fall_type = self.classify_direct_fall(
                        estimated_open, close_time, slot_start, slot_end
                    )
                    direct_fall_analysis[fall_type] += 1
            
            if overlapping_records:
                slot_df = pd.DataFrame(overlapping_records)
                
                # 按渠道统计
                channel_stats = {}
                for channel in slot_df['Channel'].unique():
                    channel_data = slot_df[slot_df['Channel'] == channel]
                    channel_stats[channel] = {
                        'count': len(channel_data),
                        'revenue': int(channel_data['Tot'].sum())
                    }
                
                slot_analysis = {
                    'slot_name': slot_name,
                    'time_range': f"{slot_start.strftime('%H:%M')}-{slot_end.strftime('%H:%M')}",
                    'total_orders': len(slot_df),
                    'total_revenue': int(slot_df['Tot'].sum()),
                    'direct_fall_analysis': direct_fall_analysis,
                    'channels': channel_stats
                }
                
                analysis_result['time_slot_analysis'][slot_key] = slot_analysis
                
                # 输出分析结果
                print(f"\n【{slot_name} ({slot_analysis['time_range']})】")
                print(f"  总订单数: {slot_analysis['total_orders']}")
                print(f"  总营业额: ¥{slot_analysis['total_revenue']:,}")
                print(f"  直落分析:")
                print(f"    真直落: {direct_fall_analysis['true_direct_fall']} 单")
                print(f"    可能补时: {direct_fall_analysis['possible_overtime']} 单")
                print(f"    正常消费: {direct_fall_analysis['normal']} 单")
                print(f"  渠道分布:")
                for channel, stats in channel_stats.items():
                    print(f"    {channel}: {stats['count']}单, ¥{stats['revenue']:,}")
        
        return analysis_result
    
    def generate_business_report(self, analysis_result):
        """生成业务报表"""
        if not analysis_result:
            return
        
        print(f"\n{'='*80}")
        print("营业报表汇总")
        print(f"{'='*80}")
        
        # 表头
        print(f"{'时间段':<15} {'订单数':<8} {'营业额':<12} {'真直落':<8} {'补时':<8} {'K+':<8} {'美团':<8} {'抖音':<8}")
        print("-" * 80)
        
        # 数据行
        for slot_key, slot_data in analysis_result['time_slot_analysis'].items():
            channels = slot_data['channels']
            direct_fall = slot_data['direct_fall_analysis']
            
            print(f"{slot_data['time_range']:<15} "
                  f"{slot_data['total_orders']:<8} "
                  f"¥{slot_data['total_revenue']:<11,} "
                  f"{direct_fall['true_direct_fall']:<8} "
                  f"{direct_fall['possible_overtime']:<8} "
                  f"{channels.get('K+', {}).get('count', 0):<8} "
                  f"{channels.get('美团', {}).get('count', 0):<8} "
                  f"{channels.get('抖音', {}).get('count', 0):<8}")
        
        print("-" * 80)
        print(f"{'合计':<15} "
              f"{analysis_result['summary']['total_orders']:<8} "
              f"¥{analysis_result['summary']['total_revenue']:<11,}")
        
        # 业务洞察
        print(f"\n{'='*60}")
        print("业务洞察")
        print(f"{'='*60}")
        
        total_direct_fall = sum(
            slot['direct_fall_analysis']['true_direct_fall'] 
            for slot in analysis_result['time_slot_analysis'].values()
        )
        
        total_overtime = sum(
            slot['direct_fall_analysis']['possible_overtime'] 
            for slot in analysis_result['time_slot_analysis'].values()
        )
        
        print(f"1. 直落分析:")
        print(f"   - 真直落客户: {total_direct_fall} 单")
        print(f"   - 可能补时客户: {total_overtime} 单")
        print(f"   - 直落比例: {total_direct_fall/analysis_result['summary']['total_orders']*100:.1f}%")
        
        print(f"\n2. 渠道分析:")
        for channel, count in analysis_result['summary']['channel_distribution'].items():
            percentage = count / analysis_result['summary']['total_orders'] * 100
            print(f"   - {channel}: {count}单 ({percentage:.1f}%)")
        
        print(f"\n3. 建议:")
        if total_overtime > total_direct_fall:
            print("   - 补时客户较多，建议优化时间段设置或加强时间管理")
        if analysis_result['summary']['channel_distribution'].get('K+', 0) > analysis_result['summary']['total_orders'] * 0.8:
            print("   - K+渠道占比较高，建议加强其他渠道推广")

def main():
    analyzer = FinalKTVAnalyzer()
    
    # 分析最近的数据
    dates_to_analyze = ['20250717', '20250716']
    
    for date in dates_to_analyze:
        try:
            result = analyzer.analyze_complete_business(date)
            
            if result:
                analyzer.generate_business_report(result)
                
                # 保存详细结果
                with open(f'final_analysis_{date}.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2, default=str)
                
                print(f"\n详细分析结果已保存到: final_analysis_{date}.json")
            
            print("\n" + "="*100 + "\n")
            
        except Exception as e:
            print(f"分析日期 {date} 时出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
