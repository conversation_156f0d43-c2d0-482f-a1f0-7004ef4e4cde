
import pyodbc

# --- 数据库连接配置 ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
# --- 配置结束 ---

# --- 诊断参数 ---
target_date = '20250724'
shop_id = 11
# --- 参数结束 ---

# 构建连接字符串
connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

# 这个查询直接检查最根本的源数据，移除了所有JOIN
sql_query = f"""
SELECT TOP 10 InvNo, FdCName, FdPrice, FdQty
FROM operatedata.dbo.FdCashBak
WHERE ShopId = ? 
  AND InvNo IN (SELECT InvNo FROM dbo.RmCloseInfo WHERE WorkDate = ? AND ShopId = ?)
  AND FdCName LIKE N'%直落%';
"""

connection = None
try:
    connection = pyodbc.connect(connection_string)
    cursor = connection.cursor()
    
    print(f"--- Final Diagnostic: Directly querying FdCashBak ---")
    print(f"Target Date: {target_date}")
    print(f"Shop ID: {shop_id}")
    print("-----------------------------------------------------")

    cursor.execute(sql_query, shop_id, target_date, shop_id)
    rows = cursor.fetchall()

    if rows:
        print("SUCCESS: Found direct fall items in FdCashBak for the target date!")
        print("This means the issue is in the JOIN logic of `usp_UpdateDirectFallFlag_ByName`.")
        print("--- Sample Found Data ---")
        for row in rows:
            print(f"InvNo: {row.InvNo}, FdCName: {row.FdCName}, Price: {row.FdPrice}, Qty: {row.FdQty}")
    else:
        print("FAILURE: No records with FdCName LIKE '%直落%' found in FdCashBak for the target date.")
        print("This confirms the issue is with the source data, not the stored procedure logic.")

except pyodbc.Error as ex:
    print(f"Database query failed: {ex}")

finally:
    if connection:
        connection.close()
