#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终正确的直落分析
基于业务规则：只有20点前进场的白天档自助餐才有直落概念
"""

from datetime import datetime, timed<PERSON>ta

def analyze_direct_fall_orders():
    """分析真正的直落订单"""
    
    print("="*80)
    print("20250717名堂店白天档直落分析")
    print("业务规则：只有20点前进场的白天档自助餐才有直落概念")
    print("="*80)
    
    # 从SQL查询结果中找到的白天档直落预约订单
    direct_fall_candidates = [
        {
            'InvNo': 'A02426806',
            'CustName': '张女士',
            'ComeTime': '11:41:10',
            'Beg_Name': '11:50-14:50',
            'End_Name': '13:30-16:30',
            'Numbers': 4,
            'RmNo': 308,
            'Remark': '14：50后按四位用餐直落至17：50',
            'CloseDatetime': '2025-07-17 14:47:49.303',
            'Tot': 609
        },
        {
            'InvNo': 'A02426879',
            'CustName': '珍先生',
            'ComeTime': '19:25:53',
            'Beg_Name': '19:00-22:00',
            'End_Name': '20:00',
            'Numbers': 5,
            'RmNo': 802,
            'Remark': '伍位，美团四人姐妹X1到1点',
            'CloseDatetime': '2025-07-18 01:57:46.637',
            'Tot': 197
        }
    ]
    
    # 时间段配置
    time_slots = {
        '11:50-14:50': {'timetype': 12, 'start': '11:50', 'end': '14:50'},
        '13:30-16:30': {'timetype': 2, 'start': '13:30', 'end': '16:30'},
        '19:00-22:00': {'timetype': 12, 'start': '19:00', 'end': '22:00'},
        '20:00': {'timetype': 126, 'start': '20:00', 'end': '次日01:00'}
    }
    
    print(f"\n📊 找到 {len(direct_fall_candidates)} 个白天档直落预约订单")
    
    real_direct_fall = []
    
    for order in direct_fall_candidates:
        print(f"\n" + "="*60)
        print(f"【{order['InvNo']} - {order['CustName']}】")
        print(f"开台时间: {order['ComeTime']}")
        print(f"预约时间段: {order['Beg_Name']} → {order['End_Name']}")
        print(f"人数: {order['Numbers']}位")
        print(f"房间: {order['RmNo']}")
        print(f"备注: {order['Remark']}")
        print(f"结账时间: {order['CloseDatetime']}")
        print(f"金额: ¥{order['Tot']}")
        
        # 判断是否白天档进场
        come_time = datetime.strptime(order['ComeTime'], '%H:%M:%S').time()
        cutoff_time = datetime.strptime('20:00:00', '%H:%M:%S').time()
        is_daytime = come_time < cutoff_time
        
        print(f"进场类型: {'✅ 白天档' if is_daytime else '❌ 夜场'}")
        
        if not is_daytime:
            print(f"❌ 判断结果: 夜场进场，无直落概念")
            continue
        
        # 分析时间段组合
        from_slot = time_slots.get(order['Beg_Name'])
        to_slot = time_slots.get(order['End_Name'])
        
        if from_slot and to_slot:
            print(f"时间段组合: TimeType {from_slot['timetype']} → {to_slot['timetype']}")
            
            # 判断timetype是否支持直落
            can_direct_fall = (from_slot['timetype'] & to_slot['timetype']) > 0 or from_slot['timetype'] == to_slot['timetype']
            print(f"TimeType直落支持: {'✅ 支持' if can_direct_fall else '❌ 不支持'}")
        else:
            print(f"❓ 时间段配置未找到")
            can_direct_fall = False
        
        # 计算实际消费时长
        try:
            come_datetime = datetime.strptime(f"2025-07-17 {order['ComeTime']}", '%Y-%m-%d %H:%M:%S')
            close_datetime = datetime.strptime(order['CloseDatetime'], '%Y-%m-%d %H:%M:%S.%f')
            
            duration = close_datetime - come_datetime
            duration_hours = duration.total_seconds() / 3600
            
            print(f"实际消费时长: {duration_hours:.1f}小时")
            
            # 白天档直落需要≥3小时
            meets_duration = duration_hours >= 3.0
            print(f"时长要求: {'✅ 符合(≥3h)' if meets_duration else '❌ 不足(<3h)'}")
            
            # 最终判断
            if is_daytime and can_direct_fall and meets_duration:
                print(f"🎯 最终判断: ✅ 真正直落")
                real_direct_fall.append(order)
            else:
                reasons = []
                if not is_daytime:
                    reasons.append("夜场进场")
                if not can_direct_fall:
                    reasons.append("时间段不支持直落")
                if not meets_duration:
                    reasons.append("消费时长不足")
                print(f"🎯 最终判断: ❌ 非直落 ({', '.join(reasons)})")
                
        except Exception as e:
            print(f"❌ 时长计算错误: {e}")
    
    # 总结
    print(f"\n" + "="*80)
    print(f"📈 最终统计结果:")
    print(f"  白天档直落预约订单: {len(direct_fall_candidates)}")
    print(f"  真正直落订单: {len(real_direct_fall)}")
    print(f"  直落成功率: {len(real_direct_fall)/len(direct_fall_candidates)*100:.1f}%")
    
    if real_direct_fall:
        print(f"\n✅ 真正直落订单汇总:")
        total_amount = 0
        for order in real_direct_fall:
            print(f"  {order['InvNo']} - {order['CustName']} - ¥{order['Tot']}")
            total_amount += order['Tot']
        print(f"  直落订单总金额: ¥{total_amount}")
    else:
        print(f"\n❌ 20250717没有找到真正的直落订单")
    
    # 业务洞察
    print(f"\n💡 业务洞察:")
    print(f"1. A02426806 (张女士): 11:41开台，预约11:50-14:50→13:30-16:30，但14:47就结账了")
    print(f"   - 备注显示'14：50后按四位用餐直落至17：50'，但实际没有直落")
    print(f"   - 可能是取消了直落或提前离开")
    print(f"2. A02426879 (珍先生): 19:25开台，预约19:00-22:00→20:00，消费到次日01:57")
    print(f"   - 虽然跨越了20点，但19:25是白天档进场")
    print(f"   - 消费时长6.5小时，符合直落要求")
    print(f"   - 这是一个真正的直落订单！")

if __name__ == "__main__":
    analyze_direct_fall_orders()
