import os
import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=***********;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_DATE = '20250724'
SHOP_ID = 11
JOB_PROC_NAME = 'dbo.usp_RunNormalizedDailyReportJob_V2'

# --- Use os.path.join for robust path construction ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
JOB_SQL_FILE = os.path.join(SCRIPT_DIR, 'usp_RunNormalizedDailyReportJob_V2_modified.sql')

# --- Main Execution Logic ---
def deploy_and_verify_job_final():
    """
    Deploys and verifies the final, corrected job procedure using a strict DROP-CREATE pattern.
    """
    try:
        # Step 1: Read the definitive SQL source code from the file
        print(f"--- Reading source code from {JOB_SQL_FILE} ---")
        with open(JOB_SQL_FILE, 'r', encoding='utf-8') as f:
            sql_to_deploy = f.read()

        # Step 2: Connect and deploy using strict DROP-CREATE pattern
        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()
            
            print(f"--- Deploying {JOB_PROC_NAME} (DROP-CREATE)... ---")
            # Drop the procedure if it exists
            cursor.execute(f"IF OBJECT_ID('{JOB_PROC_NAME}', 'P') IS NOT NULL DROP PROCEDURE {JOB_PROC_NAME}")
            # Create the new procedure from the file
            cursor.execute(sql_to_deploy)
            print("Job procedure deployed successfully.")

            # Step 3: Execution
            print(f"--- Running the job for {TARGET_DATE}... ---")
            cursor.execute(f"EXEC {JOB_PROC_NAME} @ShopId={SHOP_ID}, @TargetDate='{TARGET_DATE}'")
            print("Job executed successfully.")

            # Step 4: Verification
            print("--- Verifying data in FullDailyReport_Header... ---")
            df_final = pd.read_sql(f"SELECT * FROM FullDailyReport_Header WHERE ReportDate = '{TARGET_DATE}' AND ShopID = {SHOP_ID}", conn)
            
            print("\n--- FINAL VERIFICATION RESULT ---")
            pd.set_option('display.max_columns', None)
            print(df_final.to_string())

    except FileNotFoundError:
        print(f"ERROR: Source SQL file not found at {JOB_SQL_FILE}")
    except pyodbc.Error as ex:
        print(f"DATABASE ERROR: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    deploy_and_verify_job_final()