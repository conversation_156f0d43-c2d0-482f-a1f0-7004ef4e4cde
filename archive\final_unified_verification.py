import os
import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=***********;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_DATE = '20250724'
SHOP_ID = 11
JOB_PROC_NAME = 'dbo.usp_RunUnifiedDailyReportJob'

# --- Use os.path.join for robust path construction ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
JOB_SQL_FILE = os.path.join(SCRIPT_DIR, 'usp_RunUnifiedDailyReportJob.sql')

# --- Main Execution Logic ---
def deploy_and_verify_unified_job_final():
    """
    Deploys and verifies the final, unified report job using a strict DROP-CREATE pattern.
    """
    try:
        # Step 1: Read the definitive SQL source code from the file
        print(f"--- Reading source code from {JOB_SQL_FILE} ---")
        with open(JOB_SQL_FILE, 'r', encoding='utf-8') as f:
            sql_to_deploy = f.read()

        # Step 2: Connect and deploy using strict DROP-CREATE pattern
        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()
            
            print(f"--- Deploying {JOB_PROC_NAME} (DROP-CREATE)... ---")
            # Drop the procedure if it exists
            cursor.execute(f"IF OBJECT_ID('{JOB_PROC_NAME}', 'P') IS NOT NULL DROP PROCEDURE {JOB_PROC_NAME}")
            # Create the new procedure from the file
            cursor.execute(sql_to_deploy)
            print("Unified job procedure deployed successfully.")

            # Step 3: Execution
            print(f"--- Running the unified job for {TARGET_DATE}... ---")
            cursor.execute(f"EXEC {JOB_PROC_NAME} @ShopId={SHOP_ID}, @TargetDate='{TARGET_DATE}'")
            print("Unified job executed successfully.")

            # Step 4: Verification
            print("\n--- Verifying data in all three report tables... ---")
            
            print("\n--- 1. FullDailyReport_Header ---")
            df_header = pd.read_sql(f"SELECT * FROM FullDailyReport_Header WHERE ReportDate = '{TARGET_DATE}' AND ShopID = {SHOP_ID}", conn)
            print(df_header.to_string())

            # Get the new ReportID to verify other tables
            if not df_header.empty:
                report_id = df_header.iloc[0]['ReportID']
                print(f"\n--- 2. FullDailyReport_NightDetails (for ReportID: {report_id}) ---")
                df_night = pd.read_sql(f"SELECT * FROM FullDailyReport_NightDetails WHERE ReportID = {report_id}", conn)
                print(df_night.to_string())

                print(f"\n--- 3. FullDailyReport_TimeSlotDetails (for ReportID: {report_id}) ---")
                df_timeslot = pd.read_sql(f"SELECT * FROM FullDailyReport_TimeSlotDetails WHERE ReportID = {report_id}", conn)
                print(df_timeslot.to_string())
            else:
                print("Header not found, cannot verify detail tables.")

    except FileNotFoundError:
        print(f"ERROR: Source SQL file not found at {JOB_SQL_FILE}")
    except pyodbc.Error as ex:
        print(f"DATABASE ERROR: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    deploy_and_verify_unified_job_final()