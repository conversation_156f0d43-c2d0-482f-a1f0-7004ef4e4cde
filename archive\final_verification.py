

import os
import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=***********;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_DATE = '20250724'
SHOP_ID = 11
PROC_NAME = 'dbo.usp_GenerateDayTimeReport_Simple_V3'

# --- Use os.path.join for robust path construction ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
SOURCE_SQL_FILE = os.path.join(SCRIPT_DIR, 'usp_GenerateDayTimeReport_Simple_V3_source.sql')

# --- Main Execution Logic ---
def deploy_and_verify_final():
    """
    The simplest, most robust deployment and verification script.
    """
    try:
        # Step 1: Read the definitive SQL source code from the file
        print(f"--- Reading source code from {SOURCE_SQL_FILE} ---")
        with open(SOURCE_SQL_FILE, 'r', encoding='utf-8') as f:
            sql_to_deploy = f.read()

        # Step 2: Connect and deploy using strict DROP-CREATE pattern
        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()
            
            print(f"--- Deploying {PROC_NAME}... ---")
            # Drop the procedure if it exists
            cursor.execute(f"IF OBJECT_ID('{PROC_NAME}', 'P') IS NOT NULL DROP PROCEDURE {PROC_NAME}")
            # Create the new procedure from the file
            cursor.execute(sql_to_deploy)
            print("Procedure deployed successfully.")

            # Step 3: Verification
            print("--- Running for verification... ---")
            df_final = pd.read_sql(f"EXEC {PROC_NAME} @ShopId={SHOP_ID}, @TargetDate='{TARGET_DATE}'", conn)
            
            print("\n--- FINAL RESULT ---")
            print(df_final.to_string())

    except FileNotFoundError:
        print(f"ERROR: Source SQL file not found at {SOURCE_SQL_FILE}")
    except pyodbc.Error as ex:
        print(f"DATABASE ERROR: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    deploy_and_verify_final()

