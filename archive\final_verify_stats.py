
import pyodbc

# --- 配置 ---
SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

def final_verify():
    connection_string = (
        'DRIVER={ODBC Driver 17 for SQL Server};'
        f'SERVER={SERVER};'
        f'DATABASE={DATABASE};'
        f'UID={USERNAME};'
        f'PWD={PASSWORD};'
        f'TrustServerCertificate=yes;'
    )

    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} ---')

            # 1. 首先，检查从表是否存在
            print("\nStep 1: Verifying existence of 'RoomStatusHourlyDetail' table...")
            check_table_sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'RoomStatusHourlyDetail';"
            cursor.execute(check_table_sql)
            if cursor.fetchone()[0] == 0:
                print("--- FATAL ERROR: Table 'RoomStatusHourlyDetail' does not exist! ---")
                print("Please re-run the upgrade script (run_upgrade_stats_v2.py).")
                return
            print("Table 'RoomStatusHourlyDetail' found.")

            # 2. 执行存储过程，插入新数据
            print("\nStep 2: Executing usp_LogHourlyRoomStatistics...")
            cursor.execute("EXEC dbo.usp_LogHourlyRoomStatistics;")
            print("Execution complete.")

            # 3. 查询主表的最新一条记录
            print("\nStep 3: Fetching the latest MASTER record...")
            cursor.execute("SELECT TOP 1 * FROM dbo.RoomStatisticsHourly ORDER BY LogID DESC;")
            master_row = cursor.fetchone()
            
            if not master_row:
                print("Could not retrieve the new master record.")
                return

            new_log_id = master_row.LogID
            print("--- LATEST HOURLY STATS (MASTER) ---")
            columns = [column[0] for column in cursor.description]
            for i, col_name in enumerate(columns):
                print(f"{col_name}: {master_row[i]}")

            # 4. 根据主表LogID查询从表的明细记录
            print(f"\nStep 4: Fetching the DETAIL records for LogID: {new_log_id}...")
            cursor.execute("SELECT RmStatus, StatusCount FROM dbo.RoomStatusHourlyDetail WHERE LogID = ?;", new_log_id)
            detail_rows = cursor.fetchall()

            if detail_rows:
                print("--- HOURLY STATUS DETAILS (DETAIL) ---")
                for row in detail_rows:
                    print(f"Status: '{row.RmStatus}', Count: {row.StatusCount}")
            else:
                print("No detail records found for this LogID.")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    final_verify()
