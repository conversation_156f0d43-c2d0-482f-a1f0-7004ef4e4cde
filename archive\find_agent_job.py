
import pyodbc

try:
    conn = pyodbc.connect(
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=192.168.2.5;"
        "DATABASE=msdb;"
        "UID=sa;"
        "PWD=Musicbox123;"
    )
    cursor = conn.cursor()
    cursor.execute("""
        SELECT j.name AS job_name, s.step_name, s.command 
        FROM msdb.dbo.sysjobs j 
        JOIN msdb.dbo.sysjobsteps s ON j.job_id = s.job_id 
        WHERE s.command LIKE '%StaffReport%'
    """)
    rows = cursor.fetchall()
    if rows:
        for row in rows:
            print(f"Job Name: {row.job_name}")
            print(f"Step Name: {row.step_name}")
            print(f"Command: {row.command}")
            print("---")
    else:
        print("No SQL Server Agent job found calling 'StaffReport'.")
except Exception as e:
    print(f"An error occurred: {e}")
finally:
    if 'conn' in locals() and conn:
        conn.close()
