

import pyodbc

def find_procedure_source_for_table(server, database, username, password, table_name):
    """Finds stored procedures that reference a specific table and retrieves their source code."""
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
    
    try:
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()

            # Find procedures that reference the table
            find_query = """
            SELECT DISTINCT o.name AS procedure_name
            FROM sys.sql_modules m
            INNER JOIN sys.objects o ON m.object_id = o.object_id
            WHERE m.definition LIKE ? AND o.type = 'P';
            """
            
            search_pattern = f'%{table_name}%'
            cursor.execute(find_query, search_pattern)
            procedures = cursor.fetchall()

            if not procedures:
                print(f"No stored procedures found that reference the table '{table_name}'.")
                return

            print(f"Found procedures referencing '{table_name}':")
            for proc in procedures:
                print(f"- {proc.procedure_name}")

            # Get the definition of the first procedure found
            first_proc_name = procedures[0].procedure_name
            print(f"\n--- Analyzing source code for: {first_proc_name} ---")
            
            get_def_query = """
            SELECT m.definition
            FROM sys.sql_modules m
            INNER JOIN sys.objects o ON m.object_id = o.object_id
            WHERE o.name = ? AND o.type = 'P';
            """
            cursor.execute(get_def_query, first_proc_name)
            definition = cursor.fetchone()

            if definition:
                print(definition[0])
            else:
                print(f"Could not retrieve definition for '{first_proc_name}'.")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database connection error: {sqlstate}")
        print(ex)

if __name__ == '__main__':
    find_procedure_source_for_table(
        server='192.168.2.5',
        database='operatedata',
        username='sa',
        password='Musicbox123',
        table_name='RmCloseInfo_Day'
    )

