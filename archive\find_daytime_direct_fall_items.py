

import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 查询参数 ---
TARGET_SHOP_ID = 11
# 今天是2025-07-24，所以前两天是22和23号
TARGET_DATES = ['20250722', '20250723'] 

def find_direct_fall_items_single_db():
    """在一个数据库内直接关联查询，找出白天账单中的‘直落’下单项。"""
    cnxn = None
    try:
        # 1. 构建SQL查询语句
        placeholders = ', '.join('?' * len(TARGET_DATES))
        sql_query = f"""SELECT 
                         fb.FdCName 
                     FROM 
                         dbo.RmCloseInfo rc
                     JOIN 
                         dbo.FdCashBak fb ON rc.InvNo = fb.InvNo
                     WHERE 
                         rc.ShopId = ? 
                         AND rc.WorkDate IN ({placeholders}) 
                         AND rc.OpenDateTime IS NOT NULL 
                         AND DATEPART(hour, rc.OpenDateTime) < 20
                         AND fb.FdCName LIKE ?;"""
        
        params = [TARGET_SHOP_ID] + TARGET_DATES + ['%直落%']

        # 2. 连接数据库并执行查询
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} 并执行关联查询... ---")
        cnxn = pyodbc.connect(conn_str)
        df = pd.read_sql_query(sql_query, cnxn, params=params)
        print(f"查询完成，共找到 {len(df)} 条与‘直落’相关的下单记录。")

        # 3. 分析和汇总结果
        print("\n--- 分析结果：直落项目汇总 ---")
        if df.empty:
            print("在指定日期范围的白天账单中，没有找到任何名称包含‘直落’的下单项。")
        else:
            summary = df['FdCName'].value_counts().reset_index()
            summary.columns = ['下单项名称 (FdCName)', '出现次数']
            print(summary.to_string(index=False))

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    find_direct_fall_items_single_db()
