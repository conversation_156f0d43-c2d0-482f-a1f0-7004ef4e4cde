
import pyodbc

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- Query to find table names ---
SQL_FIND_TABLES = """
SELECT table_name
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' 
  AND (table_name LIKE '%Open%' OR table_name LIKE '%History%')
ORDER BY table_name;
"""

# --- Main Execution Logic ---
def find_history_table():
    """
    Connects to the database and searches for tables related to open history.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            print("--- Searching for tables containing 'Open' or 'History' ---")
            cursor = conn.cursor()
            cursor.execute(SQL_FIND_TABLES)
            tables = cursor.fetchall()
            
            if not tables:
                print("No relevant tables found.")
            else:
                print("Found potential tables:")
                for table in tables:
                    print(f"  - {table[0]}")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"DATABASE ERROR: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    find_history_table()
