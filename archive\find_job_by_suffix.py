
import pyodbc

# Database connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=msdb;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
JOB_SUFFIX = 'ForAllShop'

# SQL query to find jobs with a specific suffix
SQL_QUERY = f"""
SELECT
    j.name AS job_name,
    j.description,
    j.enabled,
    s.step_id,
    s.step_name,
    s.command
FROM
    dbo.sysjobs j
INNER JOIN
    dbo.sysjobsteps s ON j.job_id = s.job_id
WHERE
    j.name LIKE '%%{JOB_SUFFIX}'
ORDER BY
    j.name, s.step_id;
"""

def find_job_with_suffix():
    """Connects to msdb and finds jobs with a specific name suffix."""
    conn = None
    try:
        print(f"正在连接到数据库 'msdb'...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print(f"正在查找名称以 '{JOB_SUFFIX}' 结尾的定时任务...")
        cursor.execute(SQL_QUERY)
        rows = cursor.fetchall()

        if not rows:
            print(f"\n查询完成。没有找到任何名称以 '{JOB_SUFFIX}' 结尾的定时任务。")
            return

        print(f"\n查询到以下匹配的定时任务：")
        print("-" * 80)

        current_job = ""
        for row in rows:
            if row.job_name != current_job:
                if current_job != "":
                    print("-" * 80)
                current_job = row.job_name
                print(f"任务名称: {row.job_name}")
                print(f"描述: {row.description}")
                print(f"是否启用: {'是' if row.enabled else '否'}")
            
            print(f"  - 步骤 {row.step_id}: {row.step_name}")
            print(f"    命令: {row.command.strip()}")

        print("-" * 80)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

if __name__ == "__main__":
    find_job_with_suffix()
