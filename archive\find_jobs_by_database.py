
import pyodbc
import datetime

# Database connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=msdb;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
DB_NAME_TO_FIND = 'operatedata'

# SQL query to find all job steps that reference a specific database
# and also get the last run status for the parent job.
SQL_QUERY = f"""
WITH LastJobRun AS (
    SELECT
        job_id,
        run_status,
        run_date,
        run_time,
        ROW_NUMBER() OVER(PARTITION BY job_id ORDER BY run_date DESC, run_time DESC) as rn
    FROM
        dbo.sysjobhistory
    WHERE step_id = 0 -- Job outcome status
)
SELECT DISTINCT
    j.name AS job_name,
    s.step_id,
    s.step_name,
    s.command,
    CASE ljr.run_status
        WHEN 0 THEN 'Failed'
        WHEN 1 THEN 'Succeeded'
        WHEN 2 THEN 'Retry'
        WHEN 3 THEN 'Canceled'
        WHEN 4 THEN 'In progress'
        ELSE 'Unknown / Not run'
    END AS last_run_status,
    ljr.run_date,
    ljr.run_time
FROM
    dbo.sysjobs j
INNER JOIN
    dbo.sysjobsteps s ON j.job_id = s.job_id
LEFT JOIN
    LastJobRun ljr ON j.job_id = ljr.job_id AND ljr.rn = 1
WHERE
    s.command LIKE '%%{DB_NAME_TO_FIND}%%'
    AND j.enabled = 1
ORDER BY
    j.name, s.step_id;
"""

def format_datetime(date_int, time_int):
    """Converts SQL Server's date and time integers to a datetime object."""
    if date_int is None or time_int is None or date_int == 0:
        return None
    s_date = str(date_int)
    s_time = str(time_int).zfill(6)
    try:
        dt_str = f"{s_date[:4]}-{s_date[4:6]}-{s_date[6:]} {s_time[:2]}:{s_time[2:4]}:{s_time[4:]}"
        return datetime.datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
    except (ValueError, IndexError):
        return None

def find_jobs_for_db():
    """Connects to msdb and finds jobs that interact with a specific database."""
    conn = None
    try:
        print(f"正在连接到数据库 'msdb'...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print(f"正在查找引用了 '{DB_NAME_TO_FIND}' 数据库的定时任务...")
        cursor.execute(SQL_QUERY)
        rows = cursor.fetchall()

        if not rows:
            print(f"\n查询完成。没有找到任何引用了 '{DB_NAME_TO_FIND}' 的定时任务。\n")
            return

        print(f"\n查询到 {len(rows)} 个步骤引用了 '{DB_NAME_TO_FIND}' 数据库：")
        print("-" * 80)

        for row in rows:
            last_run_dt = format_datetime(row.run_date, row.run_time)
            print(f"任务名称: {row.job_name}")
            print(f"步骤名称: {row.step_name}")
            print(f"最后运行状态: {row.last_run_status}")
            if last_run_dt:
                print(f"最后运行时间: {last_run_dt.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                print("最后运行时间: 从未运行")
            print(f"执行的命令: {row.command.strip()}")
            print("-" * 80)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。\n")

if __name__ == "__main__":
    find_jobs_for_db()
