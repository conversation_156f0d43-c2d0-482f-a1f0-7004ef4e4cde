
import pyodbc

try:
    conn = pyodbc.connect(
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=192.168.2.5;"
        "DATABASE=operatedata;"
        "UID=sa;"
        "PWD=Musicbox123;"
    )
    cursor = conn.cursor()
    cursor.execute("""
        SELECT o.name 
        FROM sys.sql_modules m 
        JOIN sys.objects o ON m.object_id = o.object_id 
        WHERE m.definition LIKE '%rmcloseinfo_day%' AND o.type = 'P'
    """)
    rows = cursor.fetchall()
    if rows:
        for row in rows:
            print(f"Found stored procedure: {row[0]}")
    else:
        print("No stored procedure found referencing 'rmcloseinfo_day'.")
except Exception as e:
    print(f"An error occurred: {e}")
finally:
    if 'conn' in locals() and conn:
        conn.close()
