
import pyodbc

SERVER = '192.168.2.5'
DATABASE = 'msdb' # 作业信息存储在 msdb
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
PROC_NAME = 'date_Rms2019'

SQL_QUERY = f"""
SELECT
    j.name AS JobName,
    j.enabled AS IsJobEnabled,
    s.step_id AS StepID,
    s.step_name AS StepName,
    s.command AS Command,
    sch.name AS ScheduleName,
    CASE sch.freq_type
        WHEN 4 THEN 'Daily'
        ELSE 'Other'
    END AS Frequency,
    sch.active_start_time AS StartTime
FROM 
    dbo.sysjobs j
JOIN 
    dbo.sysjobsteps s ON j.job_id = s.job_id
LEFT JOIN
    dbo.sysjobschedules js ON j.job_id = js.job_id
LEFT JOIN
    dbo.sysschedules sch ON js.schedule_id = sch.schedule_id
WHERE
    s.command LIKE '%%{PROC_NAME}%%';
"""

def find_job_details():
    connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;'
    
    try:
        with pyodbc.connect(connection_string, timeout=15) as conn:
            cursor = conn.cursor()
            cursor.execute(SQL_QUERY)
            rows = cursor.fetchall()
            
            if not rows:
                print(f"--- No SQL Server Agent job found that calls the procedure '{PROC_NAME}'. ---")
                return

            print(f"--- Found job(s) calling '{PROC_NAME}' ---")
            for row in rows:
                print("\n--- JOB DETAILS ---")
                print(f"Job Name: {row.JobName}")
                print(f"Is Enabled: {{'Yes' if row.IsJobEnabled == 1 else 'No'}}")
                print(f"Step: {row.StepID} - {row.StepName}")
                print(f"Command: {row.Command}")
                print(f"Schedule: {row.ScheduleName} ({row.Frequency} at {row.StartTime})")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    find_job_details()
