
import pyodbc

SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

KEYWORD = 'opencacheinfo'

SQL_QUERY = f"""
SELECT 
    p.name AS ProcedureName,
    m.definition AS ProcedureDefinition
FROM 
    sys.procedures p
JOIN 
    sys.sql_modules m ON p.object_id = m.object_id
WHERE 
    m.definition LIKE '%%{KEYWORD}%%'
ORDER BY
    p.name;
"""

def find_procedures_with_keyword():
    connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;'
    
    try:
        with pyodbc.connect(connection_string, timeout=15) as conn:
            cursor = conn.cursor()
            cursor.execute(SQL_QUERY)
            rows = cursor.fetchall()
            
            if not rows:
                print(f"No stored procedures found containing the keyword: '{KEYWORD}'")
                return

            print(f"--- Found {len(rows)} procedures containing '{KEYWORD}' ---")
            for row in rows:
                print(f"\n--- PROCEDURE: {row.ProcedureName} ---")
                # 打印部分定义作为预览
                print(row.ProcedureDefinition[:1000] + '...')

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    find_procedures_with_keyword()
