import pyodbc
import sys

# --- 连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
TABLE_NAME = 'KTV_Simplified_Daily_Report'

def find_object_definitions():
    """
    连接到SQL Server并搜索所有对象定义中包含指定表名的对象。
    """
    cnxn = None
    try:
        # 构建连接字符串
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        
        # 连接到数据库
        print(f"正在连接到数据库: {DATABASE}...")
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("连接成功！")

        # 查询系统视图来查找引用了该表的对象
        query = f"""
        SELECT
            o.name AS object_name,
            o.type_desc
        FROM sys.sql_modules m
        INNER JOIN sys.objects o ON m.object_id = o.object_id
        WHERE m.definition LIKE '%%{TABLE_NAME}%%'
        ORDER BY o.type_desc, o.name;
        """

        print(f"\n正在搜索包含 '{TABLE_NAME}' 的对象定义...")
        cursor.execute(query)
        
        results = cursor.fetchall()
        
        if not results:
            print(f"\n--- 未找到任何结果 ---")
            print(f"数据库中没有找到任何存储过程、视图或函数直接引用了 '{TABLE_NAME}'。")
            print("这可能意味着：")
            print("1. 该表是通过一个没有保存在数据库中的 .sql 文件直接创建的。")
            print("2. 您可能需要检查项目中的 .sql 文件，查找 'CREATE TABLE {TABLE_NAME}'。")
            return

        print(f"\n--- 搜索结果 ---")
        print(f"找到了 {len(results)} 个在定义中引用了 '{TABLE_NAME}' 的对象：")
        
        for row in results:
            print(f"- 对象名称: {row.object_name} (类型: {row.type_desc})")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错！")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
        sys.exit(1)
        
    except Exception as e:
        print(f"发生未知错误: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    find_object_definitions()