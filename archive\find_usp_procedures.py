
import pyodbc

# Database connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# SQL query to find all stored procedures starting with 'usp_' 
# ordered by the most recently modified.
SQL_QUERY = """
SELECT
    name AS procedure_name,
    create_date,
    modify_date
FROM
    sys.procedures
WHERE
    name LIKE 'usp_%%'
ORDER BY
    modify_date DESC;
"""

def find_usp_procedures():
    """Connects to the database and lists stored procedures with their creation/modification dates."""
    conn = None
    try:
        print(f"正在连接到数据库 'operatedata'...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print("正在查询以 'usp_' 开头的存储过程...")
        cursor.execute(SQL_QUERY)
        rows = cursor.fetchall()

        if not rows:
            print("\n查询完成。没有找到任何以 'usp_' 开头的存储过程。")
            return

        print(f"\n查询到 {len(rows)} 个匹配的存储过程 (按最后修改时间倒序):\n")
        print(f"{ '存储过程名称':<60} {'最后修改时间':<25} {'创建时间':<25}")
        print("-" * 110)

        for row in rows:
            # Format dates for better readability
            modify_dt = row.modify_date.strftime('%Y-%m-%d %H:%M:%S')
            create_dt = row.create_date.strftime('%Y-%m-%d %H:%M:%S')
            print(f"{row.procedure_name:<60} {modify_dt:<25} {create_dt:<25}")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

if __name__ == "__main__":
    find_usp_procedures()
