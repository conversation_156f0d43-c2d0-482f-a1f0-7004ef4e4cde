

import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_TABLE = 'rmcloseinfo'
TARGET_SCHEMA = 'dbo'
SHOP_ID = 11
WORK_DATE = '20250724'
SEARCH_VALUE = '183'

# --- Main Execution Logic ---
def find_value_in_all_columns():
    """
    Dynamically builds and executes a SQL query to find a value in any column of a table.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            # Step 1: Get all column names for the target table
            print(f"--- Step 1: Fetching column names for {TARGET_TABLE}... ---")
            sql_get_columns = f"""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = '{TARGET_SCHEMA}' AND TABLE_NAME = '{TARGET_TABLE}'
            """
            df_columns = pd.read_sql(sql_get_columns, conn)
            column_names = df_columns['COLUMN_NAME'].tolist()
            print(f"Found {len(column_names)} columns.")

            # Step 2: Build the dynamic WHERE clause
            print(f"--- Step 2: Building dynamic WHERE clause to search for '{SEARCH_VALUE}'... ---")
            where_clauses = []
            for col in column_names:
                # Add a clause for each column, casting it to NVARCHAR for the LIKE comparison
                where_clauses.append(f"CAST([{col}] AS NVARCHAR(MAX)) LIKE '%{SEARCH_VALUE}%'")
            
            full_where_clause = " OR ".join(where_clauses)

            # Step 3: Build and execute the final query
            sql_final_query = f"""
            SELECT * 
            FROM {TARGET_SCHEMA}.{TARGET_TABLE}
            WHERE ShopId = {SHOP_ID} 
              AND WorkDate = '{WORK_DATE}'
              AND ({full_where_clause})
            """
            
            print("--- Step 3: Executing the final query... ---")
            df_results = pd.read_sql(sql_final_query, conn)

            # Step 4: Display results
            print(f"\n--- SEARCH RESULTS: Found {len(df_results)} records containing '{SEARCH_VALUE}' ---")
            if not df_results.empty:
                pd.set_option('display.max_columns', None)
                print(df_results.to_string())
            else:
                print("No records matched the criteria.")

    except pyodbc.Error as ex:
        print(f"DATABASE ERROR: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    find_value_in_all_columns()

