

import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_SCHEMA = 'dbo'
SHOP_ID = 11
WORK_DATE = '20250724'
SEARCH_VALUE = '183'

# --- Main Execution Logic ---
def find_value_in_joined_tables():
    """
    Dynamically builds and executes a SQL query to find a value in any column 
    of a JOIN between fdinv and fdcashbak.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            # Step 1: Get column names for both tables
            print("--- Step 1: Fetching column names for fdinv and fdcashbak... ---")
            
            sql_get_fdinv_cols = f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '{TARGET_SCHEMA}' AND TABLE_NAME = 'fdinv'"
            df_fdinv_cols = pd.read_sql(sql_get_fdinv_cols, conn)
            fdinv_cols = df_fdinv_cols['COLUMN_NAME'].tolist()
            print(f"Found {len(fdinv_cols)} columns in fdinv.")

            sql_get_fdcashbak_cols = f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '{TARGET_SCHEMA}' AND TABLE_NAME = 'fdcashbak'"
            df_fdcashbak_cols = pd.read_sql(sql_get_fdcashbak_cols, conn)
            fdcashbak_cols = df_fdcashbak_cols['COLUMN_NAME'].tolist()
            print(f"Found {len(fdcashbak_cols)} columns in fdcashbak.")

            # Step 2: Build the dynamic WHERE clause for the joined tables
            print(f"--- Step 2: Building dynamic WHERE clause to search for '{SEARCH_VALUE}'... ---")
            where_clauses = []
            for col in fdinv_cols:
                where_clauses.append(f"CAST(fi.[{col}] AS NVARCHAR(MAX)) LIKE '%{SEARCH_VALUE}%'")
            for col in fdcashbak_cols:
                where_clauses.append(f"CAST(fdc.[{col}] AS NVARCHAR(MAX)) LIKE '%{SEARCH_VALUE}%'")
            
            full_where_clause = " OR ".join(where_clauses)

            # Step 3: Build and execute the final, complex JOIN query
            # We first find the relevant invoices from rmcloseinfo to limit the search space.
            sql_final_query = f"""
            WITH RelevantInvoices AS (
                SELECT InvNo FROM {TARGET_SCHEMA}.rmcloseinfo
                WHERE ShopId = {SHOP_ID} AND WorkDate = '{WORK_DATE}'
            )
            SELECT 
                fi.*, 
                fdc.* 
            FROM {TARGET_SCHEMA}.fdinv fi
            JOIN {TARGET_SCHEMA}.fdcashbak fdc ON fi.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            -- Ensure we only search within the invoices for the target date
            JOIN RelevantInvoices ri ON fi.InvNo = ri.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE fi.ShopId = {SHOP_ID}
              AND ({full_where_clause})
            """
            
            print("--- Step 3: Executing the final joined query... ---")
            df_results = pd.read_sql(sql_final_query, conn)

            # Step 4: Display results
            print(f"\n--- SEARCH RESULTS: Found {len(df_results)} joined records containing '{SEARCH_VALUE}' ---")
            if not df_results.empty:
                pd.set_option('display.max_columns', None)
                print(df_results.to_string())
            else:
                print("No records matched the criteria.")

    except pyodbc.Error as ex:
        print(f"DATABASE ERROR: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    find_value_in_joined_tables()

