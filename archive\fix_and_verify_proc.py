
import pyodbc

SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

# 包含了主从表插入逻辑的、最终正确的存储过程定义
CORRECT_PROC_SQL = """
CREATE OR ALTER PROCEDURE usp_LogHourlyRoomStatistics
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;

    DECLARE @NewLogID INT;
    DECLARE @TotalRoomsBeforeFilter INT, @ValidRoomsCount INT, @BadRoomsCount INT, @OccupiedRoomsCount INT;
    DECLARE @OccupancyRate DECIMAL(5, 2);

    SELECT @TotalRoomsBeforeFilter = COUNT(*) FROM dbo.ROOM;

    SELECT 
        @ValidRoomsCount = COUNT(*),
        @BadRoomsCount = SUM(CASE WHEN RmStatus = 'B' THEN 1 ELSE 0 END),
        @OccupiedRoomsCount = SUM(CASE WHEN RmStatus <> 'N' AND RmStatus <> 'B' THEN 1 ELSE 0 END)
    FROM 
        dbo.ROOM
    WHERE 
        RmNo NOT LIKE '%B';

    IF (ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0)) > 0
        SET @OccupancyRate = (CAST(ISNULL(@OccupiedRoomsCount, 0) AS DECIMAL(10, 2)) / (ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0))) * 100;
    ELSE
        SET @OccupancyRate = 0;

    INSERT INTO dbo.RoomStatisticsHourly (
        TotalRoomsBeforeFilter, ValidRoomsCount, BadRoomsCount, AvailableRoomsCount, OccupancyRate
    ) 
    VALUES (
        @TotalRoomsBeforeFilter, ISNULL(@ValidRoomsCount, 0), ISNULL(@BadRoomsCount, 0), 
        ISNULL(@ValidRoomsCount, 0) - ISNULL(@BadRoomsCount, 0), @OccupancyRate
    );

    SET @NewLogID = SCOPE_IDENTITY();

    INSERT INTO dbo.RoomStatusHourlyDetail (LogID, RmStatus, StatusCount)
    SELECT 
        @NewLogID, 
        RmStatus, 
        COUNT(*)
    FROM 
        dbo.ROOM
    WHERE 
        RmNo NOT LIKE '%B'
    GROUP BY 
        RmStatus;

    COMMIT TRANSACTION;
END
"""

def fix_and_verify():
    connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;'
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print("--- Connected. Forcibly updating stored procedure... ---")
            cursor.execute(CORRECT_PROC_SQL)
            print("--- Procedure updated. Now running verification... ---")

            # 执行并验证
            cursor.execute("EXEC dbo.usp_LogHourlyRoomStatistics;")
            cursor.execute("SELECT TOP 1 * FROM dbo.RoomStatisticsHourly ORDER BY LogID DESC;")
            master_row = cursor.fetchone()
            new_log_id = master_row.LogID
            print("\n--- LATEST MASTER RECORD ---")
            print(dict(zip([column[0] for column in master_row.cursor_description], master_row)))

            cursor.execute("SELECT RmStatus, StatusCount FROM dbo.RoomStatusHourlyDetail WHERE LogID = ?;", new_log_id)
            detail_rows = cursor.fetchall()
            print("\n--- LATEST DETAIL RECORDS ---")
            for row in detail_rows:
                print(f"Status: '{row.RmStatus}', Count: {row.StatusCount}")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    fix_and_verify()
