#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复并重新创建优化版夜间详情存储过程
"""

import pyodbc

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def create_fixed_night_procedure(connection):
    """创建修复版的夜间详情存储过程"""
    print("🚀 创建修复版夜间详情存储过程...")
    
    # 先删除存储过程（如果存在）
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Fixed', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Fixed"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Fixed
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    CREATE TABLE #DailyReports (
        ReportDate date, 
        ShopName nvarchar(50), 
        Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), 
        DayTimeRevenue decimal(18, 2), 
        NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, 
        DayTimeBatchCount int, 
        NightTimeBatchCount int,
        FreeMeal_KPlus int, 
        FreeMeal_Special int, 
        FreeMeal_Meituan int, 
        FreeMeal_Douyin int,
        FreeMeal_BatchCount int, 
        FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, 
        Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, 
        Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, 
        NonPackage_Meituan int, 
        NonPackage_Douyin int, 
        NonPackage_RoomFee int,
        NonPackage_YearCard int,
        Night_Verify_BatchCount int, 
        Night_Verify_Revenue decimal(18, 2),
        DiscountFree_BatchCount int, 
        DiscountFree_Revenue decimal(18, 2)
    );

    DECLARE @CurrentDate DATE = @BeginDate;

    WHILE @CurrentDate <= @EndDate
    BEGIN
        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));
        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));

        WITH RecordsWithTimeMode AS (
            SELECT
                rt.*,
                sti.TimeMode,
                (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
                CASE
                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                    WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                    ELSE 2
                END AS RevenueClassificationMode
            FROM dbo.RmCloseInfo AS rt
            LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
        )

        INSERT INTO #DailyReports
        SELECT
            @CurrentDate AS ReportDate,
            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
            DATENAME(weekday, @CurrentDate) AS Weekday,
            
            -- 基础收入统计
            ISNULL(SUM(Revenue), 0) AS TotalRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,
            
            -- 基础批次统计
            COUNT(*) AS TotalBatchCount,
            COUNT(CASE WHEN RevenueClassificationMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN RevenueClassificationMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,

            -- 免费餐相关（晚上档 + CtNo=19）
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 AND CtNo = 2 THEN 1 ELSE NULL END) AS FreeMeal_KPlus,
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 AND AliPay > 0 THEN 1 ELSE NULL END) AS FreeMeal_Special,
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 AND MTPay > 0 THEN 1 ELSE NULL END) AS FreeMeal_Meituan,
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 AND DZPay > 0 THEN 1 ELSE NULL END) AS FreeMeal_Douyin,
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 THEN 1 ELSE NULL END) AS FreeMeal_BatchCount,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 THEN Revenue ELSE 0 END), 0) AS FreeMeal_Revenue,

            -- 买断和畅饮（通过FdCashBak表查询）
            (SELECT COUNT(DISTINCT r.InvNo) 
             FROM RecordsWithTimeMode r
             JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
             WHERE r.RevenueClassificationMode = 2 AND fdc.ShopId = @ShopId AND fdc.FdCName LIKE N'%买断%') AS Buyout_BatchCount,
            
            ISNULL((SELECT SUM(fdc.FdPrice * fdc.FdQty) 
                    FROM RecordsWithTimeMode r
                    JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
                    WHERE r.RevenueClassificationMode = 2 AND fdc.ShopId = @ShopId AND fdc.FdCName LIKE N'%买断%'), 0) AS Buyout_Revenue,
            
            (SELECT COUNT(DISTINCT r.InvNo) 
             FROM RecordsWithTimeMode r
             JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
             WHERE r.RevenueClassificationMode = 2 AND fdc.ShopId = @ShopId 
             AND fdc.FdCName LIKE N'%畅饮%' AND fdc.FdCName NOT LIKE N'%自由畅饮%') AS Changyin_BatchCount,
            
            ISNULL((SELECT SUM(fdc.FdPrice * fdc.FdQty) 
                    FROM RecordsWithTimeMode r
                    JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
                    WHERE r.RevenueClassificationMode = 2 AND fdc.ShopId = @ShopId 
                    AND fdc.FdCName LIKE N'%畅饮%' AND fdc.FdCName NOT LIKE N'%自由畅饮%'), 0) AS Changyin_Revenue,
            
            (SELECT COUNT(DISTINCT r.InvNo) 
             FROM RecordsWithTimeMode r
             JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
             WHERE r.RevenueClassificationMode = 2 AND fdc.ShopId = @ShopId AND fdc.FdCName LIKE N'%自由畅饮%') AS FreeConsumption_BatchCount,

            -- 非套餐相关（晚上档 + CtNo<>19）
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo <> 19 AND AliPay > 0 THEN 1 ELSE NULL END) AS NonPackage_Special,
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo <> 19 AND MTPay > 0 THEN 1 ELSE NULL END) AS NonPackage_Meituan,
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo <> 19 AND DZPay > 0 THEN 1 ELSE NULL END) AS NonPackage_Douyin,
            
            -- 新增：晚间档房费批次（现金或现金目标 > 0）
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo <> 19 AND (Cash > 0 OR Cash_Targ > 0) THEN 1 ELSE NULL END) AS NonPackage_RoomFee,
            
            -- 修改：年卡批次（替换原来的Others）
            (SELECT COUNT(DISTINCT r.InvNo) 
             FROM RecordsWithTimeMode r
             JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
             WHERE r.RevenueClassificationMode = 2 AND fdc.ShopId = @ShopId AND fdc.FdCName LIKE N'%年卡%') AS NonPackage_YearCard,

            -- 验证字段（减去自由餐）
            (COUNT(CASE WHEN RevenueClassificationMode = 2 THEN 1 ELSE NULL END) - COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 THEN 1 ELSE NULL END)) AS Night_Verify_BatchCount,
            (ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) - ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 THEN Revenue ELSE 0 END), 0)) AS Night_Verify_Revenue,

            -- 折扣免费
            COUNT(CASE WHEN AccOkZD > 0 THEN 1 ELSE NULL END) AS DiscountFree_BatchCount,
            ISNULL(SUM(CASE WHEN AccOkZD > 0 THEN AccOkZD ELSE 0 END), 0) AS DiscountFree_Revenue

        FROM RecordsWithTimeMode;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    SELECT * FROM #DailyReports ORDER BY ReportDate;

    DROP TABLE #DailyReports;

END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 修复版夜间详情存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建修复版存储过程失败: {str(e)}")
        return False

def test_fixed_procedure(connection):
    """测试修复版存储过程"""
    print("\n🧪 测试修复版存储过程...")
    
    try:
        cursor = connection.cursor()
        
        # 测试修复版存储过程
        test_query = """
        EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Fixed 
            @ShopId = 11,
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24'
        """
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        rows = cursor.fetchall()
        
        print(f"✅ 修复版存储过程执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        print(f"📊 返回行数: {len(rows)}")
        
        if rows:
            row = rows[0]
            print(f"\n📋 2025-07-24 店铺11 优化后的数据:")
            
            # 显示关键字段
            key_fields = [
                ('ReportDate', '日期'),
                ('TotalRevenue', '总收入'),
                ('NightTimeRevenue', '晚上档收入'),
                ('NightTimeBatchCount', '晚上档批次'),
                ('FreeMeal_BatchCount', '自由餐批次'),
                ('FreeMeal_Revenue', '自由餐收入'),
                ('NonPackage_RoomFee', '房费批次(新增)'),
                ('NonPackage_YearCard', '年卡批次(新增)'),
                ('Night_Verify_BatchCount', '净值批次(优化)'),
                ('Night_Verify_Revenue', '净值收入(优化)'),
                ('DiscountFree_BatchCount', '招待批次'),
                ('DiscountFree_Revenue', '招待金额')
            ]
            
            for field_name, display_name in key_fields:
                if field_name in columns:
                    col_index = columns.index(field_name)
                    value = row[col_index] if col_index < len(row) else "N/A"
                    print(f"   {display_name}: {value}")
            
            # 保存结果到文件
            print(f"\n💾 保存修复版结果...")
            import csv
            filename = f"修复版夜间详情_店铺11_20250724.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                # 写入列头
                writer.writerow(columns)
                # 写入数据
                for row in rows:
                    writer.writerow(row)
            
            print(f"✅ 修复版结果已保存到: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试修复版存储过程失败: {str(e)}")
        return False

def main():
    print("🚀 开始修复并测试优化版夜间详情存储过程...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 1. 创建修复版存储过程
        success1 = create_fixed_night_procedure(connection)
        
        if success1:
            # 2. 测试修复版存储过程
            success2 = test_fixed_procedure(connection)
            
            if success2:
                print(f"\n🎉 修复版夜间详情存储过程测试成功！")
                print(f"\n📋 优化内容:")
                print(f"   ✅ 新增：NonPackage_RoomFee - 统计晚间档房费批次")
                print(f"   ✅ 修改：NonPackage_YearCard - 模糊查询'年卡'关键词")
                print(f"   ✅ 优化：Night_Verify_BatchCount - 减去自由餐批次")
                print(f"   ✅ 优化：Night_Verify_Revenue - 减去自由餐收入")
                
                print(f"\n📋 使用方法:")
                print(f"   EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Fixed 11, '2025-07-24', '2025-07-24'")
            else:
                print("\n❌ 修复版存储过程测试失败")
        else:
            print("\n❌ 修复版存储过程创建失败")
    
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
