import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata' # 主数据库
FOOD_DB = 'Dbfood'       # food表所在的数据库
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 查询参数 ---
TARGET_SHOP_ID = 11
TARGET_DATES = ['20250718', '20250719', '20250720'] 

def generate_report():
    """使用 IsDirectFall 标签，按天、分时段统计直落账单详情。"""
    cnxn = None
    try:
        # 1. 构建SQL查询语句
        # 关联 operatedata.RmCloseInfo_Test, Dbfood.FdCashBak, 和 Dbfood.food
        placeholders = ', '.join('?' * len(TARGET_DATES))
        sql_query = f"""SELECT 
                         rc.WorkDate,
                         rc.InvNo,
                         rc.OpenDateTime,
                         fb.FdCName
                     FROM 
                         dbo.RmCloseInfo_Test rc
                     JOIN 
                         {FOOD_DB}.dbo.FdCashBak fb ON rc.InvNo = fb.InvNo
                     JOIN
                         {FOOD_DB}.dbo.food f ON fb.FdNo = f.FdNo
                     WHERE 
                         rc.ShopId = ? 
                         AND rc.WorkDate IN ({placeholders}) 
                         AND rc.OpenDateTime IS NOT NULL
                         AND f.IsDirectFall = 1;"""
        
        params = [TARGET_SHOP_ID] + TARGET_DATES

        # 2. 连接数据库并执行查询
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库并执行关联查询... ---")
        cnxn = pyodbc.connect(conn_str)
        df = pd.read_sql_query(sql_query, cnxn, params=params)
        print(f"查询完成，共找到 {len(df)} 条‘直落’下单记录。")

        if df.empty:
            print("在指定日期范围内，没有找到任何被打上‘直落’标签的下单记录。")
            return

        # 3. 按天、分时段生成报告
        print("\n--- 精确直落统计报告 (判断标准: food.IsDirectFall = 1) ---")
        for date in sorted(df['WorkDate'].unique()):
            print(f"\n==================================================")
            print(f"====== 日期: {date} ======")
            print(f"==================================================")
            
            day_df = df[df['WorkDate'] == date]

            # 白天档
            daytime_bills = day_df[pd.to_datetime(day_df['OpenDateTime']).dt.hour < 17]
            print(f"\n【白天档直落 (17:00前开台)】")
            if daytime_bills.empty:
                print("  -> 无白天档直落账单。")
            else:
                unique_daytime_count = daytime_bills['InvNo'].nunique()
                print(f"  -> 共 {unique_daytime_count} 张独立账单:")
                print(daytime_bills[['InvNo', 'FdCName']].to_string(index=False))

            # 晚间档
            nighttime_bills = day_df[pd.to_datetime(day_df['OpenDateTime']).dt.hour >= 17]
            print(f"\n【晚间档直落 (17:00后开台)】")
            if nighttime_bills.empty:
                print("  -> 无晚间档直落账单。")
            else:
                unique_nighttime_count = nighttime_bills['InvNo'].nunique()
                print(f"  -> 共 {unique_nighttime_count} 张独立账单:")
                print(nighttime_bills[['InvNo', 'FdCName']].to_string(index=False))

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    generate_report()
