import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 输出文件名 ---
OUTPUT_FILENAME = 'KTV白天时段综合报表_已过滤夜间.xlsx'

# --- 参照 2.txt 定义的列名和顺序 ---
FINAL_COLUMN_ORDER = [
    '日期', '门店', '星期', '营收_总收入', '营收_白天档', '营收_晚上档', 
    '带客_全天总批数', '带客_白天档_总批次', '带客_晚上档_总批次', '带客_白天档_直落', '带客_晚上档_直落', 
    '用餐_总人数', '用餐_自助餐人数', '用餐_直落人数'
    # 时段列将在此处动态插入
]

# --- 数据库列名 -> 最终中文列名 的映射 ---
COLUMN_MAPPING = {
    'ReportDate': '日期', 'ShopName': '门店', 'Weekday': '星期',
    'TotalRevenue': '营收_总收入', 'DayTimeRevenue': '营收_白天档', 'NightTimeRevenue': '营收_晚上档',
    'TotalBatchCount': '带客_全天总批数', 'DayTimeBatchCount': '带客_白天档_总批次', 'NightTimeBatchCount': '带客_晚上档_总批次',
    'DayTimeDirectFall': '带客_白天档_直落', 'NightTimeDropInBatch': '带客_晚上档_直落',
    'TotalGuestCount': '用餐_总人数', 'BuffetGuestCount': '用餐_自助餐人数', 'TotalDropInGuests': '用餐_直落人数',
}

# --- 时段详情指标 -> 中文短名 的映射 (用于透视)
PIVOT_METRIC_MAPPING = {
    'KPlus_Count': 'K+', 'Special_Count': '特权预约', 'Meituan_Count': '美团', 'Douyin_Count': '抖音',
    'RoomFee_Count': '房费', 'Subtotal_Count': '小计', 'PreviousSlot_DirectFall': '上档直落'
}

def create_daytime_report():
    cnxn = None
    try:
        # 1. 连接数据库并获取数据
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"正在连接到数据库: {DATABASE}...")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        print("正在查询报表主数据 (Header)...")
        df_header = pd.read_sql_query("SELECT * FROM dbo.FullDailyReport_Header", cnxn)
        
        print("正在查询时段详情数据 (TimeSlotDetails)...")
        df_details = pd.read_sql_query("SELECT * FROM dbo.FullDailyReport_TimeSlotDetails", cnxn)

        if df_header.empty:
            print("报表主表 (Header) 为空，无法生成报表。")
            return

        # 【新增】过滤掉开始时间为20:00或更晚的时段
        if not df_details.empty:
            original_rows = len(df_details)
            df_details = df_details[~df_details['TimeSlotName'].str.startswith(('20:', '21:', '22:', '23:'))]
            print(f"已过滤掉夜间时段数据，处理行数从 {original_rows} 减少到 {len(df_details)}。")

        # 2. 数据透视 (Pivot TimeSlotDetails)
        pivoted_df = pd.DataFrame() # 创建一个空的DataFrame以防详情表为空
        if not df_details.empty:
            print("正在对白天时段数据进行透视操作...")
            pivoted_df = df_details.pivot_table(
                index='ReportID', 
                columns='TimeSlotName', 
                values=list(PIVOT_METRIC_MAPPING.keys()),
                aggfunc='sum'
            )
            # 扁平化多级列名
            pivoted_df.columns = [f"{time_slot}_{PIVOT_METRIC_MAPPING.get(metric, metric)}" for metric, time_slot in pivoted_df.columns]
        
        # 3. 合并 Header 和透视后的 TimeSlot 数据
        print("正在合并主数据和白天时段数据...")
        final_df = pd.merge(df_header, pivoted_df, on='ReportID', how='left')

        # 4. 统一重命名所有列为最终的中文名
        print("正在重命名所有列为最终中文表头...")
        final_df.rename(columns=COLUMN_MAPPING, inplace=True)

        # 5. 确定最终的列顺序
        final_columns = FINAL_COLUMN_ORDER[:]
        if not pivoted_df.empty:
            timeslot_columns = sorted([col for col in final_df.columns if any(ts_name in col for ts_name in df_details['TimeSlotName'].unique())])
            insert_index = final_columns.index('用餐_直落人数') + 1
            final_columns[insert_index:insert_index] = timeslot_columns

        # 过滤掉不存在的列，并按最终顺序排列
        final_df = final_df[[col for col in final_columns if col in final_df.columns]]

        # 6. 清理数据并保存
        final_df.fillna(0, inplace=True);
        # 将所有数字列转换为整数，忽略错误
        for col in final_df.columns:
            if final_df[col].dtype in ['float64', 'float32']:
                final_df[col] = final_df[col].astype(int)

        print(f"正在生成最终Excel文件: {OUTPUT_FILENAME}...")
        final_df.to_excel(OUTPUT_FILENAME, sheet_name='白天时段详情报表', index=False, engine='openpyxl')
        
        print("\n--- 成功！ ---")
        print(f"已过滤夜间的白天时段报表已成功生成，请查看文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 发生未知错误！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    create_daytime_report()