
import pyodbc
import pandas as pd
from datetime import datetime
import warnings

# 忽略 pandas 中未来版本可能出现的警告
warnings.simplefilter(action='ignore', category=FutureWarning)

# --- 数据库连接定义 ---

def get_db_connection(server, database, user, password):
    """通用数据库连接函数"""
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={server};"
        f"DATABASE={database};"
        f"UID={user};"
        f"PWD={password};"
        f"TrustServerCertificate=yes;"
        f"LoginTimeout=10;"
    )
    try:
        return pyodbc.connect(conn_str)
    except pyodbc.Error as ex:
        print(f"连接到 {database}@{server} 失败: {ex}")
        return None

# --- 数据获取函数 ---

def get_total_revenue(conn, target_date, shop_id):
    """获取总营业额"""
    query = f"""
    SELECT TotalRevenue 
    FROM FullDailyReport_Header 
    WHERE ReportDate = ? AND ShopId = ?
    """
    try:
        df = pd.read_sql(query, conn, params=[target_date, shop_id])
        return df['TotalIncome'].iloc[0] if not df.empty else 0
    except Exception as e:
        print(f"获取总营业额失败: {e}")
        return 0

def get_time_slots(conn):
    """获取所有动态时间段"""
    query = "SELECT TimeName, BegTime, EndTime FROM timeinfo ORDER BY BegTime"
    return pd.read_sql(query, conn)

def get_bookings(conn, target_date, shop_id):
    """获取指定日期的预订数据"""
    # 假设 BookStatus = 1 和 CheckinStatus = 0 表示有效且未入住的预订
    query = f"""
    SELECT ComeTime, Numbers, RmNo 
    FROM bookcacheinfo 
    WHERE ComeDate = ? AND ShopId = ? AND BookStatus = 1 AND CheckinStatus = 0
    """
    return pd.read_sql(query, conn, params=[target_date, shop_id])

def get_occupied_rooms(conn, target_date, shop_id):
    """获取在店(待客)数据"""
    # OutTime 为空或空字符串表示尚未结账
    query = f"""
    SELECT InTime, OutTime, InNumbers, RmNo 
    FROM fdinv 
    WHERE WorkDate = ? AND ShopId = ? AND (OutTime IS NULL OR OutTime = '')
    """
    return pd.read_sql(query, conn, params=[target_date, shop_id])

# --- 主逻辑 ---

def generate_report(target_date_str, shop_id):
    """生成最终报表"""
    print("开始生成报表...")
    
    # --- 1. 建立所有数据库连接 ---
    print("正在连接数据库...")
    conn_op = get_db_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
    conn_rms = get_db_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
    conn_food = get_db_connection('193.112.2.229', 'Dbfood', 'sa', 'Musicbox@123')

    if not all([conn_op, conn_rms, conn_food]):
        print("数据库连接失败，无法生成报表。")
        return

    # --- 2. 获取数据 ---
    print("正在获取数据...")
    total_revenue = get_total_revenue(conn_op, target_date_str, shop_id)
    time_slots_df = get_time_slots(conn_rms)
    bookings_df = get_bookings(conn_rms, target_date_str, shop_id)
    occupied_df = get_occupied_rooms(conn_food, target_date_str, shop_id)
    
    # --- 3. 处理数据并计算指标 ---
    print("正在处理数据和计算指标...")
    report_data = []
    for index, slot in time_slots_df.iterrows():
        slot_name = slot['TimeName']
        slot_start = slot['BegTime']
        slot_end = slot['EndTime']

        # 筛选该时间段的预订
        slot_bookings = bookings_df[bookings_df['ComeTime'].between(slot_start, slot_end, inclusive='left')]
        booked_rooms = slot_bookings['RmNo'].nunique()
        booked_guests = slot_bookings['Numbers'].sum()

        # 筛选该时间段的待客信息 (逻辑简化：只要在店即可)
        # 注意：一个更精确的“时段内待客”逻辑会更复杂，这里我们统计当前所有未结账的房间
        occupied_rooms = occupied_df['RmNo'].nunique()
        occupied_guests = occupied_df.drop_duplicates(subset=['RmNo'])['InNumbers'].sum()

        # 计算开房率
        occupancy_rate = (occupied_rooms / booked_rooms) if booked_rooms > 0 else 0
        
        report_data.append({
            '时间段': slot_name,
            '预订房间数': booked_rooms,
            '预订人数': booked_guests,
            '待客房间数': occupied_rooms,
            '待客人数': occupied_guests,
            '开房率': f"{occupancy_rate:.2%}"
        })

    report_df = pd.DataFrame(report_data)

    # --- 4. 生成Excel文件 ---
    print("正在生成Excel文件...")
    target_date = datetime.strptime(target_date_str, '%Y%m%d')
    day_of_week = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'][target_date.weekday()]
    filename = f"KTV动态营业报表_{target_date_str}.xlsx"

    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 创建总览信息
        overview_data = {
            '报表日期': [target_date.strftime('%Y-%m-%d')],
            '星期': [day_of_week],
            '总营业额': [f"{total_revenue:,.2f}"]
        }
        overview_df = pd.DataFrame(overview_data)
        overview_df.to_excel(writer, sheet_name='动态营业报表', index=False, startrow=0)

        # 写入详细数据
        report_df.to_excel(writer, sheet_name='动态营业报表', index=False, startrow=4)

    print(f"报表生成成功！已保存为: {filename}")

    # --- 5. 关闭连接 ---
    conn_op.close()
    conn_rms.close()
    conn_food.close()
    print("数据库连接已关闭。")

if __name__ == '__main__':
    # 定义报表参数
    TARGET_DATE = '20250731' # 请根据需要修改日期
    SHOP_ID = 11             # 请根据需要修改店铺ID
    generate_report(TARGET_DATE, SHOP_ID)
