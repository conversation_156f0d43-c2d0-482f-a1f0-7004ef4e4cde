import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 输出文件名 ---
OUTPUT_FILENAME = 'KTV综合时段报表.xlsx'

# --- 中英文表头映射 ---
COLUMN_MAPPING = {
    # Header 表
    'ReportID': '报告ID',
    'ReportDate': '报告日期',
    'ShopID': '门店ID',
    'ShopName': '门店名称',
    'Weekday': '星期',
    'TotalRevenue': '总营收',
    'DayTimeRevenue': '白天档营收',
    'NightTimeRevenue': '夜间档营收',
    'TotalBatchCount': '总批次数',
    'DayTimeBatchCount': '白天档批次数',
    'NightTimeBatchCount': '夜间档批次数',
    'DayTimeDirectFall': '白天直落批数',
    'NightTimeDropInBatch': '夜间进场批数',
    'TotalGuestCount': '总客人数',
    'BuffetGuestCount': '自助餐客人数',
    'TotalDropInGuests': '总进场客人数',
    'MealBatchCount': '套餐批数',
    'MealDirectFallBatchCount': '套餐直落批数',
    'MealDirectFallRevenue': '套餐直落营收',
    'Night_FreeMeal_Subtotal': '夜间自由餐小计',
    'Night_FreeMeal_Amount': '夜间自由餐金额',
    'Night_After20_Revenue': '20点后营收',
    
    # TimeSlotDetails 表
    'SlotDetailID': '时段详情ID',
    'TimeSlotName': '时段名称',
    'TimeSlotOrder': '时段顺序',
    'KPlus_Count': 'K+批数',
    'Special_Count': '特权批数',
    'Meituan_Count': '美团批数',
    'Douyin_Count': '抖音批数',
    'RoomFee_Count': '房费批数',
    'Subtotal_Count': '时段批数小计',
    'PreviousSlot_DirectFall': '上时段直落批数'
}

def create_excel_report():
    """
    连接数据库，关联查询两个表，并生成中文表头的Excel报表。
    """
    cnxn = None
    try:
        # 1. 构建SQL查询语句
        sql_query = """
        SELECT 
            h.*,
            d.SlotDetailID, d.TimeSlotName, d.TimeSlotOrder, d.KPlus_Count, d.Special_Count,
            d.Meituan_Count, d.Douyin_Count, d.RoomFee_Count, d.Subtotal_Count, d.PreviousSlot_DirectFall
        FROM 
            dbo.FullDailyReport_Header AS h
        INNER JOIN 
            dbo.FullDailyReport_TimeSlotDetails AS d ON h.ReportID = d.ReportID
        ORDER BY
            h.ReportDate DESC, h.ShopID, d.TimeSlotOrder ASC;
        """

        # 2. 连接数据库并执行查询
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"正在连接到数据库: {DATABASE}...")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！正在执行查询...")
        
        df = pd.read_sql_query(sql_query, cnxn)
        print(f"查询完成，获取了 {len(df)} 条数据。")

        if df.empty:
            print("数据库中没有查询到匹配的数据，已退出。")
            return

        # 3. 重命名列为中文
        print("正在将表头重命名为中文...")
        df.rename(columns=COLUMN_MAPPING, inplace=True)

        # 4. 保存为Excel文件
        print(f"正在生成Excel文件: {OUTPUT_FILENAME}...")
        df.to_excel(OUTPUT_FILENAME, sheet_name='每日时段详情综合报表', index=False, engine='openpyxl')
        
        print("\n--- 成功！ ---")
        print(f"报表已成功生成，请查看文件: {OUTPUT_FILENAME}")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"\n--- 数据库执行出错！ ---")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
        sys.exit(1)

    except Exception as e:
        print(f"\n--- 发生未知错误！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    create_excel_report()