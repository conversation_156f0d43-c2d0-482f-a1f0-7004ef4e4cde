

import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 输出文件名 ---
OUTPUT_FILENAME = 'KTV最终版综合报表.xlsx'

# --- 列名定义：完全参照 2.txt 的格式 ---
# 这是一个列表，用于保证最终Excel的列顺序
FINAL_COLUMN_ORDER = [
    '日期', '门店', '星期', '营收_总收入', '营收_白天档', '营收_晚上档', 
    '带客_全天总批数', '带客_白天档_总批次', '带客_晚上档_总批次', '带客_白天档_直落', '带客_晚上档_直落', 
    '用餐_总人数', '用餐_自助餐人数', '用餐_直落人数',
    # 白天时段动态生成
    # 夜间时段
    '晚间_自由餐_K+', '晚间_自由餐_特权', '晚间_自由餐_美团', '晚间_自由餐_抖音', '晚间_自由餐_批数小计', '晚间_自由餐_消费金额',
    '晚间_买断套餐_批次', '晚间_买断套餐_营收', '晚间_畅饮套餐_批次', '晚间_畅饮套餐_营收', '晚间_自由消费_批次',
    '晚间_非套餐_特权', '晚间_非套餐_美团', '晚间_非套餐_抖音', '晚间_非套餐_其他'
]

# --- 数据库列名 -> 最终中文列名 的映射 ---
COLUMN_MAPPING = {
    # Header 表
    'ReportDate': '日期', 'ShopName': '门店', 'Weekday': '星期',
    'TotalRevenue': '营收_总收入', 'DayTimeRevenue': '营收_白天档', 'NightTimeRevenue': '营收_晚上档',
    'TotalBatchCount': '带客_全天总批数', 'DayTimeBatchCount': '带客_白天档_总批次', 'NightTimeBatchCount': '带客_晚上档_总批次',
    'DayTimeDirectFall': '带客_白天档_直落', 'NightTimeDropInBatch': '带客_晚上档_直落',
    'TotalGuestCount': '用餐_总人数', 'BuffetGuestCount': '用餐_自助餐人数', 'TotalDropInGuests': '用餐_直落人数',

    # NightDetails 表
    'FreeMeal_KPlus': '晚间_自由餐_K+', 'FreeMeal_Special': '晚间_自由餐_特权', 'FreeMeal_Meituan': '晚间_自由餐_美团', 'FreeMeal_Douyin': '晚间_自由餐_抖音',
    'FreeMeal_BatchCount': '晚间_自由餐_批数小计', 'FreeMeal_Revenue': '晚间_自由餐_消费金额',
    'Buyout_BatchCount': '晚间_买断套餐_批次', 'Buyout_Revenue': '晚间_买断套餐_营收',
    'Changyin_BatchCount': '晚间_畅饮套餐_批次', 'Changyin_Revenue': '晚间_畅饮套餐_营收',
    'FreeConsumption_BatchCount': '晚间_自由消费_批次',
    'NonPackage_Special': '晚间_非套餐_特权', 'NonPackage_Meituan': '晚间_非套餐_美团', 'NonPackage_Douyin': '晚间_非套餐_抖音', 'NonPackage_Others': '晚间_非套餐_其他'
}

# --- 时段详情指标 -> 中文短名 的映射 (用于透视)
PIVOT_METRIC_MAPPING = {
    'KPlus_Count': 'K+', 'Special_Count': '特权预约', 'Meituan_Count': '美团', 'Douyin_Count': '抖音',
    'RoomFee_Count': '房费', 'Subtotal_Count': '小计', 'PreviousSlot_DirectFall': '上档直落'
}

def create_final_report():
    cnxn = None
    try:
        # 1. 连接数据库并获取所有数据
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"正在连接到数据库: {DATABASE}...")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        print("正在查询报表主数据 (Header)...")
        df_header = pd.read_sql_query("SELECT * FROM dbo.FullDailyReport_Header", cnxn)
        
        print("正在查询时段详情数据 (TimeSlotDetails)...")
        df_details = pd.read_sql_query("SELECT * FROM dbo.FullDailyReport_TimeSlotDetails", cnxn)

        print("正在查询夜间详情数据 (NightDetails)...")
        df_night = pd.read_sql_query("SELECT * FROM dbo.FullDailyReport_NightDetails", cnxn)

        if df_header.empty:
            print("报表主表 (Header) 为空，无法生成报表。")
            return

        # 2. 数据透视 (Pivot TimeSlotDetails)
        pivoted_df = pd.DataFrame() # 创建一个空的DataFrame以防详情表为空
        if not df_details.empty:
            print("正在对白天时段数据进行透视操作...")
            pivoted_df = df_details.pivot_table(
                index='ReportID', 
                columns='TimeSlotName', 
                values=list(PIVOT_METRIC_MAPPING.keys()),
                aggfunc='sum' # 使用sum以防万一有重复
            )
            # 扁平化多级列名
            pivoted_df.columns = [f"{time_slot}_{PIVOT_METRIC_MAPPING.get(metric, metric)}" for metric, time_slot in pivoted_df.columns]
        
        # 3. 合并 Header 和透视后的 TimeSlot 数据
        print("正在合并主数据和白天时段数据...")
        final_df = pd.merge(df_header, pivoted_df, on='ReportID', how='left')

        # 4. 合并夜间数据
        if not df_night.empty:
            print("正在合并夜间详情数据...")
            final_df = pd.merge(final_df, df_night, on='ReportID', how='left')

        # 5. 统一重命名所有列为最终的中文名
        print("正在重命名所有列为最终中文表头...")
        final_df.rename(columns=COLUMN_MAPPING, inplace=True)

        # 6. 确定最终的列顺序
        # 从模板中获取基础列
        final_columns = [col for col in FINAL_COLUMN_ORDER if col in final_df.columns]
        # 动态获取所有时段列并排序
        timeslot_columns = sorted([col for col in final_df.columns if any(ts_name in col for ts_name in df_details['TimeSlotName'].unique())])
        # 找到插入点（在'用餐_直落人数'之后）
        try:
            insert_index = final_columns.index('用餐_直落人数') + 1
            # 将时段列插入
            final_columns[insert_index:insert_index] = timeslot_columns
        except ValueError:
            # 如果找不到插入点，就加在后面
            final_columns.extend(timeslot_columns)

        # 过滤掉不存在的列，并按最终顺序排列
        final_df = final_df[[col for col in final_columns if col in final_df.columns]]

        # 7. 清理数据并保存
        final_df.fillna(0, inplace=True)
        print(f"正在生成最终Excel文件: {OUTPUT_FILENAME}...")
        final_df.to_excel(OUTPUT_FILENAME, sheet_name='每日详情最终版', index=False, engine='openpyxl')
        
        print("\n--- 成功！ ---")
        print(f"最终版报表已成功生成，请查看文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 发生未知错误！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    create_final_report()
