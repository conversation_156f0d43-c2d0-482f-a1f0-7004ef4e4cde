import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 输出文件名 ---
OUTPUT_FILENAME = 'KTV综合时段报表_宽表.xlsx'

# --- 中英文表头映射 ---
# 基础表头
HEADER_MAPPING = {
    'ReportID': '报告ID',
    'ReportDate': '报告日期',
    'ShopID': '门店ID',
    'ShopName': '门店名称',
    'Weekday': '星期',
    'TotalRevenue': '总营收',
    'DayTimeRevenue': '白天档营收',
    'NightTimeRevenue': '夜间档营收',
    'TotalBatchCount': '总批次数',
    'DayTimeBatchCount': '白天档批次数',
    'NightTimeBatchCount': '夜间档批次数',
    'DayTimeDirectFall': '白天直落批数',
    'NightTimeDropInBatch': '夜间进场批数',
    'TotalGuestCount': '总客人数',
    'BuffetGuestCount': '自助餐客人数',
    'TotalDropInGuests': '总进场客人数',
    'MealBatchCount': '套餐批数',
    'MealDirectFallBatchCount': '套餐直落批数',
    'MealDirectFallRevenue': '套餐直落营收',
    'Night_FreeMeal_Subtotal': '夜间自由餐小计',
    'Night_FreeMeal_Amount': '夜间自由餐金额',
    'Night_After20_Revenue': '20点后营收',
}

# 时段详情的指标映射 (用于拼接新的列名)
TIMESLOT_METRIC_MAPPING = {
    'KPlus_Count': 'K+批数',
    'Special_Count': '特权批数',
    'Meituan_Count': '美团批数',
    'Douyin_Count': '抖音批数',
    'RoomFee_Count': '房费批数',
    'Subtotal_Count': '时段小计',
    'PreviousSlot_DirectFall': '上时段直落'
}

def create_pivoted_report():
    """
    连接数据库，将时段详情进行透视操作，生成“宽”格式的Excel报表。
    """
    cnxn = None
    try:
        # 1. 连接数据库并获取基础数据
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"正在连接到数据库: {DATABASE}...")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        # 查询主表和详情表
        print("正在查询报表主数据...")
        df_header = pd.read_sql_query("SELECT * FROM dbo.FullDailyReport_Header", cnxn)
        
        print("正在查询时段详情数据...")
        df_details = pd.read_sql_query("SELECT * FROM dbo.FullDailyReport_TimeSlotDetails", cnxn)

        if df_header.empty or df_details.empty:
            print("主表或详情表为空，无法生成报表。")
            return

        # 2. 数据透视 (Pivot)
        print("正在对时段数据进行透视操作...")
        pivot_df = df_details.pivot_table(
            index='ReportID', 
            columns='TimeSlotName', 
            values=list(TIMESLOT_METRIC_MAPPING.keys())
        )

        # 3. 扁平化多级列名 (Flatten MultiIndex Columns)
        # 将 (指标, 时段) 的二级列名，合并为 '时段_指标' 的一级列名
        new_columns = []
        for metric, time_slot in pivot_df.columns:
            # 获取指标的中文名
            metric_chinese = TIMESLOT_METRIC_MAPPING.get(metric, metric)
            # 拼接成新的列名，例如: '10:50-13:50_K+批数'
            new_columns.append(f"{time_slot}_{metric_chinese}")
        pivot_df.columns = new_columns

        # 4. 合并主表和透视后的详情表
        print("正在合并主数据和时段数据...")
        # 使用 left join 以主表为准
        final_df = pd.merge(df_header, pivot_df, on='ReportID', how='left')

        # 5. 重命名主表部分的列为中文
        final_df.rename(columns=HEADER_MAPPING, inplace=True)
        
        # 6. 对列进行排序，确保时段列有序
        # (此部分可以根据实际需要细化，目前按默认顺序)

        # 7. 保存为Excel文件
        print(f"正在生成Excel文件: {OUTPUT_FILENAME}...")
        final_df.to_excel(OUTPUT_FILENAME, sheet_name='每日时段详情宽表', index=False, engine='openpyxl')
        
        print("\n--- 成功！ ---")
        print(f"宽格式报表已成功生成，请查看文件: {OUTPUT_FILENAME}")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"\n--- 数据库执行出错！ ---")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
        sys.exit(1)

    except Exception as e:
        print(f"\n--- 发生未知错误！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    create_pivoted_report()
