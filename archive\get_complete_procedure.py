#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取完整存储过程定义的增强脚本
"""

import pyodbc
import re
from datetime import datetime

def connect_database():
    """连接到数据库"""
    try:
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER=192.168.2.5;
        DATABASE=operatedata;
        UID=sa;
        PWD=Musicbox123;
        TrustServerCertificate=yes;
        """
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def get_complete_procedure_definition(connection, proc_name):
    """获取完整的存储过程定义"""
    try:
        # 方法1：从sys.sql_modules获取
        query = """
        SELECT 
            p.name as procedure_name,
            p.create_date,
            p.modify_date,
            m.definition
        FROM sys.procedures p
        INNER JOIN sys.sql_modules m ON p.object_id = m.object_id
        WHERE p.name = ?
        """
        
        cursor = connection.cursor()
        cursor.execute(query, proc_name)
        result = cursor.fetchone()
        
        if result:
            return {
                'name': result[0],
                'create_date': result[1],
                'modify_date': result[2],
                'definition': result[3]
            }
        else:
            print(f"❌ 未找到存储过程: {proc_name}")
            return None
            
    except Exception as e:
        print(f"❌ 获取存储过程定义失败: {str(e)}")
        return None

def analyze_procedure_logic(definition):
    """深度分析存储过程逻辑"""
    if not definition:
        return {}
    
    lines = definition.split('\n')
    analysis = {
        'total_lines': len(lines),
        'parameters': [],
        'variables': [],
        'temp_tables': [],
        'main_tables': [],
        'called_procedures': [],
        'transaction_blocks': [],
        'error_handling': [],
        'business_logic_steps': [],
        'data_flow': []
    }
    
    current_step = None
    in_transaction = False
    in_try_block = False
    
    for i, line in enumerate(lines, 1):
        line_clean = line.strip()
        line_upper = line_clean.upper()
        
        # 参数识别
        if line_clean.startswith('@') and ('=' in line_clean or 'AS' in line_upper):
            analysis['parameters'].append(f"Line {i}: {line_clean}")
        
        # 变量声明
        if line_upper.startswith('DECLARE @'):
            analysis['variables'].append(f"Line {i}: {line_clean}")
        
        # 临时表
        if '#' in line_clean and 'CREATE TABLE' in line_upper:
            table_match = re.search(r'#(\w+)', line_clean)
            if table_match:
                analysis['temp_tables'].append(f"Line {i}: {table_match.group(1)} - {line_clean}")
        
        # 主表操作
        table_patterns = [
            (r'FROM\s+dbo\.(\w+)', 'SELECT FROM'),
            (r'JOIN\s+dbo\.(\w+)', 'JOIN'),
            (r'UPDATE\s+dbo\.(\w+)', 'UPDATE'),
            (r'INSERT\s+INTO\s+dbo\.(\w+)', 'INSERT INTO'),
            (r'DELETE\s+FROM\s+dbo\.(\w+)', 'DELETE FROM')
        ]
        
        for pattern, operation in table_patterns:
            matches = re.findall(pattern, line_upper)
            for match in matches:
                analysis['main_tables'].append(f"Line {i}: {operation} {match}")
        
        # 存储过程调用
        if 'EXEC' in line_upper:
            proc_match = re.search(r'EXEC\s+dbo\.(\w+)', line_upper)
            if proc_match:
                analysis['called_procedures'].append(f"Line {i}: {proc_match.group(1)} - {line_clean}")
        
        # 事务处理
        if 'BEGIN TRANSACTION' in line_upper:
            in_transaction = True
            analysis['transaction_blocks'].append(f"Line {i}: Transaction started")
        elif 'COMMIT' in line_upper and in_transaction:
            analysis['transaction_blocks'].append(f"Line {i}: Transaction committed")
        elif 'ROLLBACK' in line_upper and in_transaction:
            analysis['transaction_blocks'].append(f"Line {i}: Transaction rolled back")
        
        # 错误处理
        if 'BEGIN TRY' in line_upper:
            in_try_block = True
            analysis['error_handling'].append(f"Line {i}: TRY block started")
        elif 'BEGIN CATCH' in line_upper:
            analysis['error_handling'].append(f"Line {i}: CATCH block started")
        elif 'END TRY' in line_upper or 'END CATCH' in line_upper:
            analysis['error_handling'].append(f"Line {i}: Error handling block ended")
        
        # 业务逻辑步骤识别
        if '-- ===' in line_clean or 'Step' in line_clean:
            current_step = line_clean
            analysis['business_logic_steps'].append(f"Line {i}: {current_step}")
        
        # 数据流分析
        if 'INSERT INTO' in line_upper and 'EXEC' in line_upper:
            analysis['data_flow'].append(f"Line {i}: Data pipeline - {line_clean}")
    
    return analysis

def generate_comprehensive_report(proc_info, analysis):
    """生成综合分析报告"""
    report = []
    
    report.append("=" * 80)
    report.append(f"存储过程深度分析报告: {proc_info['name']}")
    report.append("=" * 80)
    
    report.append(f"\n📋 基本信息:")
    report.append(f"   名称: {proc_info['name']}")
    report.append(f"   创建时间: {proc_info['create_date']}")
    report.append(f"   最后修改: {proc_info['modify_date']}")
    report.append(f"   代码行数: {analysis['total_lines']}")
    
    report.append(f"\n📝 参数分析 ({len(analysis['parameters'])} 个):")
    for param in analysis['parameters']:
        report.append(f"   {param}")
    
    report.append(f"\n🔧 变量声明 ({len(analysis['variables'])} 个):")
    for var in analysis['variables']:
        report.append(f"   {var}")
    
    report.append(f"\n🗂️ 临时表 ({len(analysis['temp_tables'])} 个):")
    for table in analysis['temp_tables']:
        report.append(f"   {table}")
    
    report.append(f"\n🗃️ 主表操作 ({len(analysis['main_tables'])} 个):")
    for table in analysis['main_tables']:
        report.append(f"   {table}")
    
    report.append(f"\n🔗 调用的存储过程 ({len(analysis['called_procedures'])} 个):")
    for proc in analysis['called_procedures']:
        report.append(f"   {proc}")
    
    report.append(f"\n💾 事务处理 ({len(analysis['transaction_blocks'])} 个):")
    for trans in analysis['transaction_blocks']:
        report.append(f"   {trans}")
    
    report.append(f"\n⚠️ 错误处理 ({len(analysis['error_handling'])} 个):")
    for error in analysis['error_handling']:
        report.append(f"   {error}")
    
    report.append(f"\n🎯 业务逻辑步骤 ({len(analysis['business_logic_steps'])} 个):")
    for step in analysis['business_logic_steps']:
        report.append(f"   {step}")
    
    report.append(f"\n🔄 数据流管道 ({len(analysis['data_flow'])} 个):")
    for flow in analysis['data_flow']:
        report.append(f"   {flow}")
    
    return "\n".join(report)

def main():
    proc_name = "usp_RunUnifiedDailyReportJob"
    
    # 连接数据库
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 获取存储过程定义
        print(f"\n🔍 正在分析存储过程: {proc_name}")
        proc_info = get_complete_procedure_definition(connection, proc_name)
        
        if not proc_info:
            return
        
        # 深度分析
        analysis = analyze_procedure_logic(proc_info['definition'])
        
        # 生成报告
        report = generate_comprehensive_report(proc_info, analysis)
        
        # 输出到控制台
        print(report)
        
        # 保存完整代码
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        code_file = f"usp_RunUnifiedDailyReportJob_complete_{timestamp}.sql"
        
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(proc_info['definition'])
        
        print(f"\n✅ 完整存储过程代码已保存到: {code_file}")
        
        # 保存分析报告
        report_file = f"usp_RunUnifiedDailyReportJob_analysis_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 分析报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {str(e)}")
    
    finally:
        connection.close()
        print("✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
