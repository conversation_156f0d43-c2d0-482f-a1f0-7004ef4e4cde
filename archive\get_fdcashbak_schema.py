import pyodbc

# --- Connection Details ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- Main Execution Logic ---
def get_table_schema():
    """
    Connects to the database and prints the columns of the FdCashBak table.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            cursor = conn.cursor()
            print("--- Columns in FdCashBak table ---")
            for row in cursor.columns(table='FdCashBak'):
                print(row.column_name)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database Error Occurred: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    get_table_schema()