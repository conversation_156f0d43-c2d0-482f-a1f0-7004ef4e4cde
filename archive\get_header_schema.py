

import pyodbc
import sys

OPERATEDATA_SERVER = "192.168.2.5"
OPERATEDATA_DATABASE = "operatedata"
OPERATEDATA_USER = "sa"
OPERATEDATA_PASSWORD = "Musicbox123"

def get_connection(server, database, user, password):
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={server};"
        f"DATABASE={database};"
        f"UID={user};"
        f"PWD={password};"
        f"TrustServerCertificate=yes;"
        f"LoginTimeout=10;"
    )
    try:
        return pyodbc.connect(conn_str)
    except pyodbc.Error as ex:
        print(f"Connection to {database}@{server} failed: {ex}", file=sys.stderr)
        return None

def get_table_schema(conn, table_name):
    try:
        cursor = conn.cursor()
        cursor.execute(f"EXEC sp_columns @table_name = ?", (table_name,))
        columns = cursor.fetchall()
        if not columns:
            return f"Table '{table_name}' not found or has no columns."
        
        schema_info = f"Schema for '{table_name}':\n"
        schema_info += "{:<30} {:<20}\n".format('Column Name', 'Data Type')
        schema_info += "-" * 50 + "\n"
        for col in columns:
            schema_info += "{:<30} {:<20}\n".format(col[3], col[5])
        return schema_info
    except pyodbc.Error as e:
        return f"Failed to get schema for '{table_name}': {e}"

def main():
    conn = get_connection(OPERATEDATA_SERVER, OPERATEDATA_DATABASE, OPERATEDATA_USER, OPERATEDATA_PASSWORD)
    if conn:
        print(get_table_schema(conn, 'FullDailyReport_Header'))
        conn.close()
    else:
        sys.exit(1)

if __name__ == '__main__':
    main()

