import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
JOB_NAME = 'RMS_Daily_Data_Sync_to_HQ'

def get_failed_job_step_info():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;DATABASE=msdb;"
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- 成功连接到 {SERVER}/msdb ---')

            # 查询最新失败的步骤信息
            query = """
            SELECT TOP 1
                h.step_id,
                h.step_name,
                h.run_date,
                h.run_time,
                h.message
            FROM msdb.dbo.sysjobs j
            JOIN msdb.dbo.sysjobhistory h ON j.job_id = h.job_id
            WHERE j.name = ? AND h.run_status = 0 -- 0 = Failed
            ORDER BY h.run_date DESC, h.run_time DESC;
            """
            
            cursor.execute(query, JOB_NAME)
            result = cursor.fetchone()
            
            if result:
                print("\n--- 作业失败详情 ---")
                print(f"步骤 ID:   {result.step_id}")
                print(f"步骤名称: {result.step_name}")
                print(f"执行日期: {result.run_date}")
                print(f"执行时间: {result.run_time}")
                print(f"错误消息: {result.message}")
            else:
                print("\n未找到该作业的失败记录。")

    except Exception as e:
        print(f"执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    get_failed_job_step_info()
