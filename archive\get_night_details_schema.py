
import pyodbc

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- Main Execution Logic ---
def get_table_schema():
    """
    Connects to the database and prints the columns of the FullDailyReport_NightDetails table.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            cursor = conn.cursor()
            print("--- Columns in FullDailyReport_NightDetails table ---")
            # Use try-except to handle case where table might not exist
            try:
                for row in cursor.columns(table='FullDailyReport_NightDetails'):
                    print(f"  - {row.column_name}")
            except pyodbc.ProgrammingError:
                print("Table 'FullDailyReport_NightDetails' does not exist.")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"DATABASE ERROR: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    get_table_schema()
