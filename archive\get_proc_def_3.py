
import pyodbc

try:
    conn = pyodbc.connect(
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=192.168.2.5;"
        "DATABASE=operatedata;"
        "UID=sa;"
        "PWD=Musicbox123;"
    )
    cursor = conn.cursor()
    cursor.execute("EXEC sp_helptext 'StaffReport'")
    rows = cursor.fetchall()
    if rows:
        for row in rows:
            print(row[0])
    else:
        print("Could not retrieve stored procedure definition.")
except Exception as e:
    print(f"An error occurred: {e}")
finally:
    if 'conn' in locals() and conn:
        conn.close()
