
import pyodbc

SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'
PROC_NAME = 'usp_LogHourlyRoomStatistics'

SQL_QUERY = f"SELECT OBJECT_DEFINITION(OBJECT_ID('{PROC_NAME}'));"

connection_string = (
    f'DRIVER={{ODBC Driver 17 for SQL Server}};'
    f'SERVER={SERVER};'
    f'DATABASE={DATABASE};'
    f'UID={USERNAME};'
    f'PWD={PASSWORD};'
    f'TrustServerCertificate=yes;'
)

try:
    with pyodbc.connect(connection_string, timeout=15) as conn:
        cursor = conn.cursor()
        cursor.execute(SQL_QUERY)
        proc_definition = cursor.fetchone()[0]
        print(f"--- Definition of {PROC_NAME} ---")
        print(proc_definition)
except Exception as e:
    print(f"An error occurred: {e}")
