

import pyodbc
import sys

# --- 数据库连接配置 ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
# --- 配置结束 ---

# 从命令行参数获取存储过程名称
if len(sys.argv) < 2:
    print("错误：请提供一个存储过程名称作为参数。")
    print("用法: python get_procedure_details.py <存储过程名称>")
    sys.exit(1)

procedure_name = sys.argv[1]

# 构建连接字符串
connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

try:
    # 连接数据库
    connection = pyodbc.connect(connection_string, timeout=5)
    cursor = connection.cursor()

    print(f"--- 开始获取存储过程 [ {procedure_name} ] 的定义 ---\n")

    # 使用 sp_helptext 获取存储过程的定义
    # 使用参数化查询防止SQL注入
    sql_query = "EXEC sp_helptext ?" 
    cursor.execute(sql_query, procedure_name)
    
    # 获取所有行
    rows = cursor.fetchall()

    # 打印结果
    if not rows:
        print(f"未找到名为 [ {procedure_name} ] 的存储过程或没有权限访问。")
    else:
        for row in rows:
            print(row[0], end='')

    print(f"\n--- 结束获取存储过程 [ {procedure_name} ] 的定义 ---")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"数据库操作失败: {ex}")


finally:
    if 'connection' in locals() and connection:
        connection.close()

