
import pyodbc

# --- Configuration ---
CONN_STR_REMOTE = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=193.112.2.229;"
    "DATABASE=rms2019;"
    "UID=sa;"
    "PWD=Musicbox@123;"
)

# --- Main Execution Logic ---
def get_remote_schema():
    """
    Connects to the remote server and gets the schema of the openhistory table.
    """
    try:
        with pyodbc.connect(CONN_STR_REMOTE) as conn_remote:
            cursor = conn_remote.cursor()
            print("--- Columns in rms2019.dbo.openhistory table ---")
            for row in cursor.columns(table='openhistory'):
                print(f"  - {row.column_name}")

    except pyodbc.Error as ex:
        print(f"DATABASE ERROR on REMOTE server: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR on REMOTE server: {e}")

if __name__ == "__main__":
    get_remote_schema()
