

import pyodbc
import sys

# --- 连接信息 ---
server = "192.168.2.5"
user = "sa"
password = "Musicbox123"
database = "operatedata"

# --- 需要重构的存储过程列表 (按依赖关系从上到下) ---
PROCS_TO_REFACTOR = [
    "usp_RunNightlyKTVReportJob_Final",
    "usp_RunUnifiedDailyReport_V3_Final",
    "usp_UpdateDirectFallFlag_ByName",
    "usp_GenerateDayTimeReport_Simple_V5_Final",
    "usp_GenerateSimplifiedDailyReport_V7_Final_Corrected",
    "usp_GetTimeSlotDetails_WithDirectFall"
]

def get_source_code_for_refactor():
    """连接数据库，获取所有待重构存储过程的最新源代码。"""
    conn = None
    definitions = {}
    try:
        conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password};TrustServerCertificate=yes;LoginTimeout=10;"
        
        print(f"--- 正在连接到 {server}/{database} 获取源代码...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("--- 连接成功！---")

        for proc_name in PROCS_TO_REFACTOR:
            print(f"--- 正在获取 {proc_name} 的定义...")
            sql_query = f"EXEC sp_helptext '{proc_name}'"
            cursor.execute(sql_query)
            rows = cursor.fetchall()
            if not rows:
                print(f"!!! 警告: 未能找到存储过程 {proc_name} 的定义，将跳过。", file=sys.stderr)
                continue
            
            # 将返回的多行代码拼接成一个完整的字符串
            full_definition = "".join([row.Text for row in rows])
            definitions[proc_name] = full_definition
            print(f"--- 成功获取 {proc_name}。 ---")

        # 将所有定义打印出来，用于下一步构建最终脚本
        print("\n" + "="*80)
        print("--- 所有存储过程的源代码已获取完毕 --- ")
        print("="*80 + "\n")
        for name, code in definitions.items():
            print(f"-- DEFINITION FOR: {name}")
            print(code)
            print(f"-- END DEFINITION FOR: {name}\n" + "-"*80 + "\n")

    except pyodbc.Error as ex:
        print(f"数据库操作失败: {ex}", file=sys.stderr)
    except Exception as e:
        print(f"发生未知错误: {e}", file=sys.stderr)
    finally:
        if conn:
            conn.close()
            print("--- 数据库连接已关闭。---")

# --- 执行 ---
if __name__ == "__main__":
    get_source_code_for_refactor()

