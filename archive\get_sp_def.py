
import pyodbc

SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
PROC_NAME = 'usp_GenerateSimplifiedDailyReport_V5_Final'
FILE_NAME = f"{PROC_NAME}_user_modified.sql"

def get_sp_def():
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
    query = "SELECT m.definition FROM sys.sql_modules m INNER JOIN sys.objects o ON m.object_id = o.object_id WHERE o.name = ?"
    try:
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()
            result = cursor.execute(query, PROC_NAME).fetchone()
            if result:
                with open(FILE_NAME, 'w', encoding='utf-8') as f:
                    f.write(result[0])
                print(f"Successfully saved user-modified SP definition to {FILE_NAME}")
            else:
                print(f"Could not find SP: {PROC_NAME}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    get_sp_def()
