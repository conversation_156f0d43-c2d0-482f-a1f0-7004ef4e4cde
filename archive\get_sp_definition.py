
import pyodbc

def get_procedure_definition(server, database, username, password, procedure_name, output_filename):
    """Retrieves the source code for a specific stored procedure and saves it to a file."""
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
    try:
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()
            get_def_query = "SELECT m.definition FROM sys.sql_modules m INNER JOIN sys.objects o ON m.object_id = o.object_id WHERE o.name = ? AND o.type = 'P';"
            cursor.execute(get_def_query, procedure_name)
            definition = cursor.fetchone()

            if definition:
                with open(output_filename, 'w', encoding='utf-8') as f:
                    f.write(definition[0])
                print(f"Successfully saved definition for '{procedure_name}' to '{output_filename}'")
            else:
                print(f"Could not retrieve definition for '{procedure_name}'.")

    except pyodbc.Error as ex:
        print(f"Database connection error: {ex.args[0]}")
        print(ex)

if __name__ == '__main__':
    get_procedure_definition(
        server='192.168.2.5',
        database='operatedata',
        username='sa',
        password='Musicbox123',
        procedure_name='usp_GenerateDayTimeReport_Simple_V3',
        output_filename='usp_GenerateDayTimeReport_Simple_V3_original.sql'
    )
