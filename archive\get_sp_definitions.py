
import pyodbc

# Database connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# Stored procedures to analyze
SP_NAMES = ['usp_Job_GenerateDailyPerformanceReport', 'usp_GenerateDynamicDailyReport']

def get_sp_definitions():
    """Connects to the database and retrieves the definitions of specified stored procedures."""
    conn = None
    try:
        print(f"正在连接到数据库 'operatedata'...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        for sp_name in SP_NAMES:
            print(f"\n--- 开始获取存储过程: {sp_name} ---")
            try:
                # Using sp_helptext to get the definition
                cursor.execute(f"EXEC sp_helptext '{sp_name}'")
                rows = cursor.fetchall()
                
                if not rows:
                    print(f"错误：无法找到存储过程 '{sp_name}' 或没有权限访问。")
                    continue

                print(f"-- {sp_name} 的定义 --")
                sp_definition = "".join([row.Text for row in rows])
                print(sp_definition)
                print(f"--- 结束获取存储过程: {sp_name} ---")

            except pyodbc.Error as ex_inner:
                print(f"执行 sp_helptext for {sp_name} 时出错: {ex_inner}")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

if __name__ == "__main__":
    get_sp_definitions()
