
import pyodbc

# Database connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# Stored procedures to analyze
SP_NAMES = [
    'usp_Util_UpdateDirectFallFlags',
    'usp_Util_CalculateDayTimeMetrics',
    'usp_Util_CalculateSimplifiedMetrics',
    'usp_Util_GetTimeSlotDetailsWithDirectFall'
]

def get_sp_definitions():
    """Connects to the database and retrieves the definitions of specified stored procedures for analysis."""
    conn = None
    try:
        print(f"正在连接到数据库 'operatedata'...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print("--- 开始获取存储过程定义用于性能分析 ---")
        for sp_name in SP_NAMES:
            print(f"\n========== 获取: {sp_name} ==========")
            try:
                cursor.execute(f"EXEC sp_helptext '{sp_name}'")
                rows = cursor.fetchall()
                
                if not rows:
                    print(f"错误：无法找到存储过程 '{sp_name}' 或没有权限访问。")
                    continue

                sp_definition = "".join([row.Text for row in rows])
                print(sp_definition)

            except pyodbc.Error as ex_inner:
                print(f"执行 sp_helptext for {sp_name} 时出错: {ex_inner}")
        print("========== 获取完成 ==========")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

if __name__ == "__main__":
    get_sp_definitions()
