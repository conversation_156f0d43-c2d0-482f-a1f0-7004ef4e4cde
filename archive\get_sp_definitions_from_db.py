import pyodbc
import sys

# --- 连接信息 ---
server = "192.168.2.5"
user = "sa"
password = "Musicbox123"
database = "operatedata"

# --- 要查询的存储过程 ---
procedures_to_fetch = [
    "usp_GenerateDynamicDailyReport",
    "usp_RunNightlyKTVReportJob_Final"
]

def get_sp_definition_with_pyodbc(proc_name):
    """使用 pyodbc 连接数据库并获取指定存储过程的定义"""
    conn = None
    try:
        print(f"--- 开始使用 pyodbc 获取 {proc_name} 的定义 ---")
        
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={server};"
            f"DATABASE={database};"
            f"UID={user};"
            f"PWD={password};"
            f"TrustServerCertificate=yes;"
        )
        
        conn = pyodbc.connect(conn_str, timeout=5)
        cursor = conn.cursor()
        
        # 使用 sp_helptext 来获取定义
        cursor.execute(f"EXEC sp_helptext '{proc_name}'")
        
        rows = cursor.fetchall()
        if not rows:
            print(f"错误: 存储过程 '{proc_name}' 不存在或无法访问。")
            return None

        definition = "".join([row.Text for row in rows])
        
        print(f"--- 成功获取 {proc_name} 的定义 ---")
        print(definition)
        print(f"--- {proc_name} 定义结束 ---\n")
        
        return definition

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"获取 {proc_name} 定义时发生数据库错误 (SQLSTATE: {sqlstate}): {ex}", file=sys.stderr)
        if 'IM002' in sqlstate:
            print("错误提示: ODBC Driver 17 for SQL Server 未找到。请确保已安装。", file=sys.stderr)
        return None
    except Exception as e:
        print(f"获取 {proc_name} 定义时发生未知错误: {e}", file=sys.stderr)
        return None
    finally:
        if conn:
            conn.close()

# --- 主执行逻辑 ---
if __name__ == "__main__":
    all_definitions = {}
    for sp in procedures_to_fetch:
        sp_def = get_sp_definition_with_pyodbc(sp)
        if sp_def:
            all_definitions[sp] = sp_def
    
    if len(all_definitions) == len(procedures_to_fetch):
        print("已成功获取所有指定的存储过程定义。")
    else:
        print("未能获取所有存储过程的定义，请检查上面的错误信息。", file=sys.stderr)
        sys.exit(1)