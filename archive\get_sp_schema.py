
import pyodbc
import pandas as pd
from datetime import datetime, timedelta

# Connection details
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

# Stored procedure parameters
shop_id = 3
report_date = '20250502'

try:
    # Establish connection
    cnxn = pyodbc.connect(cnxn_str)
    cursor = cnxn.cursor()

    # SQL to execute the stored procedure
    sql = f"EXEC usp_GenerateSimplifiedDailyReport @ShopId=?, @BeginDate=?, @EndDate=?"
    
    # Execute and fetch data
    cursor.execute(sql, shop_id, report_date, report_date)
    
    # Get column names from cursor description
    columns = [column[0] for column in cursor.description]
    print("Stored Procedure Columns:", columns)
    
    # Fetch the first row to understand data types
    first_row = cursor.fetchone()
    if first_row:
        print("First row of data:", first_row)

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database Error Occurred: {sqlstate}")
    print(ex)
finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
