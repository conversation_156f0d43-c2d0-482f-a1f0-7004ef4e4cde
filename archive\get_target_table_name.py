
import pyodbc

# Database connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# Stored procedure to analyze
SP_NAME = 'usp_Report_CreateDailyPerformance'

def get_sp_definition():
    """Connects to the database and retrieves the definition of a specified stored procedure."""
    conn = None
    try:
        print(f"正在连接到数据库 'operatedata'...")
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        print(f"\n--- 正在获取存储过程: {SP_NAME} 的定义 ---")
        try:
            cursor.execute(f"EXEC sp_helptext '{SP_NAME}'")
            rows = cursor.fetchall()
            
            if not rows:
                print(f"错误：无法找到存储过程 '{SP_NAME}' 或没有权限访问。")
                return

            sp_definition = "".join([row.Text for row in rows])
            print(sp_definition)
            print(f"--- 获取完成 ---")

        except pyodbc.Error as ex_inner:
            print(f"执行 sp_helptext for {SP_NAME} 时出错: {ex_inner}")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"数据库连接或查询出错。")
        print(f"SQLSTATE: {sqlstate}")
        print(f"错误信息: {ex}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
            print("\n数据库连接已关闭。")

if __name__ == "__main__":
    get_sp_definition()
