import pyodbc

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
PROC_NAME = 'dbo.usp_GenerateDayTimeReport_Simple_V3'
OUTPUT_FILE = 'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\usp_GenerateDayTimeReport_Simple_V3_source.sql'

# --- Main Execution Logic ---
def get_source_code():
    """
    Connects to the DB and saves the source code of a stored procedure to a file.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            cursor = conn.cursor()
            print(f"--- Getting source code for {PROC_NAME}... ---")
            original_sql = ""
            for row in cursor.execute(f"EXEC sp_helptext '{PROC_NAME}'"):
                original_sql += row[0]
            
            with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
                f.write(original_sql)
            print(f"Source code successfully saved to {OUTPUT_FILE}")

    except pyodbc.Error as ex:
        print(f"DATABASE ERROR: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    get_source_code()