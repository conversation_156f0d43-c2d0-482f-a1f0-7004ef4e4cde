
import pyodbc
from datetime import datetime, timedelta

# 数据库连接参数
server = '192.168.2.14'
database = 'dbfood'
username = 'sa'
password = '123'
conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;"

# 定义最后一批测试案例
final_test_cases = [
    {'id': 'FNL01', 'age_minutes': 3, 'room_tot': 550, 'payments': [550], 'desc': '最终数据-正常支付'},
    {'id': 'FNL02', 'age_minutes': 5, 'room_tot': 3000, 'payments': [2000, 800], 'desc': '最终数据-支付不足'},
    {'id': 'FNL03', 'age_minutes': 1, 'room_tot': 199, 'payments': [199], 'desc': '最终数据-另一次正常支付'},
]

connection = None
cursor = None

try:
    print(f"正在连接到数据库 {server}...")
    connection = pyodbc.connect(conn_str)
    cursor = connection.cursor()
    print("连接成功。准备插入最后一批近期数据。")

    for case in final_test_cases:
        inv_no = case['id']
        rm_no = inv_no[1:] # 使用后几位作为 RmNo, e.g., 'NL01'
        print(f"\n--- 正在处理案例: {inv_no} ({case['desc']}) ---")
        
        checkout_time = datetime.now() - timedelta(minutes=case['age_minutes'])
        acc_date = checkout_time.strftime('%Y%m%d')
        acc_time = checkout_time.strftime('%H:%M')

        # 1. 插入 ROOM 表
        sql_room = """INSERT INTO ROOM 
                         (InvNo, RmNo, RtNo, AreaNo, RmStatus, AccDate, AccTime, Tot, 
                          PriceNo, PrnFIndex, PrnDIndex, AccountManagerID, AccountManagerCName, 
                          CustomerServiceManagerID, CustomerServiceManagerName) 
                         VALUES (?, ?, '01', 'A', 'A', ?, ?, ?, '1', 'P', 'P', 'N/A', 'N/A', 'N/A', 'N/A')"""
        params_room = (inv_no, rm_no, acc_date, acc_time, case['room_tot'])
        try:
            cursor.execute("DELETE FROM ROOM WHERE InvNo = ?", inv_no)
            cursor.execute(sql_room, params_room)
            print(f"  -> 成功插入账单到 ROOM: {inv_no}")
        except pyodbc.IntegrityError as e:
            print(f"  -> 插入 ROOM 表失败 (可能是主键已存在): {e}")
            continue

        # 2. 插入 wxpayinfo 表
        payment_count = 0
        for payment_amount in case['payments']:
            payment_count += 1
            trans_id = f"wx_{inv_no}_{payment_count}"
            sql_wx = "INSERT INTO wxpayinfo (ShopId, RmNo, InvNo, Tot, transaction_id, out_trade_no) VALUES (1, ?, ?, ?, ?, ?)"
            params_wx = (rm_no, inv_no, payment_amount, trans_id, f"out_{trans_id}")
            cursor.execute("DELETE FROM wxpayinfo WHERE transaction_id = ?", trans_id)
            cursor.execute(sql_wx, params_wx)
            print(f"  -> 成功插入微信支付记录: {trans_id}")

    connection.commit()
    print("\n最后一批测试数据已成功提交到数据库！")

except pyodbc.Error as ex:
    print(f"数据库操作失败: {ex}")
    if connection:
        print("正在回滚事务...")
        connection.rollback()
except Exception as e:
    print(f"发生了未知错误: {e}")
finally:
    if cursor:
        cursor.close()
    if connection:
        connection.close()
        print("数据库连接已关闭。")
