import pyodbc
from datetime import datetime, timedelta

# 新数据库的连接参数
server = '192.168.2.14'
database = 'dbfood'
username = 'sa'
password = '123'

# 创建连接字符串
conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;"

connection = None
cursor = None

try:
    print(f"正在连接到新数据库 {server}...")
    connection = pyodbc.connect(conn_str)
    cursor = connection.cursor()
    print("连接成功。准备插入模拟数据。")

    # --- 设计模拟数据 ---
    checkout_time = datetime.now() - timedelta(minutes=2)
    acc_date = checkout_time.strftime('%Y%m%d')
    acc_time = checkout_time.strftime('%H:%M')

    mock_bills = [
        {'inv_no': 'TNV1', 'rm_no': 'T1', 'room_tot': 888, 'status': 'A'},
        {'inv_no': 'TNV2', 'rm_no': 'T2', 'room_tot': 1500, 'status': 'A'}
    ]

    mock_wx_payments = [
        {'inv_no': 'TNV1', 'wx_tot': 888, 'trans_id': f"wx_{checkout_time.strftime('%H%M%S')}_01"},
        {'inv_no': 'TNV2', 'wx_tot': 1000, 'trans_id': f"wx_{checkout_time.strftime('%H%M%S')}_02"},
        {'inv_no': 'TNV2', 'wx_tot': 500, 'trans_id': f"wx_{checkout_time.strftime('%H%M%S')}_03"}
    ]

    # --- 执行插入操作 ---
    print("\n正在插入 ROOM 表数据...")
    for bill in mock_bills:
        cursor.execute("DELETE FROM ROOM WHERE InvNo = ?", bill['inv_no'])
        # 使用从 RmType 表中查到的合法 RtNo '01'
        sql = """INSERT INTO ROOM 
                     (InvNo, RmNo, RtNo, AreaNo, RmStatus, AccDate, AccTime, Tot, 
                      PriceNo, PrnFIndex, PrnDIndex, AccountManagerID, AccountManagerCName, 
                      CustomerServiceManagerID, CustomerServiceManagerName) 
                     VALUES (?, ?, '01', 'A', ?, ?, ?, ?, '1', 'P', 'P', 'N/A', 'N/A', 'N/A', 'N/A')"""
        params = (bill['inv_no'], bill['rm_no'], bill['status'], acc_date, acc_time, bill['room_tot'])
        cursor.execute(sql, params)
        print(f"  -> 成功插入账单: {bill['inv_no']}")

    print("\n正在插入 wxpayinfo 表数据...")
    for payment in mock_wx_payments:
        cursor.execute("DELETE FROM wxpayinfo WHERE transaction_id = ?", payment['trans_id'])
        sql = "INSERT INTO wxpayinfo (ShopId, RmNo, InvNo, Tot, transaction_id, out_trade_no) VALUES (1, 'T0', ?, ?, ?, ?)"
        params = (payment['inv_no'], payment['wx_tot'], payment['trans_id'], f"out_{payment['trans_id']}")
        cursor.execute(sql, params)
        print(f"  -> 成功插入微信支付记录: {payment['trans_id']} (账单: {payment['inv_no']})")

    connection.commit()
    print("\n数据已成功提交到数据库！")

except pyodbc.Error as ex:
    print(f"数据库操作失败: {ex}")
    if connection:
        print("正在回滚事务...")
        connection.rollback()
except Exception as e:
    print(f"发生了未知错误: {e}")
finally:
    if cursor:
        cursor.close()
    if connection:
        connection.close()
        print("数据库连接已关闭。")
