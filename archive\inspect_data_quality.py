

import pyodbc
import pandas as pd
import sys

# --- 连接信息 ---
SOURCE_SERVER = '193.112.2.229'
SOURCE_DB = 'Dbfood'
SOURCE_USER = 'sa'
SOURCE_PASS = 'Musicbox@123'

# --- 表名 ---
TABLES_TO_INSPECT = ['FdType', 'food', 'foodlabel']

def inspect_data_quality():
    """审查源数据质量，特别是日期时间等可能导致转换错误的列。"""
    print("--- 正在连接到源数据库进行数据质量审查 ---")
    try:
        with pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SOURCE_SERVER};DATABASE={SOURCE_DB};UID={SOURCE_USER};PWD={SOURCE_PASS}') as cnxn:
            print("连接成功！")
            for table in TABLES_TO_INSPECT:
                print(f"\n--- 正在审查表: {table} ---")
                try:
                    df = pd.read_sql(f"SELECT * FROM {table}", cnxn)
                    print(f"成功提取 {len(df)} 行数据。开始检查各列...")
                    has_error = False
                    for col in df.columns:
                        # 重点检查 object/string 类型的列，它们最可能包含无效的日期格式
                        if df[col].dtype == 'object':
                            try:
                                # 尝试将列转换为datetime，如果失败则说明有问题
                                pd.to_datetime(df[col], errors='raise')
                            except (ValueError, TypeError):
                                # 如果转换失败，找出具体是哪些值有问题
                                for index, value in df[col].items():
                                    try:
                                        pd.to_datetime(value)
                                    except (ValueError, TypeError):
                                        if pd.notna(value): # 只报告非空的无效值
                                            print(f"  -> 发现潜在问题数据！表: [{table}], 列: [{col}], 行号: {index}, 值: '{value}'")
                                            has_error = True
                        # 也可以添加对其他数据类型的检查

                    if not has_error:
                        print("  -> 此表数据质量良好，未发现明显的类型转换问题。")

                except Exception as table_error:
                    print(f"审查表 {table} 时出错: {table_error}")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

if __name__ == '__main__':
    inspect_data_quality()

