import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 专案调查对象 ---
TARGET_INV_NO = '*********'
TARGET_WORK_DATE = '20250722' # 必须用日期来唯一锁定账单

def inspect_bill_details_final():
    """最终版的账单深度剖析，使用正确的列名和日期进行精确定位。"""
    cnxn = None
    try:
        # 1. 连接数据库
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} ---")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        # 2. 精确查询账单头信息
        print(f"\n--- 步骤A: 查询账单【{TARGET_INV_NO}】在日期【{TARGET_WORK_DATE}】的头信息 ---")
        sql_header = "SELECT InvNo, Tot, Numbers, OpenDateTime, CloseDatetime FROM dbo.RmCloseInfo WHERE InvNo = ? AND WorkDate = ?;"
        df_header = pd.read_sql_query(sql_header, cnxn, params=[TARGET_INV_NO, TARGET_WORK_DATE])

        if df_header.empty:
            print(f"错误：在 RmCloseInfo 中未找到 InvNo={TARGET_INV_NO} 且 WorkDate={TARGET_WORK_DATE} 的账单。")
            return

        print("账单头信息如下:")
        print(df_header.to_string(index=False))

        # 3. 精确查询账单所有消费明细
        print(f"\n--- 步骤B: 查询该账单的所有消费明细 (from FdCashBak) ---")
        # 通过关联RmCloseInfo来确保日期正确
        sql_details = """SELECT 
                           fb.FdCName, fb.FdQty, fb.FdPrice 
                       FROM 
                           dbo.FdCashBak fb
                       JOIN 
                           dbo.RmCloseInfo rc ON fb.InvNo = rc.InvNo
                       WHERE 
                           fb.InvNo = ? AND rc.WorkDate = ?
                       ORDER BY 
                           fb.CashTime;"""
        df_details = pd.read_sql_query(sql_details, cnxn, params=[TARGET_INV_NO, TARGET_WORK_DATE])

        if df_details.empty:
            print(f"警告：在 FdCashBak 中未找到该账单的任何消费明细。")
        else:
            # 计算总金额以供核对
            df_details['计算金额'] = df_details['FdQty'] * df_details['FdPrice']
            print("消费明细列表如下:")
            print(df_details.to_string(index=False))
            header_total = df_header.iloc[0]['Tot']
            details_total = df_details['计算金额'].sum()
            print(f"\n核对：头信息总额(Tot) = {header_total}, 明细计算总额 = {details_total:.2f}")
        
        # 4. 分析结论
        print("\n--- 步骤C: 分析结论 ---")
        direct_fall_items = df_details[df_details['FdCName'].str.contains('直落')]
        if len(direct_fall_items) > 1:
            print(f"结论：这张账单确实包含了 {len(direct_fall_items)} 个‘直落’相关项目。")
            print("这证实了系统在记录一个‘直落’套餐时，会将其拆分为多个记账项。")
        else:
            print("结论：未发现明显异常，该账单只包含一个‘直落’项目。")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    inspect_bill_details_final()