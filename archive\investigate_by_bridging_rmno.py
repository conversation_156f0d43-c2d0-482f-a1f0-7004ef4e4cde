import pyodbc
import pandas as pd
import sys

# --- 数据源 1: operatedata (获取缺失InvNo列表) ---
OPERATEDATA_SERVER = '192.168.2.5'
OPERATEDATA_DB = 'operatedata'
OPERATEDATA_USER = 'sa'
OPERATEDATA_PASS = 'Musicbox123'

# --- 数据源 2: 名堂本地数据库 (调查源) ---
RMS_SERVER = '193.112.2.229'
RMS_USER = 'sa'
RMS_PASS = 'Musicbox@123'
RMS_DB = 'rms2019'
FOOD_DB = 'Dbfood'

# --- 查询参数 ---
TARGET_SHOP_ID = 11
TARGET_WORK_DATE = '20250723'

# --- 输出文件名 ---
OUTPUT_FILENAME = 'final_investigation_report.txt'

def get_missing_invoices():
    """第一步：从 operatedata 获取缺失 OpenDateTime 的 InvNo 列表。"""
    print("--- 步骤 1: 从 operatedata 获取缺失 InvNo 列表 ---")
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={OPERATEDATA_SERVER};DATABASE={OPERATEDATA_DB};UID={OPERATEDATA_USER};PWD={OPERATEDATA_PASS}'
        cnxn = pyodbc.connect(conn_str)
        sql_query = "SELECT InvNo FROM dbo.RmCloseInfo WHERE ShopId = ? AND WorkDate = ? AND OpenDateTime IS NULL;"
        df = pd.read_sql_query(sql_query, cnxn, params=[TARGET_SHOP_ID, TARGET_WORK_DATE])
        invoice_list = df['InvNo'].tolist()
        print(f"成功获取到 {len(invoice_list)} 个需要调查的 InvNo。")
        return invoice_list
    finally:
        if cnxn: cnxn.close()

def investigate_by_bridging(invoice_list):
    """第二步：使用正确的列名进行最终的桥接查询。"""
    print(f"\n--- 步骤 2: 连接到服务器 {RMS_SERVER} 进行最终调查 ---")
    cnxn = None
    report_lines = [f"最终调查报告：ShopID={TARGET_SHOP_ID}, WorkDate={TARGET_WORK_DATE}"]
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={RMS_SERVER};DATABASE={RMS_DB};UID={RMS_USER};PWD=**********'
        cnxn = pyodbc.connect(conn_str)
        print("数据库连接成功！开始逐一核查...")

        for invno in invoice_list:
            report_lines.append(f"\n--------------------------------------------------")
            report_lines.append(f"【调查对象】结账单号 (InvNo): {invno}")

            # 1. 用 InvNo 从 Dbfood.fdinv 查找 RmNo
            df_fdinv = pd.read_sql_query(f"SELECT DISTINCT RmNo FROM {FOOD_DB}.dbo.fdinv WHERE InvNo = ?", cnxn, params=[invno])
            if df_fdinv.empty or pd.isna(df_fdinv.iloc[0, 0]):
                report_lines.append(f"  -> 步骤A: 在 Dbfood.fdinv 中未能找到该单号对应的房间号(RmNo)。调查中断。")
                continue
            
            room_no = df_fdinv.iloc[0, 0]
            report_lines.append(f"  -> 步骤A: 在 Dbfood.fdinv 中找到对应房间号 (RmNo): {room_no}")

            # 2. 用 RmNo 和 ComeDate 去 rms2019.opencacheinfo 查找开台记录
            report_lines.append(f"  -> 步骤B: 在 rms2019.opencacheinfo 中查找房间 {room_no} 在日期 {TARGET_WORK_DATE} 的所有开台记录...")
            # 注意：opencacheinfo 中的日期字段是 ComeDate
            sql_opencache = f"SELECT Invno, ComeDate, ComeTime FROM {RMS_DB}.dbo.opencacheinfo WHERE RmNo = ? AND ComeDate = ?"
            df_opencache = pd.read_sql_query(sql_opencache, cnxn, params=[room_no, TARGET_WORK_DATE])

            if df_opencache.empty:
                report_lines.append(f"     结果: 未找到任何记录。这证实了该房间当天没有在 opencacheinfo 中留下开台信息。")
            else:
                report_lines.append(f"     结果: 找到了 {len(df_opencache)} 条开台记录，详情如下:")
                report_lines.append(df_opencache.to_string(index=False))
                # 检查InvNo是否匹配
                if invno in df_opencache['Invno'].tolist():
                    report_lines.append(f"     分析: 警告！开台记录中包含当前单号 {invno}，但它在 operatedata 中却缺失 OpenDateTime，这不应该发生。")
                else:
                    report_lines.append(f"     分析: 开台记录中的单号与结账单号 {invno} 不匹配，这可能是导致问题的直接原因。")

        print("\n所有调查已完成！")
        return "\n".join(report_lines)

    finally:
        if cnxn: cnxn.close()

if __name__ == '__main__':
    try:
        missing_invoices = get_missing_invoices()
        if not missing_invoices:
            print("没有需要调查的InvNo，程序退出。")
            sys.exit(0)
        
        report_content = investigate_by_bridging(missing_invoices)
        
        print(f"\n--- 步骤 3: 生成并保存最终调查报告 ---")
        with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print("\n--- 最终调查报告预览 ---")
        print(report_content)
        print(f"\n调查报告已完整保存至文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)