

import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'Dbfood' # 明确指定 Dbfood 数据库
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

def investigate_schemas():
    """连接到数据库，获取 food 和 foodlabel 表的结构信息。"""
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} ---")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！正在侦查表结构...")

        # 使用 INFORMATION_SCHEMA.COLUMNS 来获取表结构，这是最标准的方法
        sql_food_schema = "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'food';"
        sql_foodlabel_schema = "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'foodlabel';"

        print("\n--- 表 a: dbo.food 的结构 ---")
        df_food = pd.read_sql_query(sql_food_schema, cnxn)
        print(df_food.to_string(index=False))

        print("\n--- 表 b: dbo.foodlabel 的结构 ---")
        df_foodlabel = pd.read_sql_query(sql_foodlabel_schema, cnxn)
        print(df_foodlabel.to_string(index=False))

        # 初步分析关联可能性
        food_cols = set(df_food['COLUMN_NAME'])
        label_cols = set(df_foodlabel['COLUMN_NAME'])
        common_cols = food_cols.intersection(label_cols)
        
        print("\n--- 关联性初步分析 ---")
        if common_cols:
            print(f"发现共同列，可能是关联键: {list(common_cols)}")
        else:
            print("未发现直接的共同列名，可能存在中间表或通过不同名称的键关联。")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    investigate_schemas()

