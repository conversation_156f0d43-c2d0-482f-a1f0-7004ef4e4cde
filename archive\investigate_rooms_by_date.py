import pyodbc
import pandas as pd
import sys

# --- 数据源 1: operatedata (用于获取问题房间列表) ---
OPERATEDATA_SERVER = '192.168.2.5'
OPERATEDATA_DB = 'operatedata'
OPERATEDATA_USER = 'sa'
OPERATEDATA_PASS = 'Musicbox123'

# --- 数据源 2: 名堂本地数据库 (用于调查) ---
RMS_SERVER = '193.112.2.229'
RMS_USER = 'sa'
RMS_PASS = 'Musicbox@123'
RMS_DB = 'rms2019'

# --- 查询参数 ---
TARGET_SHOP_ID = 11
TARGET_WORK_DATE = '20250723'

# --- 输出文件名 ---
OUTPUT_FILENAME = 'room_investigation_report.txt'

def get_problem_rooms():
    """第一步：从 operatedata 获取问题房间号列表。"""
    print("--- 步骤 1: 从 operatedata 获取房间号列表 ---")
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={OPERATEDATA_SERVER};DATABASE={OPERATEDATA_DB};UID={OPERATEDATA_USER};PWD={OPERATEDATA_PASS}'
        cnxn = pyodbc.connect(conn_str)
        sql_query = "SELECT DISTINCT RmNo FROM dbo.RmCloseInfo WHERE ShopId = ? AND WorkDate = ? AND OpenDateTime IS NULL;"
        df = pd.read_sql_query(sql_query, cnxn, params=[TARGET_SHOP_ID, TARGET_WORK_DATE])
        room_list = df['RmNo'].tolist()
        print(f"成功获取到 {len(room_list)} 个需要调查的房间号: {room_list}")
        return room_list
    finally:
        if cnxn: cnxn.close()

def investigate_rooms(room_list):
    """第二步：连接到名堂本地库，按房间和日期查询 opencacheinfo。"""
    print(f"\n--- 步骤 2: 连接到服务器 {RMS_SERVER} 进行调查 ---")
    cnxn = None
    report_lines = []
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={RMS_SERVER};DATABASE={RMS_DB};UID={RMS_USER};PWD=**********'
        cnxn = pyodbc.connect(conn_str)
        print("数据库连接成功！开始逐一核查...")

        for room_no in room_list:
            report_lines.append(f"\n--- 正在调查房间号: {room_no} ---")
            sql_query = "SELECT InvNo, OpenTime, CloseTime, WorkDate FROM dbo.opencacheinfo WHERE RmNo = ? AND WorkDate = ?"
            df_room_data = pd.read_sql_query(sql_query, cnxn, params=[room_no, TARGET_WORK_DATE])
            
            if df_room_data.empty:
                report_lines.append(f"结果: 在 {TARGET_WORK_DATE} 这一天，房间 {room_no} 在 opencacheinfo 中没有任何开台记录。")
            else:
                report_lines.append(f"结果: 在 {TARGET_WORK_DATE} 这一天，为房间 {room_no} 找到了 {len(df_room_data)} 条开台记录:")
                report_lines.append(df_room_data.to_string(index=False))
        
        print("\n所有房间核查完毕！")
        return "\n".join(report_lines)

    finally:
        if cnxn: cnxn.close()

if __name__ == '__main__':
    try:
        problem_rooms = get_problem_rooms()
        if not problem_rooms:
            print("没有需要调查的房间，程序退出。")
            sys.exit(0)
        
        report_content = investigate_rooms(problem_rooms)
        
        print(f"\n--- 步骤 3: 生成并保存调查报告 ---")
        with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print("\n--- 最终调查报告 ---")
        print(report_content)
        print(f"\n调查报告已成功保存至文件: {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)
