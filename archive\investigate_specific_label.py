import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'Dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 专案调查对象 ---
TARGET_FDCNAME = '11:50-14:50K+自助餐(周六日)'

def investigate_label():
    """调查一个特定商品，展示它的名称和它在foodlabel中的关联标签。"""
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} ---")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        # 构建SQL查询，关联food和foodlabel表
        sql_query = """SELECT 
                         f.FdNo, 
                         f.FdCName, 
                         fl.Type, 
                         fl.Category1, 
                         fl.CtTypeName
                     FROM 
                         dbo.food f
                     LEFT JOIN 
                         dbo.foodlabel fl ON f.FdNo = fl.FdNo
                     WHERE 
                         f.FdCName = ?;"""
        
        print(f"\n--- 正在调查项目:【{TARGET_FDCNAME}】--- ")
        df = pd.read_sql_query(sql_query, cnxn, params=[TARGET_FDCNAME])

        if df.empty:
            print("错误：在 food 表中未找到该项目。")
            return

        print("\n--- 调查结果 ---")
        print("该项目在 food 表中的名称以及在 foodlabel 表中对应的标签如下：")
        print(df.to_string(index=False))

        # 分析结论
        print("\n--- 分析结论 ---")
        # 检查任何一个标签字段是否包含‘直落’
        label_contains_keyword = df.apply(
            lambda row: 
                ('直落' in str(row['Type'])) or 
                ('直落' in str(row['Category1'])) or 
                ('直落' in str(row['CtTypeName'])), 
            axis=1
        ).any()

        if label_contains_keyword:
            print("结论：找到了！虽然该项目的【名称】不含‘直落’，但它在 foodlabel 表中的某个标签字段包含了‘直落’字样。")
            print("这证实了它是根据我们的组合逻辑被正确打上 IsDirectFall=1 标签的。")
        else:
            print("结论：未在标签中发现‘直落’关键字。这可能是一个预期之外的情况，需要进一步检查打标签的逻辑。")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    investigate_label()
