#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KTV门店营业数据分析系统
分析开台、结账数据，生成营业报表
"""

import pyodbc
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from typing import Dict, List, Tuple, Optional

class KTVDataAnalyzer:
    def __init__(self):
        # 数据库连接配置
        self.rms_config = {
            'server': '193.112.2.229',
            'database': 'rms2019',
            'username': 'sa',
            'password': 'Musicbox@123'
        }
        
        self.operate_config = {
            'server': '192.168.2.5',
            'database': 'operatedata',
            'username': 'sa',
            'password': 'Musicbox123'
        }
        
        self.shop_id = 11  # 名堂店ID
        
    def get_connection(self, config: Dict) -> pyodbc.Connection:
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={config['server']};
        DATABASE={config['database']};
        UID={config['username']};
        PWD={config['password']};
        """
        return pyodbc.connect(conn_str)
    
    def get_time_slots(self) -> pd.DataFrame:
        """获取时间段配置"""
        try:
            conn = self.get_connection(self.rms_config)
            
            # 获取门店时间段配置
            query = f"""
            SELECT st.*, t.TimeName, t.BegTime, t.EndTime
            FROM shoptimeinfo st
            LEFT JOIN timeinfo t ON st.timeno = t.timeno
            WHERE st.shopid = {self.shop_id}
            ORDER BY t.BegTime
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            return df
            
        except Exception as e:
            print(f"获取时间段配置失败: {e}")
            return pd.DataFrame()
    
    def get_open_data(self, work_date: str) -> pd.DataFrame:
        """获取开台数据"""
        try:
            conn = self.get_connection(self.rms_config)
            
            query = f"""
            SELECT Ikey, BookNo, ShopId, CustName, ComeDate, ComeTime, 
                   RmNo, BookStatus, CheckinStatus
            FROM opencacheinfo
            WHERE shopid = {self.shop_id} AND ComeDate = '{work_date}'
            ORDER BY ComeTime
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            # 处理时间格式
            if not df.empty:
                df['OpenDateTime'] = pd.to_datetime(df['ComeDate'] + ' ' + df['ComeTime'])
            
            return df
            
        except Exception as e:
            print(f"获取开台数据失败: {e}")
            return pd.DataFrame()
    
    def get_close_data(self, work_date: str) -> pd.DataFrame:
        """获取结账数据"""
        try:
            conn = self.get_connection(self.operate_config)
            
            query = f"""
            SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot,
                   Cash, Cash_Targ, Vesa, VesaName
            FROM rmcloseinfo
            WHERE shopid = {self.shop_id} AND WorkDate = '{work_date}'
            ORDER BY CloseDatetime
            """
            
            print(f"执行查询: {query}")
            df = pd.read_sql(query, conn)
            conn.close()
            
            print(f"查询结果: {len(df)} 条记录")
            return df
            
        except Exception as e:
            print(f"获取结账数据失败: {e}")
            return pd.DataFrame()
    
    def identify_channel(self, row) -> str:
        """识别消费渠道"""
        # 根据支付方式和其他信息判断渠道
        if pd.notna(row.get('VesaName')):
            vesa_name = str(row['VesaName']).lower()
            if '美团' in vesa_name or 'meituan' in vesa_name:
                return '美团'
            elif '抖音' in vesa_name or 'douyin' in vesa_name:
                return '抖音'
            elif '特权' in vesa_name:
                return '特权预约'
        
        # 默认渠道
        return 'K+'
    
    def time_overlap(self, start1: datetime, end1: datetime, 
                    start2: datetime, end2: datetime) -> bool:
        """检查两个时间段是否重叠"""
        return start1 < end2 and end1 > start2
    
    def parse_time_slot(self, beg_time: int, end_time: int, base_date: datetime) -> Tuple[datetime, datetime]:
        """解析时间段"""
        # 处理时间格式 (如1050表示10:50)
        beg_hour = beg_time // 100
        beg_min = beg_time % 100
        end_hour = end_time // 100
        end_min = end_time % 100
        
        start_dt = base_date.replace(hour=beg_hour, minute=beg_min, second=0, microsecond=0)
        
        # 如果结束时间小于开始时间，说明跨天了
        if end_time < beg_time:
            end_dt = (base_date + timedelta(days=1)).replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
        else:
            end_dt = base_date.replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
        
        return start_dt, end_dt
    
    def analyze_daily_data(self, work_date: str) -> Dict:
        """分析单日数据"""
        print(f"分析日期: {work_date}")
        
        # 获取基础数据
        time_slots = self.get_time_slots()
        open_data = self.get_open_data(work_date)
        close_data = self.get_close_data(work_date)
        
        print(f"时间段配置: {len(time_slots)} 个")
        print(f"开台记录: {len(open_data)} 条")
        print(f"结账记录: {len(close_data)} 条")
        
        if time_slots.empty or close_data.empty:
            return {}
        
        # 为结账数据添加渠道信息
        close_data['Channel'] = close_data.apply(self.identify_channel, axis=1)
        
        # 分析结果
        result = {
            'work_date': work_date,
            'time_slots': {},
            'summary': {
                'total_revenue': close_data['Tot'].sum(),
                'total_orders': len(close_data),
                'channels': close_data['Channel'].value_counts().to_dict()
            }
        }
        
        # 基准日期
        base_date = datetime.strptime(work_date, '%Y%m%d')
        
        # 分析每个时间段
        for _, slot in time_slots.iterrows():
            if pd.isna(slot['BegTime']) or pd.isna(slot['EndTime']):
                continue
                
            slot_start, slot_end = self.parse_time_slot(
                int(slot['BegTime']), int(slot['EndTime']), base_date
            )
            
            slot_name = f"{slot['BegTime']:04d}-{slot['EndTime']:04d}"
            
            # 找到与此时间段重叠的消费记录
            overlapping_orders = []
            direct_fall_count = 0  # 直落数量
            
            for _, order in close_data.iterrows():
                # 假设开台时间等于结账时间减去2小时（这里需要根据实际业务调整）
                close_time = pd.to_datetime(order['CloseDatetime'])
                estimated_open_time = close_time - timedelta(hours=2)
                
                # 检查是否与时间段重叠
                if self.time_overlap(estimated_open_time, close_time, slot_start, slot_end):
                    overlapping_orders.append(order)
                    
                    # 检查是否为直落（开台时间早于时间段开始）
                    if estimated_open_time < slot_start:
                        direct_fall_count += 1
            
            # 统计该时间段的数据
            slot_df = pd.DataFrame(overlapping_orders)
            
            if not slot_df.empty:
                channel_stats = {}
                for channel in slot_df['Channel'].unique():
                    channel_data = slot_df[slot_df['Channel'] == channel]
                    channel_stats[channel] = {
                        'count': len(channel_data),
                        'revenue': channel_data['Tot'].sum()
                    }
                
                result['time_slots'][slot_name] = {
                    'slot_info': {
                        'start': slot_start.strftime('%H:%M'),
                        'end': slot_end.strftime('%H:%M'),
                        'name': slot.get('TimeName', slot_name)
                    },
                    'total_orders': len(slot_df),
                    'total_revenue': slot_df['Tot'].sum(),
                    'direct_fall_count': direct_fall_count,
                    'channels': channel_stats
                }
        
        return result
    
    def generate_report(self, analysis_result: Dict) -> str:
        """生成分析报告"""
        if not analysis_result:
            return "无数据可分析"
        
        report = f"""
=== KTV营业数据分析报告 ===
分析日期: {analysis_result['work_date']}
门店: 名堂店 (ID: {self.shop_id})

=== 总体概况 ===
总营业额: ¥{analysis_result['summary']['total_revenue']:,}
总订单数: {analysis_result['summary']['total_orders']}

=== 渠道分布 ===
"""
        
        for channel, count in analysis_result['summary']['channels'].items():
            report += f"{channel}: {count} 单\n"
        
        report += "\n=== 时间段分析 ===\n"
        
        for slot_name, slot_data in analysis_result['time_slots'].items():
            report += f"\n【{slot_data['slot_info']['name']} ({slot_data['slot_info']['start']}-{slot_data['slot_info']['end']})】\n"
            report += f"订单数: {slot_data['total_orders']}\n"
            report += f"营业额: ¥{slot_data['total_revenue']:,}\n"
            report += f"直落数: {slot_data['direct_fall_count']}\n"
            
            if slot_data['channels']:
                report += "渠道明细:\n"
                for channel, stats in slot_data['channels'].items():
                    report += f"  {channel}: {stats['count']}单, ¥{stats['revenue']:,}\n"
        
        return report

def main():
    analyzer = KTVDataAnalyzer()
    
    # 分析最近几天的数据
    dates_to_analyze = [
        '20250717',  # 有数据的日期
        '20250718'   # 今天
    ]
    
    for date in dates_to_analyze:
        try:
            result = analyzer.analyze_daily_data(date)
            report = analyzer.generate_report(result)
            
            print(f"\n{'='*50}")
            print(report)
            print(f"{'='*50}\n")
            
            # 保存结果到文件
            with open(f'analysis_result_{date}.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
                
        except Exception as e:
            print(f"分析日期 {date} 时出错: {e}")

if __name__ == "__main__":
    main()
