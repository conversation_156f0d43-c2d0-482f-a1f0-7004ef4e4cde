

import pyodbc
import sys

# 要查询的本地服务器
LOCAL_SERVER = "***********"
LOCAL_DATABASE = "operatedata"
LOCAL_USER = "sa"
LOCAL_PASSWORD = "Musicbox123"

def get_connection():
    """建立到本地 operatedata 数据库的连接"""
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={LOCAL_SERVER};"
        f"DATABASE={LOCAL_DATABASE};"
        f"UID={LOCAL_USER};"
        f"PWD={LOCAL_PASSWORD};"
        f"TrustServerCertificate=yes;"
        f"LoginTimeout=10;"
    )
    try:
        return pyodbc.connect(conn_str)
    except pyodbc.Error as ex:
        print(f"数据库连接失败: {ex}", file=sys.stderr)
        return None

def list_linked_servers(conn):
    """查询并列出所有已配置的链接服务器"""
    # is_linked = 1 确保我们只选择真正的链接服务器
    query = "SELECT name, product, provider, data_source FROM sys.servers WHERE is_linked = 1;"
    print(f"正在服务器 {LOCAL_SERVER} 上执行查询: {query}")
    try:
        cursor = conn.cursor()
        cursor.execute(query)
        rows = cursor.fetchall()
        if not rows:
            print("在 *********** 上没有找到任何已配置的链接服务器。")
            return

        print("\n在 *********** 上找到的链接服务器:")
        print("-" * 80)
        print("{:<30} {:<20} {:<30}".format('链接服务器名称 (别名)', '产品', '数据源 (IP地址或名称)'))
        print("-" * 80)
        for row in rows:
            print("{:<30} {:<20} {:<30}".format(row.name, row.product, row.data_source))
        print("-" * 80)

    except pyodbc.Error as e:
        print(f"查询链接服务器失败: {e}")

def main():
    conn = get_connection()
    if conn:
        list_linked_servers(conn)
        conn.close()
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()

