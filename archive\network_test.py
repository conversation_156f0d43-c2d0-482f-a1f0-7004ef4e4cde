'''
import socket

# Server details
server_ip = '*************'
server_port = 1433  # Default SQL Server port

print(f"正在尝试连接到服务器 {server_ip} 的端口 {server_port}...")

# Create a socket object
s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

# Set a timeout for the connection attempt
s.settimeout(5)

try:
    # Attempt to connect
    s.connect((server_ip, server_port))
    print(f"成功连接到 {server_ip}:{server_port}！网络连接通畅。")
except socket.timeout:
    print(f"连接超时。无法在5秒内连接到 {server_ip}:{server_port}。")
except ConnectionRefusedError:
    print(f"连接被拒绝。服务器 {server_ip} 在运行，但端口 {server_port} 可能已关闭或被防火墙阻止。")
except socket.gaierror:
    print(f"地址解析错误。无法解析主机名/IP地址 {server_ip}。")
except Exception as e:
    print(f"发生未知网络错误: {e}")
finally:
    # Close the socket
    s.close()

'''