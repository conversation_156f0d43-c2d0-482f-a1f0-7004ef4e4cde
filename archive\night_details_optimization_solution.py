#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FullDailyReport_NightDetails 表和存储过程优化解决方案
发现的关键问题和解决方案
"""

def analyze_field_mapping_issues():
    """分析字段映射问题"""
    
    print("=" * 80)
    print("🔍 FullDailyReport_NightDetails 优化分析报告")
    print("=" * 80)
    
    print("\n❌ 发现的关键问题:")
    
    print("\n1. 字段映射不匹配问题:")
    print("   📊 存储过程 usp_GenerateSimplifiedDailyReport_V7_Final 输出字段:")
    procedure_fields = [
        'ReportDate', 'ShopName', 'Weekday',
        'TotalRevenue', 'DayTimeRevenue', 'NightTimeRevenue',
        'TotalBatchCount', 'DayTimeBatchCount', 'NightTimeBatchCount',
        'FreeMeal_KPlus', 'FreeMeal_Special', 'FreeMeal_Meituan', 'FreeMeal_Douyin',
        'FreeMeal_BatchCount', 'FreeMeal_Revenue',
        'Buyout_BatchCount', 'Buyout_Revenue',
        'Changyin_BatchCount', 'Changyin_Revenue',
        'FreeConsumption_BatchCount',
        'NonPackage_Special', 'NonPackage_Meituan', 'NonPackage_Douyin', 'NonPackage_Others',
        'Night_Verify_BatchCount', 'Night_Verify_Revenue'
    ]
    
    print("   🗃️ FullDailyReport_NightDetails 表字段:")
    table_fields = [
        'NightDetailID', 'ReportID',  # 系统字段
        'FreeMeal_KPlus', 'FreeMeal_Special', 'FreeMeal_Meituan', 'FreeMeal_Douyin',
        'FreeMeal_BatchCount', 'FreeMeal_Revenue',
        'Buyout_BatchCount', 'Buyout_Revenue',
        'Changyin_BatchCount', 'Changyin_Revenue',
        'FreeConsumption_BatchCount',
        'NonPackage_Special', 'NonPackage_Meituan', 'NonPackage_Douyin', 'NonPackage_Others',
        'DiscountFree_BatchCount', 'DiscountFree_Revenue'  # 表中有但存储过程没有
    ]
    
    # 找出存储过程有但表不需要的字段
    proc_only_fields = [f for f in procedure_fields if f not in table_fields]
    print(f"\n   ⚠️ 存储过程输出但表不需要的字段 ({len(proc_only_fields)} 个):")
    for field in proc_only_fields:
        print(f"      • {field}")
    
    # 找出表有但存储过程没有的字段
    table_only_fields = [f for f in table_fields if f not in procedure_fields and f not in ['NightDetailID', 'ReportID']]
    print(f"\n   ❌ 表需要但存储过程没有输出的字段 ({len(table_only_fields)} 个):")
    for field in table_only_fields:
        print(f"      • {field} ⚠️ 这是关键问题！")
    
    print("\n2. 数据使用率分析:")
    usage_analysis = {
        'unused_fields': [
            'FreeMeal_KPlus', 'FreeMeal_Special', 'FreeMeal_Meituan', 'FreeMeal_Douyin',
            'FreeMeal_BatchCount', 'FreeMeal_Revenue',
            'NonPackage_Special', 'NonPackage_Meituan', 'NonPackage_Douyin',
            'DiscountFree_BatchCount', 'DiscountFree_Revenue'
        ],
        'active_fields': [
            'Buyout_BatchCount', 'Buyout_Revenue',
            'Changyin_BatchCount', 'Changyin_Revenue',
            'FreeConsumption_BatchCount', 'NonPackage_Others'
        ]
    }
    
    print(f"   🔴 未使用字段 ({len(usage_analysis['unused_fields'])} 个): 使用率 0%")
    for field in usage_analysis['unused_fields']:
        print(f"      • {field}")
    
    print(f"   🟢 活跃字段 ({len(usage_analysis['active_fields'])} 个): 使用率 100%")
    for field in usage_analysis['active_fields']:
        print(f"      • {field}")
    
    return procedure_fields, table_fields, usage_analysis

def generate_optimization_solutions():
    """生成优化解决方案"""
    
    print("\n" + "=" * 80)
    print("💡 优化解决方案")
    print("=" * 80)
    
    print("\n🎯 方案1: 修改存储过程添加缺失字段")
    print("   优点: 保持表结构不变，支持未来扩展")
    print("   缺点: 需要实现DiscountFree业务逻辑")
    
    print("\n🎯 方案2: 优化表结构，移除未使用字段")
    print("   优点: 减少存储空间，提高性能")
    print("   缺点: 需要修改表结构和相关代码")
    
    print("\n🎯 方案3: 创建优化版本的插入逻辑")
    print("   优点: 不破坏现有结构，只插入有用数据")
    print("   缺点: 需要维护两套逻辑")
    
    print("\n📋 推荐方案: 方案1 + 方案3 组合")
    print("   1. 修改存储过程添加DiscountFree字段")
    print("   2. 创建优化版本只插入有意义的数据")
    print("   3. 保留原有字段结构以备未来使用")

def generate_optimized_procedure():
    """生成优化的存储过程"""
    
    print("\n" + "=" * 80)
    print("🔧 优化的存储过程代码")
    print("=" * 80)
    
    optimized_procedure = """
-- 优化版本: usp_GenerateSimplifiedDailyReport_V8_Optimized
-- 添加DiscountFree字段并优化输出结构
CREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V8_Optimized
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    CREATE TABLE #DailyReports (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        
        -- FreeMeal相关字段（当前未使用，但保留结构）
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        
        -- 活跃使用的字段
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        
        -- NonPackage相关字段（部分未使用）
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, NonPackage_Others int,
        
        -- 验证字段
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
        
        -- 新增：DiscountFree字段
        DiscountFree_BatchCount int, DiscountFree_Revenue decimal(18, 2)
    );

    DECLARE @CurrentDate DATE = @BeginDate;

    WHILE @CurrentDate <= @EndDate
    BEGIN
        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));
        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));

        -- CTE for base records
        WITH RecordsWithTimeMode AS (
            SELECT
                rt.*,
                sti.TimeMode,
                (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
                CASE
                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                    WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                    ELSE 2
                END AS RevenueClassificationMode
            FROM dbo.RmCloseInfo AS rt
            LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
        ),
        -- CTE for package data (night-time only)
        PackageData AS (
            SELECT
                r.InvNo,
                fdc.FdCName,
                (fdc.FdPrice * fdc.FdQty) as ItemRevenue
            FROM RecordsWithTimeMode r
            JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE r.TimeMode = 2
            AND fdc.ShopId = @ShopId
            AND (fdc.FdCName LIKE N'%买断%' OR fdc.FdCName LIKE N'%畅饮%' OR fdc.FdCName LIKE N'%折扣免费%')
        )
        -- Final aggregation and insertion
        INSERT INTO #DailyReports
        SELECT
            @CurrentDate AS ReportDate,
            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
            DATENAME(weekday, @CurrentDate) AS Weekday,
            
            -- Core Metrics
            ISNULL(SUM(Revenue), 0) AS TotalRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,
            (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,

            -- Free Meal Metrics (当前逻辑可能有问题，需要根据实际业务调整)
            0 AS FreeMeal_KPlus,      -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_Special,    -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_Meituan,    -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_Douyin,     -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_BatchCount, -- 暂时设为0，需要实现具体逻辑
            0 AS FreeMeal_Revenue,    -- 暂时设为0，需要实现具体逻辑

            -- Package Metrics (活跃字段)
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') AS Buyout_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%买断%'), 0) AS Buyout_Revenue,
            ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) AS Changyin_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%畅饮%' AND FdCName NOT LIKE N'%自由畅饮%'), 0) AS Changyin_Revenue,
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') AS FreeConsumption_BatchCount,

            -- Non-Package Metrics (大部分未使用)
            0 AS NonPackage_Special,  -- 暂时设为0
            0 AS NonPackage_Meituan,  -- 暂时设为0
            0 AS NonPackage_Douyin,   -- 暂时设为0
            
            -- NonPackage_Others (活跃字段，保持原逻辑)
            (COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) - 
            (
                0 + -- FreeMeal相关暂时为0
                (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') + 
                ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) + 
                (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') + 
                0 + 0 + 0  -- NonPackage_Special, Meituan, Douyin暂时为0
            ) AS NonPackage_Others,

            -- Verification Metrics
            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS Night_Verify_BatchCount,
            ISNULL(SUM(CASE WHEN rt.TimeMode = 2 THEN rt.Revenue ELSE 0 END), 0) AS Night_Verify_Revenue,
            
            -- 新增：DiscountFree Metrics
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%折扣免费%') AS DiscountFree_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%折扣免费%'), 0) AS DiscountFree_Revenue

        FROM RecordsWithTimeMode rt;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    SELECT * FROM #DailyReports ORDER BY ReportDate;

    DROP TABLE #DailyReports;

END
"""
    
    print(optimized_procedure)
    return optimized_procedure

def generate_optimized_insert_statement():
    """生成优化的插入语句"""
    
    print("\n" + "=" * 80)
    print("📝 优化的插入语句")
    print("=" * 80)
    
    # 只插入有意义的字段
    essential_fields_insert = """
-- 优化版本1: 只插入有意义的数据
INSERT INTO dbo.FullDailyReport_NightDetails (
    ReportID, 
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NonPackage_Others,
    DiscountFree_BatchCount, DiscountFree_Revenue
) SELECT 
    @ReportID,
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NonPackage_Others,
    DiscountFree_BatchCount, DiscountFree_Revenue
FROM #TempNightDetails;
"""
    
    # 完整字段插入（为未使用字段设置默认值）
    complete_fields_insert = """
-- 优化版本2: 完整字段插入（未使用字段设为NULL或0）
INSERT INTO dbo.FullDailyReport_NightDetails (
    ReportID,
    FreeMeal_KPlus, FreeMeal_Special, FreeMeal_Meituan, FreeMeal_Douyin,
    FreeMeal_BatchCount, FreeMeal_Revenue,
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NonPackage_Special, NonPackage_Meituan, NonPackage_Douyin, NonPackage_Others,
    DiscountFree_BatchCount, DiscountFree_Revenue
) SELECT 
    @ReportID,
    NULL, NULL, NULL, NULL,  -- FreeMeal字段设为NULL
    NULL, NULL,              -- FreeMeal批次和收入设为NULL
    Buyout_BatchCount, Buyout_Revenue,
    Changyin_BatchCount, Changyin_Revenue,
    FreeConsumption_BatchCount,
    NULL, NULL, NULL, NonPackage_Others,  -- 大部分NonPackage字段设为NULL
    DiscountFree_BatchCount, DiscountFree_Revenue
FROM #TempNightDetails;
"""
    
    print("🎯 推荐使用版本1（只插入有意义的数据）:")
    print(essential_fields_insert)
    
    print("\n🎯 备选版本2（完整字段插入）:")
    print(complete_fields_insert)
    
    return essential_fields_insert, complete_fields_insert

def main():
    """主函数"""
    print("🚀 开始生成FullDailyReport_NightDetails优化解决方案...")
    
    # 1. 分析字段映射问题
    procedure_fields, table_fields, usage_analysis = analyze_field_mapping_issues()
    
    # 2. 生成优化解决方案
    generate_optimization_solutions()
    
    # 3. 生成优化的存储过程
    optimized_procedure = generate_optimized_procedure()
    
    # 4. 生成优化的插入语句
    essential_insert, complete_insert = generate_optimized_insert_statement()
    
    # 5. 保存优化方案到文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存优化的存储过程
    with open(f"usp_GenerateSimplifiedDailyReport_V8_Optimized_{timestamp}.sql", 'w', encoding='utf-8') as f:
        f.write(optimized_procedure)
    
    # 保存优化的插入语句
    with open(f"optimized_insert_statements_{timestamp}.sql", 'w', encoding='utf-8') as f:
        f.write("-- 优化的插入语句集合\n\n")
        f.write(essential_insert)
        f.write("\n\n")
        f.write(complete_insert)
    
    print(f"\n✅ 优化方案已保存:")
    print(f"   📄 存储过程: usp_GenerateSimplifiedDailyReport_V8_Optimized_{timestamp}.sql")
    print(f"   📄 插入语句: optimized_insert_statements_{timestamp}.sql")
    
    print(f"\n🎯 下一步行动建议:")
    print("   1. 审查优化的存储过程，特别是DiscountFree业务逻辑")
    print("   2. 测试优化的插入语句")
    print("   3. 考虑是否需要修改usp_RunUnifiedDailyReportJob主存储过程")
    print("   4. 验证数据完整性和业务逻辑正确性")

if __name__ == "__main__":
    from datetime import datetime
    main()
