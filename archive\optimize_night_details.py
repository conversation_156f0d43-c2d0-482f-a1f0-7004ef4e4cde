#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化晚间档数据字段
1. 增加晚间档房费字段（统计批次）
2. 将"其他"字段改为模糊查询"年卡"关键词
3. Night_Verify 字段减去自由餐数据
"""

import pyodbc

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def update_night_details_procedure(connection):
    """更新夜间详情存储过程"""
    print("🚀 更新夜间详情存储过程...")
    
    # 先删除存储过程（如果存在）
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    CREATE TABLE #DailyReports (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, 
        NonPackage_RoomFee int, NonPackage_YearCard int,
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
        DiscountFree_BatchCount int, DiscountFree_Revenue decimal(18, 2)
    );

    DECLARE @CurrentDate DATE = @BeginDate;

    WHILE @CurrentDate <= @EndDate
    BEGIN
        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));
        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));

        WITH RecordsWithTimeMode AS (
            SELECT
                rt.*,
                sti.TimeMode,
                (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
                CASE
                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                    WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                    ELSE 2
                END AS RevenueClassificationMode
            FROM dbo.RmCloseInfo AS rt
            LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
        ),
        PackageData AS (
            SELECT
                r.InvNo,
                fdc.FdCName,
                (fdc.FdPrice * fdc.FdQty) as ItemRevenue
            FROM RecordsWithTimeMode r
            JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE r.TimeMode = 2
            AND fdc.ShopId = @ShopId
            AND (fdc.FdCName LIKE N'%买断%' OR fdc.FdCName LIKE N'%畅饮%')
        ),
        YearCardData AS (
            SELECT DISTINCT
                r.InvNo
            FROM RecordsWithTimeMode r
            JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE r.TimeMode = 2
            AND fdc.ShopId = @ShopId
            AND fdc.FdCName LIKE N'%年卡%'
        )
        INSERT INTO #DailyReports
        SELECT
            @CurrentDate AS ReportDate,
            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
            DATENAME(weekday, @CurrentDate) AS Weekday,
            
            ISNULL(SUM(Revenue), 0) AS TotalRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,
            (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,

            -- 免费餐相关
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.CtNo = 2 THEN rt.InvNo END) AS FreeMeal_KPlus,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS FreeMeal_Special,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS FreeMeal_Meituan,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS FreeMeal_Douyin,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.InvNo END) AS FreeMeal_BatchCount,
            ISNULL(SUM(CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.Revenue ELSE 0 END), 0) AS FreeMeal_Revenue,

            -- 买断和畅饮
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%买断%') AS Buyout_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%买断%'), 0) AS Buyout_Revenue,
            ((SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%畅饮%') - (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%')) AS Changyin_BatchCount,
            ISNULL((SELECT SUM(ItemRevenue) FROM PackageData WHERE FdCName LIKE N'%畅饮%' AND FdCName NOT LIKE N'%自由畅饮%'), 0) AS Changyin_Revenue,
            (SELECT COUNT(DISTINCT InvNo) FROM PackageData WHERE FdCName LIKE N'%自由畅饮%') AS FreeConsumption_BatchCount,

            -- 非套餐相关（优化后）
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS NonPackage_Special,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS NonPackage_Meituan,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS NonPackage_Douyin,
            
            -- 新增：晚间档房费批次
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND (rt.Cash > 0 OR rt.Cash_Targ > 0) THEN rt.InvNo END) AS NonPackage_RoomFee,
            
            -- 修改：年卡批次（替换原来的Others）
            (SELECT COUNT(DISTINCT InvNo) FROM YearCardData) AS NonPackage_YearCard,

            -- 验证字段（减去自由餐）
            (COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) - COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.InvNo END)) AS Night_Verify_BatchCount,
            (ISNULL(SUM(CASE WHEN rt.TimeMode = 2 THEN rt.Revenue ELSE 0 END), 0) - ISNULL(SUM(CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.Revenue ELSE 0 END), 0)) AS Night_Verify_Revenue,

            -- 折扣免费
            COUNT(CASE WHEN rt.AccOkZD > 0 THEN 1 ELSE NULL END) AS DiscountFree_BatchCount,
            ISNULL(SUM(CASE WHEN rt.AccOkZD > 0 THEN rt.AccOkZD ELSE 0 END), 0) AS DiscountFree_Revenue

        FROM RecordsWithTimeMode rt;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    SELECT * FROM #DailyReports ORDER BY ReportDate;

    DROP TABLE #DailyReports;

END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 优化版夜间详情存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建优化版存储过程失败: {str(e)}")
        return False

def update_unified_report_procedure(connection):
    """更新联合报表存储过程"""
    print("🚀 更新联合报表存储过程...")
    
    # 先删除存储过程（如果存在）
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateDynamicUnifiedDailyReport_V2', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateDynamicUnifiedDailyReport_V2"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateDynamicUnifiedDailyReport_V2
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX) = N'';
    DECLARE @pivot_columns NVARCHAR(MAX) = N'';

    -- 1. 动态获取该店铺的白天档时段
    SELECT @pivot_columns = @pivot_columns + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.KPlus_Count END), 0) AS [' + ti.TimeName + '_K+]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Special_Count END), 0) AS [' + ti.TimeName + '_特权预约]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Meituan_Count END), 0) AS [' + ti.TimeName + '_美团]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Douyin_Count END), 0) AS [' + ti.TimeName + '_抖音]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.RoomFee_Count END), 0) AS [' + ti.TimeName + '_房费]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Subtotal_Count END), 0) AS [' + ti.TimeName + '_小计]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.PreviousSlot_DirectFall END), 0) AS [' + ti.TimeName + '_上档直落]'
    FROM dbo.shoptimeinfo sti
    JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
    WHERE sti.Shopid = @ShopId AND sti.TimeMode = 1
    ORDER BY ti.BegTime;

    -- 2. 创建临时表存储优化后的夜间数据
    CREATE TABLE #TempNightData (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, 
        NonPackage_RoomFee int, NonPackage_YearCard int,
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
        DiscountFree_BatchCount int, DiscountFree_Revenue decimal(18, 2)
    );

    -- 3. 获取优化后的夜间数据
    INSERT INTO #TempNightData
    EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized 
        @ShopId = @ShopId, 
        @BeginDate = @BeginDate, 
        @EndDate = @EndDate;

    -- 4. 构建动态SQL查询
    SET @sql = N'
    SELECT
        -- 基础信息
        nd.ReportDate AS [日期],
        nd.ShopName AS [门店],
        nd.Weekday AS [星期],
        ISNULL(h.TotalRevenue, nd.TotalRevenue) AS [营收_总收入],
        ISNULL(h.DayTimeRevenue, nd.DayTimeRevenue) AS [营收_白天档],
        ISNULL(h.NightTimeRevenue, nd.NightTimeRevenue) AS [营收_晚上档],
        ISNULL(h.TotalBatchCount, nd.TotalBatchCount) AS [全天总批数],
        ISNULL(h.DayTimeBatchCount, nd.DayTimeBatchCount) AS [白天档_总批次],
        ISNULL(h.NightTimeBatchCount, nd.NightTimeBatchCount) AS [晚上档_总批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [白天档_直落],
        ISNULL(h.NightTimeDropInBatch, 0) AS [晚上档_直落],
        ISNULL(h.BuffetGuestCount, 0) AS [自助餐人数],
        ISNULL(h.TotalDirectFallGuests, 0) AS [直落人数]'
        + @pivot_columns + ',
        -- 白天档汇总字段
        ISNULL(h.DayTimeBatchCount, nd.DayTimeBatchCount) AS [k+餐批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [k+餐直落批数],
        ISNULL(h.NightTimeDropInBatch, 0) AS [17点 18点 19点档直落],
        
        -- 晚上档数据（优化后的字段）
        ISNULL(nd.FreeMeal_KPlus, 0) AS [k+自由餐_k+],
        ISNULL(nd.FreeMeal_Special, 0) AS [k+自由餐_特权预约],
        ISNULL(nd.FreeMeal_Meituan, 0) AS [k+自由餐_美团],
        ISNULL(nd.FreeMeal_Douyin, 0) AS [k+自由餐_抖音],
        ISNULL(nd.FreeMeal_BatchCount, 0) AS [k+自由餐_小计],
        ISNULL(nd.FreeMeal_Revenue, 0) AS [k+自由餐_营业额],
        
        ISNULL(nd.Buyout_BatchCount, 0) AS [20点后_买断],
        ISNULL(nd.Buyout_Revenue, 0) AS [20点后_买断_营业额],
        
        ISNULL(nd.Changyin_BatchCount, 0) AS [20点后_畅饮],
        ISNULL(nd.Changyin_Revenue, 0) AS [20点后_畅饮_营业额],
        
        ISNULL(nd.FreeConsumption_BatchCount, 0) AS [20点后_自由消套餐],
        
        ISNULL(nd.NonPackage_Special, 0) AS [20点后_促销套餐_特权预约],
        ISNULL(nd.NonPackage_Meituan, 0) AS [20点后_促销套餐_美团],
        ISNULL(nd.NonPackage_Douyin, 0) AS [20点后_促销套餐_抖音],
        ISNULL(nd.NonPackage_RoomFee, 0) AS [20点后_房费],
        ISNULL(nd.NonPackage_YearCard, 0) AS [20点后_年卡],
        
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [招待批次],
        ISNULL(nd.DiscountFree_Revenue, 0) AS [招待金额],
        
        ISNULL(nd.Night_Verify_BatchCount, 0) AS [20点后_批次小计_净值],
        ISNULL(nd.Night_Verify_Revenue, 0) AS [20点后_营收金额_净值]
        
    FROM #TempNightData nd
    LEFT JOIN dbo.FullDailyReport_Header AS h ON nd.ReportDate = h.ReportDate AND h.ShopID = ' + CAST(@ShopId AS NVARCHAR) + '
    LEFT JOIN dbo.FullDailyReport_TimeSlotDetails AS d ON h.ReportID = d.ReportID
    GROUP BY
        nd.ReportDate, nd.ShopName, nd.Weekday, nd.TotalRevenue, nd.DayTimeRevenue, nd.NightTimeRevenue,
        nd.TotalBatchCount, nd.DayTimeBatchCount, nd.NightTimeBatchCount,
        h.TotalRevenue, h.DayTimeRevenue, h.NightTimeRevenue, h.TotalBatchCount, h.DayTimeBatchCount, h.NightTimeBatchCount,
        h.DayTimeDropInBatch, h.NightTimeDropInBatch, h.BuffetGuestCount, h.TotalDirectFallGuests,
        nd.FreeMeal_KPlus, nd.FreeMeal_Special, nd.FreeMeal_Meituan, nd.FreeMeal_Douyin,
        nd.FreeMeal_BatchCount, nd.FreeMeal_Revenue, nd.Buyout_BatchCount, nd.Buyout_Revenue,
        nd.Changyin_BatchCount, nd.Changyin_Revenue, nd.FreeConsumption_BatchCount,
        nd.NonPackage_Special, nd.NonPackage_Meituan, nd.NonPackage_Douyin, nd.NonPackage_RoomFee, nd.NonPackage_YearCard,
        nd.DiscountFree_BatchCount, nd.DiscountFree_Revenue, nd.Night_Verify_BatchCount, nd.Night_Verify_Revenue
    ORDER BY nd.ReportDate';

    -- 5. 执行动态SQL
    EXEC sp_executesql @sql;

    -- 6. 清理临时表
    DROP TABLE #TempNightData;
END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 优化版联合报表存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建优化版联合报表存储过程失败: {str(e)}")
        return False

def test_optimized_procedures(connection):
    """测试优化后的存储过程"""
    print("\n🧪 测试优化后的存储过程...")
    
    try:
        cursor = connection.cursor()
        
        # 测试优化版联合报表存储过程
        test_query = """
        EXEC dbo.usp_GenerateDynamicUnifiedDailyReport_V2 
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24', 
            @ShopId = 11
        """
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        rows = cursor.fetchall()
        
        print(f"✅ 优化版存储过程执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        print(f"📊 返回行数: {len(rows)}")
        
        if rows:
            print(f"\n📋 新增和修改的字段:")
            new_fields = ['20点后_房费', '20点后_年卡', '20点后_批次小计_净值', '20点后_营收金额_净值']
            for field in new_fields:
                if field in columns:
                    col_index = columns.index(field)
                    val = rows[0][col_index] if col_index < len(rows[0]) else "N/A"
                    print(f"   ✅ {field}: {val}")
            
            # 保存结果到文件
            print(f"\n💾 保存优化结果到CSV文件...")
            import csv
            filename = f"优化版联合报表_店铺{11}_20250724.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                # 写入列头
                writer.writerow(columns)
                # 写入数据
                for row in rows:
                    writer.writerow(row)
            
            print(f"✅ 优化结果已保存到: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试优化版存储过程失败: {str(e)}")
        return False

def main():
    print("🚀 开始优化晚间档数据字段...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 1. 更新夜间详情存储过程
        success1 = update_night_details_procedure(connection)
        
        if success1:
            # 2. 更新联合报表存储过程
            success2 = update_unified_report_procedure(connection)
            
            if success2:
                # 3. 测试优化后的存储过程
                test_success = test_optimized_procedures(connection)
                
                if test_success:
                    print(f"\n🎉 晚间档数据优化完成！")
                    print(f"📦 新存储过程:")
                    print(f"   1. usp_GenerateSimplifiedDailyReport_V7_Final_Optimized - 优化版夜间详情")
                    print(f"   2. usp_GenerateDynamicUnifiedDailyReport_V2 - 优化版联合报表")
                    
                    print(f"\n📋 优化内容:")
                    print(f"   ✅ 新增：20点后_房费 字段（统计晚间档房费批次）")
                    print(f"   ✅ 修改：20点后_年卡 字段（替换原Others，模糊查询'年卡'关键词）")
                    print(f"   ✅ 优化：20点后_批次小计_净值 = 原值 - 自由餐批次")
                    print(f"   ✅ 优化：20点后_营收金额_净值 = 原值 - 自由餐收入")
                    
                    print(f"\n📋 使用方法:")
                    print(f"   EXEC dbo.usp_GenerateDynamicUnifiedDailyReport_V2 '2025-07-24', '2025-07-24', 11")
                else:
                    print("\n❌ 优化版存储过程测试失败")
            else:
                print("\n❌ 联合报表存储过程更新失败")
        else:
            print("\n❌ 夜间详情存储过程更新失败")
    
    except Exception as e:
        print(f"❌ 优化过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
