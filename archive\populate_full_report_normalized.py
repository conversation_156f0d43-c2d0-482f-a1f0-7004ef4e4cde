import pyodbc
from datetime import datetime, timedelta

# --- Configuration ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

shop_id = 3
start_date_str = '20250502'
end_date_str = '20250510'

# --- SQL Queries ---
# Query to get header data from the main SP
# We only select the first 11 columns which are the summary fields
GET_HEADER_DATA_SQL = """
    EXEC dbo.usp_GenerateFullDailyReport @ShopId=?, @BeginDate=?, @EndDate=?;
"""

# Query to get time slot details, adapted from zhiluo.sql
# This is now a parameterized query
GET_TIMESLOT_DETAILS_SQL = """
    WITH TimeSlots AS (
        SELECT
            ti.TimeNo, ti.TimeName, ti.BegTime,
            DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(? AS datetime))) AS SlotStartDateTime,
            LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(? AS datetime))), 1, '2999-12-31') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
        FROM dbo.shoptimeinfo AS sti
        JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
        WHERE sti.ShopId = ?
    ),
    TrueDropInData AS (
        SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
        JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
        WHERE rt.ShopId = ? AND rt.WorkDate = ? AND rt.OpenDateTime IS NOT NULL
          AND rt.Beg_Key <> rt.End_Key
          AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
          AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
    )
    SELECT
        ti_main.TimeName,
        ti_main.BegTime, -- For ordering
        COUNT(CASE WHEN rt.CtNo = 2 THEN 1 ELSE NULL END) AS KPlus_Count,
        COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS Special_Count,
        COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS Meituan_Count,
        COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS Douyin_Count,
        COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS RoomFee_Count,
        COUNT(rt.InvNo) AS Subtotal_Count,
        ISNULL((
            SELECT COUNT(tdi.InvNo)
            FROM TrueDropInData AS tdi
            JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo
            WHERE ts_beg.BegTime < ti_main.BegTime
              AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime
        ), 0) AS PreviousSlot_DirectFall
    FROM dbo.RmCloseInfo_Test AS rt
    JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
    JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
    WHERE rt.ShopId = ? AND rt.WorkDate = ? AND rt.OpenDateTime IS NOT NULL
    GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
    ORDER BY ti_main.BegTime;
"""

def run_migration():
    """Main function to perform the data migration."""
    try:
        cnxn = pyodbc.connect(cnxn_str)
        cursor = cnxn.cursor()
        print("Database connection successful.")

        current_date = datetime.strptime(start_date_str, '%Y%m%d')
        end_date = datetime.strptime(end_date_str, '%Y%m%d')

        while current_date <= end_date:
            date_str = current_date.strftime('%Y%m%d')
            print(f"\n--- Processing Date: {date_str} ---")

            # Check if data for this date and shop already exists
            if cursor.execute("SELECT 1 FROM FullDailyReport_Header WHERE ReportDate = ? AND ShopID = ?", date_str, shop_id).fetchone():
                print(f"Data for {date_str} already exists. Skipping.")
                current_date += timedelta(days=1)
                continue

            # --- Step 1: Insert Header Data ---
            print("Fetching header data...")
            cursor.execute(GET_HEADER_DATA_SQL, shop_id, date_str, date_str)
            header_row = cursor.fetchone()
            
            if not header_row:
                print(f"No header data found for {date_str}. Skipping.")
                current_date += timedelta(days=1)
                continue

            # Extract only the summary columns (first 11)
            # ReportDate, ShopName, Weekday, TotalRevenue, DayTimeRevenue, TotalBatchCount, DayTimeBatchCount, DayTimeDirectFall, MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue
            # We need to get ShopName separately if not in the SP output
            shop_name_row = cursor.execute("SELECT ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = ?", shop_id).fetchone()
            shop_name = shop_name_row[0] if shop_name_row else f"ShopID_{shop_id}"

            # Assuming the first 8 columns of the SP are what we need for the header.
            # ReportDate, ShopName, Weekday, TotalRevenue, DayTimeRevenue, TotalBatchCount, DayTimeBatchCount, DayTimeDirectFall, MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue
            # Let's take the first 11 columns from the SP result.
            header_data_to_insert = (date_str, shop_id, shop_name, header_row[2]) + tuple(header_row[3:11])

            cursor.execute("""
                INSERT INTO FullDailyReport_Header (ReportDate, ShopID, ShopName, Weekday, TotalRevenue, DayTimeRevenue, TotalBatchCount, DayTimeBatchCount, DayTimeDirectFall, MealBatchCount, MealDirectFallBatchCount, MealDirectFallRevenue)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """, date_str, shop_id, shop_name, header_row[2], header_row[3], header_row[4], header_row[5], header_row[6], header_row[7], header_row[8], header_row[9], header_row[10])

            # Get the newly created ReportID
            report_id = cursor.execute("SELECT @@IDENTITY AS ID;").fetchone()[0]
            print(f"Header data inserted. New ReportID: {report_id}")

            # --- Step 2: Insert Time Slot Details ---
            print("Fetching time slot details...")
            params = (date_str, date_str, shop_id, shop_id, date_str, shop_id, date_str)
            cursor.execute(GET_TIMESLOT_DETAILS_SQL, params)
            slot_rows = cursor.fetchall()

            slot_insert_count = 0
            for i, slot_row in enumerate(slot_rows):
                # Data to insert: ReportID, TimeSlotName, TimeSlotOrder, KPlus, Special, Meituan, Douyin, RoomFee, Subtotal, DirectFall
                slot_data_to_insert = (report_id, slot_row.TimeName, i + 1, slot_row.KPlus_Count, slot_row.Special_Count, slot_row.Meituan_Count, slot_row.Douyin_Count, slot_row.RoomFee_Count, slot_row.Subtotal_Count, slot_row.PreviousSlot_DirectFall)
                cursor.execute("""
                    INSERT INTO FullDailyReport_TimeSlotDetails (ReportID, TimeSlotName, TimeSlotOrder, KPlus_Count, Special_Count, Meituan_Count, Douyin_Count, RoomFee_Count, Subtotal_Count, PreviousSlot_DirectFall)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
                """, slot_data_to_insert)
                slot_insert_count += 1
            
            print(f"{slot_insert_count} time slot detail rows inserted.")
            cursor.commit()
            current_date += timedelta(days=1)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"\n--- DATABASE ERROR ---")
        print(f"SQLSTATE: {sqlstate}")
        print(ex)
        if 'cnxn' in locals() and cnxn:
            print("Rolling back transaction...")
            cnxn.rollback()
    finally:
        if 'cnxn' in locals() and cnxn:
            cnxn.close()
            print("\nDatabase connection closed.")

if __name__ == "__main__":
    run_migration()
