import pyodbc
import pandas as pd
from datetime import datetime, timedelta

# --- Connection Details ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

# --- Parameters ---
shop_id = 3
start_date_str = '20250502'
end_date_str = '20250510'
table_name = 'KTV_Full_Daily_Report'
procedure_name = 'dbo.usp_GenerateFullDailyReport'

# --- Column Definitions (Based on the 87 columns from the probe) ---
# We will create them generically as we don't have proper names
num_columns = 87

def create_table_if_not_exists(cursor):
    """Creates the target table with a generic schema if it doesn't exist."""
    if cursor.tables(table=table_name, tableType='TABLE').fetchone():
        print(f"Table '{table_name}' already exists.")
        return

    print(f"Creating table '{table_name}'...")
    # Start with a primary key
    create_sql = f"CREATE TABLE {table_name} (Id INT IDENTITY(1,1) PRIMARY KEY, "
    
    # Add columns generically. We know the first 3 are date, nvarchar, nvarchar.
    # The rest are mostly numeric, so we'll use decimal for safety.
    create_sql += "ReportDate DATE, ShopName NVARCHAR(50), Weekday NVARCHAR(10), "
    
    # Add the rest of the columns (from 4 to 87)
    for i in range(4, num_columns + 1):
        # A mix of int and decimal(18,2) would be ideal, but decimal is safer
        create_sql += f"Col_{i:02d} DECIMAL(18, 2)"
        if i < num_columns:
            create_sql += ", "
    
    create_sql += ")"
    
    cursor.execute(create_sql)
    cursor.commit()
    print(f"Table '{table_name}' created successfully.")

def process_and_insert_data(cursor, start_date, end_date):
    """Executes the SP for a date range and inserts data into the target table."""
    insert_count = 0
    
    # Prepare insert statement with placeholders
    # We skip the Identity column 'Id'
    placeholders = ", ".join(['?' for _ in range(num_columns)])
    # Build column list for insert statement
    col_names_list = ["ReportDate", "ShopName", "Weekday"] + [f"Col_{i:02d}" for i in range(4, num_columns + 1)]
    col_names = ", ".join(col_names_list)
    insert_sql = f"INSERT INTO {table_name} ({col_names}) VALUES ({placeholders})"

    current_date = start_date
    while current_date <= end_date:
        report_date_str = current_date.strftime('%Y%m%d')
        print(f"Processing data for: {report_date_str}")
        
        try:
            sp_sql = f"EXEC {procedure_name} @ShopId=?, @BeginDate=?, @EndDate=?"
            cursor.execute(sp_sql, shop_id, report_date_str, report_date_str)
            
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    cursor.execute(insert_sql, list(row))
                    insert_count += 1
            else:
                print(f"  -> No data returned for {report_date_str}.")
        except pyodbc.Error as ex:
            print(f"  -> An error occurred for date {report_date_str}: {ex}")
            
        current_date += timedelta(days=1)
    
    cursor.commit()
    print(f"\nData insertion complete. Total rows inserted: {insert_count}")

# --- Main Execution ---
def main():
    """Main function to run the data migration process."""
    try:
        cnxn = pyodbc.connect(cnxn_str)
        cursor = cnxn.cursor()
        
        create_table_if_not_exists(cursor)
        
        start_date = datetime.strptime(start_date_str, '%Y%m%d')
        end_date = datetime.strptime(end_date_str, '%Y%m%d')
        process_and_insert_data(cursor, start_date, end_date)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database Error Occurred: {sqlstate}")
        print(ex)
    finally:
        if 'cnxn' in locals() and cnxn:
            cnxn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    main()
