

import pyodbc
import pandas as pd
from datetime import datetime, timedelta

# --- Connection Details ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

# --- Parameters ---
shop_id = 3
start_date_str = '20250502'
end_date_str = '20250510'
table_name = 'KTV_Simplified_Daily_Report'

# --- Column Mapping (from SP output to Table schema) ---
# Note: The order MUST match the stored procedure's output columns
column_mapping = {
    0: ('ReportDate', 'date'),
    1: ('ShopName', 'nvarchar(50)'),
    2: ('Weekday', 'nvarchar(10)'),
    3: ('TotalRevenue', 'decimal(18, 2)'),
    4: ('DayTimeRevenue', 'decimal(18, 2)'),
    5: ('NightTimeRevenue', 'decimal(18, 2)'),
    6: ('TotalBatchCount', 'int'),
    7: ('DayTimeBatchCount', 'int'),
    8: ('NightTimeBatchCount', 'int'),
    9: ('FreeMeal_KPlus', 'int'),
    10: ('FreeMeal_Special', 'int'),
    11: ('FreeMeal_Meituan', 'int'),
    12: ('FreeMeal_Douyin', 'int'),
    13: ('FreeMeal_BatchCount', 'int'),
    14: ('FreeMeal_Revenue', 'decimal(18, 2)'),
    15: ('Buyout_BatchCount', 'int'),
    16: ('Buyout_Revenue', 'decimal(18, 2)'),
    17: ('Changyin_BatchCount', 'int'),
    18: ('Changyin_Revenue', 'decimal(18, 2)'),
    19: ('FreeConsumption_BatchCount', 'int'),
    20: ('NonPackage_Special', 'int'),
    21: ('NonPackage_Meituan', 'int'),
    22: ('NonPackage_Douyin', 'int'),
23: ('NonPackage_Others', 'int'),
    24: ('Night_Verify_BatchCount', 'int'),
    25: ('Night_Verify_Revenue', 'decimal(18, 2)')
}

def create_table_if_not_exists(cursor):
    """Checks if the target table exists and creates it if it doesn't."""
    table_check_sql = f"IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{table_name}' and xtype='U') CREATE TABLE {table_name} (Id INT IDENTITY(1,1) PRIMARY KEY)"
    cursor.execute(table_check_sql)
    cursor.commit()
    
    # Add columns based on mapping
    for i in sorted(column_mapping.keys()):
        col_name, col_type = column_mapping[i]
        # Check if column exists before adding
        col_check_sql = f"IF NOT EXISTS (SELECT * FROM sys.columns WHERE Name = N'{col_name}' AND Object_ID = Object_ID(N'{table_name}')) ALTER TABLE {table_name} ADD {col_name} {col_type}"
        cursor.execute(col_check_sql)
    cursor.commit()
    print(f"Table '{table_name}' is ready.")

def process_and_insert_data(cursor, start_date, end_date):
    """Executes the SP for a date range and inserts data into the target table."""
    current_date = start_date
    insert_count = 0
    
    # Prepare insert statement
    col_names = ", ".join([val[0] for val in column_mapping.values()])
    placeholders = ", ".join(['?' for _ in column_mapping])
    insert_sql = f"INSERT INTO {table_name} ({col_names}) VALUES ({placeholders})"

    while current_date <= end_date:
        report_date_str = current_date.strftime('%Y%m%d')
        print(f"Processing data for: {report_date_str}")
        
        # Execute stored procedure
        sp_sql = f"EXEC usp_GenerateSimplifiedDailyReport @ShopId=?, @BeginDate=?, @EndDate=?"
        cursor.execute(sp_sql, shop_id, report_date_str, report_date_str)
        
        # Fetch and insert rows
        rows = cursor.fetchall()
        if rows:
            for row in rows:
                # Convert row to list for insertion
                cursor.execute(insert_sql, list(row))
                insert_count += 1
        else:
            print(f"  -> No data returned for {report_date_str}.")
            
        current_date += timedelta(days=1)
    
    cursor.commit()
    print(f"\nData insertion complete. Total rows inserted: {insert_count}")


# --- Main Execution ---
def main():
    """Main function to run the data migration process."""
    try:
        cnxn = pyodbc.connect(cnxn_str)
        cursor = cnxn.cursor()
        
        # 1. Create table if it doesn't exist
        create_table_if_not_exists(cursor)
        
        # 2. Process date range and insert data
        start_date = datetime.strptime(start_date_str, '%Y%m%d')
        end_date = datetime.strptime(end_date_str, '%Y%m%d')
        process_and_insert_data(cursor, start_date, end_date)

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database Error Occurred: {sqlstate}")
        print(ex)
    finally:
        if 'cnxn' in locals() and cnxn:
            cnxn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    main()

