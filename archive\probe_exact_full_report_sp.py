import pyodbc
import pandas as pd
from datetime import datetime

# --- Connection Details ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
cnxn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

# --- Parameters ---
shop_id = 3
report_date = '20250502'
procedure_name = 'dbo.usp_GenerateFullDailyReport'

def probe_procedure():
    """Connects to the DB and probes the specified stored procedure."""
    try:
        cnxn = pyodbc.connect(cnxn_str)
        cursor = cnxn.cursor()

        # We assume the parameters are the same standard ones
        sql = f"EXEC {procedure_name} @ShopId=?, @BeginDate=?, @EndDate=?"
        
        print(f"Executing {procedure_name} for date {report_date}...")
        cursor.execute(sql, shop_id, report_date, report_date)
        
        columns = [column[0] for column in cursor.description]
        print("\n--- Column Names ---")
        print(columns)
        
        first_row = cursor.fetchone()
        if first_row:
            print("\n--- First Row of Data ---")
            print(list(first_row))

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"\n--- DATABASE ERROR ---")
        print(f"An error occurred while executing the stored procedure: {procedure_name}")
        print(f"SQLSTATE: {sqlstate}")
        print(ex)
    finally:
        if 'cnxn' in locals() and cnxn:
            cnxn.close()
            print("\nDatabase connection closed.")

if __name__ == "__main__":
    probe_procedure()
