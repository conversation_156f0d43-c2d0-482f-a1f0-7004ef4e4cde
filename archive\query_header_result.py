import pyodbc

# --- 数据库连接配置 ---
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
# --- 配置结束 ---

# --- 查询参数 ---
target_date = '2025-07-24'
shop_id = 11
# --- 参数结束 ---

# 构建连接字符串
connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

sql_query = "SELECT * FROM dbo.FullDailyReport_Header WHERE ReportDate = ? AND ShopID = ?;"

connection = None
try:
    connection = pyodbc.connect(connection_string)
    cursor = connection.cursor()
    
    print(f"--- Querying FullDailyReport_Header ---")
    print(f"Target Date: {target_date}")
    print(f"Shop ID: {shop_id}")
    print("-----------------------------------------")

    cursor.execute(sql_query, target_date, shop_id)
    rows = cursor.fetchall()
    headers = [column[0] for column in cursor.description]

    if rows:
        # 使用简单的循环打印，不依赖外部库
        header_line = ' | '.join(map(str, headers))
        print(header_line)
        print('-' * len(header_line))
        for row in rows:
            row_line = ' | '.join(map(str, row))
            print(row_line)
    else:
        print("Query returned no results.")

except pyodbc.Error as ex:
    print(f"Database query failed: {ex}")
finally:
    if connection:
        connection.close()