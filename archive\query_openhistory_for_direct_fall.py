import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_DATE = '20250724'
SHOP_ID = 11

# --- Query Definitions ---

# Query 1: Get the list of invoice numbers (InvNo) for '直落' items.
SQL_GET_INVNO_LIST = f"""
SELECT DISTINCT ti.invno
FROM rmcloseinfo ti
JOIN FdCashBak fdc ON ti.invno = fdc.InvNo COLLATE Chinese_PRC_CI_AS
WHERE ti.WorkDate = '{TARGET_DATE}' 
  AND ti.ShopId = {SHOP_ID}
  AND fdc.FdCName LIKE N'%直落%'
  AND fdc.ShopId = {SHOP_ID};
"""

# Query 2: Get combined details from opencacheinfo and rmcloseinfo.
SQL_GET_COMBINED_DETAILS = """
SELECT 
    ci.Invno,
    ci.RmNo,
    CAST(ci.ComeDate AS DATETIME) + CAST(ci.ComeTime AS DATETIME) AS OpenDateTime,
    rc.CloseDatetime AS CloseDateTime,
    DATEDIFF(minute, (CAST(ci.ComeDate AS DATETIME) + CAST(ci.ComeTime AS DATETIME)), rc.CloseDatetime) AS Duration_Minutes,
    ci.Beg_Key,
    t_beg.begtime AS BegTime_Formatted,
    rc.End_Key,
    t_end.endtime AS EndTime_Formatted,
    ci.Numbers AS GuestCount,
    rc.WorkDate
FROM opencacheinfo ci
JOIN rmcloseinfo rc ON ci.Invno = rc.InvNo COLLATE Chinese_PRC_CI_AS
LEFT JOIN shoptimeinfo st_beg ON ci.Shopid = st_beg.Shopid AND ci.Beg_Key = st_beg.TimeNo
LEFT JOIN timeinfo t_beg ON st_beg.TimeNo = t_beg.TimeNo
LEFT JOIN shoptimeinfo st_end ON rc.Shopid = st_end.Shopid AND rc.End_Key = st_end.TimeNo
LEFT JOIN timeinfo t_end ON st_end.TimeNo = t_end.TimeNo
WHERE ci.Invno IN ({invno_list}) AND ci.ShopId = {SHOP_ID}
ORDER BY OpenDateTime;
"""

# --- Main Execution Logic ---
def query_combined_details():
    """
    Finds invoices with '直落' items and then queries their combined details.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            print(f"--- Step 1: Getting list of invoices with '直落' items for {TARGET_DATE} ---")
            df_invnos = pd.read_sql(SQL_GET_INVNO_LIST, conn)
            
            if df_invnos.empty:
                print("No invoices found with '直落' items.")
                return

            invno_list = df_invnos['invno'].tolist()
            print(f"Found {len(invno_list)} invoices: {invno_list}")

            formatted_invno_list = ",".join([f"'{inv}'" for inv in invno_list])
            
            final_sql_details = SQL_GET_COMBINED_DETAILS.format(
                invno_list=formatted_invno_list, 
                SHOP_ID=SHOP_ID
            )

            print("\n--- Step 2: Querying combined details from opencacheinfo and rmcloseinfo... ---")
            df_details = pd.read_sql(final_sql_details, conn)

            print("\n--- COMBINED DETAILS FOR '直落' INVOICES ---")
            if df_details.empty:
                print("No matching records found.")
            else:
                # Set pandas to display all columns
                pd.set_option('display.max_columns', None)
                print(df_details.to_string())

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"DATABASE ERROR: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    query_combined_details()