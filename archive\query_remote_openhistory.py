

import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR_LOCAL = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
CONN_STR_REMOTE = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=193.112.2.229;"
    "DATABASE=rms2019;"
    "UID=sa;"
    "PWD=Musicbox@123;"
)
TARGET_DATE = '20250724'
SHOP_ID = 11

# --- Query Definitions ---

# Query 1: Get invoices and their close times from the LOCAL server.
SQL_GET_LOCAL_DATA = f"""
SELECT DISTINCT 
    rc.InvNo, 
    rc.CloseDatetime
FROM dbo.rmcloseinfo rc
JOIN dbo.FdCashBak fdc ON rc.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
WHERE rc.WorkDate = '{TARGET_DATE}' 
  AND rc.ShopId = {SHOP_ID}
  AND fdc.FdCName LIKE N'%直落%'
  AND fdc.ShopId = {SHOP_ID};
"""

# Query 2: Get open-time details from the REMOTE server's openhistory table.
SQL_GET_REMOTE_DATA = """
SELECT 
    Invno, -- Corrected case
    RmNo,
    CAST(ComeDate AS DATETIME) + CAST(ComeTime AS DATETIME) AS OpenDateTime,
    Beg_Key,
    End_Key,
    Numbers AS GuestCount
FROM openhistory
WHERE Invno IN ({invno_list}) AND ShopId = {SHOP_ID}
ORDER BY OpenDateTime;
"""

# --- Main Execution Logic ---
def query_and_merge_details():
    """
    Connects to two servers, gets data from both, and merges them for analysis.
    """
    df_local = pd.DataFrame()
    df_remote = pd.DataFrame()

    try:
        # Step 1: Get data from LOCAL server
        with pyodbc.connect(CONN_STR_LOCAL) as conn_local:
            print(f"--- Step 1: Getting '直落' invoices and close times from LOCAL server (operatedata) for {TARGET_DATE} ---")
            df_local = pd.read_sql(SQL_GET_LOCAL_DATA, conn_local)
            
            if df_local.empty:
                print("No invoices found with '直落' items.")
                return

            print(f"Found {len(df_local)} invoices.")

    except pyodbc.Error as ex:
        print(f"DATABASE ERROR on LOCAL server: {ex}")
        return

    # Step 2: Get data from REMOTE server
    try:
        invno_list = df_local['InvNo'].tolist()
        formatted_invno_list = ",".join([f"'{inv}'" for inv in invno_list])
        final_sql_remote = SQL_GET_REMOTE_DATA.format(invno_list=formatted_invno_list, SHOP_ID=SHOP_ID)

        with pyodbc.connect(CONN_STR_REMOTE) as conn_remote:
            print("\n--- Step 2: Querying openhistory on REMOTE server (rms2019)... ---")
            df_remote = pd.read_sql(final_sql_remote, conn_remote)

            if df_remote.empty:
                print("No matching openhistory records found on remote server.")
                return

    except pyodbc.Error as ex:
        print(f"DATABASE ERROR on REMOTE server: {ex}")
        return

    # Step 3: Merge and display the results
    print("\n--- Step 3: Merging local and remote data for final analysis... ---")
    # We need to correct the case of the join key if they differ
    df_local.rename(columns={'InvNo': 'Invno'}, inplace=True)
    
    final_df = pd.merge(df_remote, df_local, on='Invno', how='left')

    # Calculate duration now that we have both open and close times
    final_df['Duration_Minutes'] = (final_df['CloseDatetime'] - final_df['OpenDateTime']).dt.total_seconds() / 60
    final_df['Duration_Minutes'] = final_df['Duration_Minutes'].fillna(0).astype(int)

    print("\n--- FINAL COMBINED AND CALCULATED DETAILS ---")
    pd.set_option('display.max_columns', None)
    print(final_df.to_string())

if __name__ == "__main__":
    query_and_merge_details()
