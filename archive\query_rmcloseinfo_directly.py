

import pyodbc
import pandas as pd

# --- Configuration ---
DB_CONFIG = {
    'server': '192.168.2.5',
    'database': 'operatedata',
    'username': 'sa',
    'password': 'Musicbox123'
}

# --- Test Parameters ---
TEST_SHOP_ID = 11
TEST_DATE = '2025-07-24'

# --- Main Logic ---
def query_rmcloseinfo_directly():
    """Directly queries RmCloseInfo using StaffReport's logic and prints the calculated Turnover."""
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}"
    
    try:
        with pyodbc.connect(conn_str) as conn:
            print(f"--- Directly querying RmCloseInfo for Shop ID {TEST_SHOP_ID} on {TEST_DATE} ---\n")

            # Define the time range based on StaffReport SP
            # For 2025-07-24, this means 2025-07-24 08:00:00 to 2025-07-25 06:00:00
            begin_datetime = pd.to_datetime(TEST_DATE + ' 08:00:00')
            end_datetime = pd.to_datetime(TEST_DATE + ' 06:00:00') + pd.Timedelta(days=1)

            # Construct the SQL query with the exact StaffReport calculation formula
            sql_query = f"""
            SELECT 
                SUM(Cash+Cash_Targ*0.8+Vesa+GZ+AccOkZD+RechargeAccount+NoPayed+WXPay+AliPay+MTPay+DZPay+NMPay+[Check]+WechatDeposit+WechatShopping+ReturnAccount) AS CalculatedTurnover
            FROM 
                RmCloseInfo 
            WHERE 
                Shopid = ? 
                AND CloseDatetime BETWEEN ? AND ?
            GROUP BY 
                Shopid;
            """
            
            params = [TEST_SHOP_ID, begin_datetime, end_datetime]
            df = pd.read_sql(sql_query, conn, params=params)
            
            if not df.empty:
                calculated_turnover = df['CalculatedTurnover'].iloc[0]
                print(f"Calculated Turnover from current RmCloseInfo data: {calculated_turnover}")
            else:
                print("No data found in RmCloseInfo for the specified criteria to calculate Turnover.")
                calculated_turnover = 0

            # Print comparison with previous values
            print("\n--- Comparison with previous results ---")
            print(f"From RmCloseInfo_Day (provided): {127512}")
            print(f"From usp_GenerateDayTimeReport_Simple_V3_final_fix: {177718.0}")
            print(f"From direct query of RmCloseInfo: {calculated_turnover}")

            if calculated_turnover == 177718.0:
                print("\nConclusion: Direct query of RmCloseInfo matches usp_GenerateDayTimeReport_Simple_V3_final_fix.")
                print("This suggests RmCloseInfo_Day is an older snapshot.")
            elif calculated_turnover == 127512:
                print("\nConclusion: Direct query of RmCloseInfo matches RmCloseInfo_Day.")
                print("This suggests there's still a subtle difference in the SP logic.")
            else:
                print("\nConclusion: Direct query of RmCloseInfo yields a new value.")

    except Exception as e:
        print(f"\nAn error occurred: {e}")

if __name__ == '__main__':
    query_rmcloseinfo_directly()
