
import pyodbc

# 新数据库的连接参数
server = '192.168.2.14'
database = 'dbfood'
username = 'sa'
password = '123'

# 创建连接字符串
conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;"

connection = None

try:
    print(f"正在连接到数据库 {server}...")
    connection = pyodbc.connect(conn_str)
    cursor = connection.cursor()
    print("连接成功。正在查询 RmType 表...")

    cursor.execute("SELECT TOP 5 RtNo, RtName FROM RmType")
    rows = cursor.fetchall()

    if not rows:
        print("RmType 表中没有数据或查询失败。")
    else:
        print("\n从 RmType 表中获取到的可用房间类型:")
        for row in rows:
            print(f"  -> RtNo: {row.RtNo}, RtName: {row.RtName}")

except pyodbc.Error as ex:
    print(f"数据库操作失败: {ex}")
except Exception as e:
    print(f"发生了未知错误: {e}")
finally:
    if connection:
        connection.close()
        print("\n数据库连接已关闭。")
