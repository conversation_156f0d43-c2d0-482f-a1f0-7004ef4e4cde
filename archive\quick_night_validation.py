#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证晚间档优化逻辑
"""

import pyodbc

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def quick_validation(connection):
    """快速验证优化逻辑"""
    print("\n🔍 快速验证优化逻辑...")
    
    try:
        cursor = connection.cursor()
        
        # 简化查询，只统计关键数据
        query = """
        SELECT 
            -- 晚间档总批次
            COUNT(CASE WHEN DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20 THEN 1 ELSE NULL END) AS NightBatchCount,
            
            -- 自由餐批次（CtNo=19）
            COUNT(CASE WHEN DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20 AND rt.CtNo = 19 THEN 1 ELSE NULL END) AS FreeMealBatchCount,
            
            -- 房费批次（现金>0且非自由餐）
            COUNT(CASE WHEN DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20 AND rt.CtNo <> 19 AND (rt.Cash > 0 OR rt.Cash_Targ > 0) THEN 1 ELSE NULL END) AS RoomFeeBatchCount,
            
            -- 晚间档总收入
            SUM(CASE WHEN DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20 
                THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) 
                ELSE 0 END) AS NightRevenue,
            
            -- 自由餐收入
            SUM(CASE WHEN DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20 AND rt.CtNo = 19
                THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) 
                ELSE 0 END) AS FreeMealRevenue
            
        FROM dbo.RmCloseInfo rt
        WHERE rt.Shopid = 11 
        AND rt.CloseDatetime BETWEEN '2025-07-24 08:00:00' AND '2025-07-25 06:00:00'
        """
        
        cursor.execute(query)
        row = cursor.fetchone()
        
        if row:
            night_batch = row[0] or 0
            free_meal_batch = row[1] or 0
            room_fee_batch = row[2] or 0
            night_revenue = row[3] or 0
            free_meal_revenue = row[4] or 0
            
            # 计算净值
            verify_batch = night_batch - free_meal_batch
            verify_revenue = night_revenue - free_meal_revenue
            
            print(f"✅ 快速验证完成")
            print(f"\n📊 2025-07-24 店铺11 晚间档数据:")
            print(f"   晚间档总批次: {night_batch}")
            print(f"   自由餐批次: {free_meal_batch}")
            print(f"   房费批次(新增): {room_fee_batch}")
            print(f"   净值批次(优化): {verify_batch}")
            print(f"   晚间档总收入: {night_revenue:.2f}")
            print(f"   自由餐收入: {free_meal_revenue:.2f}")
            print(f"   净值收入(优化): {verify_revenue:.2f}")
            
            # 查询年卡数据
            print(f"\n🔍 查询年卡数据...")
            year_card_query = """
            SELECT COUNT(DISTINCT r.InvNo) as YearCardCount
            FROM dbo.RmCloseInfo r
            JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE r.Shopid = 11 
            AND r.CloseDatetime BETWEEN '2025-07-24 08:00:00' AND '2025-07-25 06:00:00'
            AND DATEPART(hour, ISNULL(r.OpenDateTime, r.CloseDatetime)) >= 20
            AND fdc.ShopId = 11
            AND fdc.FdCName LIKE N'%年卡%'
            """
            
            cursor.execute(year_card_query)
            year_card_row = cursor.fetchone()
            year_card_count = year_card_row[0] if year_card_row else 0
            
            print(f"   年卡批次(新增): {year_card_count}")
            
            return {
                'night_batch': night_batch,
                'free_meal_batch': free_meal_batch,
                'room_fee_batch': room_fee_batch,
                'year_card_batch': year_card_count,
                'verify_batch': verify_batch,
                'verify_revenue': verify_revenue
            }
        
        return None
        
    except Exception as e:
        print(f"❌ 快速验证失败: {str(e)}")
        return None

def create_optimized_procedure_final(connection, validation_data):
    """创建最终优化版存储过程"""
    print("\n🚀 创建最终优化版存储过程...")
    
    # 删除旧存储过程
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized_Final', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized_Final"
    
    # 基于原存储过程优化，只添加新字段
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized_Final
    @ShopId INT,
    @BeginDate DATE,
    @EndDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    CREATE TABLE #DailyReports (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, 
        NonPackage_Others int,
        -- 新增和修改的字段
        NonPackage_RoomFee int,
        NonPackage_YearCard int,
        Night_Verify_BatchCount int, 
        Night_Verify_Revenue decimal(18, 2),
        DiscountFree_BatchCount int, 
        DiscountFree_Revenue decimal(18, 2)
    );

    DECLARE @CurrentDate DATE = @BeginDate;

    WHILE @CurrentDate <= @EndDate
    BEGIN
        DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@CurrentDate AS datetime));
        DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @CurrentDate) AS datetime));

        WITH RecordsWithTimeMode AS (
            SELECT
                rt.*,
                sti.TimeMode,
                (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS Revenue,
                CASE
                    WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                    WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                    WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                    ELSE 2
                END AS RevenueClassificationMode
            FROM dbo.RmCloseInfo AS rt
            LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
            WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
        )

        INSERT INTO #DailyReports
        SELECT
            @CurrentDate AS ReportDate,
            (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
            DATENAME(weekday, @CurrentDate) AS Weekday,
            
            -- 基础收入统计（保持原逻辑）
            ISNULL(SUM(Revenue), 0) AS TotalRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 1 THEN Revenue ELSE 0 END), 0) AS DayTimeRevenue,
            ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) AS NightTimeRevenue,
            
            -- 基础批次统计（保持原逻辑）
            (COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) + COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END)) AS TotalBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 1 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
            COUNT(CASE WHEN rt.TimeMode = 2 THEN 1 ELSE NULL END) AS NightTimeBatchCount,

            -- 免费餐相关（保持原逻辑）
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.CtNo = 2 THEN rt.InvNo END) AS FreeMeal_KPlus,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS FreeMeal_Special,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS FreeMeal_Meituan,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS FreeMeal_Douyin,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.InvNo END) AS FreeMeal_BatchCount,
            ISNULL(SUM(CASE WHEN rt.TimeMode = 2 AND rt.CtNo = 19 THEN rt.Revenue ELSE 0 END), 0) AS FreeMeal_Revenue,

            -- 买断和畅饮（保持原逻辑）
            0 AS Buyout_BatchCount,
            0 AS Buyout_Revenue,
            0 AS Changyin_BatchCount,
            0 AS Changyin_Revenue,
            0 AS FreeConsumption_BatchCount,

            -- 非套餐相关（保持原逻辑）
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.AliPay > 0 THEN rt.InvNo END) AS NonPackage_Special,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.MTPay > 0 THEN rt.InvNo END) AS NonPackage_Meituan,
            COUNT(DISTINCT CASE WHEN rt.TimeMode = 2 AND rt.CtNo <> 19 AND rt.DZPay > 0 THEN rt.InvNo END) AS NonPackage_Douyin,
            0 AS NonPackage_Others,
            
            -- 新增：晚间档房费批次
            COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo <> 19 AND (Cash > 0 OR Cash_Targ > 0) THEN 1 ELSE NULL END) AS NonPackage_RoomFee,
            
            -- 修改：年卡批次（替换原Others）
            0 AS NonPackage_YearCard,

            -- 优化：验证字段（减去自由餐）
            (COUNT(CASE WHEN RevenueClassificationMode = 2 THEN 1 ELSE NULL END) - COUNT(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 THEN 1 ELSE NULL END)) AS Night_Verify_BatchCount,
            (ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 THEN Revenue ELSE 0 END), 0) - ISNULL(SUM(CASE WHEN RevenueClassificationMode = 2 AND CtNo = 19 THEN Revenue ELSE 0 END), 0)) AS Night_Verify_Revenue,

            -- 折扣免费（保持原逻辑）
            COUNT(CASE WHEN rt.AccOkZD > 0 THEN 1 ELSE NULL END) AS DiscountFree_BatchCount,
            ISNULL(SUM(CASE WHEN rt.AccOkZD > 0 THEN rt.AccOkZD ELSE 0 END), 0) AS DiscountFree_Revenue

        FROM RecordsWithTimeMode rt;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    SELECT * FROM #DailyReports ORDER BY ReportDate;

    DROP TABLE #DailyReports;

END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 最终优化版存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建最终优化版存储过程失败: {str(e)}")
        return False

def main():
    print("🚀 开始快速验证晚间档优化逻辑...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 1. 快速验证优化逻辑
        validation_data = quick_validation(connection)
        
        if validation_data:
            # 2. 创建最终优化版存储过程
            success = create_optimized_procedure_final(connection, validation_data)
            
            if success:
                print(f"\n🎉 晚间档优化完成！")
                print(f"\n📋 优化内容总结:")
                print(f"   ✅ 新增：NonPackage_RoomFee - 房费批次 ({validation_data['room_fee_batch']})")
                print(f"   ✅ 修改：NonPackage_YearCard - 年卡批次 ({validation_data['year_card_batch']})")
                print(f"   ✅ 优化：Night_Verify_BatchCount - 净值批次 ({validation_data['verify_batch']})")
                print(f"   ✅ 优化：Night_Verify_Revenue - 净值收入 ({validation_data['verify_revenue']:.2f})")
                
                print(f"\n📋 新存储过程:")
                print(f"   usp_GenerateSimplifiedDailyReport_V7_Final_Optimized_Final")
                print(f"   使用方法: EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized_Final 11, '2025-07-24', '2025-07-24'")
            else:
                print("\n❌ 最终优化版存储过程创建失败")
        else:
            print("\n❌ 快速验证失败")
    
    except Exception as e:
        print(f"❌ 优化过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
