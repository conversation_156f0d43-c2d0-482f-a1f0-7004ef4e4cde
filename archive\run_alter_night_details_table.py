
import pyodbc
import os

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=***********;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- Use os.path.join for robust path construction ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
SQL_FILE_PATH = os.path.join(SCRIPT_DIR, 'alter_night_details_table.sql')

# --- Main Execution Logic ---
def alter_table():
    """
    Connects to the database and executes the ALTER TABLE script.
    """
    try:
        print(f"--- Reading SQL script from {SQL_FILE_PATH} ---")
        with open(SQL_FILE_PATH, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()
            print("--- Executing ALTER TABLE script... ---")
            cursor.execute(sql_script)
            print("--- <PERSON><PERSON><PERSON> executed. Table schema should be updated. ---")

    except FileNotFoundError:
        print(f"ERROR: Source SQL file not found at {SQL_FILE_PATH}")
    except pyodbc.Error as ex:
        print(f"DATABASE ERROR: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    alter_table()
