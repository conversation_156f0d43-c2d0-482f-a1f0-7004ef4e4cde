
import pyodbc

# --- 配置 ---
SERVER = '193.112.2.229'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'

def run_and_verify():
    # 使用不会出错的原始字符串定义路径
    connection_string = (
        'DRIVER={ODBC Driver 17 for SQL Server};'
        f'SERVER={SERVER};'
        f'DATABASE={DATABASE};'
        f'UID={USERNAME};'
        f'PWD={PASSWORD};'
        f'TrustServerCertificate=yes;'
    )

    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} ---')
            
            # 1. 执行存储过程，插入一条新数据
            print("\nExecuting usp_LogHourlyRoomStatistics to insert current stats...")
            cursor.execute("EXEC dbo.usp_LogHourlyRoomStatistics;")
            print("Execution complete.")

            # 2. 查询刚刚插入的最新一条数据并展示
            print("\n--- Fetching the latest record for your verification ---")
            query = "SELECT TOP 1 * FROM dbo.RoomStatisticsHourly ORDER BY LogID DESC;"
            cursor.execute(query)
            
            columns = [column[0] for column in cursor.description]
            row = cursor.fetchone()

            if row:
                print("\n--- LATEST HOURLY ROOM STATS ---")
                for i, col_name in enumerate(columns):
                    print(f"{col_name}: {row[i]}")
            else:
                print("Could not retrieve the new record.")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    run_and_verify()
