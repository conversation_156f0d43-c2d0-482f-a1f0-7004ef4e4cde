
import pyodbc

SERVER = '*************'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'
SQL_FILE_PATH = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\create_detail_table_only.sql'

def create_table():
    connection_string = (
        f'DRIVER={{ODBC Driver 17 for SQL Server}};'
        f'SERVER={SERVER};'
        f'DATABASE={DATABASE};'
        f'UID={USERNAME};'
        f'PWD={PASSWORD};'
        f'TrustServerCertificate=yes;'
    )
    try:
        with open(SQL_FILE_PATH, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Connected to {SERVER} ---')
            cursor.execute(sql_script)
            print("--- Detail table creation script executed. ---")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    create_table()
