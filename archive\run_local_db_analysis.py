
import pyodbc

# --- 连接配置 (已切换到 2.5 服务器) ---
SERVER = '***********'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 针对 operatedata 的 SQL 分析脚本 ---
SQL_SCRIPT = """
SET NOCOUNT ON;

PRINT '--- [operatedata] Top 15 Largest Tables (by Row Count) ---
';

SELECT TOP 15
    t.NAME AS TableName,
    p.rows AS RowCount
FROM 
    sys.tables t
INNER JOIN 
    sys.indexes i ON t.object_id = i.object_id
INNER JOIN 
    sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
WHERE 
    i.index_id <= 1
ORDER BY 
    p.rows DESC;
GO

PRINT '--- [operatedata] Daily Growth Check for Key Tables (Last 3 Days) ---
';

-- 假设 FdCashBak, FdInv, RmCloseInfo 是关键增长表
SELECT 'FdCashBak' as TableName, CONVERT(date, CashTime) as Day, COUNT(*) as DailyCount FROM FdCashBak WHERE CashTime >= DATEADD(day, -3, GETDATE()) GROUP BY CONVERT(date, CashTime) ORDER BY Day DESC;
GO

SELECT 'FdInv' as TableName, CONVERT(varchar(10), AccDate, 120) as Day, COUNT(*) as DailyCount FROM FdInv WHERE AccDate >= CONVERT(char(8), DATEADD(day, -3, GETDATE()), 112) GROUP BY CONVERT(varchar(10), AccDate, 120) ORDER BY Day DESC;
GO

SELECT 'RmCloseInfo_Day' as TableName, CONVERT(date, WorkDate) as Day, COUNT(*) as DailyCount FROM RmCloseInfo_Day WHERE WorkDate >= DATEADD(day, -3, GETDATE()) GROUP BY CONVERT(date, WorkDate) ORDER BY Day DESC;
GO
"""

def execute_sql_script():
    connection_string = (
        f'DRIVER={{ODBC Driver 17 for SQL Server}}および;' 
        f'SERVER={{SERVER}};' 
        f'DATABASE={{DATABASE}};' 
        f'UID={{USERNAME}};' 
        f'PWD={{PASSWORD}};' 
        f'TrustServerCertificate=yes;' 
    )

    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=10) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {{SERVER}} ---\n')
            
            commands = SQL_SCRIPT.split('GO\n')
            for command in commands:
                if command.strip():
                    print(f'\n--- Executing Query ---\n{{command.strip()}}\n-------------------------\\n')
                    cursor.execute(command)
                    try:
                        rows = cursor.fetchall()
                        if rows:
                            columns = [column[0] for column in cursor.description]
                            print("\n--- Query Result ---")
                            print(", ".join(columns))
                            print("-" * 40)
                            for row in rows:
                                print([str(item) for item in row])
                    except pyodbc.ProgrammingError:
                        pass

    except pyodbc.Error as ex:
        print(f"A database error occurred:")
        print(ex)
    except Exception as e:
        print(f"An unexpected error occurred: {{e}}")

if __name__ == '__main__':
    execute_sql_script()
