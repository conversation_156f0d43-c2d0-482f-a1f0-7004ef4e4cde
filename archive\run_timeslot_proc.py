

import pyodbc

# --- Details ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
PROC_NAME = 'usp_GetTimeSlotDetails_WithDirectFall'
SHOP_ID = 11
TARGET_DATE = '2025-07-24'
OUTPUT_FILENAME = 'timeslot_details_baseline.txt'

def execute_and_save():
    """Executes the SP and saves the results to a file."""
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
    sql = f"EXEC {{?}} @ShopId=?, @TargetDate=?"
    params = (PROC_NAME, SHOP_ID, TARGET_DATE)
    
    try:
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()
            print(f"Executing {PROC_NAME} for baseline data...")
            cursor.execute(sql, params)
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()
            
            with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
                header = ", ".join(columns)
                f.write(header + '\n')
                print("--- Baseline Data ---")
                print(header)
                print("-" * len(header))
                for row in rows:
                    row_str = ", ".join(map(str, row))
                    f.write(row_str + '\n')
                    print(row_str)
            print(f"\nSuccessfully saved baseline to {OUTPUT_FILENAME}")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    execute_and_save()

