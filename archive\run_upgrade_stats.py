
import pyodbc

# --- 配置 ---
SERVER = '*************'
DATABASE = 'dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox@123'
SQL_FILE_PATH = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\upgrade_stats_system.sql'

def execute_sql_from_file(sql_file):
    connection_string = (
        f'DRIVER={{ODBC Driver 17 for SQL Server}}';
        f'SERVER={SERVER}';
        f'DATABASE={DATABASE}';
        f'UID={USERNAME}';
        f'PWD={PASSWORD}';
        f'TrustServerCertificate=yes;'
    )

    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- Successfully connected to {SERVER} ---')
            commands = sql_script.split('GO\n')
            for command in commands:
                if command.strip():
                    cursor.execute(command)
            print("--- Upgrade script executed successfully. ---")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    execute_sql_from_file(SQL_FILE_PATH)
