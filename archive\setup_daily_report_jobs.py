#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置KTV日报生成定时任务
每天早上12点自动执行 usp_RunUnifiedDailyReportJob_Corrected
"""

import pyodbc

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def create_daily_report_job(connection, shop_id, shop_name):
    """为指定店铺创建日报生成定时作业"""
    print(f"\n🚀 为店铺{shop_id}（{shop_name}）创建定时作业...")
    
    job_name = f"KTV_DailyReport_Shop{shop_id}_{shop_name}"
    
    try:
        cursor = connection.cursor()
        
        # 1. 检查作业是否已存在，如果存在则删除
        check_job_sql = f"""
        IF EXISTS (SELECT job_id FROM msdb.dbo.sysjobs WHERE name = N'{job_name}')
        BEGIN
            EXEC msdb.dbo.sp_delete_job @job_name = N'{job_name}', @delete_unused_schedule = 1
            PRINT '已删除现有作业: {job_name}'
        END
        """
        
        cursor.execute(check_job_sql)
        connection.commit()
        
        # 2. 创建新作业
        create_job_sql = f"""
        EXEC dbo.sp_add_job
            @job_name = N'{job_name}',
            @enabled = 1,
            @description = N'每天自动生成店铺{shop_id}（{shop_name}）的日报数据',
            @category_name = N'[Uncategorized (Local)]',
            @owner_login_name = N'sa'
        """
        
        cursor.execute(create_job_sql)
        connection.commit()
        print(f"✅ 作业 '{job_name}' 创建成功")
        
        # 3. 添加作业步骤
        add_step_sql = f"""
        EXEC dbo.sp_add_jobstep
            @job_name = N'{job_name}',
            @step_name = N'执行日报生成',
            @subsystem = N'TSQL',
            @command = N'EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected @ShopId = {shop_id}',
            @database_name = N'operatedata',
            @on_success_action = 1,
            @on_fail_action = 2,
            @retry_attempts = 3,
            @retry_interval = 5
        """
        
        cursor.execute(add_step_sql)
        connection.commit()
        print(f"✅ 作业步骤添加成功")
        
        # 4. 创建调度计划（每天早上12点）
        schedule_name = f"Daily_12AM_Schedule_Shop{shop_id}"
        
        add_schedule_sql = f"""
        EXEC dbo.sp_add_schedule
            @schedule_name = N'{schedule_name}',
            @enabled = 1,
            @freq_type = 4,
            @freq_interval = 1,
            @freq_subday_type = 1,
            @freq_subday_interval = 0,
            @freq_relative_interval = 0,
            @freq_recurrence_factor = 1,
            @active_start_date = 20250730,
            @active_end_date = 99991231,
            @active_start_time = 120000,
            @active_end_time = 235959
        """
        
        cursor.execute(add_schedule_sql)
        connection.commit()
        print(f"✅ 调度计划 '{schedule_name}' 创建成功")
        
        # 5. 将调度计划附加到作业
        attach_schedule_sql = f"""
        EXEC dbo.sp_attach_schedule
            @job_name = N'{job_name}',
            @schedule_name = N'{schedule_name}'
        """
        
        cursor.execute(attach_schedule_sql)
        connection.commit()
        print(f"✅ 调度计划已附加到作业")
        
        # 6. 将作业添加到目标服务器
        add_jobserver_sql = f"""
        EXEC dbo.sp_add_jobserver
            @job_name = N'{job_name}',
            @server_name = N'(local)'
        """
        
        cursor.execute(add_jobserver_sql)
        connection.commit()
        print(f"✅ 作业已添加到目标服务器")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建店铺{shop_id}定时作业失败: {str(e)}")
        return False

def verify_jobs(connection):
    """验证创建的定时作业"""
    print(f"\n🔍 验证创建的定时作业...")
    
    try:
        cursor = connection.cursor()
        
        # 查询创建的作业
        query_jobs_sql = """
        SELECT 
            j.name AS JobName,
            j.enabled AS Enabled,
            j.description AS Description,
            s.name AS ScheduleName,
            s.enabled AS ScheduleEnabled,
            CASE s.freq_type
                WHEN 4 THEN '每天'
                WHEN 8 THEN '每周'
                WHEN 16 THEN '每月'
                ELSE '其他'
            END AS Frequency,
            RIGHT('0' + CAST(s.active_start_time / 10000 AS VARCHAR), 2) + ':' +
            RIGHT('0' + CAST((s.active_start_time % 10000) / 100 AS VARCHAR), 2) + ':' +
            RIGHT('0' + CAST(s.active_start_time % 100 AS VARCHAR), 2) AS StartTime
        FROM msdb.dbo.sysjobs j
        LEFT JOIN msdb.dbo.sysjobschedules js ON j.job_id = js.job_id
        LEFT JOIN msdb.dbo.sysschedules s ON js.schedule_id = s.schedule_id
        WHERE j.name LIKE 'KTV_DailyReport_Shop%'
        ORDER BY j.name
        """
        
        cursor.execute(query_jobs_sql)
        rows = cursor.fetchall()
        
        print(f"✅ 定时作业验证完成")
        print(f"📊 找到 {len(rows)} 个KTV日报定时作业:")
        
        if rows:
            print(f"\n📋 作业详情:")
            for row in rows:
                job_name = row[0]
                enabled = "启用" if row[1] else "禁用"
                description = row[2]
                schedule_name = row[3] or "无调度"
                schedule_enabled = "启用" if row[4] else "禁用" if row[4] is not None else "N/A"
                frequency = row[5] or "N/A"
                start_time = row[6] or "N/A"
                
                print(f"   📦 作业名: {job_name}")
                print(f"      状态: {enabled}")
                print(f"      描述: {description}")
                print(f"      调度: {schedule_name} ({schedule_enabled})")
                print(f"      频率: {frequency}")
                print(f"      执行时间: {start_time}")
                print(f"      ─────────────────────")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证定时作业失败: {str(e)}")
        return False

def test_job_execution(connection, shop_id, shop_name):
    """测试作业执行"""
    print(f"\n🧪 测试店铺{shop_id}（{shop_name}）的作业执行...")
    
    job_name = f"KTV_DailyReport_Shop{shop_id}_{shop_name}"
    
    try:
        cursor = connection.cursor()
        
        # 手动执行作业进行测试
        test_job_sql = f"""
        EXEC dbo.sp_start_job @job_name = N'{job_name}'
        """
        
        cursor.execute(test_job_sql)
        connection.commit()
        print(f"✅ 作业 '{job_name}' 已启动执行")
        
        # 等待几秒后检查执行状态
        import time
        time.sleep(5)
        
        # 查询作业执行历史
        check_history_sql = f"""
        SELECT TOP 1
            jh.run_date,
            jh.run_time,
            jh.run_status,
            CASE jh.run_status
                WHEN 0 THEN '失败'
                WHEN 1 THEN '成功'
                WHEN 2 THEN '重试'
                WHEN 3 THEN '取消'
                WHEN 4 THEN '进行中'
                ELSE '未知'
            END AS StatusText,
            jh.message
        FROM msdb.dbo.sysjobhistory jh
        JOIN msdb.dbo.sysjobs j ON jh.job_id = j.job_id
        WHERE j.name = N'{job_name}'
        ORDER BY jh.run_date DESC, jh.run_time DESC
        """
        
        cursor.execute(check_history_sql)
        history_row = cursor.fetchone()
        
        if history_row:
            run_date = history_row[0]
            run_time = history_row[1]
            run_status = history_row[2]
            status_text = history_row[3]
            message = history_row[4] or "无消息"
            
            print(f"📊 最近执行记录:")
            print(f"   日期: {run_date}")
            print(f"   时间: {run_time}")
            print(f"   状态: {status_text}")
            print(f"   消息: {message}")
            
            if run_status == 1:
                print(f"✅ 作业执行成功！")
                return True
            else:
                print(f"⚠️ 作业执行状态: {status_text}")
                return False
        else:
            print(f"📝 暂无执行历史记录")
            return True
        
    except Exception as e:
        print(f"❌ 测试作业执行失败: {str(e)}")
        return False

def main():
    print("🚀 开始设置KTV日报生成定时任务...")
    
    # 店铺信息
    shops = [
        (3, "天河店"),
        (11, "kboss名堂")
    ]
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        success_count = 0
        
        # 为每个店铺创建定时作业
        for shop_id, shop_name in shops:
            success = create_daily_report_job(connection, shop_id, shop_name)
            if success:
                success_count += 1
        
        if success_count > 0:
            # 验证创建的作业
            verify_success = verify_jobs(connection)
            
            if verify_success:
                print(f"\n🎉 定时作业设置完成！")
                print(f"\n📋 设置总结:")
                print(f"   ✅ 成功创建 {success_count} 个定时作业")
                print(f"   ✅ 执行时间: 每天早上 12:00:00")
                print(f"   ✅ 执行内容: usp_RunUnifiedDailyReportJob_Corrected")
                print(f"   ✅ 重试机制: 失败时重试3次，间隔5分钟")
                
                print(f"\n📋 涉及店铺:")
                for shop_id, shop_name in shops:
                    print(f"   🏪 店铺{shop_id}: {shop_name}")
                
                print(f"\n📋 管理命令:")
                print(f"   查看作业状态: SELECT * FROM msdb.dbo.sysjobs WHERE name LIKE 'KTV_DailyReport_Shop%'")
                print(f"   查看执行历史: SELECT * FROM msdb.dbo.sysjobhistory WHERE job_id IN (SELECT job_id FROM msdb.dbo.sysjobs WHERE name LIKE 'KTV_DailyReport_Shop%')")
                print(f"   手动执行作业: EXEC dbo.sp_start_job @job_name = N'KTV_DailyReport_Shop3_天河店'")
                
                # 可选：测试作业执行
                print(f"\n🧪 是否要测试作业执行？（这将立即执行一次作业）")
                print(f"   注意：测试执行会生成实际的报表数据")
            else:
                print(f"\n❌ 作业验证失败")
        else:
            print(f"\n❌ 没有成功创建任何定时作业")
    
    except Exception as e:
        print(f"❌ 设置定时任务过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print(f"\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
