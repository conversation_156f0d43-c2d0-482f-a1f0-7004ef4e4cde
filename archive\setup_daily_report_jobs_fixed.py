#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置KTV日报生成定时任务（修复版）
每天早上12点自动执行 usp_RunUnifiedDailyReportJob_Corrected
"""

import pyodbc

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=msdb;"  # 使用msdb数据库来管理作业
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功（msdb）")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def check_sql_agent_service(connection):
    """检查SQL Server Agent服务状态"""
    print(f"\n🔍 检查SQL Server Agent服务状态...")
    
    try:
        cursor = connection.cursor()
        
        # 检查SQL Server Agent是否运行
        check_agent_sql = """
        SELECT 
            servicename,
            status_desc,
            startup_type_desc
        FROM sys.dm_server_services 
        WHERE servicename LIKE '%Agent%'
        """
        
        cursor.execute(check_agent_sql)
        rows = cursor.fetchall()
        
        if rows:
            for row in rows:
                service_name = row[0]
                status = row[1]
                startup_type = row[2]
                print(f"   📦 服务: {service_name}")
                print(f"      状态: {status}")
                print(f"      启动类型: {startup_type}")
                
                if "Running" in status:
                    print(f"   ✅ SQL Server Agent 服务正在运行")
                    return True
                else:
                    print(f"   ⚠️ SQL Server Agent 服务未运行")
                    return False
        else:
            print(f"   ❌ 未找到SQL Server Agent服务信息")
            return False
        
    except Exception as e:
        print(f"❌ 检查SQL Server Agent服务失败: {str(e)}")
        return False

def create_daily_report_job_fixed(connection, shop_id, shop_name):
    """为指定店铺创建日报生成定时作业（修复版）"""
    print(f"\n🚀 为店铺{shop_id}（{shop_name}）创建定时作业...")
    
    job_name = f"KTV_DailyReport_Shop{shop_id}_{shop_name}"
    
    try:
        cursor = connection.cursor()
        
        # 1. 检查作业是否已存在，如果存在则删除
        check_job_sql = f"""
        IF EXISTS (SELECT job_id FROM msdb.dbo.sysjobs WHERE name = N'{job_name}')
        BEGIN
            EXEC msdb.dbo.sp_delete_job @job_name = N'{job_name}', @delete_unused_schedule = 1
            PRINT '已删除现有作业: {job_name}'
        END
        """
        
        cursor.execute(check_job_sql)
        connection.commit()
        
        # 2. 创建新作业
        create_job_sql = f"""
        EXEC msdb.dbo.sp_add_job
            @job_name = N'{job_name}',
            @enabled = 1,
            @description = N'每天自动生成店铺{shop_id}（{shop_name}）的日报数据',
            @category_name = N'[Uncategorized (Local)]',
            @owner_login_name = N'sa'
        """
        
        cursor.execute(create_job_sql)
        connection.commit()
        print(f"✅ 作业 '{job_name}' 创建成功")
        
        # 3. 添加作业步骤
        add_step_sql = f"""
        EXEC msdb.dbo.sp_add_jobstep
            @job_name = N'{job_name}',
            @step_name = N'执行日报生成',
            @subsystem = N'TSQL',
            @command = N'EXEC operatedata.dbo.usp_RunUnifiedDailyReportJob_Corrected @ShopId = {shop_id}',
            @database_name = N'operatedata',
            @on_success_action = 1,
            @on_fail_action = 2,
            @retry_attempts = 3,
            @retry_interval = 5
        """
        
        cursor.execute(add_step_sql)
        connection.commit()
        print(f"✅ 作业步骤添加成功")
        
        # 4. 创建调度计划（每天早上12点）
        schedule_name = f"Daily_12AM_Schedule_Shop{shop_id}"
        
        add_schedule_sql = f"""
        EXEC msdb.dbo.sp_add_schedule
            @schedule_name = N'{schedule_name}',
            @enabled = 1,
            @freq_type = 4,
            @freq_interval = 1,
            @freq_subday_type = 1,
            @freq_subday_interval = 0,
            @freq_relative_interval = 0,
            @freq_recurrence_factor = 1,
            @active_start_date = 20250730,
            @active_end_date = 99991231,
            @active_start_time = 120000,
            @active_end_time = 235959
        """
        
        cursor.execute(add_schedule_sql)
        connection.commit()
        print(f"✅ 调度计划 '{schedule_name}' 创建成功")
        
        # 5. 将调度计划附加到作业
        attach_schedule_sql = f"""
        EXEC msdb.dbo.sp_attach_schedule
            @job_name = N'{job_name}',
            @schedule_name = N'{schedule_name}'
        """
        
        cursor.execute(attach_schedule_sql)
        connection.commit()
        print(f"✅ 调度计划已附加到作业")
        
        # 6. 将作业添加到目标服务器
        add_jobserver_sql = f"""
        EXEC msdb.dbo.sp_add_jobserver
            @job_name = N'{job_name}',
            @server_name = N'(local)'
        """
        
        cursor.execute(add_jobserver_sql)
        connection.commit()
        print(f"✅ 作业已添加到目标服务器")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建店铺{shop_id}定时作业失败: {str(e)}")
        return False

def verify_jobs_fixed(connection):
    """验证创建的定时作业（修复版）"""
    print(f"\n🔍 验证创建的定时作业...")
    
    try:
        cursor = connection.cursor()
        
        # 查询创建的作业
        query_jobs_sql = """
        SELECT 
            j.name AS JobName,
            j.enabled AS Enabled,
            j.description AS Description,
            s.name AS ScheduleName,
            s.enabled AS ScheduleEnabled,
            CASE s.freq_type
                WHEN 4 THEN '每天'
                WHEN 8 THEN '每周'
                WHEN 16 THEN '每月'
                ELSE '其他'
            END AS Frequency,
            RIGHT('0' + CAST(s.active_start_time / 10000 AS VARCHAR), 2) + ':' +
            RIGHT('0' + CAST((s.active_start_time % 10000) / 100 AS VARCHAR), 2) + ':' +
            RIGHT('0' + CAST(s.active_start_time % 100 AS VARCHAR), 2) AS StartTime
        FROM msdb.dbo.sysjobs j
        LEFT JOIN msdb.dbo.sysjobschedules js ON j.job_id = js.job_id
        LEFT JOIN msdb.dbo.sysschedules s ON js.schedule_id = s.schedule_id
        WHERE j.name LIKE 'KTV_DailyReport_Shop%'
        ORDER BY j.name
        """
        
        cursor.execute(query_jobs_sql)
        rows = cursor.fetchall()
        
        print(f"✅ 定时作业验证完成")
        print(f"📊 找到 {len(rows)} 个KTV日报定时作业:")
        
        if rows:
            print(f"\n📋 作业详情:")
            for row in rows:
                job_name = row[0]
                enabled = "启用" if row[1] else "禁用"
                description = row[2]
                schedule_name = row[3] or "无调度"
                schedule_enabled = "启用" if row[4] else "禁用" if row[4] is not None else "N/A"
                frequency = row[5] or "N/A"
                start_time = row[6] or "N/A"
                
                print(f"   📦 作业名: {job_name}")
                print(f"      状态: {enabled}")
                print(f"      描述: {description}")
                print(f"      调度: {schedule_name} ({schedule_enabled})")
                print(f"      频率: {frequency}")
                print(f"      执行时间: {start_time}")
                print(f"      ─────────────────────")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证定时作业失败: {str(e)}")
        return False

def create_alternative_solution(connection):
    """创建替代方案：使用Windows任务计划程序"""
    print(f"\n💡 创建替代方案：Windows任务计划程序脚本...")
    
    # 店铺信息
    shops = [
        (3, "天河店"),
        (11, "kboss名堂")
    ]
    
    try:
        # 创建批处理脚本
        for shop_id, shop_name in shops:
            bat_filename = f"KTV_DailyReport_Shop{shop_id}_{shop_name}.bat"
            
            bat_content = f"""@echo off
REM KTV日报生成批处理脚本 - 店铺{shop_id}（{shop_name}）
REM 执行时间：每天早上12:00

echo %date% %time% - 开始执行店铺{shop_id}日报生成... >> C:\\KTV_Logs\\DailyReport_Shop{shop_id}.log

sqlcmd -S 192.168.2.5 -U sa -P Musicbox123 -d operatedata -Q "EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected @ShopId = {shop_id}" >> C:\\KTV_Logs\\DailyReport_Shop{shop_id}.log 2>&1

if %ERRORLEVEL% EQU 0 (
    echo %date% %time% - 店铺{shop_id}日报生成成功 >> C:\\KTV_Logs\\DailyReport_Shop{shop_id}.log
) else (
    echo %date% %time% - 店铺{shop_id}日报生成失败，错误代码：%ERRORLEVEL% >> C:\\KTV_Logs\\DailyReport_Shop{shop_id}.log
)

echo %date% %time% - 店铺{shop_id}日报生成完成 >> C:\\KTV_Logs\\DailyReport_Shop{shop_id}.log
echo. >> C:\\KTV_Logs\\DailyReport_Shop{shop_id}.log
"""
            
            with open(bat_filename, 'w', encoding='gbk') as f:
                f.write(bat_content)
            
            print(f"✅ 批处理脚本已创建: {bat_filename}")
        
        # 创建PowerShell脚本来设置Windows任务计划
        ps_script_content = """# KTV日报定时任务设置脚本

# 创建日志目录
$logDir = "C:\\KTV_Logs"
if (!(Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force
    Write-Host "✅ 日志目录已创建: $logDir"
}

# 店铺信息
$shops = @(
    @{Id=3; Name="天河店"},
    @{Id=11; Name="kboss名堂"}
)

foreach ($shop in $shops) {
    $taskName = "KTV_DailyReport_Shop$($shop.Id)_$($shop.Name)"
    $batFile = "C:\\Users\\<USER>\\CascadeProjects\\KTV_Data_Analysis\\KTV_DailyReport_Shop$($shop.Id)_$($shop.Name).bat"
    
    # 删除现有任务（如果存在）
    $existingTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
    if ($existingTask) {
        Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
        Write-Host "✅ 已删除现有任务: $taskName"
    }
    
    # 创建新任务
    $action = New-ScheduledTaskAction -Execute $batFile
    $trigger = New-ScheduledTaskTrigger -Daily -At "12:00AM"
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
    $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
    
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description "每天自动生成店铺$($shop.Id)（$($shop.Name)）的日报数据"
    
    Write-Host "✅ 任务已创建: $taskName"
    Write-Host "   执行时间: 每天早上12:00"
    Write-Host "   批处理文件: $batFile"
    Write-Host "   日志文件: C:\\KTV_Logs\\DailyReport_Shop$($shop.Id).log"
    Write-Host "   ─────────────────────"
}

Write-Host ""
Write-Host "🎉 所有定时任务设置完成！"
Write-Host ""
Write-Host "📋 管理命令:"
Write-Host "   查看任务: Get-ScheduledTask | Where-Object {$_.TaskName -like 'KTV_DailyReport_Shop*'}"
Write-Host "   手动执行: Start-ScheduledTask -TaskName 'KTV_DailyReport_Shop3_天河店'"
Write-Host "   删除任务: Unregister-ScheduledTask -TaskName 'KTV_DailyReport_Shop3_天河店' -Confirm:$false"
Write-Host "   查看日志: Get-Content C:\\KTV_Logs\\DailyReport_Shop3.log -Tail 20"
"""
        
        with open("Setup_Windows_Tasks.ps1", 'w', encoding='utf-8') as f:
            f.write(ps_script_content)
        
        print(f"✅ PowerShell设置脚本已创建: Setup_Windows_Tasks.ps1")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建替代方案失败: {str(e)}")
        return False

def main():
    print("🚀 开始设置KTV日报生成定时任务...")
    
    # 店铺信息
    shops = [
        (3, "天河店"),
        (11, "kboss名堂")
    ]
    
    connection = connect_database()
    if not connection:
        print("\n💡 由于无法连接到msdb数据库，将创建替代方案...")
        create_alternative_solution(None)
        return
    
    try:
        # 检查SQL Server Agent服务
        agent_running = check_sql_agent_service(connection)
        
        if not agent_running:
            print(f"\n⚠️ SQL Server Agent服务未运行，无法创建SQL Server作业")
            print(f"💡 将创建Windows任务计划程序替代方案...")
            create_alternative_solution(connection)
            return
        
        success_count = 0
        
        # 为每个店铺创建定时作业
        for shop_id, shop_name in shops:
            success = create_daily_report_job_fixed(connection, shop_id, shop_name)
            if success:
                success_count += 1
        
        if success_count > 0:
            # 验证创建的作业
            verify_success = verify_jobs_fixed(connection)
            
            if verify_success:
                print(f"\n🎉 SQL Server定时作业设置完成！")
                print(f"\n📋 设置总结:")
                print(f"   ✅ 成功创建 {success_count} 个定时作业")
                print(f"   ✅ 执行时间: 每天早上 12:00:00")
                print(f"   ✅ 执行内容: usp_RunUnifiedDailyReportJob_Corrected")
                print(f"   ✅ 重试机制: 失败时重试3次，间隔5分钟")
                
                print(f"\n📋 涉及店铺:")
                for shop_id, shop_name in shops:
                    print(f"   🏪 店铺{shop_id}: {shop_name}")
                
                print(f"\n📋 管理命令:")
                print(f"   查看作业状态: SELECT * FROM msdb.dbo.sysjobs WHERE name LIKE 'KTV_DailyReport_Shop%'")
                print(f"   查看执行历史: SELECT * FROM msdb.dbo.sysjobhistory WHERE job_id IN (SELECT job_id FROM msdb.dbo.sysjobs WHERE name LIKE 'KTV_DailyReport_Shop%')")
                print(f"   手动执行作业: EXEC msdb.dbo.sp_start_job @job_name = N'KTV_DailyReport_Shop3_天河店'")
            else:
                print(f"\n❌ 作业验证失败")
        else:
            print(f"\n❌ 没有成功创建任何定时作业")
            print(f"💡 将创建Windows任务计划程序替代方案...")
            create_alternative_solution(connection)
    
    except Exception as e:
        print(f"❌ 设置定时任务过程中发生错误: {str(e)}")
        print(f"💡 将创建Windows任务计划程序替代方案...")
        create_alternative_solution(connection)
    
    finally:
        if connection:
            connection.close()
            print(f"\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
