USE operatedata;
GO
IF OBJECT_ID('usp_Sync_RMS_DailyOpenData', 'P') IS NOT NULL DROP PROCEDURE usp_Sync_RMS_DailyOpenData;
GO
CREATE PROCEDURE usp_Sync_RMS_DailyOpenData
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @TargetBusinessDate DATE = CAST(DATEADD(hour, -9, GETDATE() - 1) AS DATE);
    DECLARE @StartDate DATETIME = DATEADD(hour, 9, CAST(@TargetBusinessDate AS DATETIME));
    DECLARE @EndDate DATETIME = DATEADD(hour, 9, CAST(DATEADD(day, 1, @TargetBusinessDate) AS DATETIME));
    BEGIN TRY
        MERGE INTO rms2019.dbo.opencacheinfo AS T
        USING (SELECT * FROM cloudRms2019.rms2019.dbo.opencacheinfo WHERE BookDateTime >= @StartDate AND BookDateTime < @EndDate) AS S
        ON T.Ikey = S.Ikey
        WHEN MATCHED THEN UPDATE SET T.CheckinStatus=S.CheckinStatus, T.Invno=S.Invno, T.RmNo=S.RmNo, T.WorkDate=@TargetBusinessDate
        WHEN NOT MATCHED BY TARGET THEN INSERT (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark,WorkDate) VALUES(S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark,@TargetBusinessDate);
        MERGE INTO rms2019.dbo.openhistory AS T
        USING (SELECT * FROM cloudRms2019.rms2019.dbo.openhistory WHERE BookDateTime >= @StartDate AND BookDateTime < @EndDate) AS S
        ON T.Ikey = S.Ikey
        WHEN NOT MATCHED BY TARGET THEN INSERT (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark,WorkDate) VALUES(S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark,@TargetBusinessDate);
    END TRY
    BEGIN CATCH
        PRINT 'Error in usp_Sync_RMS_DailyOpenData: ' + ERROR_MESSAGE();
    END CATCH
END
