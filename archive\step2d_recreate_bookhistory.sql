USE rms2019;
GO

-- 1. Rename the existing, incomplete table as a backup
IF OBJECT_ID('dbo.bookhistory', 'U') IS NOT NULL
BEGIN
    -- Drop the backup if it already exists to allow re-running the script
    IF OBJECT_ID('dbo.bookhistory_backup', 'U') IS NOT NULL
        DROP TABLE dbo.bookhistory_backup;
    EXEC sp_rename 'dbo.bookhistory', 'bookhistory_backup';
END
GO

-- 2. Create a new, structurally correct table from the source
SELECT * 
INTO dbo.bookhistory
FROM cloudRms2019.rms2019.dbo.bookhistory;
GO