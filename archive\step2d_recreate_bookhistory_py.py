import pyodbc

# --- 配置 ---
SERVER = '192.168.2.5'
DATABASE = 'rms2019'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- SQL 命令 ---
RENAME_SQL = """
IF OBJECT_ID('dbo.bookhistory', 'U') IS NOT NULL
BEGIN
    -- 如果备份已存在，则先删除，以便脚本可以重复执行
    IF OBJECT_ID('dbo.bookhistory_backup', 'U') IS NOT NULL
        DROP TABLE dbo.bookhistory_backup;
    EXEC sp_rename 'dbo.bookhistory', 'bookhistory_backup';
END
"

CREATE_TABLE_SQL = """
-- 从源头完整复制表结构和数据
SELECT * 
INTO dbo.bookhistory
FROM cloudRms2019.rms2019.dbo.bookhistory;
"""

def recreate_bookhistory_table():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    try:
        # autocommit=True 确保每个 execute 都是一个独立的事务
        with pyodbc.connect(connection_string, autocommit=True, timeout=60) as conn:
            cursor = conn.cursor()
            print(f'--- 成功连接到 {SERVER}/{DATABASE} ---')
            
            print("--- 步骤 1: 备份现有的 bookhistory 表 (如果存在)... ---")
            cursor.execute(RENAME_SQL)
            print("--- 步骤 1 完成。 ---")

            print("--- 步骤 2: 从源服务器创建新的 bookhistory 表... ---")
            cursor.execute(CREATE_TABLE_SQL)
            print("--- 步骤 2 完成。 ---")
            
            print("\n成功重建 bookhistory 表。")

    except Exception as e:
        print(f"执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    recreate_bookhistory_table()
