USE rms2019;
GO

-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_Sync_RMS_DailyBookData', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_Sync_RMS_DailyBookData;
GO

-- Create the procedure
CREATE PROCEDURE dbo.usp_Sync_RMS_DailyBookData
AS
BEGIN
    SET NOCOUNT ON;

    -- 同步自然日（昨天）的预订数据
    DECLARE @TargetNaturalDate DATE = GETDATE() - 1;

    BEGIN TRY
        -- Merge bookcacheinfo
        MERGE INTO dbo.bookcacheinfo AS T
        USING (SELECT * FROM cloudRms2019.rms2019.dbo.bookcacheinfo WHERE CAST(BookDateTime AS DATE) = @TargetNaturalDate) AS S
        ON T.Ikey = S.Ikey
        WHEN MATCHED THEN 
            UPDATE SET T.BookStatus = S.BookStatus,
                       T.CheckinStatus = S.CheckinStatus,
                       T.Invno = S.Invno,
                       T.RmNo = S.RmNo
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (<PERSON><PERSON>, <PERSON><PERSON>o, <PERSON><PERSON>d, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ComeDate, ComeTime, Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, BookUserName, BookDateTime, Invno, Openmemory, OrderUserID, OrderUserName, RmNo, Val1, FromRmNo, IsBirthday, Remark)
            VALUES (S.Ikey, S.BookNo, S.ShopId, S.CustKey, S.CustName, S.CustTel, S.ComeDate, S.ComeTime, S.Beg_Key, S.Beg_Name, S.End_Key, S.End_Name, S.Numbers, S.RtNo, S.RtName, S.CtNo, S.CtName, S.PtNo, S.PtName, S.BookMemory, S.BookStatus, S.CheckinStatus, S.BookShopId, S.BookUserId, S.BookUserName, S.BookDateTime, S.Invno, S.Openmemory, S.OrderUserID, S.OrderUserName, S.RmNo, S.Val1, S.FromRmNo, S.IsBirthday, S.Remark);

        -- Merge bookhistory
        MERGE INTO dbo.bookhistory AS T
        USING (SELECT * FROM cloudRms2019.rms2019.dbo.bookhistory WHERE CAST(BookDateTime AS DATE) = @TargetNaturalDate) AS S
        ON T.Ikey = S.Ikey
        WHEN NOT MATCHED BY TARGET THEN
            INSERT (Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime, Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName, PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId, BookUserName, BookDateTime, Invno, Openmemory, OrderUserID, OrderUserName, RmNo, Val1, FromRmNo, IsBirthday, Remark)
            VALUES (S.Ikey, S.BookNo, S.ShopId, S.CustKey, S.CustName, S.CustTel, S.ComeDate, S.ComeTime, S.Beg_Key, S.Beg_Name, S.End_Key, S.End_Name, S.Numbers, S.RtNo, S.RtName, S.CtNo, S.CtName, S.PtNo, S.PtName, S.BookMemory, S.BookStatus, S.CheckinStatus, S.BookShopId, S.BookUserId, S.BookUserName, S.BookDateTime, S.Invno, S.Openmemory, S.OrderUserID, S.OrderUserName, S.RmNo, S.Val1, S.FromRmNo, S.IsBirthday, S.Remark);

    END TRY
    BEGIN CATCH
        -- In a real-world scenario, you would log this error to a table.
        PRINT 'Error in usp_Sync_RMS_DailyBookData: ' + ERROR_MESSAGE();
        -- Re-throw the error to be caught by the calling process (like a SQL Agent Job)
        THROW;
    END CATCH
END
GO

PRINT 'Stored procedure dbo.usp_Sync_RMS_DailyBookData has been created successfully.';
GO
