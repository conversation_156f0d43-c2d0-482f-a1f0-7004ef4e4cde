USE rms2019;
GO

PRINT '--- 正在获取本地 bookcacheinfo 表的详细列信息 ---';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH
FROM 
    INFORMATION_SCHEMA.COLUMNS
WHERE 
    TABLE_NAME = 'bookcacheinfo' AND TABLE_SCHEMA = 'dbo';
GO

PRINT '--- 正在获取远程 bookcacheinfo 表的详细列信息 ---';
EXEC sp_columns_ex @table_server = 'cloudRms2019', @table_name = 'bookcacheinfo', @table_schema = 'dbo', @table_catalog = 'rms2019';
GO

PRINT '--- 正在获取本地 bookhistory 表的详细列信息 ---';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH
FROM 
    INFORMATION_SCHEMA.COLUMNS
WHERE 
    TABLE_NAME = 'bookhistory' AND TABLE_SCHEMA = 'dbo';
GO

PRINT '--- 正在获取远程 bookhistory 表的详细列信息 ---';
EXEC sp_columns_ex @table_server = 'cloudRms2019', @table_name = 'bookhistory', @table_schema = 'dbo', @table_catalog = 'rms2019';
GO
