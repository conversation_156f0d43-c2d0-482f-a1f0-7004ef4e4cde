

import pyodbc
import pandas as pd
import sys
from datetime import datetime

# --- 连接信息 ---
DEST_SERVER = '192.168.2.5'
DEST_DB = 'Dbfood'
DEST_USER = 'sa'
DEST_PASS = 'Musicbox123'

SOURCE_SERVER = '193.112.2.229'
SOURCE_DB = 'Dbfood'
SOURCE_USER = 'sa'
SOURCE_PASS = 'Musicbox@123'

# --- 表名 (V4: 增加了FdType并调整了顺序) ---
TABLES_TO_SYNC = ['FdType', 'food', 'foodlabel']

def run_data_synchronization_v4():
    """V4版本：增加了对FdType表的同步，并保证了正确的处理顺序。"""
    # --- 步骤 1: 备份 ---
    print("--- 步骤 1: 备份目标表 ---")
    try:
        with pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DEST_SERVER};DATABASE={DEST_DB};UID={DEST_USER};PWD=***********') as cnxn:
            cursor = cnxn.cursor()
            today_str = datetime.now().strftime('%Y%m%d')
            for table in TABLES_TO_SYNC:
                backup_table_name = f"{table}_bak_{today_str}"
                if cursor.execute(f"IF OBJECT_ID('{backup_table_name}', 'U') IS NULL SELECT 0 ELSE SELECT 1").fetchone()[0] == 0:
                    cursor.execute(f"SELECT * INTO {backup_table_name} FROM {table};")
                    cnxn.commit()
                    print(f"备份表 {backup_table_name} 创建成功！")
                else:
                    print(f"今日备份 {backup_table_name} 已存在，跳过。")
    except Exception as e:
        print(f"\n--- 备份阶段出错！程序终止。 ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    # --- 步骤 2: 提取 ---
    print("\n--- 步骤 2: 从源数据库提取数据 ---")
    source_data = {}
    try:
        with pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SOURCE_SERVER};DATABASE={SOURCE_DB};UID={SOURCE_USER};PWD={SOURCE_PASS}') as cnxn:
            for table in TABLES_TO_SYNC:
                print(f"正在提取 {table} 表...")
                source_data[table] = pd.read_sql(f"SELECT * FROM {table}", cnxn)
                print(f"提取了 {len(source_data[table])} 行数据。")
    except Exception as e:
        print(f"\n--- 提取数据阶段出错！程序终止。 ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    # --- 步骤 3: 覆盖数据 (V4 - 最终版) ---
    print("\n--- 步骤 3: 覆盖目标数据库 ---")
    try:
        with pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DEST_SERVER};DATABASE={DEST_DB};UID={DEST_USER};PWD=***********', autocommit=True) as cnxn:
            cursor = cnxn.cursor()
            # 统一禁用所有外键
            print("正在禁用所有相关外键约束...")
            fk_disable_sqls = cursor.execute("SELECT 'ALTER TABLE ' + OBJECT_NAME(parent_object_id) + ' NOCHECK CONSTRAINT ' + name FROM sys.foreign_keys").fetchall()
            for sql in fk_disable_sqls:
                cursor.execute(sql[0])

            # 按逆序清空表
            for table in reversed(TABLES_TO_SYNC):
                print(f"正在清空目标表 {table}...")
                cursor.execute(f"DELETE FROM {table};")

            # 按正确顺序插入数据
            for table in TABLES_TO_SYNC:
                print(f"正在向 {table} 插入 {len(source_data[table])} 行新数据...")
                df = source_data[table]
                if df.empty: continue
                
                # 检查并设置IDENTITY_INSERT
                has_identity = cursor.execute(f"SELECT 1 FROM sys.identity_columns WHERE OBJECT_ID = OBJECT_ID('dbo.{table}')").fetchone()
                if has_identity:
                    cursor.execute(f"SET IDENTITY_INSERT dbo.{table} ON")
                
                # 批量插入
                cols = ', '.join([f'[{c}]' for c in df.columns])
                vals = ', '.join(['?' for _ in df.columns])
                insert_sql = f"INSERT INTO dbo.{table} ({cols}) VALUES ({vals})"
                # 清理无效的日期时间数据
                for col in df.select_dtypes(include=['datetime64']).columns:
                    df[col] = pd.to_datetime(df[col]).dt.strftime('%Y-%m-%d %H:%M:%S.%f')
                cursor.fast_executemany = True
                cursor.executemany(insert_sql, df.values.tolist())

                if has_identity:
                    cursor.execute(f"SET IDENTITY_INSERT dbo.{table} OFF")
                print(f"{table} 插入完成！")

            # 重新启用所有外键
            print("正在重新启用所有外键约束...")
            for sql in fk_disable_sqls:
                cursor.execute(sql[0].replace('NOCHECK', 'WITH CHECK CHECK'))
            print("数据覆盖和外键恢复完成！")

    except Exception as e:
        print(f"\n--- 覆盖数据阶段出错！程序终止。 ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    # --- 步骤 4: 打标签 ---
    print("\n--- 步骤 4: 重新为新数据打上‘直落’标签 ---")
    try:
        with pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DEST_SERVER};DATABASE={DEST_DB};UID={DEST_USER};PWD=***********', autocommit=True) as cnxn:
            cursor = cnxn.cursor()
            if cursor.execute("IF COL_LENGTH('dbo.food', 'IsDirectFall') IS NULL SELECT 0 ELSE SELECT 1").fetchone()[0] == 0:
                cursor.execute("ALTER TABLE dbo.food ADD IsDirectFall BIT NOT NULL DEFAULT 0;")
            cursor.execute("UPDATE dbo.food SET IsDirectFall = 0;")
            update_sql = """UPDATE f SET f.IsDirectFall = 1 FROM dbo.food f
                           LEFT JOIN dbo.foodlabel fl ON f.FdNo = fl.FdNo
                           WHERE f.FdCName LIKE N'%直落%' OR f.FdCName LIKE N'%跨时段%'
                           OR fl.Type LIKE N'%直落%' OR fl.Category1 LIKE N'%直落%' OR fl.CtTypeName LIKE N'%直落%'
                           OR fl.Type LIKE N'%跨时段%' OR fl.Category1 LIKE N'%跨时段%' OR fl.CtTypeName LIKE N'%跨时段%';"""
            cursor.execute(update_sql)
            updated_rows = cursor.rowcount
            df_verify = pd.read_sql("SELECT COUNT(*) FROM dbo.food WHERE IsDirectFall = 1", cnxn)
            final_count = df_verify.iloc[0,0]
            print(f"\n--- 成功！ ---")
            print(f"数据同步和标签重建已全部完成。最终验证，共有 {final_count} 个商品被标记为‘直落’。")
    except Exception as e:
        print(f"\n--- 打标签阶段出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

if __name__ == '__main__':
    run_data_synchronization_v4()
