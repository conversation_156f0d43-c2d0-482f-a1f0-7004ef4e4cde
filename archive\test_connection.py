

import pyodbc
import sys

# --- 连接信息 ---
SERVER = '192.204.120.139'
DATABASE = 'master' # 默认连接 master 库以测试服务器连通性
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

def test_connection():
    """尝试连接到指定的数据库服务器并报告结果。"""
    cnxn = None
    try:
        # 设置一个10秒的连接超时，避免长时间等待
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};Connection Timeout=10'
        print(f"--- 正在尝试连接到服务器: {SERVER} ---")
        cnxn = pyodbc.connect(conn_str)
        print("\n--- 连接成功！ ---")
        print("数据库服务器响应正常，用户名和密码正确。")

    except pyodbc.Error as ex:
        print("\n--- 连接失败！ ---")
        print(f"数据库返回的错误信息: {ex}")
        sys.exit(1)

    except Exception as e:
        print(f"\n--- 发生未知错误！ ---")
        print(f"错误详情: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    test_connection()

