#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的存储过程
验证DiscountFree字段是否正确添加和计算
"""

import pyodbc
import pandas as pd
from datetime import datetime, date

def connect_database():
    """连接到数据库"""
    try:
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER=192.168.2.5;
        DATABASE=operatedata;
        UID=sa;
        PWD=Musicbox123;
        TrustServerCertificate=yes;
        """
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def test_discount_free_logic(connection, test_date='2025-07-29', shop_id=11):
    """测试DiscountFree字段的逻辑"""
    
    print(f"\n🔍 测试DiscountFree逻辑 - 日期: {test_date}, 店铺: {shop_id}")
    print("-" * 60)
    
    try:
        # 1. 检查原始数据中的AccOkZD字段
        query_raw_data = f"""
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN AccOkZD > 0 THEN 1 END) as accokzd_positive_count,
            SUM(CASE WHEN AccOkZD > 0 THEN AccOkZD ELSE 0 END) as accokzd_total_amount,
            MIN(AccOkZD) as min_accokzd,
            MAX(AccOkZD) as max_accokzd,
            AVG(AccOkZD) as avg_accokzd
        FROM dbo.RmCloseInfo 
        WHERE Shopid = {shop_id} 
        AND CloseDatetime BETWEEN 
            DATEADD(hour, 8, CAST('{test_date}' AS datetime)) 
            AND DATEADD(hour, 6, CAST(DATEADD(day, 1, '{test_date}') AS datetime))
        """
        
        cursor = connection.cursor()
        cursor.execute(query_raw_data)
        raw_data = cursor.fetchone()
        
        print("📊 原始数据分析:")
        print(f"   总记录数: {raw_data[0]}")
        print(f"   AccOkZD > 0 的记录数: {raw_data[1]}")
        print(f"   AccOkZD 总金额: {raw_data[2]:.2f}")
        print(f"   AccOkZD 最小值: {raw_data[3]:.2f}")
        print(f"   AccOkZD 最大值: {raw_data[4]:.2f}")
        print(f"   AccOkZD 平均值: {raw_data[5]:.2f}")
        
        # 2. 测试修正后的存储过程
        print(f"\n🧪 测试修正后的存储过程...")
        
        # 检查存储过程是否存在
        check_proc_query = """
        SELECT COUNT(*) 
        FROM sys.procedures 
        WHERE name = 'usp_GenerateSimplifiedDailyReport_V7_Final_Corrected'
        """
        cursor.execute(check_proc_query)
        proc_exists = cursor.fetchone()[0]
        
        if proc_exists == 0:
            print("⚠️ 修正后的存储过程尚未部署，请先执行SQL文件")
            return False
        
        # 执行修正后的存储过程
        test_proc_query = f"""
        EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Corrected 
            @ShopId = {shop_id}, 
            @BeginDate = '{test_date}', 
            @EndDate = '{test_date}'
        """
        
        cursor.execute(test_proc_query)
        proc_results = cursor.fetchall()
        
        if proc_results:
            # 获取列名
            columns = [column[0] for column in cursor.description]
            result_data = dict(zip(columns, proc_results[0]))
            
            print("✅ 存储过程执行成功!")
            print(f"   DiscountFree_BatchCount: {result_data.get('DiscountFree_BatchCount', 'N/A')}")
            print(f"   DiscountFree_Revenue: {result_data.get('DiscountFree_Revenue', 'N/A')}")
            
            # 验证计算是否正确
            expected_batch_count = raw_data[1]
            expected_revenue = raw_data[2]
            actual_batch_count = result_data.get('DiscountFree_BatchCount', 0)
            actual_revenue = result_data.get('DiscountFree_Revenue', 0)
            
            print(f"\n🔍 验证结果:")
            print(f"   批次数 - 期望: {expected_batch_count}, 实际: {actual_batch_count}, {'✅' if expected_batch_count == actual_batch_count else '❌'}")
            print(f"   收入 - 期望: {expected_revenue:.2f}, 实际: {actual_revenue:.2f}, {'✅' if abs(expected_revenue - actual_revenue) < 0.01 else '❌'}")
            
            # 显示其他重要字段
            print(f"\n📋 其他字段数据:")
            important_fields = [
                'Buyout_BatchCount', 'Buyout_Revenue',
                'Changyin_BatchCount', 'Changyin_Revenue',
                'FreeConsumption_BatchCount',
                'NonPackage_Others'
            ]
            
            for field in important_fields:
                if field in result_data:
                    print(f"   {field}: {result_data[field]}")
            
            return True
        else:
            print("❌ 存储过程执行失败或无数据返回")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False

def test_main_procedure(connection, test_date='2025-07-29', shop_id=11):
    """测试主存储过程"""
    
    print(f"\n🔍 测试主存储过程 - 日期: {test_date}, 店铺: {shop_id}")
    print("-" * 60)
    
    try:
        # 检查主存储过程是否存在
        check_main_proc_query = """
        SELECT COUNT(*) 
        FROM sys.procedures 
        WHERE name = 'usp_RunUnifiedDailyReportJob_Corrected'
        """
        cursor = connection.cursor()
        cursor.execute(check_main_proc_query)
        main_proc_exists = cursor.fetchone()[0]
        
        if main_proc_exists == 0:
            print("⚠️ 修正后的主存储过程尚未部署，请先执行SQL文件")
            return False
        
        print("🧪 执行主存储过程...")
        
        # 执行主存储过程
        main_proc_query = f"""
        EXEC dbo.usp_RunUnifiedDailyReportJob_Corrected 
            @TargetDate = '{test_date}', 
            @ShopId = {shop_id}
        """
        
        cursor.execute(main_proc_query)
        print("✅ 主存储过程执行完成")
        
        # 检查插入的数据
        check_data_query = f"""
        SELECT 
            h.ReportID, h.ReportDate, h.ShopName,
            n.DiscountFree_BatchCount, n.DiscountFree_Revenue,
            n.Buyout_BatchCount, n.Buyout_Revenue,
            n.Changyin_BatchCount, n.Changyin_Revenue
        FROM dbo.FullDailyReport_Header h
        LEFT JOIN dbo.FullDailyReport_NightDetails n ON h.ReportID = n.ReportID
        WHERE h.ReportDate = '{test_date}' AND h.ShopID = {shop_id}
        ORDER BY h.ReportID DESC
        """
        
        cursor.execute(check_data_query)
        inserted_data = cursor.fetchall()
        
        if inserted_data:
            print("📊 插入的数据:")
            for row in inserted_data:
                print(f"   ReportID: {row[0]}")
                print(f"   ReportDate: {row[1]}")
                print(f"   ShopName: {row[2]}")
                print(f"   DiscountFree_BatchCount: {row[3]}")
                print(f"   DiscountFree_Revenue: {row[4]}")
                print(f"   Buyout_BatchCount: {row[5]}")
                print(f"   Buyout_Revenue: {row[6]}")
                print(f"   Changyin_BatchCount: {row[7]}")
                print(f"   Changyin_Revenue: {row[8]}")
                break  # 只显示第一条记录
            
            return True
        else:
            print("❌ 未找到插入的数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试主存储过程时发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试修正后的存储过程...")
    
    # 连接数据库
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 测试DiscountFree逻辑
        test1_result = test_discount_free_logic(connection)
        
        # 测试主存储过程
        test2_result = test_main_procedure(connection)
        
        # 总结
        print(f"\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        print(f"DiscountFree逻辑测试: {'✅ 通过' if test1_result else '❌ 失败'}")
        print(f"主存储过程测试: {'✅ 通过' if test2_result else '❌ 失败'}")
        
        if test1_result and test2_result:
            print("\n🎉 所有测试通过！修正后的存储过程工作正常。")
        else:
            print("\n⚠️ 部分测试失败，请检查存储过程部署情况。")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
