
import pyodbc
import sys

# Database connection parameters
server = '193.112.2.229'
database = 'dbfood'
username = 'sa'
password = 'Musicbox@123'

# Connection string
conn_str = (
    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
    f"SERVER={server};"
    f"DATABASE={database};"
    f"UID={username};"
    f"PWD={password};"
    f"TrustServerCertificate=yes;"
    f"Connection Timeout=10;"
)

try:
    # Establish the connection
    cnxn = pyodbc.connect(conn_str)
    print("数据库连接成功！")
    # Close the connection
    cnxn.close()
except pyodbc.Error as ex:
    print(f"数据库连接失败。")
    # The ex object contains detailed error information
    print(f"错误详情: {ex}")
except Exception as e:
    print(f"发生了未知错误: {e}")
