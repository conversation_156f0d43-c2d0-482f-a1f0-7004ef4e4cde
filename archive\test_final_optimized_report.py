#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终优化版联合报表
"""

import pyodbc
import csv

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def test_optimized_night_procedure(connection):
    """测试优化版夜间详情存储过程"""
    print("\n🧪 测试优化版夜间详情存储过程...")
    
    try:
        cursor = connection.cursor()
        
        # 测试优化版存储过程
        test_query = """
        EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized_Final 
            @ShopId = 11,
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24'
        """
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        rows = cursor.fetchall()
        
        print(f"✅ 优化版夜间详情存储过程执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        print(f"📊 返回行数: {len(rows)}")
        
        if rows:
            row = rows[0]
            print(f"\n📋 2025-07-24 店铺11 优化后的夜间详情:")
            
            # 显示关键优化字段
            key_fields = [
                ('ReportDate', '日期'),
                ('NightTimeBatchCount', '晚上档批次'),
                ('FreeMeal_BatchCount', '自由餐批次'),
                ('NonPackage_RoomFee', '房费批次(新增)'),
                ('NonPackage_YearCard', '年卡批次(新增)'),
                ('Night_Verify_BatchCount', '净值批次(优化)'),
                ('Night_Verify_Revenue', '净值收入(优化)'),
                ('DiscountFree_BatchCount', '招待批次'),
                ('DiscountFree_Revenue', '招待金额')
            ]
            
            for field_name, display_name in key_fields:
                if field_name in columns:
                    col_index = columns.index(field_name)
                    value = row[col_index] if col_index < len(row) else "N/A"
                    print(f"   {display_name}: {value}")
            
            # 保存结果到文件
            print(f"\n💾 保存优化版夜间详情结果...")
            filename = f"最终优化版夜间详情_店铺11_20250724.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                # 写入列头
                writer.writerow(columns)
                # 写入数据
                for row in rows:
                    writer.writerow(row)
            
            print(f"✅ 优化版夜间详情结果已保存到: {filename}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ 测试优化版夜间详情存储过程失败: {str(e)}")
        return False

def create_final_unified_report_procedure(connection):
    """创建最终版联合报表存储过程"""
    print("\n🚀 创建最终版联合报表存储过程...")
    
    # 删除旧存储过程
    drop_sql = "IF OBJECT_ID('dbo.usp_GenerateFinalUnifiedDailyReport', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateFinalUnifiedDailyReport"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_GenerateFinalUnifiedDailyReport
    @BeginDate DATE,
    @EndDate DATE,
    @ShopId INT = 11
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX) = N'';
    DECLARE @pivot_columns NVARCHAR(MAX) = N'';

    -- 1. 动态获取该店铺的白天档时段
    SELECT @pivot_columns = @pivot_columns + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.KPlus_Count END), 0) AS [' + ti.TimeName + '_K+]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Special_Count END), 0) AS [' + ti.TimeName + '_特权预约]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Meituan_Count END), 0) AS [' + ti.TimeName + '_美团]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Douyin_Count END), 0) AS [' + ti.TimeName + '_抖音]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.RoomFee_Count END), 0) AS [' + ti.TimeName + '_房费]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.Subtotal_Count END), 0) AS [' + ti.TimeName + '_小计]' + 
        ', ISNULL(MAX(CASE WHEN d.TimeSlotName = ''' + ti.TimeName + ''' THEN d.PreviousSlot_DirectFall END), 0) AS [' + ti.TimeName + '_上档直落]'
    FROM dbo.shoptimeinfo sti
    JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
    WHERE sti.Shopid = @ShopId AND sti.TimeMode = 1
    ORDER BY ti.BegTime;

    -- 2. 创建临时表存储优化后的夜间数据
    CREATE TABLE #TempNightData (
        ReportDate date, ShopName nvarchar(50), Weekday nvarchar(10),
        TotalRevenue decimal(18, 2), DayTimeRevenue decimal(18, 2), NightTimeRevenue decimal(18, 2),
        TotalBatchCount int, DayTimeBatchCount int, NightTimeBatchCount int,
        FreeMeal_KPlus int, FreeMeal_Special int, FreeMeal_Meituan int, FreeMeal_Douyin int,
        FreeMeal_BatchCount int, FreeMeal_Revenue decimal(18, 2),
        Buyout_BatchCount int, Buyout_Revenue decimal(18, 2),
        Changyin_BatchCount int, Changyin_Revenue decimal(18, 2),
        FreeConsumption_BatchCount int,
        NonPackage_Special int, NonPackage_Meituan int, NonPackage_Douyin int, 
        NonPackage_Others int, NonPackage_RoomFee int, NonPackage_YearCard int,
        Night_Verify_BatchCount int, Night_Verify_Revenue decimal(18, 2),
        DiscountFree_BatchCount int, DiscountFree_Revenue decimal(18, 2)
    );

    -- 3. 获取优化后的夜间数据
    INSERT INTO #TempNightData
    EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized_Final 
        @ShopId = @ShopId, 
        @BeginDate = @BeginDate, 
        @EndDate = @EndDate;

    -- 4. 构建动态SQL查询
    SET @sql = N'
    SELECT
        -- 基础信息
        nd.ReportDate AS [日期],
        nd.ShopName AS [门店],
        nd.Weekday AS [星期],
        ISNULL(h.TotalRevenue, nd.TotalRevenue) AS [营收_总收入],
        ISNULL(h.DayTimeRevenue, nd.DayTimeRevenue) AS [营收_白天档],
        ISNULL(h.NightTimeRevenue, nd.NightTimeRevenue) AS [营收_晚上档],
        ISNULL(h.TotalBatchCount, nd.TotalBatchCount) AS [全天总批数],
        ISNULL(h.DayTimeBatchCount, nd.DayTimeBatchCount) AS [白天档_总批次],
        ISNULL(h.NightTimeBatchCount, nd.NightTimeBatchCount) AS [晚上档_总批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [白天档_直落],
        ISNULL(h.NightTimeDropInBatch, 0) AS [晚上档_直落],
        ISNULL(h.BuffetGuestCount, 0) AS [自助餐人数],
        ISNULL(h.TotalDirectFallGuests, 0) AS [直落人数]'
        + @pivot_columns + ',
        -- 白天档汇总字段
        ISNULL(h.DayTimeBatchCount, nd.DayTimeBatchCount) AS [k+餐批次],
        ISNULL(h.DayTimeDropInBatch, 0) AS [k+餐直落批数],
        ISNULL(h.NightTimeDropInBatch, 0) AS [17点 18点 19点档直落],
        
        -- 晚上档数据（优化后的字段）
        ISNULL(nd.FreeMeal_KPlus, 0) AS [k+自由餐_k+],
        ISNULL(nd.FreeMeal_Special, 0) AS [k+自由餐_特权预约],
        ISNULL(nd.FreeMeal_Meituan, 0) AS [k+自由餐_美团],
        ISNULL(nd.FreeMeal_Douyin, 0) AS [k+自由餐_抖音],
        ISNULL(nd.FreeMeal_BatchCount, 0) AS [k+自由餐_小计],
        ISNULL(nd.FreeMeal_Revenue, 0) AS [k+自由餐_营业额],
        
        ISNULL(nd.Buyout_BatchCount, 0) AS [20点后_买断],
        ISNULL(nd.Buyout_Revenue, 0) AS [20点后_买断_营业额],
        
        ISNULL(nd.Changyin_BatchCount, 0) AS [20点后_畅饮],
        ISNULL(nd.Changyin_Revenue, 0) AS [20点后_畅饮_营业额],
        
        ISNULL(nd.FreeConsumption_BatchCount, 0) AS [20点后_自由消套餐],
        
        ISNULL(nd.NonPackage_Special, 0) AS [20点后_促销套餐_特权预约],
        ISNULL(nd.NonPackage_Meituan, 0) AS [20点后_促销套餐_美团],
        ISNULL(nd.NonPackage_Douyin, 0) AS [20点后_促销套餐_抖音],
        
        -- 新增和优化的字段
        ISNULL(nd.NonPackage_RoomFee, 0) AS [20点后_房费],
        ISNULL(nd.NonPackage_YearCard, 0) AS [20点后_年卡],
        
        ISNULL(nd.DiscountFree_BatchCount, 0) AS [招待批次],
        ISNULL(nd.DiscountFree_Revenue, 0) AS [招待金额],
        
        ISNULL(nd.Night_Verify_BatchCount, 0) AS [20点后_批次小计_净值],
        ISNULL(nd.Night_Verify_Revenue, 0) AS [20点后_营收金额_净值]
        
    FROM #TempNightData nd
    LEFT JOIN dbo.FullDailyReport_Header AS h ON nd.ReportDate = h.ReportDate AND h.ShopID = ' + CAST(@ShopId AS NVARCHAR) + '
    LEFT JOIN dbo.FullDailyReport_TimeSlotDetails AS d ON h.ReportID = d.ReportID
    GROUP BY
        nd.ReportDate, nd.ShopName, nd.Weekday, nd.TotalRevenue, nd.DayTimeRevenue, nd.NightTimeRevenue,
        nd.TotalBatchCount, nd.DayTimeBatchCount, nd.NightTimeBatchCount,
        h.TotalRevenue, h.DayTimeRevenue, h.NightTimeRevenue, h.TotalBatchCount, h.DayTimeBatchCount, h.NightTimeBatchCount,
        h.DayTimeDropInBatch, h.NightTimeDropInBatch, h.BuffetGuestCount, h.TotalDirectFallGuests,
        nd.FreeMeal_KPlus, nd.FreeMeal_Special, nd.FreeMeal_Meituan, nd.FreeMeal_Douyin,
        nd.FreeMeal_BatchCount, nd.FreeMeal_Revenue, nd.Buyout_BatchCount, nd.Buyout_Revenue,
        nd.Changyin_BatchCount, nd.Changyin_Revenue, nd.FreeConsumption_BatchCount,
        nd.NonPackage_Special, nd.NonPackage_Meituan, nd.NonPackage_Douyin, 
        nd.NonPackage_RoomFee, nd.NonPackage_YearCard,
        nd.DiscountFree_BatchCount, nd.DiscountFree_Revenue, nd.Night_Verify_BatchCount, nd.Night_Verify_Revenue
    ORDER BY nd.ReportDate';

    -- 5. 执行动态SQL
    EXEC sp_executesql @sql;

    -- 6. 清理临时表
    DROP TABLE #TempNightData;
END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 最终版联合报表存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建最终版联合报表存储过程失败: {str(e)}")
        return False

def test_final_unified_report(connection):
    """测试最终版联合报表"""
    print("\n🧪 测试最终版联合报表...")
    
    try:
        cursor = connection.cursor()
        
        # 测试最终版联合报表存储过程
        test_query = """
        EXEC dbo.usp_GenerateFinalUnifiedDailyReport 
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24', 
            @ShopId = 11
        """
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        rows = cursor.fetchall()
        
        print(f"✅ 最终版联合报表执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        print(f"📊 返回行数: {len(rows)}")
        
        if rows:
            row = rows[0]
            print(f"\n📋 2025-07-24 店铺11 最终版联合报表关键数据:")
            
            # 显示关键优化字段
            key_fields = [
                '营收_总收入', '营收_白天档', '营收_晚上档',
                '全天总批数', '白天档_总批次', '晚上档_总批次',
                '20点后_房费', '20点后_年卡', 
                '20点后_批次小计_净值', '20点后_营收金额_净值',
                '招待批次', '招待金额'
            ]
            
            for field_name in key_fields:
                if field_name in columns:
                    col_index = columns.index(field_name)
                    value = row[col_index] if col_index < len(row) else "N/A"
                    print(f"   {field_name}: {value}")
            
            # 保存结果到文件
            print(f"\n💾 保存最终版联合报表结果...")
            filename = f"最终版联合报表_店铺11_20250724.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                # 写入列头
                writer.writerow(columns)
                # 写入数据
                for row in rows:
                    writer.writerow(row)
            
            print(f"✅ 最终版联合报表结果已保存到: {filename}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ 测试最终版联合报表失败: {str(e)}")
        return False

def main():
    print("🚀 开始测试最终优化版联合报表...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 1. 测试优化版夜间详情存储过程
        success1 = test_optimized_night_procedure(connection)
        
        if success1:
            # 2. 创建最终版联合报表存储过程
            success2 = create_final_unified_report_procedure(connection)
            
            if success2:
                # 3. 测试最终版联合报表
                success3 = test_final_unified_report(connection)
                
                if success3:
                    print(f"\n🎉 最终优化版联合报表测试完成！")
                    print(f"\n📋 完成的优化:")
                    print(f"   ✅ 新增：20点后_房费 - 统计晚间档房费批次")
                    print(f"   ✅ 修改：20点后_年卡 - 模糊查询'年卡'关键词")
                    print(f"   ✅ 优化：20点后_批次小计_净值 - 减去自由餐批次")
                    print(f"   ✅ 优化：20点后_营收金额_净值 - 减去自由餐收入")
                    
                    print(f"\n📋 新存储过程:")
                    print(f"   1. usp_GenerateSimplifiedDailyReport_V7_Final_Optimized_Final - 优化版夜间详情")
                    print(f"   2. usp_GenerateFinalUnifiedDailyReport - 最终版联合报表")
                    
                    print(f"\n📋 使用方法:")
                    print(f"   EXEC dbo.usp_GenerateFinalUnifiedDailyReport '2025-07-24', '2025-07-24', 11")
                else:
                    print("\n❌ 最终版联合报表测试失败")
            else:
                print("\n❌ 最终版联合报表存储过程创建失败")
        else:
            print("\n❌ 优化版夜间详情存储过程测试失败")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
