#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的晚间档数据字段
"""

import pyodbc
import csv

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def test_optimized_night_procedure(connection):
    """测试优化后的夜间详情存储过程"""
    print("\n🧪 测试优化版夜间详情存储过程...")
    
    try:
        cursor = connection.cursor()
        
        # 测试优化版夜间详情存储过程
        test_query = """
        EXEC dbo.usp_GenerateSimplifiedDailyReport_V7_Final_Optimized 
            @ShopId = 11,
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24'
        """
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        rows = cursor.fetchall()
        
        print(f"✅ 优化版夜间详情存储过程执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        print(f"📊 返回行数: {len(rows)}")
        
        if rows:
            row = rows[0]
            print(f"\n📋 2025-07-24 店铺11 优化后的数据:")
            
            # 显示关键字段
            field_mapping = {
                'ReportDate': '日期',
                'ShopName': '门店',
                'TotalRevenue': '总收入',
                'NightTimeRevenue': '晚上档收入',
                'NightTimeBatchCount': '晚上档批次',
                'FreeMeal_BatchCount': '自由餐批次',
                'FreeMeal_Revenue': '自由餐收入',
                'NonPackage_RoomFee': '房费批次(新增)',
                'NonPackage_YearCard': '年卡批次(新增)',
                'Night_Verify_BatchCount': '净值批次(优化)',
                'Night_Verify_Revenue': '净值收入(优化)',
                'DiscountFree_BatchCount': '招待批次',
                'DiscountFree_Revenue': '招待金额'
            }
            
            for i, col in enumerate(columns):
                if col in field_mapping:
                    value = row[i] if i < len(row) else "N/A"
                    print(f"   {field_mapping[col]}: {value}")
            
            # 保存结果到文件
            print(f"\n💾 保存优化版夜间详情结果...")
            filename = f"优化版夜间详情_店铺11_20250724.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                # 写入列头
                writer.writerow(columns)
                # 写入数据
                for row in rows:
                    writer.writerow(row)
            
            print(f"✅ 优化版夜间详情结果已保存到: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试优化版夜间详情存储过程失败: {str(e)}")
        return False

def verify_year_card_logic(connection):
    """验证年卡查询逻辑"""
    print("\n🔍 验证年卡查询逻辑...")
    
    try:
        cursor = connection.cursor()
        
        # 查询包含"年卡"关键词的订单
        verify_query = """
        SELECT DISTINCT
            r.InvNo,
            fdc.FdCName,
            fdc.FdPrice * fdc.FdQty as ItemRevenue
        FROM dbo.RmCloseInfo r
        JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
        LEFT JOIN dbo.shoptimeinfo sti ON r.Shopid = sti.Shopid AND r.Beg_Key = sti.TimeNo
        WHERE r.Shopid = 11 
        AND r.CloseDatetime BETWEEN '2025-07-24 08:00:00' AND '2025-07-25 06:00:00'
        AND (sti.TimeMode = 2 OR (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(r.OpenDateTime, r.CloseDatetime)) >= 20))
        AND fdc.ShopId = 11
        AND fdc.FdCName LIKE N'%年卡%'
        ORDER BY r.InvNo
        """
        
        cursor.execute(verify_query)
        rows = cursor.fetchall()
        
        print(f"✅ 年卡相关订单查询完成")
        print(f"📊 找到年卡相关订单: {len(rows)}个")
        
        if rows:
            print(f"\n📋 年卡订单详情:")
            for row in rows:
                print(f"   订单号: {row[0]}, 项目: {row[1]}, 金额: {row[2]}")
        else:
            print("   📝 2025-07-24 没有找到包含'年卡'关键词的订单")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证年卡查询逻辑失败: {str(e)}")
        return False

def main():
    print("🚀 开始测试优化后的晚间档数据...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 1. 测试优化版夜间详情存储过程
        success1 = test_optimized_night_procedure(connection)
        
        if success1:
            # 2. 验证年卡查询逻辑
            verify_year_card_logic(connection)
            
            print(f"\n🎉 优化版晚间档数据测试完成！")
            print(f"\n📋 优化内容总结:")
            print(f"   ✅ 新增：NonPackage_RoomFee - 晚间档房费批次统计")
            print(f"   ✅ 修改：NonPackage_YearCard - 替换Others，模糊查询'年卡'")
            print(f"   ✅ 优化：Night_Verify_BatchCount - 减去自由餐批次")
            print(f"   ✅ 优化：Night_Verify_Revenue - 减去自由餐收入")
        else:
            print("\n❌ 优化版夜间详情存储过程测试失败")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
