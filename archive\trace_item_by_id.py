

import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'Dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 专案调查对象 ---
TARGET_FDCNAME_IN_TRANSACTION = '11:50-14:50K+自助餐(周六日)'

def trace_item_by_id():
    """通过交易记录中的名称找到FdNo，再用FdNo反查主数据和标签。"""
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} ---")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        # 1. 从交易记录中获取 FdNo
        print(f"\n--- 步骤A: 从 FdCashBak 中查找‘{TARGET_FDCNAME_IN_TRANSACTION}’对应的 FdNo ---")
        sql_get_id = "SELECT DISTINCT FdNo FROM dbo.FdCashBak WHERE FdCName = ?;"
        df_id = pd.read_sql_query(sql_get_id, cnxn, params=[TARGET_FDCNAME_IN_TRANSACTION])

        if df_id.empty:
            print(f"错误：在 FdCashBak 中也未找到名为‘{TARGET_FDCNAME_IN_TRANSACTION}’的交易记录。")
            return
        
        target_fdno = df_id.iloc[0, 0]
        print(f"成功找到对应的商品编号 (FdNo): {target_fdno}")

        # 2. 用 FdNo 反查 food 主数据表
        print(f"\n--- 步骤B: 用 FdNo={target_fdno} 反查当前的商品主表 (food) ---")
        sql_food = "SELECT FdNo, FdCName, IsDirectFall FROM dbo.food WHERE FdNo = ?;"
        df_food = pd.read_sql_query(sql_food, cnxn, params=[target_fdno])

        if df_food.empty:
            print(f"发现问题：商品 {target_fdno} 在当前的 food 主表中不存在！这证实了该商品已被删除。")
        else:
            print("在 food 主表中找到了该商品当前的记录：")
            print(df_food.to_string(index=False))

        # 3. 用 FdNo 反查 foodlabel 标签表
        print(f"\n--- 步骤C: 用 FdNo={target_fdno} 反查标签表 (foodlabel) ---")
        sql_label = "SELECT Type, Category1, CtTypeName FROM dbo.foodlabel WHERE FdNo = ?;"
        df_label = pd.read_sql_query(sql_label, cnxn, params=[target_fdno])

        if df_label.empty:
            print(f"发现问题：商品 {target_fdno} 在 foodlabel 表中没有任何标签记录。")
        else:
            print("在 foodlabel 表中找到了该商品的标签信息：")
            print(df_label.to_string(index=False))
            # 检查标签是否包含关键字
            label_contains_keyword = df_label.apply(
                lambda row: ('直落' in str(row['Type'])) or ('跨时段' in str(row['Category1'])) or \
                            ('直落' in str(row['Category1'])) or ('跨时段' in str(row['Category1'])) or \
                            ('直落' in str(row['CtTypeName'])) or ('跨时段' in str(row['CtTypeName'])), 
                axis=1
            ).any()
            if label_contains_keyword:
                print("\n>>> 最终结论：找到了！虽然该商品当前的名称可能已变，或者已从主表删除，但它关联的【标签】确实包含了‘直落’或‘跨时段’。")
                print("这就是它被我们的系统正确识别并打上 IsDirectFall=1 标志的原因。")
            else:
                 print("\n>>> 最终结论：奇怪！该商品的标签中也不包含‘直落’或‘跨时段’。这说明它被打上标签可能是个错误，需要重新检查打标逻辑。")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    trace_item_by_id()
