#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联合报表测试（单日数据：2025-07-24，店铺：11）
"""

import pyodbc
import pandas as pd

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def get_daytime_data(connection):
    """获取白天档数据"""
    print("🌅 获取白天档数据（2025-07-24，店铺11）...")
    
    try:
        cursor = connection.cursor()
        
        exec_query = """
        EXEC dbo.usp_GenerateDaytimePivotedReport 
            @BeginDate = '2025-07-24', 
            @EndDate = '2025-07-24', 
            @ShopId = 11
        """
        
        cursor.execute(exec_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据行
        rows = []
        for row in cursor.fetchall():
            rows.append(list(row))
        
        print(f"✅ 白天档数据获取成功: {len(rows)} 行, {len(columns)} 列")
        
        # 显示前10个字段的数据
        if rows:
            print("📊 白天档数据预览（前10个字段）:")
            for i, col in enumerate(columns[:10]):
                value = rows[0][i] if i < len(rows[0]) else "N/A"
                print(f"   {col}: {value}")
        
        return columns, rows
        
    except Exception as e:
        print(f"❌ 获取白天档数据失败: {str(e)}")
        return [], []

def get_night_data(connection):
    """获取晚上档数据"""
    print("\n🌙 获取晚上档数据（2025-07-24，店铺11）...")
    
    try:
        cursor = connection.cursor()
        
        # 获取晚上档数据，只选择有业务意义的字段
        night_query = """
        SELECT 
            h.ReportDate AS [日期],
            h.ShopName AS [门店],
            
            -- 免费餐相关
            nd.FreeMeal_KPlus AS [免费餐_K+],
            nd.FreeMeal_Special AS [免费餐_特权预约],
            nd.FreeMeal_Meituan AS [免费餐_美团],
            nd.FreeMeal_Douyin AS [免费餐_抖音],
            nd.FreeMeal_BatchCount AS [免费餐_批次数],
            nd.FreeMeal_Revenue AS [免费餐_收入],
            
            -- 买断相关
            nd.Buyout_BatchCount AS [买断_批次数],
            nd.Buyout_Revenue AS [买断_收入],
            
            -- 畅饮相关
            nd.Changyin_BatchCount AS [畅饮_批次数],
            nd.Changyin_Revenue AS [畅饮_收入],
            
            -- 自由消费
            nd.FreeConsumption_BatchCount AS [自由消费_批次数],
            
            -- 非套餐相关
            nd.NonPackage_Special AS [非套餐_特权预约],
            nd.NonPackage_Meituan AS [非套餐_美团],
            nd.NonPackage_Douyin AS [非套餐_抖音],
            nd.NonPackage_Others AS [非套餐_其他],
            
            -- 折扣免费相关（新增字段）
            nd.DiscountFree_BatchCount AS [折扣免费_批次数],
            nd.DiscountFree_Revenue AS [折扣免费_收入]
            
        FROM dbo.FullDailyReport_Header h
        INNER JOIN dbo.FullDailyReport_NightDetails nd ON h.ReportID = nd.ReportID
        WHERE h.ReportDate = '2025-07-24'
            AND h.ShopID = 11
        """
        
        cursor.execute(night_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据行
        rows = []
        for row in cursor.fetchall():
            rows.append(list(row))
        
        print(f"✅ 晚上档数据获取成功: {len(rows)} 行, {len(columns)} 列")
        
        # 显示数据
        if rows:
            print("📊 晚上档数据预览:")
            for i, col in enumerate(columns):
                value = rows[0][i] if i < len(rows[0]) else "N/A"
                print(f"   {col}: {value}")
        
        return columns, rows
        
    except Exception as e:
        print(f"❌ 获取晚上档数据失败: {str(e)}")
        return [], []

def create_excel_report(daytime_columns, daytime_rows, night_columns, night_rows):
    """创建Excel报表"""
    print("\n📊 创建Excel联合报表...")
    
    try:
        # 创建Excel文件
        filename = "KTV联合报表_20250724_店铺11.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # 1. 白天档数据表
            if daytime_rows:
                daytime_df = pd.DataFrame(daytime_rows, columns=daytime_columns)
                daytime_df.to_excel(writer, sheet_name='白天档数据', index=False)
                print("✅ 白天档数据已写入Excel")
            
            # 2. 晚上档数据表
            if night_rows:
                night_df = pd.DataFrame(night_rows, columns=night_columns)
                night_df.to_excel(writer, sheet_name='晚上档数据', index=False)
                print("✅ 晚上档数据已写入Excel")
            
            # 3. 联合数据表（如果两个数据源都有数据）
            if daytime_rows and night_rows:
                # 创建联合数据
                combined_data = []
                
                # 基础信息（来自白天档的前几列）
                base_info = daytime_rows[0][:3]  # 日期、门店、星期
                
                # 合并一行数据：白天档数据 + 晚上档数据（去掉重复的日期和门店）
                combined_row = base_info + daytime_rows[0][3:] + night_rows[0][2:]  # 跳过晚上档的日期和门店
                combined_columns = daytime_columns[:3] + daytime_columns[3:] + night_columns[2:]
                
                combined_data.append(combined_row)
                
                combined_df = pd.DataFrame(combined_data, columns=combined_columns)
                combined_df.to_excel(writer, sheet_name='联合报表', index=False)
                print("✅ 联合报表已写入Excel")
            
            # 4. 字段说明表
            field_info = []
            
            # 白天档字段说明
            for col in daytime_columns:
                field_info.append([col, "白天档", "来自usp_GenerateDaytimePivotedReport存储过程"])
            
            # 晚上档字段说明（去掉重复的基础字段）
            for col in night_columns[2:]:  # 跳过日期和门店
                field_info.append([col, "晚上档", "来自FullDailyReport_NightDetails表"])
            
            field_df = pd.DataFrame(field_info, columns=['字段名', '数据来源', '说明'])
            field_df.to_excel(writer, sheet_name='字段说明', index=False)
            print("✅ 字段说明已写入Excel")
        
        print(f"\n🎉 Excel报表创建成功: {filename}")
        
        # 显示统计信息
        print(f"\n📋 报表统计:")
        print(f"   白天档字段数: {len(daytime_columns)}")
        print(f"   晚上档字段数: {len(night_columns)}")
        print(f"   联合报表字段数: {len(daytime_columns) + len(night_columns) - 2}")  # 减去重复的日期和门店
        print(f"   数据日期: 2025-07-24")
        print(f"   店铺ID: 11")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建Excel报表失败: {str(e)}")
        return False

def main():
    print("🚀 开始创建KTV联合报表测试（2025-07-24，店铺11）...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 获取白天档数据
        daytime_columns, daytime_rows = get_daytime_data(connection)
        
        # 获取晚上档数据
        night_columns, night_rows = get_night_data(connection)
        
        # 创建Excel报表
        if daytime_columns or night_columns:
            success = create_excel_report(daytime_columns, daytime_rows, night_columns, night_rows)
            
            if success:
                print("\n🎉 联合报表测试完成！")
                print("📄 请查看生成的Excel文件：KTV联合报表_20250724_店铺11.xlsx")
            else:
                print("\n❌ 报表创建失败")
        else:
            print("\n❌ 没有获取到任何数据")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
