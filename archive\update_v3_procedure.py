

import pyodbc
import pandas as pd
import re

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_DATE = '20250724'
SHOP_ID = 11
PROC_NAME = 'dbo.usp_GenerateDayTimeReport_Simple_V3'

# --- Main Execution Logic ---
def get_modify_and_deploy():
    """
    Connects to the DB, gets the live procedure code, modifies it in memory,
    deploys the new version, and runs it for verification.
    """
    try:
        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()

            # --- Step 1: Get the live procedure code ---
            print(f"--- Step 1: Getting live source code for {PROC_NAME}... ---")
            original_sql = ""
            try:
                for row in cursor.execute(f"EXEC sp_helptext '{PROC_NAME}'"):
                    original_sql += row[0]
            except pyodbc.ProgrammingError:
                print(f"Error: Stored procedure {PROC_NAME} not found in the database.")
                return
            
            print("Source code fetched successfully.")

            # --- Step 2: Define the new logic and modify the code ---
            print("--- Step 2: Modifying the procedure logic in memory... ---")

            # This is the new CTE block we will insert
            new_cte_logic = """
    -- CTE for new, item-name-based direct fall counts
    WITH DirectFallCounts AS (
        SELECT
            COUNT(DISTINCT CASE WHEN ti_beg.BegTime < 1700 THEN rci.InvNo END) AS DayTimeDropInBatch_New,
            COUNT(DISTINCT CASE WHEN ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN rci.InvNo END) AS NightTimeDropInBatch_New
        FROM dbo.RmCloseInfo AS rci
        JOIN operatedata.dbo.FdCashBak fdc ON rci.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS AND rci.Shopid = fdc.ShopId
        LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rci.Shopid = sti_beg.Shopid AND rci.Beg_Key = sti_beg.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        WHERE rci.ShopId = @ShopId
          AND rci.CloseDatetime BETWEEN @BeginDate AND @EndDate
          AND fdc.FdCName LIKE N'%直落%'
    ),
    -- Main CTE for all other metrics
    AllMetrics AS (
"""
            # This is the new final SELECT list
            new_select_logic = """
    SELECT
        @TargetDate AS WorkDate,
        b.ShopName,
        DATENAME(weekday, @TargetDate) AS WeekdayName,
        -- Retained Metrics
        SUM((rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount)) AS TotalRevenue,
        SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS DayTimeRevenue,
        SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS NightTimeRevenue,
        COUNT(rt.InvNo) AS TotalBatchCount,
        COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
        COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
        SUM(rt.Numbers) AS TotalGuestCount,
        SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,
        -- New Direct Fall Metrics
        ISNULL((SELECT DayTimeDropInBatch_New FROM DirectFallCounts), 0) AS DayTimeDropInBatch,
        ISNULL((SELECT NightTimeDropInBatch_New FROM DirectFallCounts), 0) AS NightTimeDropInBatch
"""
            # Use regex to replace the old logic with the new, cleaner logic
            modified_sql = re.sub(r'CREATE PROCEDURE', 'CREATE OR ALTER PROCEDURE', original_sql, 1)
            modified_sql = re.sub(r'WITH AllMetrics AS \(', new_cte_logic, modified_sql, 1)
            modified_sql = re.sub(r'SELECT\s+@TargetDate AS WorkDate,.*?FROM dbo\.RmCloseInfo AS rt', new_select_logic + "\n    FROM dbo.RmCloseInfo AS rt", modified_sql, 1, re.DOTALL)
            # Clean up any leftover CTEs from the original V3 logic
            modified_sql = re.sub(r',\s*-- CTE 2:.*?NightTimeDetailData.*?(FROM AllMetrics am.*?)', r'\1', modified_sql, 1, re.DOTALL)

            # --- Step 3: Deploy the modified procedure ---
            print("--- Step 3: Deploying modified procedure to the database... ---")
            cursor.execute(modified_sql)
            print("Procedure deployed successfully.")

            # --- Step 4: Verification ---
            print("--- Step 4: Running the newly deployed procedure for verification... ---")
            df_verify = pd.read_sql(f"EXEC {PROC_NAME} @ShopId={SHOP_ID}, @TargetDate='{TARGET_DATE}'", conn)
            
            print("\n--- FINAL VERIFICATION RESULT ---")
            print(df_verify.to_string())

    except pyodbc.Error as ex:
        print(f"DATABASE ERROR: {ex}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    get_modify_and_deploy()

