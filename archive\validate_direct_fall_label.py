

import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'Dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 关键词 ---
KEYWORD = '%直落%'

def validate_label_reliability():
    """验证通过标签定位‘直落’项目的可靠性。"""
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} ---")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        # 1. 方法A: 标签法
        # 我们猜测‘直落’标签可能存在于 Type, Category1, 或 CtTypeName 字段中
        print("\n--- 方法A: 使用标签进行查找 ---")
        sql_label = f"""SELECT DISTINCT f.FdNo, f.FdCName 
                       FROM dbo.food f
                       JOIN dbo.foodlabel fl ON f.FdNo = fl.FdNo
                       WHERE fl.Type LIKE ? OR fl.Category1 LIKE ? OR fl.CtTypeName LIKE ?;"""
        df_label = pd.read_sql_query(sql_label, cnxn, params=[KEYWORD, KEYWORD, KEYWORD])
        print(f"通过标签法，在 foodlabel 的 Type/Category1/CtTypeName 字段中找到 {len(df_label)} 个‘直落’商品。")
        if not df_label.empty:
            print("详情如下:")
            print(df_label.to_string(index=False))

        # 2. 方法B: 模糊匹配法
        print("\n--- 方法B: 直接模糊查询 food 表 ---")
        sql_fuzzy = "SELECT FdNo, FdCName FROM dbo.food WHERE FdCName LIKE ?;"
        df_fuzzy = pd.read_sql_query(sql_fuzzy, cnxn, params=[KEYWORD])
        print(f"通过直接模糊查询 food.FdCName，找到 {len(df_fuzzy)} 个‘直落’商品。")
        if not df_fuzzy.empty:
            print("详情如下:")
            print(df_fuzzy.to_string(index=False))

        # 3. 对比分析
        print("\n--- 对比分析结论 ---")
        # 使用 merge 来找到两边的交集和差异
        merged_df = pd.merge(df_label, df_fuzzy, on=['FdNo', 'FdCName'], how='outer', indicator=True)
        
        label_only = merged_df[merged_df['_merge'] == 'left_only']
        fuzzy_only = merged_df[merged_df['_merge'] == 'right_only']
        both = merged_df[merged_df['_merge'] == 'both']

        print(f"共同找到的商品数量: {len(both)}")
        if len(label_only) == 0 and len(fuzzy_only) == 0:
            print("-> 结论：非常棒！两种方法找到的结果完全一致。这证明使用 foodlabel 表中的标签来识别‘直落’是高度可靠的。")
        else:
            print(f"-> 警告：两种方法结果不完全一致！")
            if not label_only.empty:
                print(f"  - 仅在标签法中找到的商品 ({len(label_only)}个):\n{label_only[['FdNo', 'FdCName']].to_string(index=False)}")
            if not fuzzy_only.empty:
                print(f"  - 仅在模糊查询中找到的商品 ({len(fuzzy_only)}个):\n{fuzzy_only[['FdNo', 'FdCName']].to_string(index=False)}")
            print("-> 建议：需要人工检查差异，以确定哪种方法更准确，或是否需要组合使用。")

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    validate_label_reliability()

