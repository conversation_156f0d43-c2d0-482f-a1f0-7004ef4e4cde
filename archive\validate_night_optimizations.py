#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接验证晚间档优化逻辑
"""

import pyodbc
import csv

def connect_database():
    """连接到数据库"""
    try:
        conn_str = (
            "DRIVER={SQL Server};"
            "SERVER=192.168.2.5;"
            "DATABASE=operatedata;"
            "UID=sa;"
            "PWD=Musicbox123;"
        )
        
        connection = pyodbc.connect(conn_str)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def validate_room_fee_logic(connection):
    """验证房费批次逻辑"""
    print("\n🔍 验证房费批次逻辑...")
    
    try:
        cursor = connection.cursor()
        
        # 查询晚间档房费相关订单
        query = """
        SELECT 
            rt.InvNo,
            rt.OpenDateTime,
            rt.CloseDatetime,
            rt.Cash,
            rt.Cash_Targ,
            rt.CtNo,
            sti.TimeMode,
            CASE
                WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode
                WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) < 20 THEN 1
                WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2
                WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1
                ELSE 2
            END AS RevenueClassificationMode,
            CASE WHEN (rt.Cash > 0 OR rt.Cash_Targ > 0) THEN 1 ELSE 0 END AS HasRoomFee
        FROM dbo.RmCloseInfo rt
        LEFT JOIN dbo.shoptimeinfo sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
        WHERE rt.Shopid = 11 
        AND rt.CloseDatetime BETWEEN '2025-07-24 08:00:00' AND '2025-07-25 06:00:00'
        AND (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
        )
        AND rt.CtNo <> 19
        AND (rt.Cash > 0 OR rt.Cash_Targ > 0)
        ORDER BY rt.InvNo
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        print(f"✅ 房费批次查询完成")
        print(f"📊 晚间档房费批次数: {len(rows)}")
        
        if rows:
            print(f"\n📋 房费批次详情（前5个）:")
            for i, row in enumerate(rows[:5]):
                print(f"   {i+1}. 订单: {row[0]}, 现金: {row[3]}, 现金目标: {row[4]}, CtNo: {row[5]}")
        
        return len(rows)
        
    except Exception as e:
        print(f"❌ 验证房费批次逻辑失败: {str(e)}")
        return 0

def validate_year_card_logic(connection):
    """验证年卡批次逻辑"""
    print("\n🔍 验证年卡批次逻辑...")
    
    try:
        cursor = connection.cursor()
        
        # 查询包含"年卡"关键词的订单
        query = """
        SELECT DISTINCT
            r.InvNo,
            fdc.FdCName,
            fdc.FdPrice * fdc.FdQty as ItemRevenue,
            r.OpenDateTime,
            r.CloseDatetime,
            sti.TimeMode
        FROM dbo.RmCloseInfo r
        JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
        LEFT JOIN dbo.shoptimeinfo sti ON r.Shopid = sti.Shopid AND r.Beg_Key = sti.TimeNo
        WHERE r.Shopid = 11 
        AND r.CloseDatetime BETWEEN '2025-07-24 08:00:00' AND '2025-07-25 06:00:00'
        AND (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(r.OpenDateTime, r.CloseDatetime)) >= 20)
        )
        AND fdc.ShopId = 11
        AND fdc.FdCName LIKE N'%年卡%'
        ORDER BY r.InvNo
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        print(f"✅ 年卡批次查询完成")
        print(f"📊 年卡相关批次数: {len(rows)}")
        
        if rows:
            print(f"\n📋 年卡批次详情:")
            for row in rows:
                print(f"   订单: {row[0]}, 项目: {row[1]}, 金额: {row[2]}")
        else:
            print("   📝 2025-07-24 没有找到包含'年卡'关键词的订单")
        
        return len(rows)
        
    except Exception as e:
        print(f"❌ 验证年卡批次逻辑失败: {str(e)}")
        return 0

def validate_night_verify_logic(connection):
    """验证净值逻辑"""
    print("\n🔍 验证净值逻辑...")
    
    try:
        cursor = connection.cursor()
        
        # 查询晚间档总批次和自由餐批次
        query = """
        SELECT 
            COUNT(CASE WHEN (
                (sti.TimeMode = 2) OR 
                (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
            ) THEN 1 ELSE NULL END) AS NightTimeBatchCount,
            
            COUNT(CASE WHEN (
                (sti.TimeMode = 2) OR 
                (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
            ) AND rt.CtNo = 19 THEN 1 ELSE NULL END) AS FreeMealBatchCount,
            
            SUM(CASE WHEN (
                (sti.TimeMode = 2) OR 
                (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
            ) THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS NightTimeRevenue,
            
            SUM(CASE WHEN (
                (sti.TimeMode = 2) OR 
                (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
            ) AND rt.CtNo = 19 THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) AS FreeMealRevenue
            
        FROM dbo.RmCloseInfo rt
        LEFT JOIN dbo.shoptimeinfo sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
        WHERE rt.Shopid = 11 
        AND rt.CloseDatetime BETWEEN '2025-07-24 08:00:00' AND '2025-07-25 06:00:00'
        """
        
        cursor.execute(query)
        row = cursor.fetchone()
        
        if row:
            night_batch = row[0] or 0
            free_meal_batch = row[1] or 0
            night_revenue = row[2] or 0
            free_meal_revenue = row[3] or 0
            
            verify_batch = night_batch - free_meal_batch
            verify_revenue = night_revenue - free_meal_revenue
            
            print(f"✅ 净值逻辑验证完成")
            print(f"📊 晚间档总批次: {night_batch}")
            print(f"📊 自由餐批次: {free_meal_batch}")
            print(f"📊 净值批次: {verify_batch}")
            print(f"📊 晚间档总收入: {night_revenue:.2f}")
            print(f"📊 自由餐收入: {free_meal_revenue:.2f}")
            print(f"📊 净值收入: {verify_revenue:.2f}")
            
            return verify_batch, verify_revenue
        
        return 0, 0
        
    except Exception as e:
        print(f"❌ 验证净值逻辑失败: {str(e)}")
        return 0, 0

def create_simple_optimized_procedure(connection):
    """创建简化版优化存储过程"""
    print("\n🚀 创建简化版优化存储过程...")
    
    # 基于验证结果创建简化版存储过程
    drop_sql = "IF OBJECT_ID('dbo.usp_NightDetailsOptimized_Simple', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_NightDetailsOptimized_Simple"
    
    procedure_sql = """
CREATE PROCEDURE dbo.usp_NightDetailsOptimized_Simple
    @ShopId INT = 11,
    @BeginDate DATE = '2025-07-24',
    @EndDate DATE = '2025-07-24'
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @LoopBeginDateTime datetime = DATEADD(hour, 8, CAST(@BeginDate AS datetime));
    DECLARE @LoopEndDateTime datetime = DATEADD(hour, 6, CAST(DATEADD(day, 1, @BeginDate) AS datetime));

    SELECT
        @BeginDate AS ReportDate,
        (SELECT TOP 1 ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = @ShopId) AS ShopName,
        DATENAME(weekday, @BeginDate) AS Weekday,
        
        -- 基础统计
        COUNT(CASE WHEN (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
        ) THEN 1 ELSE NULL END) AS NightTimeBatchCount,
        
        COUNT(CASE WHEN (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
        ) AND rt.CtNo = 19 THEN 1 ELSE NULL END) AS FreeMeal_BatchCount,
        
        -- 新增：房费批次
        COUNT(CASE WHEN (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
        ) AND rt.CtNo <> 19 AND (rt.Cash > 0 OR rt.Cash_Targ > 0) THEN 1 ELSE NULL END) AS NonPackage_RoomFee,
        
        -- 修改：年卡批次
        (SELECT COUNT(DISTINCT r.InvNo) 
         FROM dbo.RmCloseInfo r
         JOIN operatedata.dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
         LEFT JOIN dbo.shoptimeinfo s ON r.Shopid = s.Shopid AND r.Beg_Key = s.TimeNo
         WHERE r.Shopid = @ShopId 
         AND r.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime
         AND ((s.TimeMode = 2) OR (s.TimeMode IS NULL AND DATEPART(hour, ISNULL(r.OpenDateTime, r.CloseDatetime)) >= 20))
         AND fdc.ShopId = @ShopId AND fdc.FdCName LIKE N'%年卡%') AS NonPackage_YearCard,
        
        -- 优化：净值批次（减去自由餐）
        (COUNT(CASE WHEN (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
        ) THEN 1 ELSE NULL END) - COUNT(CASE WHEN (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
        ) AND rt.CtNo = 19 THEN 1 ELSE NULL END)) AS Night_Verify_BatchCount,
        
        -- 优化：净值收入（减去自由餐）
        (SUM(CASE WHEN (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
        ) THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END) - 
         SUM(CASE WHEN (
            (sti.TimeMode = 2) OR 
            (sti.TimeMode IS NULL AND DATEPART(hour, ISNULL(rt.OpenDateTime, rt.CloseDatetime)) >= 20)
        ) AND rt.CtNo = 19 THEN (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) ELSE 0 END)) AS Night_Verify_Revenue
        
    FROM dbo.RmCloseInfo rt
    LEFT JOIN dbo.shoptimeinfo sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
    WHERE rt.Shopid = @ShopId 
    AND rt.CloseDatetime BETWEEN @LoopBeginDateTime AND @LoopEndDateTime

END
"""
    
    try:
        cursor = connection.cursor()
        # 先删除存储过程
        cursor.execute(drop_sql)
        connection.commit()
        # 创建新存储过程
        cursor.execute(procedure_sql)
        connection.commit()
        print("✅ 简化版优化存储过程创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建简化版存储过程失败: {str(e)}")
        return False

def test_simple_procedure(connection):
    """测试简化版存储过程"""
    print("\n🧪 测试简化版存储过程...")
    
    try:
        cursor = connection.cursor()
        
        # 测试简化版存储过程
        test_query = "EXEC dbo.usp_NightDetailsOptimized_Simple 11, '2025-07-24', '2025-07-24'"
        
        cursor.execute(test_query)
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 获取数据
        rows = cursor.fetchall()
        
        print(f"✅ 简化版存储过程执行成功！")
        print(f"📊 返回字段数: {len(columns)}")
        print(f"📊 返回行数: {len(rows)}")
        
        if rows:
            row = rows[0]
            print(f"\n📋 2025-07-24 店铺11 优化结果:")
            
            for i, col in enumerate(columns):
                value = row[i] if i < len(row) else "N/A"
                print(f"   {col}: {value}")
            
            # 保存结果到文件
            print(f"\n💾 保存简化版优化结果...")
            import csv
            filename = f"简化版优化结果_店铺11_20250724.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                # 写入列头
                writer.writerow(columns)
                # 写入数据
                for row in rows:
                    writer.writerow(row)
            
            print(f"✅ 简化版优化结果已保存到: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试简化版存储过程失败: {str(e)}")
        return False

def main():
    print("🚀 开始验证晚间档优化逻辑...")
    
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 1. 验证房费批次逻辑
        room_fee_count = validate_room_fee_logic(connection)
        
        # 2. 验证年卡批次逻辑
        year_card_count = validate_year_card_logic(connection)
        
        # 3. 验证净值逻辑
        verify_batch, verify_revenue = validate_night_verify_logic(connection)
        
        # 4. 创建简化版优化存储过程
        success1 = create_simple_optimized_procedure(connection)
        
        if success1:
            # 5. 测试简化版存储过程
            success2 = test_simple_procedure(connection)
            
            if success2:
                print(f"\n🎉 晚间档优化验证完成！")
                print(f"\n📋 验证结果汇总:")
                print(f"   ✅ 房费批次: {room_fee_count}")
                print(f"   ✅ 年卡批次: {year_card_count}")
                print(f"   ✅ 净值批次: {verify_batch}")
                print(f"   ✅ 净值收入: {verify_revenue:.2f}")
                
                print(f"\n📋 新存储过程:")
                print(f"   usp_NightDetailsOptimized_Simple - 简化版优化存储过程")
                print(f"   使用方法: EXEC dbo.usp_NightDetailsOptimized_Simple 11, '2025-07-24', '2025-07-24'")
            else:
                print("\n❌ 简化版存储过程测试失败")
        else:
            print("\n❌ 简化版存储过程创建失败")
    
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {str(e)}")
    
    finally:
        if connection:
            connection.close()
            print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
