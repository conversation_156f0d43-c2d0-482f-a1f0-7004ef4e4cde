#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直落订单验证脚本 - 生成SQL查询用于验证直落识别的准确性
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta

class DirectFallVerifier:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def get_data_for_verification(self, work_date):
        """获取用于验证的数据"""
        # 获取结账数据
        conn = self.get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
        
        close_query = f"""
        SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
        FROM rmcloseinfo 
        WHERE shopid = {self.shop_id} AND WorkDate = '{work_date}'
        ORDER BY CloseDatetime
        """
        
        close_df = pd.read_sql(close_query, conn)
        conn.close()
        
        # 获取时间段配置
        conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
        
        time_query = f"""
        SELECT st.*, t.TimeName, t.BegTime, t.EndTime
        FROM shoptimeinfo st
        LEFT JOIN timeinfo t ON st.timeno = t.timeno
        WHERE st.shopid = {self.shop_id}
        ORDER BY t.BegTime
        """
        
        time_df = pd.read_sql(time_query, conn)
        conn.close()
        
        # 过滤有效时间段
        time_df = time_df.dropna(subset=['BegTime', 'EndTime'])
        
        return close_df, time_df
    
    def parse_time_slot(self, beg_time, end_time, base_date):
        """解析时间段"""
        try:
            beg_time = int(beg_time)
            end_time = int(end_time)
            
            beg_hour = beg_time // 100
            beg_min = beg_time % 100
            end_hour = end_time // 100
            end_min = end_time % 100
            
            start_dt = base_date.replace(hour=beg_hour, minute=beg_min, second=0, microsecond=0)
            
            # 处理跨天时间段
            if end_time < beg_time:
                end_dt = (base_date + timedelta(days=1)).replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
            else:
                end_dt = base_date.replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
            
            return start_dt, end_dt
            
        except Exception as e:
            return None, None
    
    def estimate_consumption_period(self, close_time, avg_duration=2.5):
        """估算消费时段"""
        close_dt = pd.to_datetime(close_time)
        estimated_open = close_dt - timedelta(hours=avg_duration)
        return estimated_open, close_dt
    
    def time_overlap(self, start1, end1, start2, end2):
        """检查两个时间段是否重叠"""
        return start1 < end2 and end1 > start2
    
    def identify_direct_fall_orders(self, work_date):
        """识别直落订单"""
        print(f"分析 {work_date} 的直落订单...")
        
        close_df, time_df = self.get_data_for_verification(work_date)
        
        if close_df.empty:
            print("没有结账数据")
            return []
        
        print(f"结账记录: {len(close_df)} 条")
        print(f"时间段配置: {len(time_df)} 个")
        
        # 基准日期
        base_date = datetime.strptime(work_date, '%Y%m%d')
        
        # 存储直落订单信息
        direct_fall_orders = []
        
        # 分析每个时间段
        for _, slot in time_df.iterrows():
            slot_start, slot_end = self.parse_time_slot(
                slot['BegTime'], slot['EndTime'], base_date
            )
            
            if slot_start is None or slot_end is None:
                continue
            
            slot_name = slot.get('TimeName', f"{int(slot['BegTime']):04d}-{int(slot['EndTime']):04d}")
            
            print(f"\n分析时间段: {slot_name} ({slot_start.strftime('%H:%M')}-{slot_end.strftime('%H:%M')})")
            
            # 检查每个订单
            for _, order in close_df.iterrows():
                estimated_open, close_time = self.estimate_consumption_period(order['CloseDatetime'])
                
                # 检查是否与时间段重叠
                if self.time_overlap(estimated_open, close_time, slot_start, slot_end):
                    # 检查是否为直落（开台时间早于时间段开始）
                    if estimated_open < slot_start:
                        direct_fall_orders.append({
                            'InvNo': order['InvNo'],
                            'WorkDate': order['WorkDate'],
                            'CloseDatetime': order['CloseDatetime'],
                            'Tot': order['Tot'],
                            'EstimatedOpen': estimated_open,
                            'SlotName': slot_name,
                            'SlotStart': slot_start,
                            'SlotEnd': slot_end,
                            'TimeSlotRange': f"{slot_start.strftime('%H:%M')}-{slot_end.strftime('%H:%M')}"
                        })
        
        return direct_fall_orders
    
    def generate_verification_sql(self, direct_fall_orders, work_date):
        """生成验证SQL"""
        if not direct_fall_orders:
            print("没有识别到直落订单")
            return
        
        # 提取订单号
        invoice_numbers = [order['InvNo'] for order in direct_fall_orders]
        invoice_list = "', '".join(invoice_numbers)
        
        print(f"\n{'='*80}")
        print(f"直落订单验证SQL - {work_date}")
        print(f"{'='*80}")
        
        print(f"\n识别到 {len(direct_fall_orders)} 个直落订单:")
        for order in direct_fall_orders[:10]:  # 显示前10个
            print(f"  {order['InvNo']} - {order['TimeSlotRange']} - 估算开台: {order['EstimatedOpen'].strftime('%H:%M')} - 结账: {pd.to_datetime(order['CloseDatetime']).strftime('%H:%M')}")
        
        if len(direct_fall_orders) > 10:
            print(f"  ... 还有 {len(direct_fall_orders) - 10} 个订单")
        
        # 生成验证SQL
        verification_sql = f"""
-- 验证直落订单的SQL查询
-- 这些是我的算法识别为直落的订单，请检查是否准确

-- 1. 查看结账数据和估算的开台时间
SELECT 
    c.InvNo as '订单号',
    c.WorkDate as '营业日期',
    c.CloseDatetime as '结账时间',
    DATEADD(HOUR, -2.5, c.CloseDatetime) as '估算开台时间',
    c.Tot as '金额',
    c.VesaName as '支付方式'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '{work_date}'
    AND c.InvNo IN ('{invoice_list}')
ORDER BY c.CloseDatetime;

-- 2. 如果有开台数据，查看实际开台时间进行对比
SELECT 
    o.BookNo as '开台号',
    o.CustName as '客户名称',
    o.ComeDate as '开台日期',
    o.ComeTime as '开台时间',
    o.RmNo as '房间号',
    CONVERT(datetime, o.ComeDate + ' ' + o.ComeTime) as '开台时间戳'
FROM opencacheinfo o
WHERE o.shopid = 11 
    AND o.ComeDate = '{work_date}'
ORDER BY o.ComeTime;

-- 3. 时间段配置
SELECT 
    t.TimeName as '时间段名称',
    t.BegTime as '开始时间',
    t.EndTime as '结束时间',
    CASE 
        WHEN t.EndTime < t.BegTime THEN '跨天时间段'
        ELSE '当天时间段'
    END as '时间段类型'
FROM shoptimeinfo st
LEFT JOIN timeinfo t ON st.timeno = t.timeno
WHERE st.shopid = 11
ORDER BY t.BegTime;

-- 4. 详细分析：按时间段查看重叠订单
WITH TimeSlots AS (
    SELECT 
        t.TimeName,
        t.BegTime,
        t.EndTime,
        CASE 
            WHEN t.EndTime < t.BegTime THEN 
                CONVERT(datetime, '{work_date} ' + FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00'))
            ELSE 
                CONVERT(datetime, '{work_date} ' + FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00'))
        END as SlotStart,
        CASE 
            WHEN t.EndTime < t.BegTime THEN 
                CONVERT(datetime, DATEADD(day, 1, '{work_date}') + ' ' + FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00'))
            ELSE 
                CONVERT(datetime, '{work_date} ' + FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00'))
        END as SlotEnd
    FROM shoptimeinfo st
    LEFT JOIN timeinfo t ON st.timeno = t.timeno
    WHERE st.shopid = 11 AND t.BegTime IS NOT NULL
),
OrdersWithEstimatedOpen AS (
    SELECT 
        InvNo,
        CloseDatetime,
        DATEADD(HOUR, -2.5, CloseDatetime) as EstimatedOpen,
        Tot
    FROM rmcloseinfo
    WHERE shopid = 11 AND WorkDate = '{work_date}'
        AND InvNo IN ('{invoice_list}')
)
SELECT 
    ts.TimeName as '时间段',
    FORMAT(ts.SlotStart, 'HH:mm') + '-' + FORMAT(ts.SlotEnd, 'HH:mm') as '时间范围',
    o.InvNo as '订单号',
    FORMAT(o.EstimatedOpen, 'yyyy-MM-dd HH:mm') as '估算开台时间',
    FORMAT(o.CloseDatetime, 'yyyy-MM-dd HH:mm') as '结账时间',
    o.Tot as '金额',
    CASE 
        WHEN o.EstimatedOpen < ts.SlotStart THEN '直落订单'
        ELSE '正常订单'
    END as '订单类型'
FROM TimeSlots ts
CROSS JOIN OrdersWithEstimatedOpen o
WHERE o.EstimatedOpen < ts.SlotEnd AND o.CloseDatetime > ts.SlotStart
ORDER BY ts.BegTime, o.CloseDatetime;
"""
        
        print(f"\n{'='*80}")
        print("请执行以下SQL来验证直落订单识别的准确性:")
        print(f"{'='*80}")
        print(verification_sql)
        
        # 保存SQL到文件
        with open(f'verify_direct_fall_{work_date}.sql', 'w', encoding='utf-8') as f:
            f.write(verification_sql)
        
        print(f"\nSQL已保存到文件: verify_direct_fall_{work_date}.sql")
        
        return verification_sql

def main():
    verifier = DirectFallVerifier()
    
    # 分析20250717的数据
    work_date = '20250717'
    
    try:
        # 识别直落订单
        direct_fall_orders = verifier.identify_direct_fall_orders(work_date)
        
        # 生成验证SQL
        verifier.generate_verification_sql(direct_fall_orders, work_date)
        
    except Exception as e:
        print(f"验证过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
