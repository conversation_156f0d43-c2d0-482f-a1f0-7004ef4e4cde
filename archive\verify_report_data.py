

import pyodbc
import pandas as pd

# --- Connection Details ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=192.168.2.5;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)

# --- SQL to Verify the Data ---
SQL_VERIFY = """
SELECT * 
FROM dbo.KTV_Simplified_Daily_Report 
WHERE ReportDate = '2025-07-24' AND ShopName = (SELECT ShopName FROM MIMS.dbo.ShopInfo WHERE Shopid = 11);
"""

# --- Main Execution Logic ---
def verify_report_data():
    """
    Connects to the database and verifies the data inserted by the report job.
    """
    try:
        with pyodbc.connect(CONN_STR) as conn:
            print("--- Verifying the data in KTV_Simplified_Daily_Report for 2025-07-24 ---")
            df = pd.read_sql(SQL_VERIFY, conn)
            print("\n--- Verification Results ---")
            if df.empty:
                print("Verification FAILED: No data found for the specified date and shop.")
            else:
                print("Verification SUCCESSFUL! Data found:")
                print(df.to_string())

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database Error Occurred: {sqlstate}")
        print(ex)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    verify_report_data()

