import pyodbc
import pandas as pd

# --- Configuration ---
DB_CONFIG = {
    'server': '***********',
    'database': 'operatedata',
    'username': 'sa',
    'password': 'Musicbox123'
}
SP_SCRIPT_FILE = 'usp_GenerateDayTimeReport_Simple_V3_revenue_fix.sql'
SP_NAME = 'usp_GenerateDayTimeReport_Simple_V3_fixed'

# --- Test Parameters ---
TEST_SHOP_ID = 11
TEST_DATE = '2025-07-24'

# --- Main Logic ---
def create_and_test_fixed_sp():
    """Creates and tests the revenue-fixed stored procedure."""
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']}"
    
    try:
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()

            # 1. Read and clean the SQL script
            print(f"--- Reading script from '{SP_SCRIPT_FILE}' ---")
            with open(SP_SCRIPT_FILE, 'r', encoding='utf-8') as f:
                sql_script = f.read().replace('GO', '')

            # 2. Drop existing procedure if it exists
            print(f"--- Dropping procedure '{SP_NAME}' if it exists ---")
            drop_sql = f"IF OBJECT_ID('{SP_NAME}', 'P') IS NOT NULL DROP PROCEDURE {SP_NAME}"
            cursor.execute(drop_sql)
            conn.commit()

            # 3. Create the new procedure
            print(f"--- Creating procedure '{SP_NAME}' ---")
            # The script now uses CREATE OR ALTER, but we will stick to DROP/CREATE for compatibility
            create_script = sql_script.replace('CREATE OR ALTER PROCEDURE', 'CREATE PROCEDURE')
            cursor.execute(create_script)
            conn.commit()
            print("Procedure created successfully.")

            # 4. Execute and verify
            print(f"\n--- Executing '{SP_NAME}' for Shop ID {TEST_SHOP_ID} on {TEST_DATE} ---")
            sp_exec_sql = f"EXEC {SP_NAME} @ShopId=?, @TargetDate=?"
            params = [TEST_SHOP_ID, TEST_DATE]
            
            df = pd.read_sql(sp_exec_sql, conn, params=params)

            if not df.empty:
                print("\n--- Test Execution Successful. Results: ---")
                print("Note: Revenue columns should now be calculated based on the full formula.")
                print(df.to_string())
            else:
                print("\nExecution successful, but no data was returned.")

    except Exception as e:
        print(f"\nAn error occurred: {e}")

if __name__ == '__main__':
    create_and_test_fixed_sp()
