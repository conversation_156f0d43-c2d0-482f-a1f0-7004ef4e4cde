import pyodbc
import datetime

# --- 配置 ---
SERVER = '192.168.2.5'
DATABASE = 'rms2019'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
JOB_NAME = 'RMS_Daily_Data_Sync_to_HQ'

def verify_sync_status():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    job_status = "Unknown"
    data_counts = {}

    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- 成功连接到 {SERVER} ---')

            # 1. 检查作业执行状态
            print(f'\n--- 1. 正在检查作业 "{JOB_NAME}" 的最后执行状态... ---')
            job_history_sql = """
            SELECT TOP 1 h.run_status
            FROM msdb.dbo.sysjobs j
            JOIN msdb.dbo.sysjobhistory h ON j.job_id = h.job_id
            WHERE j.name = ? AND h.step_id = 0
            ORDER BY h.run_date DESC, h.run_time DESC;
            """
            cursor.execute(job_history_sql, JOB_NAME)
            result = cursor.fetchone()
            if result:
                status_code = result[0]
                if status_code == 1:
                    job_status = "成功 (Succeeded)"
                elif status_code == 0:
                    job_status = "失败 (Failed)"
                else:
                    job_status = f"其他状态 (Code: {status_code})"
            else:
                job_status = "未找到执行记录 (Not Found)"
            print(f"作业状态: {job_status}")

            # 2. 如果作业成功，检查数据
            if job_status.startswith("成功"):
                print(f'\n--- 2. 正在检查同步的数据... ---')
                # 计算日期
                yesterday = datetime.date.today() - datetime.timedelta(days=1)
                business_date = datetime.date.today() - datetime.timedelta(days=1) # 开台按营业日，但为简化，我们先按自然日检查
                if datetime.datetime.now().hour < 9:
                     business_date = datetime.date.today() - datetime.timedelta(days=2)
                else:
                     business_date = datetime.date.today() - datetime.timedelta(days=1)

                print(f"目标营业日 (WorkDate): {business_date}")
                print(f"目标自然日 (BookDateTime): {yesterday}")

                # 查询数据
                tables_to_check = {
                    'opencacheinfo': f"SELECT COUNT(*) FROM rms2019.dbo.opencacheinfo WHERE WorkDate = '{business_date}'",
                    'openhistory': f"SELECT COUNT(*) FROM rms2019.dbo.openhistory WHERE WorkDate = '{business_date}'",
                    'bookcacheinfo': f"SELECT COUNT(*) FROM rms2019.dbo.bookcacheinfo WHERE CAST(BookDateTime AS DATE) = '{yesterday}'",
                    'bookhistory': f"SELECT COUNT(*) FROM rms2019.dbo.bookhistory WHERE CAST(BookDateTime AS DATE) = '{yesterday}'"
                }

                for table, sql in tables_to_check.items():
                    cursor.execute(sql)
                    count = cursor.fetchone()[0]
                    data_counts[table] = count
                    print(f"- 表 {table} 中找到 {count} 条昨日记录。")

    except Exception as e:
        print(f"执行过程中发生意外错误: {e}")

    return job_status, data_counts

if __name__ == '__main__':
    verify_sync_status()
