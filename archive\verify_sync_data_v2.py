import pyodbc
import datetime
import time

# --- 配置 ---
SERVER = '192.168.2.5'
DATABASE = 'rms2019'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
JOB_NAME = 'RMS_Daily_Data_Sync_to_HQ'

def get_job_status(cursor):
    """获取作业的当前运行状态"""
    # SQL from Microsoft Docs: https://docs.microsoft.com/en-us/sql/relational-databases/system-stored-procedures/sp-help-job-transact-sql
    sql = """
    EXEC msdb.dbo.sp_help_job @job_name = ?, @job_aspect = N'JOB';
    """
    try:
        cursor.execute(sql, JOB_NAME)
        result = cursor.fetchone()
        if result:
            return result.current_execution_status # 4 = Idle, 1 = Executing
    except pyodbc.ProgrammingError:
        # sp_help_job can sometimes return multiple result sets, which pyodbc doesn't like.
        # We can ignore this for our purpose.
        pass
    return None # Return None if status can't be determined

def get_last_run_status(cursor):
    """获取作业的最后一次完成状态"""
    sql = """
    SELECT TOP 1 h.run_status
    FROM msdb.dbo.sysjobs j
    JOIN msdb.dbo.sysjobhistory h ON j.job_id = h.job_id
    WHERE j.name = ? AND h.step_id = 0
    ORDER BY h.run_date DESC, h.run_time DESC;
    """
    cursor.execute(sql, JOB_NAME)
    result = cursor.fetchone()
    if result:
        return result[0] # 1 = Succeeded, 0 = Failed
    return None

def verify_sync_status_with_wait():
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;"
    
    try:
        with pyodbc.connect(connection_string, autocommit=True, timeout=15) as conn:
            cursor = conn.cursor()
            print(f'--- 成功连接到 {SERVER} ---')

            # 1. 启动作业
            print(f'\n--- 1. 正在启动作业 "{JOB_NAME}"... ---')
            cursor.execute("EXEC msdb.dbo.sp_start_job N'RMS_Daily_Data_Sync_to_HQ';")
            print("作业启动请求已发送。")

            # 2. 等待作业完成
            print("\n--- 2. 正在等待作业完成 (最长等待90秒)... ---")
            for i in range(18):
                time.sleep(5) # 每5秒检查一次
                # We need a new cursor for this check as the previous one might be in a weird state
                # after sp_help_job
                check_cursor = conn.cursor()
                status = get_job_status(check_cursor)
                if status == 4: # 4 = Idle
                    print("作业已完成执行。")
                    break
                else:
                    print(f"等待中... (当前状态: {status})")
            else:
                print("作业等待超时！")
                return

            # 3. 获取最终结果
            print(f'\n--- 3. 正在获取最终执行结果... ---')
            final_status_code = get_last_run_status(cursor)
            job_status = "未知"
            if final_status_code == 1:
                job_status = "成功 (Succeeded)"
            elif final_status_code == 0:
                job_status = "失败 (Failed)"
            else:
                job_status = f"未找到执行记录或状态未知 (Code: {final_status_code})"
            print(f"最终作业状态: {job_status}")

            # 4. 如果成功，检查数据
            if job_status.startswith("成功"):
                print(f'\n--- 4. 正在检查同步的数据... ---')
                yesterday = datetime.date.today() - datetime.timedelta(days=1)
                business_date = datetime.date.today() - datetime.timedelta(days=2) if datetime.datetime.now().hour < 9 else datetime.date.today() - datetime.timedelta(days=1)

                print(f"目标营业日 (WorkDate): {business_date}")
                print(f"目标自然日 (BookDateTime): {yesterday}")

                tables_to_check = {
                    'opencacheinfo': f"SELECT COUNT(*) FROM rms2019.dbo.opencacheinfo WHERE WorkDate = '{business_date}'",
                    'openhistory': f"SELECT COUNT(*) FROM rms2019.dbo.openhistory WHERE WorkDate = '{business_date}'",
                    'bookcacheinfo': f"SELECT COUNT(*) FROM rms2019.dbo.bookcacheinfo WHERE CAST(BookDateTime AS DATE) = '{yesterday}'",
                    'bookhistory': f"SELECT COUNT(*) FROM rms2019.dbo.bookhistory WHERE CAST(BookDateTime AS DATE) = '{yesterday}'"
                }

                for table, sql in tables_to_check.items():
                    cursor.execute(sql)
                    count = cursor.fetchone()[0]
                    print(f"- 表 {table} 中找到 {count} 条昨日记录。")

    except Exception as e:
        print(f"执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    verify_sync_status_with_wait()
