import pyodbc
import pandas as pd
import sys

# --- 数据库连接信息 ---
SERVER = '192.168.2.5'
DATABASE = 'Dbfood'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- 列名 ---
COLUMN_NAME = 'IsDirectFall'

def verify_tagged_items():
    """验证并查询所有被打上‘直落’标签的商品。"""
    cnxn = None
    try:
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
        print(f"--- 正在连接数据库 {DATABASE} ---")
        cnxn = pyodbc.connect(conn_str)
        print("连接成功！")

        print(f"\n--- 正在查询所有 '{COLUMN_NAME}' = 1 的商品 --- ")
        verify_sql = f"SELECT FdNo, FdCName FROM dbo.food WHERE {COLUMN_NAME} = 1 ORDER BY FdNo;"
        df_verify = pd.read_sql_query(verify_sql, cnxn)
        
        print(f"查询成功！共找到 {len(df_verify)} 个被打上‘直落’标签的商品。")
        print("列表如下：")
        print(df_verify.to_string(index=False))

    except Exception as e:
        print(f"\n--- 程序执行出错！ ---")
        print(f"错误信息: {e}")
        sys.exit(1)

    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    verify_tagged_items()
