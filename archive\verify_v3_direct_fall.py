
import os
import pyodbc
import pandas as pd

# --- Configuration ---
CONN_STR = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=***********;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
)
TARGET_DATE = '20250724'
SHOP_ID = 11

# --- Get path to the V3 SQL file ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
V3_PROC_PATH = os.path.join(SCRIPT_DIR, 'usp_GenerateDayTimeReport_Simple_V3_final_fix.sql')

# --- Query Definitions ---
SQL_EXEC_V3_PROC = f"EXEC dbo.usp_GenerateDayTimeReport_Simple_V3_final_fix @ShopId={SHOP_ID}, @TargetDate='{TARGET_DATE}'"
SQL_ITEM_NAME_LOGIC = f"""
WITH TargetInvoices AS (
    SELECT invno
    FROM rmcloseinfo
    WHERE WorkDate = '{TARGET_DATE}' AND ShopId = {SHOP_ID}
)
SELECT 
    COUNT(DISTINCT ti.invno) AS TotalDirectFall_ItemName
FROM TargetInvoices ti
JOIN FdCashBak fdc ON ti.invno = fdc.InvNo COLLATE Chinese_PRC_CI_AS
WHERE fdc.FdCName LIKE N'%直落%' AND fdc.ShopId = {SHOP_ID};
"""

# --- Main Execution Logic ---
def deploy_and_compare_robust():
    """
    Deploys the V3 procedure using a strict DROP-CREATE pattern, then runs the comparison.
    """
    try:
        print("--- Reading V3 procedure file... ---")
        with open(V3_PROC_PATH, 'r', encoding='utf-8') as f:
            sql_create_v3_proc = f.read()
        
        sql_drop_v3_proc = "IF OBJECT_ID('dbo.usp_GenerateDayTimeReport_Simple_V3_final_fix', 'P') IS NOT NULL DROP PROCEDURE dbo.usp_GenerateDayTimeReport_Simple_V3_final_fix"

        with pyodbc.connect(CONN_STR, autocommit=True) as conn:
            cursor = conn.cursor()

            print("\nSTEP 1: Deploying the V3 procedure (Strict DROP-CREATE)...")
            cursor.execute(sql_drop_v3_proc)
            cursor.execute(sql_create_v3_proc)
            print("V3 Procedure deployed successfully.")

            print("\nSTEP 2: Running comparison...")
            print("  Querying by EXECUTING the V3 procedure directly...")
            df_behavior = pd.read_sql(SQL_EXEC_V3_PROC, conn)
            
            if df_behavior.empty:
                print("    - V3 Procedure returned no data.")
                day_behavior_count = 0
                night_behavior_count = 0
            else:
                day_behavior_count = df_behavior.iloc[0].get('DayTimeDropInBatch', 0)
                night_behavior_count = df_behavior.iloc[0].get('NightTimeDropInBatch', 0)

            total_behavior_count = day_behavior_count + night_behavior_count
            print(f"    - Day-time Drop-in Batches (from Proc): {day_behavior_count}")
            print(f"    - Night-time Drop-in Batches (from Proc): {night_behavior_count}")
            print(f"    - TOTAL by Behavior (from Proc): {total_behavior_count}")

            print("\n  Querying based on ITEM NAME logic (FdCName LIKE '%直落%')...")
            df_item = pd.read_sql(SQL_ITEM_NAME_LOGIC, conn)
            item_name_count = df_item.iloc[0]['TotalDirectFall_ItemName']
            print(f"    - TOTAL by Item Name: {item_name_count}")

            print("\n--- ANALYSIS ---")
            print(f"The difference between the two methods is: {abs(total_behavior_count - item_name_count)}")
            if total_behavior_count > item_name_count:
                print("The BEHAVIORAL logic (from V3 proc) finds more instances than the item name search.")
            elif item_name_count > total_behavior_count:
                print("The ITEM NAME search finds more instances than the behavioral logic.")
            else:
                print("The two methods produce the exact same count.")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"DATABASE ERROR: {sqlstate}")
        print(ex)
    except FileNotFoundError:
        print(f"FILE ERROR: Could not find the V3 SQL file at {V3_PROC_PATH}")
    except Exception as e:
        print(f"AN UNEXPECTED ERROR OCCURRED: {e}")

if __name__ == "__main__":
    deploy_and_compare_robust()
