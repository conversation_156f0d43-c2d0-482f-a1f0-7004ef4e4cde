import pyodbc
from datetime import datetime, timedelta

SERVER = '192.168.2.5'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'
DATABASE = 'operatedata'

SHOP_ID = 11
TARGET_DATE = '2025-07-24'

BEGIN_DATETIME = f"{TARGET_DATE} 08:00:00"
end_date_obj = datetime.strptime(TARGET_DATE, '%Y-%m-%d') + timedelta(days=1)
END_DATETIME = end_date_obj.strftime('%Y-%m-%d 06:00:00')

def verify_with_full_details_final():
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}'
    
    # Corrected FOR XML PATH logic
    query = f"""
    WITH InvoiceItems AS (
        SELECT DISTINCT InvNo
        FROM dbo.FdCashBak
    )
    SELECT 
        rc.InvNo,
        rc.Tot AS RmCloseInfo_Total,
        STUFF((
            SELECT ', ' + FdCName
            FROM dbo.FdCashBak AS d
            WHERE d.InvNo = rc.InvNo
            FOR XML PATH('')
        ), 1, 2, '') AS AllItems
    FROM 
        dbo.RmCloseInfo rc
    JOIN 
        InvoiceItems ii ON rc.InvNo = ii.InvNo
    WHERE 
        rc.Shopid = ?
        AND rc.CloseDatetime BETWEEN ? AND ?
        AND (
            EXISTS (SELECT 1 FROM dbo.FdCashBak d WHERE d.InvNo = rc.InvNo AND d.FdCName LIKE N'%买断%')
            OR EXISTS (SELECT 1 FROM dbo.FdCashBak d WHERE d.InvNo = rc.InvNo AND d.FdCName LIKE N'%畅饮%')
            OR EXISTS (SELECT 1 FROM dbo.FdCashBak d WHERE d.InvNo = rc.InvNo AND d.FdCName LIKE N'%欢唱%')
            OR EXISTS (SELECT 1 FROM dbo.FdCashBak d WHERE d.InvNo = rc.InvNo AND d.FdCName LIKE N'%自由消%')
        )
    ORDER BY
        rc.InvNo;
    """

    try:
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()
            print(f"Verifying bills with full item details for ShopID={SHOP_ID} on {TARGET_DATE}...")
            cursor.execute(query, (SHOP_ID, BEGIN_DATETIME, END_DATETIME))
            rows = cursor.fetchall()
            
            print("\n--- Final Verification Result: Bills with Full Item Lists ---")
            header = "InvNo | RmCloseInfo_Total | All_Items_In_Bill"
            print(header)
            print("-" * (len(header) + 2 * 3))
            
            if not rows:
                print("(No bills found containing the specified keywords)")
            else:
                for row in rows:
                    print(f"{row.InvNo} | {row.RmCloseInfo_Total} | {row.All_Items_In_Bill}")
            
            print("\n--- Summary ---")
            print(f"Total distinct invoices found: {len(rows)}")
            print("-----------------")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    verify_with_full_details_final()